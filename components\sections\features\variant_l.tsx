import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { FeaturedItem } from "../../../types";

import { Button } from "components/ui";
export default function Features_L({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
}: FeaturesProps) {
  return (
    <Section className="py-20 bg-gray-100">
      <Container maxWidth={1280}>
        <div className="text-center max-w-2xl mx-auto mb-12">
          <CaptionAndTitleSection
            caption={caption}
            title={title}
            description={description}
          />
        </div>
        <FeatureItems features={featuredItems} />
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Text fontSize="sm" className="text-gray-600 mb-2">
          {caption}
        </Text>
      )}
      {title && (
        <Heading fontSize="3xl" type="h2" className="mb-4">
          {title}
        </Heading>
      )}
    </>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </div>
  );
}

export function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  const [activeIndex, setActiveIndex] = React.useState(0);

  return (
    <div className="w-full bg-white p-6">
      <div className="flex flex-col h-full">
        {/* Title Section */}
        <div className="flex-1 text-center mb-2">
          <Text fontSize="xl" className="text-gray-800 !font-semibold">
            {feature.title}
          </Text>
        </div>

        {/* Title and Button Section */}
        {feature.arrayOfTitleAndPrimaryButton &&
          feature.arrayOfTitleAndPrimaryButton.length > 0 && (
            <div className="text-center mb-6">
              {feature.arrayOfTitleAndPrimaryButton[activeIndex].title && (
                <Button
                  as="link"
                  link={feature?.primaryButton}
                  ariaLabel={
                    feature?.primaryButton?.ariaLabel ??
                    feature?.primaryButton?.label
                  }
                  className="text-black text-xs font-semibold hover:border-b-2 hover:border-primary"
                >
                  {feature.arrayOfTitleAndPrimaryButton[activeIndex].title}
                </Button>
              )}
            </div>
          )}

        {/* Image Section */}
        <div className="rounded-lg overflow-hidden mb-6">
          {feature?.mainImage?.image && (
            <Image
              className="object-cover h-[160px] w-full"
              src={feature.mainImage.image}
              width={350}
              height={250}
              alt={feature.mainImage.alt ?? `features-image-${index}`}
            />
          )}
        </div>

        {/* Pagination Section */}
        {feature.arrayOfTitleAndPrimaryButton &&
          feature.arrayOfTitleAndPrimaryButton.length > 0 && (
            <div className="flex justify-center gap-3 mb-8">
              {feature.arrayOfTitleAndPrimaryButton.map((_, idx) => (
                <button
                  key={idx}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    idx === activeIndex
                      ? "bg-transparent border-2 border-black"
                      : "bg-black/90"
                  }`}
                  onClick={() => setActiveIndex(idx)}
                  aria-label={`Go to slide ${idx + 1}`}
                />
              ))}
            </div>
          )}

        {/* Primary Button Section */}
        {feature?.primaryButton?.label && (
          <div className="flex justify-center">
            <Button
              as="link"
              link={feature?.primaryButton}
              ariaLabel={
                feature?.primaryButton?.ariaLabel ??
                feature?.primaryButton?.label
              }
              size="lg"
              className="text-primary flex items-center space-x-2 transition-all duration-200 hover:text-primary hover:scale-110 origin-left"
            >
              <span>{feature?.primaryButton?.label}</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export { Features_L };
