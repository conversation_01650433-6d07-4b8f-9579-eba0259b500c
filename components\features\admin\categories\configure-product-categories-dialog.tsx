import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog-shadcn";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { useDeleteProductCategoryMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { AddProductCategoryButton } from "./add-product-category-button";
import { DeleteProductCategoryButton } from "./delete-product-category-dialog";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";

interface ConfigureProductCategoriesDialogProps {
    product: any;
}

export function ConfigureProductCategoriesDialog({ product }: ConfigureProductCategoriesDialogProps) {
    const [open, setOpen] = useState(false);
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const token = useAuthStore((state) => state.token);
    const { mutate: deleteProductCategory, isPending: isDeleting } = useDeleteProductCategoryMutation(product.id, token);
    const { toast } = useToast();
    const permissions = useAuthStore((state) => state.permissions);
    const hasPermission = checkUserHasPermission(permissions, "delete:product_categories");

    const handleBulkDelete = () => {
        if (!hasPermission) {
            toast({
                title: "Error",
                description: "You are not authorized to delete categories",
                variant: "destructive",
            });
            return;
        }

        Promise.all(
            selectedCategories.map(categoryId =>
                deleteProductCategory(categoryId, {
                    onSuccess: () => {
                        toast({
                            title: "Success",
                            description: "Selected categories removed successfully",
                        });
                        setSelectedCategories([]);
                    },
                    onError: (error) => {
                        toast({
                            title: "Error",
                            description: error.message || "Failed to remove categories",
                            variant: "destructive",
                        });
                    },
                })
            )
        );
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                    Configure Categories
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>Configure Product Categories</DialogTitle>
                    <DialogDescription>
                        Manage categories for {product.name}
                    </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            {selectedCategories.length > 0 ? (
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={handleBulkDelete}
                                    disabled={isDeleting || !hasPermission}
                                >
                                    {isDeleting ? "Removing..." : "Remove Selected"}
                                    {isDeleting && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                                </Button>
                            ) : (
                                <AddProductCategoryButton productId={product.id} product={product} />
                            )}
                        </div>
                    </div>
                    <div className="border rounded-md">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b">
                                    <th className="h-10 px-4 text-left">Category Name</th>
                                    <th className="h-10 px-4 text-left">Value</th>
                                    <th className="h-10 px-4 text-right">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {product.product_categories?.map((cat: any) => (
                                    <tr key={cat.category_data.id} className="border-b last:border-0">
                                        <td className="px-4 py-2">{cat.category_data.name}</td>
                                        <td className="px-4 py-2">{cat.category_data.value}</td>
                                        <td className="px-4 py-2 text-right">
                                            <DeleteProductCategoryButton productId={product.id} categoryId={cat.category_data.id} />
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
} 