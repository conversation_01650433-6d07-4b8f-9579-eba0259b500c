import { default as author } from "./schemas/author";
import { default as blogBlockContent } from "./schemas/blogBlockContent";
import { default as category } from "./schemas/category";
import { default as post } from "./schemas/post";
import { default as tag } from "./schemas/tag";
import { default as archiveYear } from "./schemas/archiveYear";
import { default as archiveMonth } from "./schemas/archiveMonth";

const blogSchema = {
  author,
  blogBlockContent,
  category,
  post,
  tag,
  archiveYear,
  archiveMonth,
};

export default blogSchema;
