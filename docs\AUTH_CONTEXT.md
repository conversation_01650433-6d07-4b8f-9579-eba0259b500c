# Auth Context Middleware

## Table of Contents

- [Overview](#overview)
- [Implementation Details](#implementation-details)
  - [Route Configuration](#route-configuration)
  - [Redirect Logic](#redirect-logic)
  - [Authentication State Management](#authentication-state-management)
  - [Automatic Token Refresh](#automatic-token-refresh)
  - [Role-Based Redirects](#role-based-redirects)
  - [Server-Side Rendering Considerations](#server-side-rendering-considerations)

The `auth-context.tsx` file implements a client-side middleware-like pattern for handling authentication, route protection, and role-based access control.

## Overview

The Auth Context Provider acts as a middleware by:

1. Managing authentication state
2. Protecting routes based on authentication status
3. Implementing role-based access control
4. Handling automatic token refresh
5. Managing redirects based on user roles

## Implementation Details

### Route Configuration

The Auth Context defines four types of routes:

**Public Routes**

```typescript
const PUBLIC_ROUTES = [
  "/",
  "/log-in",
  "/sign-up",
  "/forgot-password",
  "/auth/callback",
  "/store/products",
  "/store/checkout",
  "/store/order-confirmation",
];
```

These routes are accessible to all users, regardless of authentication status.

**Protected Routes**

```typescript
const PROTECTED_ROUTES = [
  "/store/dashboard",
  "/store/orders",
  "/store/customers",
  "/store/account",
];
```

These routes require authentication and are primarily for store customers.

**Admin Routes**

```typescript
const ADMIN_ROUTES = [
  "/admin/dashboard",
  "/admin/orders",
  "/admin/customers",
  "/admin/products",
  "/admin/products/new",
  "/admin/products/[id]/edit",
  "/admin/requests",
  "/admin/groups",
  "/admin/categories",
];
```

These routes are restricted to admin users only.

**Auth Routes**

```typescript
const AUTH_ROUTES = [
  "/log-in",
  "/sign-up",
  "/forgot-password",
  "/auth/callback",
];
```

These are authentication-related routes that should redirect authenticated users to appropriate dashboards.

### Redirect Logic

The middleware implements a redirection mechanism based on the user's authentication status and role:

```typescript
function handleRedirects(
  slug: string,
  isAuthenticated: boolean,
  isAdmin: boolean
) {
  if (isAdmin && PROTECTED_ROUTES.includes(slug)) {
    return "/admin/dashboard";
  }

  if (isAuthenticated && AUTH_ROUTES.includes(slug)) {
    return "/store/dashboard";
  }

  if (!isAuthenticated && PROTECTED_ROUTES.includes(slug)) {
    return "/log-in";
  }

  return null;
}
```

This function:

- Redirects admin users from customer routes to the admin dashboard
- Redirects authenticated users from auth routes to the dashboard
- Redirects unauthenticated users from protected routes to sign-in
- Returns null if no redirect is needed

### Authentication State Management

The middleware uses Zustand's `authStore` to manage authentication state:

```typescript
const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
const accessTokenExpiry = useAuthStore((state) => state.expiresAt);
const refreshSession = useAuthStore((state) => state.refreshSession);
```

### Automatic Token Refresh

The middleware implements an automatic token refresh mechanism:

```typescript
useEffect(
  function validateAccessToken() {
    if (!pathname || !isAuthenticated() || !accessTokenExpiry) {
      return;
    }

    const calculateRefreshTime = () => {
      const currentTime = Date.now();
      const expirationTime = accessTokenExpiry * 1000;
      const bufferTime = 60000; // 1 minute buffer

      return Math.max(expirationTime - currentTime - bufferTime, 0);
    };

    const refreshTime = calculateRefreshTime();
    console.log("Refresh time:", refreshTime);

    const refreshTimer = setTimeout(() => {
      console.log("Refreshing session...");
      refreshSession();
    }, refreshTime);

    return () => clearTimeout(refreshTimer);
  },
  [isAuthenticated(), accessTokenExpiry, refreshSession, redirectTo]
);
```

This effect:

- Calculates when the token will expire
- Sets a timer to refresh the token 1 minute before expiration
- Cleans up the timer when the component unmounts

### Role-Based Redirects

The middleware also handles role-based redirects to ensure users only access appropriate sections:

```typescript
useEffect(() => {
  if (isAuthenticated() && redirectTo && redirectTo.startsWith("/")) {
    router.push(redirectTo);
    return;
  }

  if (isAuthenticated()) {
    // redirect admin to admin dashboard only from store dashboard
    if (isAdmin && pathname === "/store/dashboard") {
      router.replace("/admin/dashboard");
      return;
    }

    // Prevent admins from accessing customer-specific protected routes
    if (
      isAdmin &&
      pathname.startsWith("/store") &&
      PROTECTED_ROUTES.includes(pathname) &&
      !pathname.includes("/store/products") &&
      !pathname.includes("/store/orders") &&
      !pathname.includes("/store/order-confirmation") &&
      !pathname.includes("/store/checkout")
    ) {
      router.replace("/admin/dashboard");
      return;
    }

    // Only redirect non-admin users from admin paths
    if (
      !isAdmin &&
      (ADMIN_ROUTES.includes(pathname) ||
        (pathname.startsWith("/admin/products") && pathname.endsWith("/edit")))
    ) {
      router.replace("/store/dashboard");
    }
  }
}, [isAuthenticated(), isAdmin, pathname, redirectTo]);
```

This effect handles:

- Redirecting admin users from the store dashboard to the admin dashboard
- Preventing admins from accessing customer-specific protected routes
- Redirecting non-admin users from admin routes to the store dashboard

### Server-Side Rendering Considerations

The Auth Context Provider is configured to disable server-side rendering:

```typescript
export const AuthContextProvider = dynamic(
  () => Promise.resolve(AuthProvider),
  {
    ssr: false,
  }
);
```

This ensures authentication checks run only on the client-side, avoiding hydration mismatches.

## Flow Diagram

```mermaid
flowchart TD
    A[Page Load] --> B{Is User Authenticated?}
    B -->|No| C{Is Protected Route?}
    C -->|Yes| D[Redirect to Sign In]
    C -->|No| E[Allow Access]

    B -->|Yes| F{Is Auth Route?}
    F -->|Yes| G[Redirect to Dashboard]
    F -->|No| H{Is Admin?}

    H -->|Yes| I{Is Admin Route?}
    I -->|Yes| J[Allow Access]
    I -->|No| K{Is Protected Route?}
    K -->|Yes| L[Redirect to Admin Dashboard]
    K -->|No| M[Allow Access]

    H -->|No| N{Is Admin Route?}
    N -->|Yes| O[Redirect to Store Dashboard]
    N -->|No| P[Allow Access]

    Q[Token Expired] -->|Yes| R[Refresh Token]
```

## Integration with Next.js App

The AuthContextProvider is used to wrap the entire application:

```tsx
// In _app.tsx or layout.tsx
<AuthContextProvider>
  <Component {...pageProps} />
</AuthContextProvider>
```

## Authentication Process

1. **Initial Load**:

   - Check authentication status from AuthStore
   - Apply route protection based on current path

2. **Route Change**:

   - Re-evaluate authentication and permissions
   - Redirect if necessary

3. **Token Management**:

   - Monitor token expiration
   - Schedule refresh before expiry
   - Handle session timeout

4. **Role Enforcement**:
   - Ensure admins can only access admin routes or public routes
   - Ensure customers can only access store routes or public routes
   - Redirect unauthorized access attempts
