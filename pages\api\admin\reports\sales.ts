import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { format, subDays, subMonths, subWeeks } from "date-fns";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

// Define interfaces for the response types
export interface SalesDataPoint {
  label: string;
  orders: number;
  gross_sales: number;
  item_discounts: number;
  net_sales: number;
}

export interface WeeklySalesDataPoint extends SalesDataPoint {
  week_start: string;
  week_end: string;
}

export interface SalesSummary {
  orders: number;
  gross_sales: number;
  item_discounts: number;
  net_sales: number;
}

export interface SalesReportResponse {
  error?: string;
  data?: SalesDataPoint[] | WeeklySalesDataPoint[];
  summary?: SalesSummary;
}

// Query schema validation
const querySchema = z.object({
  period: z.enum(["day", "week", "month"]).default("day"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  range: z.string().optional(), // e.g., "30" for 30 days, "12" for 12 months, etc.
});

// Helper function to get week number
function getWeekNumber(date: Date) {
  const d = new Date(
    Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())
  );
  const dayNum = d.getUTCDay() || 7;
  d.setUTCDate(d.getUTCDate() + 4 - dayNum);
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
}

// Helper function to get week range
function getWeekRange(date: Date) {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const weekStart = new Date(date);
  weekStart.setDate(diff);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);

  return {
    start: weekStart.toISOString().split("T")[0],
    end: weekEnd.toISOString().split("T")[0],
  };
}

async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SalesReportResponse>
) {
  try {
    const { period, startDate, endDate, range } = querySchema.parse(req.query);
    const supabase = createSupabaseAdminClient();

    const today = new Date();
    const defaultEndDate = format(today, "yyyy-MM-dd");
    let defaultStartDate: string;

    if (range) {
      const rangeValue = parseInt(range);
      if (period === "day") {
        defaultStartDate = format(subDays(today, rangeValue), "yyyy-MM-dd");
      } else if (period === "week") {
        defaultStartDate = format(subWeeks(today, rangeValue), "yyyy-MM-dd");
      } else {
        defaultStartDate = format(subMonths(today, rangeValue), "yyyy-MM-dd");
      }
    } else {
      // Default ranges if not specified
      if (period === "day") {
        defaultStartDate = format(subDays(today, 30), "yyyy-MM-dd");
      } else if (period === "week") {
        defaultStartDate = format(subDays(today, 84), "yyyy-MM-dd"); // ~12 weeks
      } else {
        defaultStartDate = format(subMonths(today, 12), "yyyy-MM-dd");
      }
    }

    const start = startDate || defaultStartDate;
    const end = endDate || defaultEndDate;

    const { data: orders, error: ordersError } = await supabase
      .from("orders")
      .select(
        "id, created_at, total_amount, customer_id, order_items(id, product_id, order_id, item_price, quantity, products(id, price, product_group_prices(group_id, custom_price)))"
      )
      .gte("created_at", `${start}T00:00:00.000Z`)
      .lte("created_at", `${end}T23:59:59.999Z`)
      .order("created_at", { ascending: true });

    if (ordersError) {
      console.error("Error fetching orders:", ordersError);
      return res.status(400).json({ error: ordersError.message });
    }

    const productPriceMap: Record<string, number> = {};
    let salesData: Record<string, any> = {};

    orders.forEach((order) => {
      const orderDate = new Date(order.created_at);
      let key: string;
      const items = order.order_items || [];

      if (period === "day") {
        key = format(orderDate, "yyyy-MM-dd");
      } else if (period === "week") {
        const year = orderDate.getFullYear();
        const weekNum = getWeekNumber(orderDate);
        key = `${year}-W${weekNum.toString().padStart(2, "0")}`;

        if (!salesData[key]) {
          const weekRange = getWeekRange(orderDate);
          salesData[key] = {
            label: key,
            week_start: weekRange.start,
            week_end: weekRange.end,
            orders: 0,
            gross_sales: 0,
            item_discounts: 0,
            net_sales: 0,
          };
        }
      } else {
        // month
        const year = orderDate.getFullYear();
        const month = orderDate.getMonth() + 1;
        key = `${year}-${month.toString().padStart(2, "0")}`;
      }

      if (!salesData[key]) {
        salesData[key] = {
          label: key,
          orders: 0,
          gross_sales: 0,
          item_discounts: 0,
          net_sales: 0,
        };
      }

      // Calculate totals and discounts correctly
      let orderGrossSales = 0;
      let orderDiscounts = 0;

      items.forEach((item) => {
        const originalPrice = productPriceMap[item.product_id || ""] || 0;
        const originalTotal = originalPrice * item.quantity;
        const actualPrice = item.item_price || 0;
        const actualTotal = actualPrice * item.quantity;

        // Discount is the difference between list price and actual price
        const discount = originalTotal - actualTotal;
        orderDiscounts += discount > 0 ? discount : 0;
        orderGrossSales += originalTotal;
      });

      salesData[key].orders += 1;
      salesData[key].gross_sales += orderGrossSales;
      salesData[key].item_discounts += orderDiscounts;
      salesData[key].net_sales += order.total_amount || 0;
    });

    // Convert to array and sort by label
    const result = Object.values(salesData).sort((a: any, b: any) =>
      a.label.localeCompare(b.label)
    );

    // Calculate summary
    const summary = result.reduce(
      (acc: SalesSummary, item: SalesDataPoint) => {
        acc.orders += item.orders;
        acc.gross_sales += item.gross_sales;
        acc.item_discounts += item.item_discounts;
        acc.net_sales += item.net_sales;
        return acc;
      },
      {
        orders: 0,
        gross_sales: 0,
        item_discounts: 0,
        net_sales: 0,
      }
    );

    return res.status(200).json({ data: result, summary });
  } catch (error) {
    console.error("Error fetching sales data:", error);
    return res.status(500).json({ error: "Failed to fetch sales data" });
  }
}

export default checkAdmin(matchRoute({ GET: handler }));
