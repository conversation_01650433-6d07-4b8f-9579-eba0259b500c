import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase/index";
import { parse, serialize } from "cookie";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
    POST: handler
});

const sessionSchema = z.object({
    refresh_token: z.string().min(1),
    expires_in: z.number().min(1),
    expires_at: z.number().optional(),
    token_type: z.string().min(1),
});

async function handler(req: NextApiRequest, res: NextApiResponse) {
    const cookies = parse(req.headers.cookie ?? "");
    const session = cookies['maxton-auth-session'];

    if (!session) {
        return res.status(400).json({
            error: "Session not found",
        });
    }

    let parsedSession = {};
    try {
        parsedSession = JSON.parse(session);
    } catch {
        resetSessionCookie(res);
        return res.status(400).json({
            error: "Invalid session",
        });
    }

    const sessionObj = sessionSchema.safeParse(parsedSession);

    if (sessionObj.error) {
        resetSessionCookie(res);
        return res.status(400).json({
            error: sessionObj.error.message,
        });
    }

    const { data: sessionData } = sessionObj;
    const supabaseAdminClient = createSupabaseAdminClient();

    const { data: { user, session: newSession }, error } = await supabaseAdminClient.auth.refreshSession({ refresh_token: sessionData.refresh_token });

    if (error) {
        console.log("This shouldn't happen");
        resetSessionCookie(res);
        return res.status(403).json({
            error: error.message,
        });
    }

    // Remove user from session since we only need the refresh token from the session
    const newSessionStr = JSON.stringify({
        ...newSession,
        access_token: undefined,
        user: undefined,
    });

    const newCookie = serialize('maxton-auth-session', newSessionStr, {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'development',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 365,
        path: '/',
    });

    res.setHeader("Set-Cookie", newCookie);

    return res.status(200).json({
        user,
        access_token: newSession?.access_token,
        access_token_expires_in: newSession?.expires_in,
        access_token_expires_at: newSession?.expires_at,
    });
}

export function resetSessionCookie(res: NextApiResponse) {
    const newCookie = serialize('maxton-auth-session', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'development',
        sameSite: 'strict',
        maxAge: 0,
        path: '/',
    });

    res.setHeader("Set-Cookie", newCookie);
}