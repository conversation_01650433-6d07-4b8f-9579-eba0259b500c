import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { TextComponentProps } from ".";
import { MyPortableTextComponents } from "types";
import { useState, useEffect } from "react";

// Helper function to extract text from children
const extractTextFromChildren = (children: any) => {
  if (Array.isArray(children)) {
    return children
      .map((child) => {
        if (typeof child === "object") {
          // Handle nested strong tags
          if (child.props && child.props.children) {
            return extractTextFromChildren(child.props.children);
          }
          return child.text || "";
        }
        return String(child || "");
      })
      .join("");
  }
  return String(children || "");
};

// Improved generateId function
const generateId = (text: string) => {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");
};

// Modified extractHeadings function
const extractHeadings = (blocks: any[]) => {
  const headings = blocks
    .filter((block: any) => ["h1", "h2", "h3", "h4"].includes(block.style))
    .map((block: any) => {
      const text = block.children
        .map((child: any) => child.text)
        .join("")
        .trim();
      const id = generateId(text);
      return {
        text,
        level: parseInt(block.style.charAt(1)),
        id,
      };
    });
  return headings;
};

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      const text = extractTextFromChildren(children);
      const id = generateId(text);
      return (
        <h1
          id={id}
          className="mb-6 leading-loose text-gray-900 text-5xl sm:text-7xl scroll-mt-32"
        >
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      const text = extractTextFromChildren(children);
      const id = generateId(text);
      return (
        <h2
          id={id}
          className="mb-6 text-3xl sm:text-3xl leading-loose text-primary scroll-mt-32"
        >
          {children}
        </h2>
      );
    },
    h3: ({ children }) => {
      const text = extractTextFromChildren(children);
      const id = generateId(text);
      return (
        <h3
          id={id}
          className="mb-6 text-lg md:text-xl leading-loose text-primary scroll-mt-32"
        >
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      const text = extractTextFromChildren(children);
      const id = generateId(text);
      return (
        <h4
          id={id}
          className="mb-6 text-base sm:text-lg leading-loose text-gray-900 scroll-mt-32"
        >
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-3 font-body text-sm sm:text-base text-gray-900">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => {
      // Pass through the text content for ID generation
      return <strong>{children}</strong>;
    },
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        // className="text-tertiary hover:text-gray-900 underline"
        className="text-primary hover:text-primary/70 underline"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function TextComponent_D({
  heading,
  firstColumn,
}: TextComponentProps) {
  const headings = firstColumn ? extractHeadings(firstColumn) : [];
  const [activeId, setActiveId] = useState("");

  useEffect(() => {
    const elements = document.querySelectorAll("h1, h2, h3, h4");
  }, [headings]);

  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    setActiveId(id);

    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        {heading && (
          <Heading fontSize="xl" className="mb-5 text-center">
            {heading}
          </Heading>
        )}
        <Flex wrap justify="between" className="mx-auto">
          {/* Table of Contents */}
          {headings.length > 0 && (
            <div className="w-full hidden lg:inline lg:w-1/3 pr-8 mb-8 lg:mb-0 ">
              <div className="sticky top-24 bg-gray-100 p-6 rounded-lg">
                <h2 className="text-xl font-semibold mb-4">
                  Table of Contents
                </h2>
                <div className="flex flex-col">
                  {headings.map((heading, index) => {
                    const isIndented =
                      heading.level === 3 &&
                      headings
                        .slice(0, index)
                        .reverse()
                        .find((prev) => prev.level === 2)?.level === 2;

                    return (
                      <a
                        key={index}
                        href={`#${heading.id}`}
                        onClick={(e) => handleClick(e, heading.id)}
                        className={`block transition-colors duration-200 
                          ${isIndented ? "pl-6" : "pl-0"}
                          ${
                            activeId === heading.id
                              ? "text-primary font-medium"
                              : "text-gray-600"
                          }
                          hover:text-primary p-1`}
                      >
                        {heading.text}
                      </a>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          {firstColumn && (
            <div className="w-full lg:w-2/3 text-xs lg:pl-5 md:mb-0 lg:text-base">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

export { TextComponent_D };
