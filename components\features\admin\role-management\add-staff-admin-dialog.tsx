import { <PERSON><PERSON> } from "@/components/ui/shadcn-button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { RoleBadge } from "@/components/ui/role-badge";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { UserRole } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Search, User } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import useAuthStore from "@/stores/auth-store";
import { useUpdateUserRoleMutation, useSearchUsersQuery } from "@/queries/admin-queries";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface AddStaffAdminDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const searchUserSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

const promoteRoleSchema = z.object({
  role: z.enum(["admin", "staff", "manager"], {
    required_error: "Please select a role",
  }),
});

type SearchUserFormData = z.infer<typeof searchUserSchema>;
type PromoteRoleFormData = z.infer<typeof promoteRoleSchema>;

const roleOptions: { value: "admin" | "staff" | "manager"; label: string; description: string }[] = [
  {
    value: "manager",
    label: "Customer",
    description: "Customer with management capabilities",
  },
  {
    value: "staff",
    label: "Staff",
    description: "Limited admin access for day-to-day operations",
  },
  {
    value: "admin",
    label: "Admin",
    description: "Full administrative access to all features",
  },
];

export function AddStaffAdminDialog({
  open,
  onOpenChange,
}: AddStaffAdminDialogProps) {
  const [selectedUser, setSelectedUser] = useState<PublicUserWithCustomer | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const token = useAuthStore((state) => state.token);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const searchForm = useForm<SearchUserFormData>({
    resolver: zodResolver(searchUserSchema),
    defaultValues: {
      email: "",
    },
  });

  const roleForm = useForm<PromoteRoleFormData>({
    resolver: zodResolver(promoteRoleSchema),
    defaultValues: {
      role: "manager",
    },
  });

  const updateRoleMutation = useUpdateUserRoleMutation(token || "");

  // Use the search query hook - only search if query has length
  const searchUsersQuery = useSearchUsersQuery(
    searchQuery,
    1,
    50, // Increase limit to get more results for filtering
    token || "",
  );

  // Filter results client-side to show users with email containing the search query 
  const searchResults = searchQuery.length >= 3
    ? (searchUsersQuery.data?.data || []).filter((user: PublicUserWithCustomer) =>
      user?.email?.toLowerCase().includes(searchQuery.toLowerCase())
    )
    : [];

  const isSearching = searchUsersQuery.isLoading;
  const searchPerformed = searchQuery.length >= 3;

  // Handle search errors
  useEffect(() => {
    if (searchUsersQuery.error && searchQuery.length >= 3) {
      toast({
        title: "Search Error",
        description: (searchUsersQuery.error as Error).message || "Failed to search users",
        variant: "destructive",
      });
    }
  }, [searchUsersQuery.error, searchQuery]);

  // Debounced search handler
  const handleEmailChange = (email: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    setSelectedUser(null);

    if (email.length >= 3) {
      debounceTimerRef.current = setTimeout(() => {
        setSearchQuery(email);
      }, 500);
    } else {
      setSearchQuery("");
    }
  };

  const handleSearchSubmit = (data: SearchUserFormData) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    setSearchQuery(data.email);
  };

  const handleUserSelect = (user: PublicUserWithCustomer) => {
    setSelectedUser(user);
  };

  const handleRolePromotion = (data: PromoteRoleFormData) => {
    if (!selectedUser) return;

    updateRoleMutation.mutate(
      { id: selectedUser.id, role: data.role },
      {
        onSuccess: (responseData) => {
          toast({
            title: "Success",
            description: responseData.message || `${selectedUser.first_name} ${selectedUser.last_name}'s role has been updated to ${data.role}`,
            variant: "default",
          });

          // Reset and close dialog
          handleDialogClose(false);
        },
        onError: (error: Error) => {
          toast({
            title: "Error",
            description: error.message || "Failed to promote user",
            variant: "destructive",
          });
        },
      }
    );
  };

  const handleDialogClose = (open: boolean) => {
    if (!open) {
      searchForm.reset();
      roleForm.reset();
      setSearchQuery("");
      setSelectedUser(null);
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    }
    onOpenChange(open);
  };

  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Change User Role</DialogTitle>
          <DialogDescription>
            Search for an existing user and change their role between Customer, Staff, or Admin.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Search Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Find User</h3>

            <Form {...searchForm}>
              <form onSubmit={searchForm.handleSubmit(handleSearchSubmit)} className="space-y-4">
                <FormField
                  control={searchForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Search by Email</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Enter user's email address"
                            className="pl-10"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              handleEmailChange(e.target.value);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>

          {/* Search Results */}
          {isSearching && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span className="text-gray-600">Searching users...</span>
            </div>
          )}

          {searchPerformed && !isSearching && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Search Results</h3>

              {searchResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No users found with the given email.</p>
                  <p className="text-sm mt-2">Only users with manager role can be promoted.</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className={`
                        flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors
                        ${selectedUser?.id === user.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        }
                      `}
                      onClick={() => handleUserSelect(user)}
                    >
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="text-sm">
                            {user.first_name?.charAt(0)}
                            {user.last_name?.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {user.first_name} {user.last_name}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <RoleBadge role={user.role} />
                        {selectedUser?.id === user.id && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Role Selection */}
          {selectedUser && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Update User Role</h3>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="text-sm">
                      {selectedUser.first_name?.charAt(0)}
                      {selectedUser.last_name?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {selectedUser.first_name} {selectedUser.last_name}
                    </div>
                    <div className="text-sm text-gray-500">{selectedUser.email}</div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Current Role:</span>
                  <RoleBadge role={selectedUser.role} />
                </div>
              </div>

              <Form {...roleForm}>
                <form onSubmit={roleForm.handleSubmit(handleRolePromotion)} className="space-y-4">
                  <FormField
                    control={roleForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel className="text-base font-medium">
                          Select New Role
                        </FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            value={field.value}
                            className="space-y-3"
                          >
                            {roleOptions.map((option) => (
                              <div
                                key={option.value}
                                className="flex items-start space-x-3 space-y-0"
                              >
                                <RadioGroupItem
                                  value={option.value}
                                  id={option.value}
                                  className="mt-1.5"
                                />
                                <div className="flex-1 space-y-1">
                                  <label
                                    htmlFor={option.value}
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                  >
                                    <div className="flex items-center gap-2">
                                      <RoleBadge role={option.value} />
                                      <span>{option.label}</span>
                                    </div>
                                  </label>
                                  <p className="text-xs text-gray-500">
                                    {option.description}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleDialogClose(false)}
                      disabled={updateRoleMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={updateRoleMutation.isPending}
                    >
                      {updateRoleMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        "Update Role"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          )}

          {/* No user selected footer */}
          {!selectedUser && (
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleDialogClose(false)}
              >
                Cancel
              </Button>
            </DialogFooter>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}