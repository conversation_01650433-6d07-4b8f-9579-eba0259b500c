"use client"

import { use<PERSON><PERSON><PERSON>, EditorContent } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Link from "@tiptap/extension-link"
import { cn } from "@/lib/utils"
import { useEffect } from "react"
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Code,
  Quote,
  Link as LinkIcon,
} from "lucide-react"
import { Controller, useFormContext, type Control, type FieldPath, type FieldValues } from "react-hook-form"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"

const MenuBar = ({ editor }: { editor: any }) => {
  if (!editor) {
    return null
  }

  // Function to set link
  const setLink = () => {
    if (!editor) return

    const previousUrl = editor.getAttributes('link').href
    const url = window.prompt('URL', previousUrl)

    // cancelled
    if (url === null) return

    // empty
    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run()
      return
    }

    // update link
    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }

  return (
    <div className="flex flex-wrap items-center gap-1 border-b border-input bg-background p-1">
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!editor.can().chain().focus().toggleBold().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("bold") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Bold"
      >
        <Bold className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleItalic().run()}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("italic") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Italic"
      >
        <Italic className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={setLink}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("link") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Link"
      >
        <LinkIcon className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("heading", { level: 1 }) && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Heading 1"
      >
        <Heading1 className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("heading", { level: 2 }) && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Heading 2"
      >
        <Heading2 className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("bulletList") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Bullet List"
      >
        <List className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("orderedList") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Ordered List"
      >
        <ListOrdered className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleCode().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("code") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Code"
      >
        <Code className="h-4 w-4" />
      </button>
      <button
        type="button"
        onClick={() => editor.chain().focus().toggleBlockquote().run()}
        className={cn(
          "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
          "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
          "disabled:pointer-events-none disabled:opacity-50",
          editor.isActive("blockquote") && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50"
        )}
        aria-label="Blockquote"
      >
        <Quote className="h-4 w-4" />
      </button>
    </div>
  )
}

interface FormTipTapEditorProps {
  name: string
  label?: string
  placeholder?: string
  disabled?: boolean
}

export function FormTipTapEditor({
  name,
  label,
  placeholder = "Start typing...",
  disabled = false,
}: FormTipTapEditorProps) {
  const form = useFormContext()

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="space-y-1">
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <TipTapWrapper
              value={field.value}
              onChange={field.onChange}
              placeholder={placeholder}
              disabled={disabled}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}

interface TipTapWrapperProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
}

export function TipTapWrapper({
  value,
  onChange,
  placeholder,
  disabled,
}: TipTapWrapperProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Link.configure({
        openOnClick: true,
        linkOnPaste: true,
        protocols: ["http", "https", "mailto", "tel"],
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800 transition-colors',
        },
      }),
    ],
    content: value || "<p></p>",
    editable: !disabled,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
      onChange(html)
    },
    editorProps: {
      attributes: {
        class: "prose dark:prose-invert focus:outline-none max-w-none min-h-[100px] px-3 py-2",
      },
    },
  })

  // Update content when it changes from parent component (e.g., form reset or loaded data)
  useEffect(() => {
    if (editor && value && value !== editor.getHTML()) {
      editor.commands.setContent(value)
    }
  }, [value, editor])

  return (
    <div
      className={cn(
        "relative rounded-md border border-input bg-transparent",
        disabled && "cursor-not-allowed opacity-50"
      )}
      data-placeholder={placeholder}
    >
      <MenuBar editor={editor} />
      <EditorContent
        editor={editor}
        className={cn(
          // Style for links within the editor content
          "[&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:transition-colors"
        )}
      />
    </div>
  )
}

export function FormEditorExample() {
  return (
    <div className="w-full space-y-4">
      <h2 className="text-xl font-semibold">TipTap Editor with React Hook Form</h2>
      <p>
        This example shows how to use the TipTap editor with React Hook Form.
        You need to wrap this component with a <code>FormProvider</code> from react-hook-form.
      </p>
      <pre className="rounded-md bg-neutral-100 p-4 text-sm dark:bg-neutral-800">
        {`// Example usage
import { useForm, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { FormTipTapEditor } from "@/components/features/block-editor/form-editor"

const formSchema = z.object({
  content: z.string().min(10, "Content must be at least 10 characters long"),
})

export function MyForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "<p>Hello world</p>",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
  }

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormTipTapEditor 
          name="content" 
          label="Content" 
          placeholder="Write something awesome..." 
        />
        <button type="submit">Submit</button>
      </form>
    </FormProvider>
  )
}`}
      </pre>
    </div>
  )
}

export default FormTipTapEditor 