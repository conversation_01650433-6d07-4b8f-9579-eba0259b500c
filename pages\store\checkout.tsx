import { CheckoutBillingInformation } from "@/components/features/store/checkout/billing-information";
import { CheckoutDeliveryMethod } from "@/components/features/store/checkout/delivery-method";
import { CheckoutNotes } from "@/components/features/store/checkout/checkout-notes";
import { CheckoutOrderConfirmationDetails } from "@/components/features/store/checkout/order-confirmation";
import { CheckoutOrderSummary, CheckoutOrderSummarySkeleton } from "@/components/features/store/checkout/order-summary";
import { CheckoutShippingInformation } from "@/components/features/store/checkout/shipping-information";
import { CheckoutTaxExemptCard } from "@/components/features/store/checkout/tax-exempt";
import StoreLayout from "@/components/features/store/layout";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import {
  useAddNewOrderMutation,
  useAddShippingAddressMutation,
  useGetBillingAddressesQuery,
  useGetCustomerQuery,
  useGetShippingAddressesQuery,
  useUploadFile
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore from "@/stores/cart-store";
import { supabaseClient } from "@/supabase";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle2,
  CreditCard,
  Package,
  Receipt,
  ShoppingBag
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_DOCUMENT_TYPES = [
  "application/pdf",
  "application/docx",
  "application/doc",
];

const formSchema = z.object({
  // Billing Information
  billingAddressId: z.string().min(1, "Please select a billing address"),
  // Shipping Information
  shippingAddressId: z.string().min(1, "Please select a shipping address"),
  // Delivery Method
  ship_collect: z.boolean().optional(),
  ups_account_number: z.string().optional(),
  delivery_method: z.string().min(1, "Please select a delivery method"),
  // Payment Information
  paymentType: z.enum(["credit_card", "purchase_order"]),
  // cardNumber: z.string().optional(),
  // expiryDate: z.string().optional(),
  // cvc: z.string().optional(),
  // PO Information
  poNumber: z.string().optional(),
  tax_exempt: z.boolean().default(false),
  // Order Notes
  notes: z.string().max(500, "Notes must be 500 characters or less").optional(),
  poFile: z
    .instanceof(File)
    .refine(
      (file) => (file?.size || 0) <= MAX_FILE_SIZE,
      `Max file size is ${MAX_FILE_SIZE} bytes.`
    )
    .refine(
      (file) => ACCEPTED_DOCUMENT_TYPES.includes(file?.type || ""),
      "Only PDF, DOC, and DOCX files are allowed."
    )
    .optional(),
  items: z
    .array(
      z.object({
        id: z.string(),
        quantity: z.number().min(1),
        options: z
          .array(
            z.object({
              name: z.string(),
              value: z.union([z.string(), z.number()]),
            })
          )
          .optional(),
        calculatorData: z
          .object({
            providedData: z
              .object({
                "Jack Type": z.string(),
                "Piston Dia.": z.string(),
                "Car Speed": z.string(),
                "Empty Car": z.string(),
                Capacity: z.string(),
                "Down Speed Regulation": z.string(),
              })
              .optional(),
            results: z
              .object({
                "Rated Flow": z.number(),
                "Empty Static Pressure": z.number(),
                "Loaded Car Pressure": z.number(),
              })
              .optional(),
          })
          .optional(),
      })
    )
    .default([]),
});

export type CheckoutFormValues = z.infer<typeof formSchema>;

export default function Checkout() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const items = useCartStore((state) => state.items);
  const clearCart = useCartStore((state) => state.clearCart);
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const customerData = useGetCustomerQuery(userId);

  const billingAddresses = useGetBillingAddressesQuery(userId);
  const shippingAddresses = useGetShippingAddressesQuery(userId);

  const addNewOrderMutation = useAddNewOrderMutation(userId);
  const uploadFileMutation = useUploadFile(userId, "purchase-orders");
  const addShippingAddressMutation = useAddShippingAddressMutation(userId);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      billingAddressId: "",
      shippingAddressId: "",
      poNumber: "",
      poFile: undefined,
      items: [],
      paymentType: "credit_card",
      tax_exempt: false,
      notes: "",
    },
  });

  useEffect(
    function setItems() {
      form.setValue(
        "items",
        items.map((item) => {
          return {
            id: item.id,
            quantity: item.quantity,
            options: item.selectedOptions ?? undefined,
            calculatorData: item.calculatorData ?? undefined,
          };
        })
      );
    },
    [items]
  );

  useEffect(
    function setBillingAddress() {
      if (billingAddresses.data?.length === 0) return;
      if (billingAddresses.data?.length === 1) {
        const defaultAddress = billingAddresses.data[0].id ?? "";
        form.setValue("billingAddressId", defaultAddress);
      }
      const defaultAddress = billingAddresses.data?.find(
        (address) => address.default
      );
      form.setValue("billingAddressId", defaultAddress?.id ?? "");
    },
    [billingAddresses.data]
  );

  useEffect(
    function setShippingAddress() {
      if (shippingAddresses.data?.length === 0) return;
      if (shippingAddresses.data?.length === 1) {
        const defaultAddress = shippingAddresses.data[0].id ?? "";
        form.setValue("shippingAddressId", defaultAddress);
      }
      const defaultAddress = shippingAddresses.data?.find(
        (address) => address.default
      );
      form.setValue("shippingAddressId", defaultAddress?.id ?? "");
    },
    [shippingAddresses.data]
  );

  useEffect(
    function setTaxExemptBasedOnShippingState() {
      const shippingAddressId = form.getValues("shippingAddressId");
      if (!shippingAddressId || !shippingAddresses.data) return;

      const shippingAddress = shippingAddresses.data.find(
        (addr) => addr.id === shippingAddressId
      );

      // If shipping state is not Nevada, automatically set tax_exempt to false
      if (shippingAddress?.state !== "Nevada") {
        form.setValue("tax_exempt", false);
      }
    },
    [form.watch("shippingAddressId"), shippingAddresses.data]
  );

  // useEffect(function redirectToOrderConfirmation() {
  //     if (!addNewOrderMutation.isSuccess || !addNewOrderMutation.data) return;

  //     // Immediate redirect instead of setTimeout
  //     router.push(`/store/order-confirmation?orderId=${addNewOrderMutation.data}`);
  // }, [addNewOrderMutation.isSuccess, addNewOrderMutation.data, router])

  const MINIMUM_ORDER_AMOUNT = 50;
  const [showMinOrderAlert, setShowMinOrderAlert] = useState(false);

  const cartSubtotal = useCartStore((state) => state.getSubtotal)();
  const additionalAmount = useCartStore((state) => state.getAdditionalAmount)();
  const total = cartSubtotal + additionalAmount;

  const [activeTab, setActiveTab] = useState("shipping");
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [isAttachment, setSwitchToAttachment] = useState(false);

  const goToTab = (tab: string) => {
    setActiveTab(tab);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  useEffect(() => {
    if (activeTab === "payment") {
      // Get the current shipping address
      const shippingAddressId = form.getValues("shippingAddressId");
      const shippingAddress = shippingAddresses.data?.find(
        (addr) => addr.id === shippingAddressId
      );

      // Check if the shipping address is for a special country
      const shippingCountry = shippingAddress?.country?.toLowerCase();
      const shippingState = shippingAddress?.state?.toLowerCase();
      const isSpecialCountry =
        shippingCountry === "mexico" ||
        shippingCountry === "puerto rico" ||
        shippingState === "puerto rico" ||
        shippingState === "mexico";

      // Check shipping requirements based on country
      const shipCollectValue = form.getValues("ship_collect");
      const deliveryMethodValue = form.getValues("delivery_method");

      // Determine validation conditions based on country
      const hasShippingError = isSpecialCountry
        ? !deliveryMethodValue || deliveryMethodValue.trim() === ""
        : shipCollectValue === undefined ||
        !deliveryMethodValue ||
        deliveryMethodValue.trim() === "";

      if (hasShippingError) {
        // If shipping requirements aren't met, go back to shipping tab
        setActiveTab("shipping");

        // Show appropriate errors
        if (!isSpecialCountry && shipCollectValue === undefined) {
          form.setError("ship_collect", {
            message: "Please select whether to ship collect via UPS",
          });
        }

        if (!deliveryMethodValue || deliveryMethodValue.trim() === "") {
          form.setError("delivery_method", {
            message: "Please select a delivery method",
          });
        }

        // Scroll to the first error
        setTimeout(() => {
          const errorElement = document.querySelector(
            '[aria-invalid="true"], .text-destructive'
          );
          if (errorElement) {
            errorElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }, 100);
      }
    }
  }, [activeTab]);

  async function onSubmit(values: CheckoutFormValues) {
    console.log("onSubmit function called with values:", values);
    if (total < MINIMUM_ORDER_AMOUNT) {
      console.log("Order below minimum amount:", total);
      setShowMinOrderAlert(true);
      return;
    }

    setIsSubmitting(true);
    if (items.length === 0) {
      toast({
        title: "No items in cart",
        description: "Please add items to your cart before checking out",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Get the current shipping address
    const shippingAddressId = form.getValues("shippingAddressId");
    console.log("Current shipping address ID:", shippingAddressId);

    if (!shippingAddressId) {
      toast({
        title: "Missing shipping address",
        description: "Please select a shipping address before proceeding",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const shippingAddress = shippingAddresses.data?.find(
      (addr) => addr.id === shippingAddressId
    );

    // Check if the shipping address is for a special country
    const shippingCountry = shippingAddress?.country?.toLowerCase();
    const shippingState = shippingAddress?.state?.toLowerCase();
    const isSpecialCountry =
      shippingCountry === "mexico" ||
      shippingCountry === "puerto rico" ||
      shippingState === "puerto rico" ||
      shippingState === "mexico";

    const poFile = form.getValues("poFile");
    const poNumber = form.getValues("poNumber");

    if (values.paymentType === "purchase_order" && !poFile && !poNumber) {
      setIsSubmitting(false);
      form.setError("poFile", {
        message: "Please upload a PO file or enter a PO number",
      });
      form.setError("poNumber", {
        message: "Please upload a PO file or enter a PO number",
      });
      return;
    }

    // Explicitly check ship_collect is defined for non-special countries
    if (!isSpecialCountry && values.ship_collect === undefined) {
      setIsSubmitting(false);
      form.setError("ship_collect", {
        message: "Please select whether to ship collect via UPS",
      });
      return;
    }

    // Only check UPS account number if ship_collect is true and not a special country
    if (
      !isSpecialCountry &&
      values.ship_collect &&
      (!values.ups_account_number || values.ups_account_number.trim() === "")
    ) {
      setIsSubmitting(false);
      form.setError("ups_account_number", {
        message: "Please enter your UPS account number",
      });
      return;
    }

    let fileData: { path: string } | undefined;

    try {
      if (poFile) {
        try {
          fileData = await uploadFileMutation.mutateAsync(poFile);
        } catch (e: any) {
          if (e.message === "The resource already exists") {
            toast({
              title: "PO file already exists",
              description: "File with the same name already exists.",
              variant: "destructive",
            });
          } else {
            toast({
              title: "Error uploading PO file",
              description: "Error uploading file.",
              variant: "destructive",
            });
          }
          setIsSubmitting(false);
          return;
        }
      }

      const orderData = {
        billingAddressId: values.billingAddressId,
        shippingAddressId: values.shippingAddressId,
        ship_collect:
          values.ship_collect === undefined ? false : values.ship_collect,
        ups_account_number: values.ups_account_number,
        delivery_method: values.delivery_method,
        items: values.items,
        poFile: fileData?.path,
        poNumber: values.poNumber,
        paymentType: values.paymentType,
        tax_exempt: values.tax_exempt,
        notes: values.notes,
      };

      console.log("Submitting order data:", orderData);

      const result = await addNewOrderMutation.mutateAsync(orderData);
      console.log("Order submission result:", result);

      if (result) {
        setOrderId(result);
        setOrderConfirmed(true);
        goToTab("confirmation");
        clearCart();
        form.reset();
      }
    } catch (e) {
      console.error("Error during order submission:", e);
      if (fileData?.path) {
        await supabaseClient.storage
          .from("maxton-bucket")
          .remove([fileData.path]);
      }
      toast({
        title: "Error placing order",
        description: "An error occurred while placing your order",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <StoreLayout>
      <Head>
        <title>Checkout</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {/* <Button variant="ghost" size="icon" className="shrink-0" asChild>
                                <Link href="/store/cart">
                                    <ArrowLeft className="h-5 w-5" />
                                    <span className="sr-only">Back to cart</span>
                                </Link>
                            </Button> */}
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">
                    Checkout
                  </h1>
                  <p className="text-muted-foreground">
                    Complete your order details
                  </p>
                </div>
              </div>
              <Badge variant="outline" className="px-6 py-2 text-lg">
                <ShoppingBag className="mr-2 h-4 w-4" />
                <span>
                  {items.length} {items.length === 1 ? "item" : "items"}
                </span>
              </Badge>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={goToTab} className="space-y-8">
            <TabsList className="w-full grid grid-cols-3 gap-4 bg-transparent p-0">
              <TabsTrigger
                value="shipping"
                disabled={orderConfirmed}
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={activeTab === "shipping" ? "default" : "outline"}
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    1
                  </Badge>
                  <span>Shipping</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="payment"
                disabled={(() => {
                  // Get current shipping address to check if it's a special country
                  const shippingAddressId = form.getValues("shippingAddressId");
                  const shippingAddress = shippingAddresses.data?.find(
                    (addr) => addr.id === shippingAddressId
                  );

                  // Check if it's a special country
                  const shippingCountry =
                    shippingAddress?.country?.toLowerCase();
                  const shippingState = shippingAddress?.state?.toLowerCase();
                  const isSpecialCountry =
                    shippingCountry === "mexico" ||
                    shippingCountry === "puerto rico" ||
                    shippingState === "puerto rico" ||
                    shippingState === "mexico";

                  // For special countries, only check delivery method
                  // For regular countries, check both ship_collect and delivery method
                  return isSpecialCountry
                    ? !form.getValues("delivery_method") ||
                    form.getValues("delivery_method").trim() === ""
                    : form.getValues("ship_collect") === undefined ||
                    !form.getValues("delivery_method") ||
                    form.getValues("delivery_method").trim() === "";
                })()}
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={activeTab === "payment" ? "default" : "outline"}
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    2
                  </Badge>
                  <span>Payment</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="confirmation"
                disabled={!orderConfirmed}
                className="data-[state=active]:bg-muted data-[state=active]:text-foreground"
              >
                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      activeTab === "confirmation" ? "default" : "outline"
                    }
                    className="h-8 w-8 rounded-full p-0 flex items-center justify-center"
                  >
                    3
                  </Badge>
                  <span>Confirmation</span>
                </div>
              </TabsTrigger>
            </TabsList>

            <div
              className={`gap-8 ${activeTab === "confirmation" ? "" : "grid lg:grid-cols-12"
                }`}
            >
              <div className="lg:col-span-8 space-y-6">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <TabsContent value="shipping" className="space-y-6 mt-0">
                      <CheckoutBillingInformation checkoutForm={form} />
                      <CheckoutShippingInformation checkoutForm={form} />
                      <CheckoutDeliveryMethod checkoutForm={form} />
                      <CheckoutNotes checkoutForm={form} />
                      <div className="flex justify-end">
                        <Button
                          type="button"
                          onClick={() => {
                            // Get the current shipping address
                            const shippingAddressId =
                              form.getValues("shippingAddressId");
                            const shippingAddress =
                              shippingAddresses.data?.find(
                                (addr) => addr.id === shippingAddressId
                              );

                            // Check if the shipping address is for a special country
                            const shippingCountry =
                              shippingAddress?.country?.toLowerCase();
                            const shippingState =
                              shippingAddress?.state?.toLowerCase();
                            const isSpecialCountry =
                              shippingCountry === "mexico" ||
                              shippingCountry === "puerto rico" ||
                              shippingState === "puerto rico" ||
                              shippingState === "mexico";

                            // Only validate ship_collect if not a special country
                            if (!isSpecialCountry) {
                              if (
                                form.getValues("ship_collect") !== undefined
                              ) {
                                form.clearErrors("ship_collect");
                              } else {
                                form.setError("ship_collect", {
                                  message:
                                    "Please select whether to ship collect via UPS",
                                });
                              }
                            }

                            // Check if delivery_method has a value and clear error if it does
                            if (
                              form.getValues("delivery_method") &&
                              form.getValues("delivery_method").trim() !== ""
                            ) {
                              form.clearErrors("delivery_method");
                            } else {
                              form.setError("delivery_method", {
                                message: "Please select a delivery method",
                              });
                            }

                            // Proceed to payment tab only if validation passes
                            const shouldProceed = isSpecialCountry
                              ? form.getValues("delivery_method") &&
                              form.getValues("delivery_method").trim() !== ""
                              : form.getValues("ship_collect") !== undefined &&
                              form.getValues("delivery_method") &&
                              form.getValues("delivery_method").trim() !== "";

                            if (shouldProceed) {
                              goToTab("payment");
                            } else {
                              // Scroll to the first error
                              setTimeout(() => {
                                const errorElement = document.querySelector(
                                  '[aria-invalid="true"], .text-destructive'
                                );
                                if (errorElement) {
                                  errorElement.scrollIntoView({
                                    behavior: "smooth",
                                    block: "center",
                                  });
                                }
                              }, 100);
                            }
                          }}
                        >
                          Continue to Payment
                          <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="payment" className="space-y-6 mt-0">
                      <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center gap-4">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <CardTitle>Payment Method</CardTitle>
                            <CardDescription>
                              Select your preferred payment method
                            </CardDescription>
                          </div>
                        </CardHeader>
                        <CardContent className="grid gap-6">
                          <div className="grid grid-cols-2 gap-4">
                            <Card
                              className="relative cursor-pointer hover:border-primary [&:has(input:checked)]:border-primary"
                              onClick={() =>
                                form.setValue("paymentType", "credit_card")
                              }
                            >
                              <input
                                type="radio"
                                name="paymentType"
                                value="credit_card"
                                className="sr-only"
                                checked={
                                  form.watch("paymentType") === "credit_card"
                                }
                                onChange={() => { }}
                              />
                              <CardContent className="pt-6 text-center">
                                <CreditCard className="mx-auto h-6 w-6 mb-2" />
                                <p className="font-medium">Credit Card</p>
                              </CardContent>
                            </Card>
                            <Card
                              className="relative cursor-pointer hover:border-primary [&:has(input:checked)]:border-primary"
                              onClick={() =>
                                form.setValue("paymentType", "purchase_order")
                              }
                            >
                              <input
                                type="radio"
                                name="paymentType"
                                value="purchase_order"
                                className="sr-only"
                                checked={
                                  form.watch("paymentType") === "purchase_order"
                                }
                                onChange={() => { }}
                              />
                              <CardContent className="pt-6 text-center">
                                <Receipt className="mx-auto h-6 w-6 mb-2" />
                                <p className="font-medium">PO Number</p>
                              </CardContent>
                            </Card>
                          </div>

                          {form.watch("paymentType") === "credit_card" ? (
                            <div className="rounded-lg border bg-muted/50 p-4">
                              <p className="text-sm text-muted-foreground">
                                Credit cards are no longer accepted online. If
                                you wish to pay via credit card instead of
                                submitting a purchase order, payments may be
                                made with one of the 4 major bank credit/debit
                                cards by calling Maxton at{" "}
                                <a
                                  className="underline text-primary hover:text-primary/80"
                                  href="tel:************"
                                >
                                  ************
                                </a>{" "}
                                or faxing{" "}
                                <a
                                  className="underline text-primary hover:text-primary/80"
                                  href="tel:************"
                                >
                                  ************
                                </a>
                                .
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <div className="flex items-center justify-end">
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    setSwitchToAttachment(!isAttachment)
                                  }
                                  className="flex items-center gap-2"
                                >
                                  {isAttachment ? (
                                    <>
                                      <Receipt className="h-4 w-4" />
                                      Switch to PO Number
                                    </>
                                  ) : (
                                    <>
                                      <Package className="h-4 w-4" />
                                      Switch to PO File
                                    </>
                                  )}
                                </Button>
                              </div>

                              <FormField
                                control={form.control}
                                name="poNumber"
                                render={({ field }) => (
                                  <FormItem
                                    data-show={!isAttachment}
                                    className="data-[show=true]:block hidden"
                                  >
                                    <FormLabel>PO Number</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="Enter your purchase order number"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name="poFile"
                                render={({
                                  field: { value, onChange, ...field },
                                }) => (
                                  <FormItem
                                    data-show={isAttachment}
                                    className="data-[show=true]:block hidden"
                                  >
                                    <FormLabel>Attach PO Document</FormLabel>
                                    <FormControl>
                                      <div className="flex flex-col gap-2">
                                        <Input
                                          type="file"
                                          accept=".pdf,.doc,.docx"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (
                                              file &&
                                              file.size <= MAX_FILE_SIZE
                                            ) {
                                              onChange(file);
                                            }
                                          }}
                                          {...field}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                          Accepted file types: PDF, DOC, DOCX
                                          (Max size: 5MB)
                                        </p>
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <CheckoutTaxExemptCard
                        form={form}
                        shippingAddresses={shippingAddresses}
                      />
                    </TabsContent>

                    <TabsContent
                      value="confirmation"
                      className="space-y-6 mt-0"
                    >
                      <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                          <div className="rounded-full bg-green-100 p-3 mb-4">
                            <CheckCircle2 className="h-12 w-12 text-green-600" />
                          </div>
                          <h2 className="text-2xl font-bold mb-2">
                            Order Confirmed!
                          </h2>
                          <p className="text-muted-foreground mb-6">
                            Thank you for your order. We&apos;ve sent you a
                            confirmation email with the details.
                          </p>
                        </CardContent>
                      </Card>

                      {orderId && (
                        <CheckoutOrderConfirmationDetails orderId={orderId} />
                      )}

                      <div className="flex justify-end gap-4">
                        <Button variant="outline" asChild>
                          <Link href="/store/orders">View All Orders</Link>
                        </Button>
                        <Button asChild>
                          <Link href="/store/products">Continue Shopping</Link>
                        </Button>
                      </div>
                    </TabsContent>
                  </form>
                </Form>
              </div>

              {activeTab !== "confirmation" && (
                <div className="lg:col-span-4 space-y-6">
                  {customerData.isLoading ? (
                    <CheckoutOrderSummarySkeleton />
                  ) : (
                    <>
                      <CheckoutOrderSummary
                        items={items}
                        activeTab={activeTab}
                        orderConfirmed={orderConfirmed}
                        isSubmitting={isSubmitting}
                        form={form}
                        uploadFileMutation={uploadFileMutation}
                        addNewOrderMutation={addNewOrderMutation}
                        onSubmit={onSubmit}
                        shippingAddresses={shippingAddresses}
                      />
                    </>
                  )}
                </div>
              )}
            </div>
          </Tabs>
        </div>
      </div>

      <AlertDialog open={showMinOrderAlert} onOpenChange={setShowMinOrderAlert}>
        <AlertDialogContent className="sm:max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-amber-700">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Minimum Order Requirement
            </AlertDialogTitle>
            <AlertDialogDescription>
              Your order total must be at least{" "}
              {formatPrice(MINIMUM_ORDER_AMOUNT)} to proceed with the order.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Current total:</span>
              <span className="text-lg">{formatPrice(total)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Amount needed:</span>
              <span className="text-lg text-amber-600">
                {formatPrice(MINIMUM_ORDER_AMOUNT - total)}
              </span>
            </div>
          </div>

          <div className="flex justify-end">
            <Link
              href="/store/products"
              className="bg-neutral-900 p-3 rounded-md text-neutral-50 hover:bg-neutral-900/90 dark:bg-neutral-50 dark:text-neutral-900 dark:hover:bg-neutral-50/90"
            >
              Continue Shopping
            </Link>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </StoreLayout>
  );
}
