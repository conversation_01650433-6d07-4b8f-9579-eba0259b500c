import { PortableTextBlock } from "@portabletext/types";
import { lazy } from "react";
import dynamic from "next/dynamic";
import { SectionsProps, MainImage, Images, LabeledRoute } from "../../../types";

import * as TextComponentVariant from "@stackshift-ui/text-component";

const Variants = {
  variant_a: TextComponentVariant.TextComponent_A,
  variant_b: TextComponentVariant.TextComponent_B,
  variant_c: TextComponentVariant.TextComponent_C,
  variant_d: dynamic(() => import("./variant_d")),
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
};

export interface TextComponentProps {
  heading?: string;
  description?: string;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  thirdColumn?: PortableTextBlock[];
  mainImage?: MainImage;
  subtitle?: string;
  images?: Images[] | undefined;
  primaryButton?: LabeledRoute;
}

const displayName = "TextComponent";

export const TextComponent: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    heading: data?.variants?.title ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    secondColumn: data?.variants?.secondColumn ?? undefined,
    thirdColumn: data?.variants?.thirdColumn ?? undefined,
    mainImage: data?.variants?.mainImage ?? undefined,
    subtitle: data?.variants?.subtitle ?? undefined,
    description: data?.variants?.description ?? undefined,
    images: data?.variants?.images ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

TextComponent.displayName = displayName;
