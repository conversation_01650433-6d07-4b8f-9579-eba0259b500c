# Maxton Valve Project

## Table of Contents
- [Documentation](#documentation)
  - [API Documentation](#api-documentation)
  - [Features Documentation](#features-documentation)
  - [Technical Documentation](#technical-documentation)
  - [Auth Context Middleware](#auth-context-middleware)

## Documentation

The project documentation is split into several sections:

1. [API Documentation](./API.md)
   - Endpoints
   - Response types
   - Status codes

2. [Features Documentation](./FEATURES.md)
   - Store Dashboard features
   - Admin Dashboard features
   - Feature flows with diagrams

3. [Technical Documentation](./TECHNICAL.md)
   - State management
   - Data fetching
   - Libraries and tools
   - Authentication implementation
   - Architecture diagrams

4. [Auth Context Middleware](./AUTH_CONTEXT.md)
   - Route protection
   - Role-based access control
   - Authentication flow
   - Token refresh mechanism
