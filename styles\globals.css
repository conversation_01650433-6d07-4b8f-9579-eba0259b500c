@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/**
  IMPORTANT: ONLY ADD STYLES HERE IF THEY WILL BE USED GLOBALLY (IN ALL PAGES).
  IF NOT, THEN ADD THEM BY CSS MODULES (e.g. Products.module.css - contains all styling for product pages only).
**/

html {
  -webkit-text-size-adjust: 100%;
}

/* Custom scrollbar for testimonials */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ECWID COMPONENT CUSTOM STYLING */
/* TODO: MIGRATE THIS TO CSS MODULE */
/* .cart-icon {
  @apply relative before:content-[attr(data-count)] before:absolute before:flex before:items-center before:justify-center before:bg-primary before:rounded-full before:h-6 before:w-6 before:-top-3 before:-right-3 before:text-white;
} */
html#ecwid_html body#ecwid_body .cart-icon {
  position: relative;
}
html#ecwid_html body#ecwid_body .cart-icon .cart-link {
  display: block;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}
html#ecwid_html body#ecwid_body .cart-icon .ec-minicart__icon svg {
  width: 32px;
  height: 32px;
}
html#ecwid_html body#ecwid_body .ec-minicart__icon .icon-default path[stroke] {
  stroke-width: 1;
}
html#ecwid_html body#ecwid_body .ec-minicart--m .ec-minicart__counter::after {
  background-color: #007aff !important;
}
html#ecwid_html body#ecwid_body .ec-size .ec-store .ec-breadcrumbs {
  display: none !important;
}

/*
   THEME SETTINGS

   Default dark mode background and text color
   To override this, add background and text color classes along with a 'dark:' prefix to target components

   Example:
   <button className="bg-blue-500 text-white dark:bg-blue-50 text-blue-500">
     Save
   </button>
*/
:is(.dark) {
  @apply bg-background text-white;
}
/* END OF THEME SETTINGS */

/* hide navigation sidebar when screen is less than screen size */
@media (min-width: 1024px) {
  .mobile-nav {
    display: none;
  }
}

@media (min-width: 1560px) {
  .main-nav {
    left: 50%;
    align-items: center;
    width: auto;
  }
}
@media (max-width: 1559px) {
  .main-nav {
    left: 40%;
    align-items: center;
  }
}

/* PRODUCT INFO SWIPER CUSTOM STYLE */
/* TODO: MIGRATE THIS TO CSS MODULE */
.swiper-slide-thumb-active img {
  border: 1px solid #0045d8 !important;
}

.product-images-thumbs-swiper.swiper-container .swiper-slide {
  height: 0 !important;
  padding-bottom: 25%;
}

.product-images-thumbs-swiper.swiper-container .swiper-slide > div {
  height: 0 !important;
  padding-bottom: 100%;
}

.swiper-button-disabled {
  opacity: 0.5 !important;
}

.product-images-swiper.swiper-container {
  height: calc(auto + 1px);
  /* position: relative; */
}

.product-images-swiper.swiper-container {
  padding-top: 10px;
}

.product-images-swiper.swiper-container .swiper-wrapper {
  position: relative;
  height: 0 !important;
  padding-bottom: 100%;
}

.product-images-swiper.swiper-container .swiper-slide {
  height: 0 !important;
  padding-bottom: 100%;
}

.product-images-swiper.swiper-container .swiper-slide > div {
  height: 0 !important;
  padding-bottom: 100%;
}

.toastContainer {
  z-index: 1;
}

/** Sanity Studio embed **/

/* Cosmetic: Remove border-right of tools menu */
div[data-ui="Navbar"] > div > div:first-child > div:last-child {
  border-right: 0 !important;
}

/* Hide every thing in the right navbar */
div[data-ui="Navbar"] > div > div:last-child {
  display: none !important;
}

/*  Fallback: Hide manage project from menu */
a[data-ui="MenuItem"][href^="https://sanity.io/manage/project"],
a[data-ui="MenuItem"][href^="https://sanity.io/manage/project"] + hr
{
  display: none !important;
}

/* Fallback: Hide user and logout popover from menu */
button#presence-menu,
button#login-status-menu {
  display: none !important;
}

/* Desktop: when using field groups, we hide the first tab */
[data-ui="TabList"][data-testid="field-group-tabs"] > div:first-child {
  display: none;
}

/* Mobile: when using field groups, hide the first option */
select[data-testid="field-group-select"] > option:first-child {
  display: none;
}

/* Duplicate page settings modal */
.showBtn:hover .hide {
  display: block;
}

/** Help Guide **/
ul[aria-label="List of Content"] {
  height: 100% !important;
  display: flex !important;
  flex-direction: column;
}

ul[aria-label="List of Content"] > li {
  transform: none !important;
  flex: none !important;
  position: relative !important;
  left: initial !important;
  top: initial !important;
}

ul[aria-label="List of Content"] > li:last-child {
  margin-top: auto;
  padding-top: 10px;
}

ul[aria-label="List of Content"] > li:last-child:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid var(--card-border-color);
}

/**** STUDIO > COMPONENTS > component filter ****/
#component_filter .optionContainer li:hover,
#component_filter .optionContainer .highlight {
  background: #f2f3f5 !important;
}

/* hide default right icon */
#component_filter .icon_down_dir {
  display: none;
}
/* custom right icon */
#component_filter:after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 35px;
  border-left: 1px solid #ced2d9;
  background: url("data:image/svg+xml,%3Csvg data-sanity-icon='chevron-down' width='1em' height='1em' viewBox='0 0 25 25' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M17 10.5L12.5 15L8 10.5' stroke='currentColor' stroke-width='1.2' stroke-linejoin='round'%3E%3C/path%3E%3C/svg%3E");
  background-size: 25px;
  background-position: center;
  background-repeat: no-repeat;
}
/* add outline when dropdown is active/focused */
#component_filter:has(.optionListContainer.displayBlock) .search-wrapper {
  outline: 2px solid #2275fb;
  outline-offset: 1px;
}
#component_filter .select-component-close-btn:hover {
  fill: red;
}

#webriq_components_filter .select-component-close-btn:hover {
  fill: red;
}
/**** end of STUDIO > COMPONENTS > component filter ****/
/* End */

/* theme settings color picker */
.color-picker {
  margin-bottom: 10px;
}

.custom-layout .react-colorful__saturation {
  margin: 15px 0;
  border-radius: 5px;
  border-bottom: none;
}

.custom-layout .react-colorful__hue {
  order: 0;
}

.custom-layout .react-colorful__pointer {
  position: absolute;
  z-index: 1;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border: 2px solid #fff;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-layout .react-colorful__hue,
.custom-layout .react-colorful__alpha {
  height: 10px;
  border-radius: 5px;
}

.custom-layout .react-colorful__hue-pointer,
.custom-layout .react-colorful__alpha-pointer {
  width: 20px;
  height: 20px;
}

/* END OF theme settings color picker */

@layer base {
  :root {
    --color-primary: 209 99% 32%;
    --color-secondary: 220 1% 39%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

/* ANIMATION */
@keyframes blink {
  50% {
    opacity: 0.5;
  }
}

.animate-blink {
  animation: blink 1s infinite alternate;
}

/* Google Translate Styles */
.goog-te-gadget {
  font-size: 0 !important;
}

.goog-te-gadget .goog-te-combo {
  padding: 8px 12px !important;
  border-radius: 6px !important;
  border: 1px solid #ccc !important;
  font-size: 14px !important;
  font-family: inherit !important;
  background-color: white !important;
  color: #333 !important;
  outline: none !important;
  cursor: pointer !important;
  width: 100% !important;
  transition: all 0.2s !important;
  /* max-width: 140px !important; */
}

.goog-te-gadget .goog-te-combo:hover {
  border-color: #999 !important;
}

.goog-te-gadget .goog-te-combo:focus {
  border-color: #2275fb !important;
  box-shadow: 0 0 0 2px rgba(34, 117, 251, 0.2) !important;
}

.goog-te-banner-frame {
  display: none !important;
}

.goog-te-gadget img {
  display: none !important;
}
/*
.goog-te-menu-value span:first-child {
  display: none !important;
} */

.goog-te-menu-value span {
  color: #333 !important;
  font-family: inherit !important;
}

.skiptranslate iframe {
  display: none !important;
}

.skiptranslate.goog-te-gadget {
  display: inline-block !important;
}
.skiptranslate.goog-te-gadget a {
  display: none !important;
}

/* Custom select arrow */
/* .goog-te-combo {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
} */

.nav-close .sidenav {
  transition: all 1s ease-in-out;
  animation: slide-close 0.3s ease-in-out;
  transform: translateX(-400px);
}

.nav-open .sidenav {
  transition: all 1s ease-in-out;
  animation: slide-open 0.3s ease-in-out;
  transform: translateX(0);
}

@keyframes slide-close {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-400px);
  }
}

@keyframes slide-open {
  0% {
    transform: translateX(-400px);
  }
  100% {
    transform: translateX(0);
  }
}

.nav-close {
  animation: close 0.3s ease-in-out;
  display: none;
}

.nav-open {
  animation: open 0.3s ease-in-out;
  display: block;
}

@keyframes close {
  0% {
    display: block;
  }
  100% {
    display: none;
  }
}

@keyframes open {
  0% {
    display: none;
  }
  100% {
    display: block;
  }
}

/* Scrollbar styling for sidenav */
.sidenav::-webkit-scrollbar {
  width: 4px;
}

.sidenav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.sidenav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  transition: background 0.2s ease;
}

.sidenav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* For Firefox */
.sidenav {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

/* Custom Google Translate Dropdown Styling */
.manual-translate-dropdown {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7rem top 50%;
  background-size: 0.65rem auto;
  padding-right: 2rem !important;
}

.goog-te-combo {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7rem top 50%;
  background-size: 0.65rem auto;
  padding-right: 2rem !important;
  border-radius: 99999px !important;
}

/* Hide unwanted options in Google Translate dropdown */
.goog-te-combo
  option:not([value=""]):not([value="en"]):not([value="zh-CN"]):not(
    [value="fr"]
  ):not([value="de"]):not([value="ja"]):not([value="es"]) {
  display: none !important;
}

/* Desktop Google Translate Dropdown Styling */
.desktop-translate-dropdown {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7rem top 50%;
  background-size: 0.65rem auto;
  padding-right: 2rem !important;
  font-size: 14px;
  font-family: inherit;
  width: 100%;
}

/* Hide the original Google Translate element */
#google_translate_original {
  display: none !important;
}

/* Hide the Google Translate banner */
.goog-te-banner-frame {
  display: none !important;
}

body {
  top: 0 !important;
}

/* Custom Language Dropdown Styling */
.custom-dropdown-container {
  position: relative;
  width: 100%;
  z-index: 10;
}

.selected-option {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: white;
  color: black;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}

.desktop-dropdown .selected-option {
  min-width: 160px;
  padding: 8px;
  font-size: 14px;
  border-radius: 4px;
  border: none;
  background-color: white;
}

.mobile-dropdown .selected-option {
  border: none;
}

.selected-option img {
  width: 25px;
  height: 25px;
  margin-right: 8px;
}

.selected-option .dropdown-arrow {
  margin-left: auto;
}

.options-container {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: none;
  z-index: 20;
}

.dropdown-option {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdown-option:hover {
  background-color: #f5f5f5;
}

.dropdown-option img {
  width: 25px;
  height: 25px;
  margin-right: 8px;
}

/* Hide the Google Translate banner and original elements */
.goog-te-banner-frame,
.skiptranslate,
#google_translate_original {
  display: none !important;
}

body {
  top: 0 !important;
}

/* Desktop-specific dropdown styling to match mobile */
.desktop-dropdown .selected-option {
  min-width: 160px;
  padding: 2px 8px;
  font-size: 20px;
  /* border-radius: 4px; */
  border: 1px solid white;
  color: white;
  background-color: transparent;
}

.desktop-dropdown .dropdown-arrow {
  font-size: 20px;
  color: #ffffff;
}

.desktop-options {
  min-width: 220px;
  overflow-y: auto;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.desktop-options .dropdown-option {
  font-size: 14px;
  padding: 8px;
}

.desktop-options .dropdown-option:last-child {
  border-bottom: none;
}

.desktop-options .dropdown-option:hover {
  background-color: #f5f5f5;
}

.textShadow {
  text-shadow: 2px 3px 3px #0005;
}

/* Card download image bounce animation */
@keyframes bounce-subtle {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 1s ease-in-out paused;
}

.group:hover .animate-bounce-subtle {
  animation-play-state: running;
}

/* Reset animation when not hovering */
.group:not(:hover) .animate-bounce-subtle {
  animation: none;
}
