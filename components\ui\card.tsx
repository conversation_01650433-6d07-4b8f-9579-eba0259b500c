import * as React from "react";
import { cn } from "@/lib/utils"; // Unified import path

// Original shadcn/ui Card components (HEAD version)
const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "border border-neutral-200 bg-white text-neutral-950 shadow-sm dark:border-neutral-800 dark:bg-neutral-950 dark:text-neutral-50",
      className
    )}
    {...props}
  />
));
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

// Using semantic elements (h3, p) for Title and Description
const CardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3 // Use h3 for semantic title
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p // Use p for semantic description
    ref={ref}
    className={cn("text-sm text-neutral-500 dark:text-neutral-400", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

// New AdvancedCard component (based on redesign version)
type AdvancedCardBorderRadius = "none" | "sm" | "md" | "lg" | "xl";

interface AdvancedCardProps {
  children?: React.ReactNode;
  className?: string;
  borderRadius?: AdvancedCardBorderRadius;
  variant?:
    | "normal"
    | "horizontalCard"
    | "verticalCard"
    | "roundedCard"
    | "normalCard";
  icon?: React.ReactNode;
  title?: string;
  description?: string;
  downloadImage?: React.ReactNode;
}

const AdvancedCard = ({
  children,
  className,
  borderRadius,
  variant = "normal",
  icon,
  title,
  description,
  downloadImage,
}: AdvancedCardProps) => {
  const radiusMap = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
  };

  const borderRadiusClass = borderRadius
    ? radiusMap[borderRadius]
    : "rounded-lg"; // Default from redesign

  // Base classes from redesign, applied consistently
  const baseClasses = `border border-solid border-slate-300/30 shadow-sm ${borderRadiusClass}`;

  const variantClasses = {
    normal: `border-none shadow-none hover:bg-light flex flex-col w-full px-6 py-3`,
    horizontalCard: `bg-white hover:bg-primary ${borderRadiusClass} overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-primary/70 group h-full px-3 py-10 flex items-center justify-start gap-x-3 relative border-none pr-14`,
    verticalCard: `flex flex-col mb-5 bg-gray-800 p-6 rounded-lg border-none text-white shadow-md`,
    roundedCard: `flex flex-col h-full md:h-[200px] px-6 py-12 !bg-white group-hover:!bg-primary text-left lg:mb-0 group-hover:shadow-primary group-hover:shadow-md !border-0 !rounded-2xl cursor-pointer !gap-y-4`,
    normalCard: `flex flex-col h-full px-6 py-12 !bg-white group-hover:!bg-primary text-left lg:mb-0 group-hover:shadow-primary group-hover:shadow-md !border-0 rounded-none cursor-pointer !gap-y-4`,
  };

  // Logic from redesign
  if (variant === "horizontalCard" || variant === "verticalCard") {
    return (
      <div className={cn(baseClasses, variantClasses[variant], className)}>
        {icon && (
          <div className="text-gray-400 group-hover:text-white transition-colors text-5xl flex-shrink-0">
            {icon}
          </div>
        )}
        {(title || description) && (
          <div className="flex-grow">
            {title && (
              <h3 className="font-semibold text-base text-gray-900 group-hover:text-white transition-colors uppercase">
                {title}
              </h3>
            )}
            {description && (
              <p className="text-gray-700 text-sm mt-3 group-hover:text-white">
                {description}
              </p>
            )}
          </div>
        )}
        {downloadImage && (
          <div className="absolute bottom-[40%] right-5 w-8 h-8 shrink-0 opacity-0 group-hover:opacity-100 group-hover:invert group-hover:brightness-100 transition-all duration-300 animate-bounce-subtle">
            {downloadImage}
          </div>
        )}
      </div>
    );
  } else {
    // Handle other variants: 'normal', 'roundedCard', 'normalCard'
    return (
      <div
        className={cn(
          baseClasses,
          variantClasses[variant] || variantClasses.normal, // Fallback to normal
          className
        )}
      >
        {children}
      </div>
    );
  }
};

// Export all components
export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  AdvancedCard, // Export the new component
};
