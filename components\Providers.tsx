import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ReactNode } from "react";
import { Toaster } from "./ui/toaster";
import { AuthContextProvider } from "context/auth-context";

interface ProvidersProps {
    children: ReactNode;
}

const queryClient = new QueryClient();

function ReactQueryProvider({ children }: Readonly<ProvidersProps>) {
    return (
        <QueryClientProvider client={queryClient}>
            {children}
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}

export function Providers({ children }: Readonly<ProvidersProps>) {
    return <>
        <ReactQueryProvider>
            <AuthContextProvider>
                {children}
                <Toaster />
            </AuthContextProvider>
        </ReactQueryProvider>
    </>;
}