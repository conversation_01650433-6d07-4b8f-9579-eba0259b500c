import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { BillingAddress } from "@/supabase/types";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
  matchRoute({
    GET: getBillingAddressesHandler,
    POST: addBillingAddressHandler,
  })
);

export interface GetBillingAddressResponse {
  billing_address?: BillingAddress[];
  error?: string;
}

async function getBillingAddressesHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetBillingAddressResponse>
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  const userId = req.query.id?.toString();

  if (!userId) {
    return res.status(400).json({ error: "Missing user id" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customerId = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customerId.error) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const billing_addresses = await supabaseAdminClient
    .from("billing_addresses")
    .select("*")
    .eq("customer_id", customerId.data.id);

  if (billing_addresses.error) {
    return res.status(400).json({ error: billing_addresses.error.message });
  }

  return res.status(200).json({
    billing_address: billing_addresses.data,
  });
}

export const addBillingAddressSchema = z.object({
  address: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  address_type: z.enum(["residential", "commercial"], {
    required_error: "Address type is required",
  }),
  company_name: z.string().optional(),
  city: z.string().min(1, "City is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  zip_code: z.string().min(1, "Zip code is required"),
  effective_date: z
    .string()
    .datetime({
      message: "Invalid effective date format",
    })
    .refine(
      (dateStr) => {
        const date = new Date(dateStr);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return date >= today;
      },
      {
        message: "Effective date cannot be in the past",
      }
    ),
  note: z
    .string()
    .min(1, "Please provide a reason for adding this billing address"),
});

export type AddBillingAddress = z.infer<typeof addBillingAddressSchema>;

export interface AddBillingAddressResponse {
  error?: string;
  billing_address?: BillingAddress;
}

async function addBillingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<AddBillingAddressResponse>
) {
  const user_id = req.query.id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const data = addBillingAddressSchema.safeParse(req.body);

  if (data.error) {
    return res.status(400).json({ error: data.error.issues[0].message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const {
    address,
    address2,
    address_type,
    company_name,
    city,
    country,
    state,
    zip_code,
    effective_date,
    note,
  } = data.data as AddBillingAddress;

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  const newBillingAddress = await supabaseAdminClient
    .from("billing_addresses")
    .insert({
      address,
      address_2: address2,
      address_type,
      company_name,
      city,
      country,
      customer_id: customerId,
      state,
      zip_code,
      effective_date: effective_date,
      note,
      default: false,
      approved: false,
    })
    .select()
    .single();

  if (newBillingAddress.error) {
    return res.status(400).json({ error: newBillingAddress.error.message });
  }

  return res.status(200).json({
    billing_address: newBillingAddress.data,
  });
}
