import { useMemo } from 'react';
import { OrderItemsWithProduct } from '@/queries/customer-queries';
import { checkForMissingProducts } from '@/lib/utils/order-calculations';
import { MissingProductsDetection, DetectionConfig } from '@/types/order-calculations';

/**
 * Custom hook to detect missing products in an order
 * @param orderItems Array of order items with product information
 * @param storedTotal The total amount stored in the order
 * @param taxRate Tax rate as decimal (e.g., 0.08 for 8%)
 * @param taxExempt Whether the order is tax exempt
 * @param config Optional configuration for detection
 * @returns Detection result with missing products information
 */
export function useMissingProductsDetection(
  orderItems: OrderItemsWithProduct[] | undefined,
  storedTotal: number | undefined,
  taxRate: number = 0,
  taxExempt: boolean = false,
  config: DetectionConfig = {}
): MissingProductsDetection {
  const detection = useMemo(() => {
    // Return early if required data is missing
    if (!orderItems || !Array.isArray(orderItems) || typeof storedTotal !== 'number') {
      return {
        hasMissingProducts: false,
        difference: 0,
        calculatedTotal: 0,
        storedTotal: storedTotal || 0,
        calculatedBreakdown: { subtotal: 0, taxAmount: 0, total: 0 },
        isValid: false,
        error: 'Missing required order data',
      };
    }

    // Perform the detection
    return checkForMissingProducts(
      orderItems,
      storedTotal,
      taxRate,
      taxExempt
    );
  }, [orderItems, storedTotal, taxRate, taxExempt, config.tolerance]);

  return detection;
}

/**
 * Hook variant that only returns whether products are missing (for simple use cases)
 * @param orderItems Array of order items with product information
 * @param storedTotal The total amount stored in the order
 * @param taxRate Tax rate as decimal
 * @param taxExempt Whether the order is tax exempt
 * @returns Boolean indicating if products are missing
 */
export function useHasMissingProducts(
  orderItems: OrderItemsWithProduct[] | undefined,
  storedTotal: number | undefined,
  taxRate: number = 0,
  taxExempt: boolean = false
): boolean {
  const detection = useMissingProductsDetection(orderItems, storedTotal, taxRate, taxExempt);
  return detection.hasMissingProducts && detection.isValid;
}