import { AddCustomerCategoryDialog } from "@/components/features/admin/customers/add-customer-category";
import { AddUserNotes } from "@/components/features/admin/customers/add-user-notes";
import { AddUserToGroupDialog } from "@/components/features/admin/customers/add-user-to-group";
import { DeleteUserDialog } from "@/components/features/admin/customers/delete-user-dialog";
import { ViewUserDetails } from "@/components/features/admin/customers/user-details";
import AdminLayout from "@/components/features/admin/layout";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
  useGetAllUsersQuery,
  useGetGroupsQuery,
  useSearchUsersQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { BusinessDetail, Group, UserStatus } from "@/supabase/types";
import {
  ColumnDef,
  getCoreRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { MoreHorizontal, User } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import React, { useEffect, useMemo, useRef, useState } from "react";

// Extend the existing types to include business_details
declare module "@/pages/api/users/index" {
  interface PublicUserWithCustomer {
    business_details?: BusinessDetail[] | null;
    notes?: string | null;
  }

  interface CustomerWithGroup {
    categories?: Array<{
      id: string;
      category_id: string;
      category_data?: {
        id: string;
        name: string;
      };
    }>;
    billing_addresses?: Array<{
      address?: string;
      city?: string;
      state?: string;
    }>;
  }
}

const columns: ColumnDef<PublicUserWithCustomer>[] = [
  {
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Company Name" />
    ),
    accessorKey: "companyName",
    cell: ({ row }) => {
      const customer = row.original?.customer_data?.[0];
      return (
        <div className="flex items-center gap-2">
          {customer?.company_name?.trim() ? customer?.company_name : "N/A"}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    id: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    accessorFn: (row) => `${row.first_name} ${row.last_name}`,
    cell: ({ row }) => {
      const customer = row.original;

      return (
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarFallback>{customer.first_name?.charAt(0)}</AvatarFallback>
          </Avatar>
          {customer.first_name} {customer.last_name}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const customer = row.original;
      return <CustomerStatusBadge status={customer.status} />;
    },
    enableSorting: true,
  },
  {
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    accessorKey: "email",
    cell: ({ row }) => {
      const customer = row.original;
      return <div className="flex items-center gap-2">{customer.email}</div>;
    },
    enableSorting: true,
  },
  {
    header: "Address",
    accessorKey: "address",
    cell: ({ row }) => {
      const customer = row.original;
      return (
        <div className="flex items-center gap-2">
          {customer.customer_data?.[0]?.billing_addresses?.[0]?.address}
        </div>
      );
    },
  },
  {
    header: "City/State",
    accessorKey: "city",
    cell: ({ row }) => {
      const address = row.original.customer_data?.[0]?.billing_addresses?.[0];
      return (
        <div className="flex items-center gap-2">
          {address?.city} / {address?.state}
        </div>
      );
    },
  },
  {
    header: "Contact Info",
    accessorKey: "contactInfo",
    cell: ({ row }) => {
      const phone = row.original.customer_data?.[0]?.phone?.trim();
      const customerNumber =
        row.original.customer_data?.[0]?.customer_number?.trim();

      return (
        <div className="flex items-center gap-2">
          {customerNumber ? customerNumber : phone ? phone : "N/A"}
        </div>
      );
    },
  },
  {
    header: "Group",
    accessorKey: "customer_data",
    cell: ({ row }) => {
      const customer = row.original;
      const firstCustomerData = customer.customer_data?.[0];
      const groupName = firstCustomerData?.group_data?.data?.name;
      return (
        <div className="flex items-center gap-2">{groupName || "None"}</div>
      );
    },
  },
  {
    header: "Notes",
    accessorKey: "notes",
    cell: ({ row }) => {
      const customer = row.original;
      return (
        <div className="flex items-center gap-2">
          {customer.notes
            ? customer.notes.length > 20
              ? `${customer.notes.slice(0, 20)}...`
              : customer.notes
            : "N/A"}
        </div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue("created_at") as string;
      const date = new Date(createdAt);
      return (
        <div>
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
          })}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Last Updated" />
    ),
    accessorKey: "updated_at",
    cell: ({ row }) => {
      const createdAt = row.getValue("updated_at") as string;
      const date = new Date(createdAt);
      return (
        <div>
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
          })}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    header: "Actions",
    accessorKey: "actions",
    cell: UserTableActions,
    meta: {
      className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
    },
  },
];

function CustomerSearchFilter({
  onSearch,
}: {
  onSearch: (value: string) => void;
}) {
  const [searchValue, setSearchValue] = useState("");
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    // Debounce the search callback to avoid rapid pagination changes
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      onSearch(value);
    }, 300);
  };

  // Clean up debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <Input
      placeholder="Search customer name or email"
      value={searchValue}
      onChange={handleSearchChange}
      className="max-w-lg rounded-full"
    />
  );
}

export default function AdminCustomers() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");

  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [groupFilter, setGroupFilter] = useState<string>("all");

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "updated_at",
      desc: true,
    },
  ]);
  const [rowSelection, setRowSelection] = useState({});

  const token = useAuthStore((state) => state.token);
  const sortBy = sorting.length > 0 ? sorting[0].id : "created_at";
  const sortOrder =
    sorting.length > 0 ? (sorting[0].desc ? "desc" : "asc") : "desc";

  const {
    data: regularData,
    isLoading: isRegularLoading,
    isError: isRegularError,
  } = useGetAllUsersQuery(
    page,
    pageSize,
    token,
    undefined,
    statusFilter,
    groupFilter,
    sortBy,
    sortOrder
  );

  const { data: groupsData, isLoading: isGroupsLoading } = useGetGroupsQuery(
    1,
    100,
    token
  );

  const {
    data: searchData,
    isLoading: isSearchLoading,
    isError: isSearchError,
  } = useSearchUsersQuery(
    searchQuery,
    page,
    pageSize,
    token,
    undefined,
    statusFilter,
    groupFilter,
    sortBy,
    sortOrder
  );

  const data = searchQuery ? searchData : regularData;
  const isLoading = searchQuery ? isSearchLoading : isRegularLoading;
  const isError = searchQuery ? isSearchError : isRegularError;

  const customers = useMemo(() => data?.data ?? [], [data]);
  const totalItems = data?.total ?? 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    if (page !== 1) setPage(1);
  };

  const handleStatusFilterChange = (newStatus: string) => {
    setStatusFilter(newStatus);
    if (page !== 1) setPage(1);
  };

  const handleGroupFilterChange = (newGroupId: string) => {
    setGroupFilter(newGroupId);
    if (page !== 1) setPage(1);
  };

  const table = useReactTable({
    data: customers,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: (updater) => {
      setSorting(updater);
      // Reset to page 1 when sorting changes
      if (page !== 1) {
        setPage(1);
      }
    },
    state: {
      sorting,
      rowSelection,
      pagination: {
        pageIndex: page - 1,
        pageSize,
      },
    },
    manualPagination: true,
    manualFiltering: true,
    manualSorting: true,
    pageCount: totalPages,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPaginationState = updater(table.getState().pagination);
        console.log(
          "onPaginationChange - newPaginationState:",
          newPaginationState
        );
        setPage(newPaginationState.pageIndex + 1);
        setPageSize(newPaginationState.pageSize);
      } else {
        console.log("onPaginationChange - direct object updater:", updater);
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
  });

  return (
    <AdminLayout>
      <Head>
        <title>Customers</title>
      </Head>
      <div className="space-y-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-4xl font-bold tracking-tight">Customers</h2>
            <p className="text-zinc-500 text-sm">
              You can manage your customers here.
            </p>
          </div>
          <Button variant="outline" className="w-full md:w-auto" asChild>
            <Link href="/admin/customers/pending">
              Manage Pending Customers
            </Link>
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
            <User className="mr-2 h-4 w-4" />
            <span className="text-primary">Customers: {totalItems}</span>
          </Badge>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-center py-4">
          <div className="flex-grow">
            <CustomerSearchFilter onSearch={handleSearchChange} />
          </div>
          <div className="w-full md:w-auto">
            <CustomerStatusFilter
              currentStatus={statusFilter}
              onStatusChange={handleStatusFilterChange}
            />
          </div>
          {!isGroupsLoading && groupsData?.groups && (
            <div className="w-full md:w-auto">
              <CustomerGroupFilter
                groups={groupsData.groups ?? []}
                currentGroupId={groupFilter}
                onGroupChange={handleGroupFilterChange}
              />
            </div>
          )}
        </div>

        {isLoading ? (
          <DataTableSkeleton />
        ) : (
          <DataTable data={customers} columns={columns} table={table} />
        )}
      </div>
    </AdminLayout>
  );
}

export function CustomerStatusBadge({ status }: { status: UserStatus }) {
  const variant =
    status === "approved"
      ? "outline"
      : status === "pending"
      ? "pending"
      : status === "rejected"
      ? "destructive"
      : "default";

  return (
    <Badge variant={variant} className="uppercase">
      {status}
    </Badge>
  );
}

function UserTableActions({ row }: { row: Row<PublicUserWithCustomer> }) {
  const user = row.original;
  const isApprovedUser = row.original.status === "approved";

  const token = useAuthStore((state) => state.token);
  const isAdmin = useAuthStore((state) => state.isAdmin)();
  const { data: groups, isLoading: isGroupsLoading } = useGetGroupsQuery(
    1,
    100,
    token
  );

  return (
    <div className="flex items-center justify-center px-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          {isApprovedUser ? (
            <>
              <ViewUserDetails user={user} />
              <AddUserToGroupDialog user={user} groups={groups?.groups ?? []} />
              {/* <ViewCustomerCategoriesDialog user={user} /> */}
              <AddCustomerCategoryDialog user={user} />
              <AddUserNotes user={user} />
              {isAdmin && (
                <>
                  <DropdownMenuSeparator />
                </>
              )}
            </>
          ) : null}
          {isAdmin && (
            <>
              <DeleteUserDialog user={user} />
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function CustomerStatusFilter({
  currentStatus,
  onStatusChange,
}: {
  currentStatus: string;
  onStatusChange: (status: string) => void;
}) {
  const statusOptions: {
    value: string;
    label: string;
    userStatus?: UserStatus;
  }[] = [
    { value: "all", label: "All Statuses" },
    { value: "approved", label: "Approved", userStatus: "approved" },
    { value: "pending", label: "Pending", userStatus: "pending" },
    { value: "rejected", label: "Rejected", userStatus: "rejected" },
  ];

  return (
    <Select onValueChange={onStatusChange} value={currentStatus}>
      <SelectTrigger className="uppercase rounded-md min-w-[200px] w-full md:w-auto justify-between">
        <SelectValue placeholder="Filter by status" />
      </SelectTrigger>
      <SelectContent>
        {statusOptions.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            className="capitalize"
          >
            {option.userStatus ? (
              <CustomerStatusBadge status={option.userStatus} />
            ) : (
              option.label
            )}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

function CustomerGroupFilter({
  groups,
  currentGroupId,
  onGroupChange,
}: {
  groups: Group[];
  currentGroupId: string;
  onGroupChange: (groupId: string) => void;
}) {
  return (
    <Select onValueChange={onGroupChange} value={currentGroupId}>
      <SelectTrigger className="w-full md:w-auto rounded-md min-w-[200px] uppercase justify-between">
        <SelectValue placeholder="Filter by group" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Groups</SelectItem>
        {groups?.map((group) => (
          <SelectItem key={group.id} value={group.id}>
            {group.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
