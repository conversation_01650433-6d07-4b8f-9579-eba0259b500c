import { SvgSpinners90Ring } from "@/components/common/icons";
import { Button } from "@/components/ui/shadcn-button";
import Link from "next/link";
import { useRouter } from "next/router";
import { SignUpCallbackSession } from "pages/api/auth/callback";
import { useSignUpCallbackMutation } from "queries/user-queries";
import { useEffect, useState } from "react";
import useAuthStore from "stores/auth-store";

export default function AuthCallback() {
    const router = useRouter();
    const path = router.asPath.replace("/auth/callback#", "");
    const searchParams = new URLSearchParams(path);

    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const signupCallback = useSignUpCallbackMutation();
    const [error, setError] = useState("");

    useEffect(function signIn() {
        if (isAuthenticated()) return;

        const errorDescription = searchParams.get("error_description");
        const accessToken = searchParams.get("access_token");
        const expiresIn = searchParams.get("expires_in") ?? "";
        const expiresAt = searchParams.get("expires_at") ?? "";
        const refreshToken = searchParams.get("refresh_token") ?? "";
        const tokenType = searchParams.get("token_type") ?? "";
        const type = searchParams.get("type") ?? "";

        const hasData = !!accessToken && !!expiresIn && !!expiresAt && !!refreshToken && !!tokenType && !!type;

        if (errorDescription) {
            setError(errorDescription);

            const timeout = setTimeout(() => {
                router.push("/");
            }, 3000);

            return () => clearTimeout(timeout);
        };
        if (!hasData) return;

        const signUpCallbackData: SignUpCallbackSession = {
            access_token: accessToken,
            expires_in: expiresIn,
            expires_at: expiresAt,
            refresh_token: refreshToken,
            token_type: tokenType,
            type: type,
        };

        signupCallback.mutateAsync(signUpCallbackData)
            .catch(e => { });
    }, [router, isAuthenticated()]);

    useEffect(function redirectToHomepage() {
        if (isAuthenticated()) return;

        const timeout = setTimeout(() => {
            router.push("/");
        }, 3000);

        return () => clearTimeout(timeout);
    }, [signupCallback.isSuccess]);

    const renderContent = () => {
        // URL parameter error
        if (error) {
            return (
                <div className="flex flex-col items-center justify-center gap-2">
                    <h3>{error}</h3>
                    <Button variant="primary" asChild className="rounded-none">
                        <Link href="/">Go to Home</Link>
                    </Button>
                </div>
            );
        }

        // Verification in progress
        if (!isAuthenticated() && signupCallback.isPending) {
            return (
                <>
                    <h3>Verifying session...</h3>
                    <SvgSpinners90Ring className="w-10 h-10 animate-spin" />
                </>
            );
        }

        // Already verified cases
        const isAlreadyVerifiedError = signupCallback.isError && (
            signupCallback.error.message === "User already confirmed" ||
            signupCallback.error.message === "Please wait for your account to be approved by the admin."
        );

        if (isAlreadyVerifiedError) {
            return <div className="flex flex-col items-center justify-center gap-2">
                <h3>User already verified, waiting for admin approval...</h3>
                <Button variant="primary" asChild className="rounded-none">
                    <Link href="/">Go to Home</Link>
                </Button>
            </div>
        }

        // Success case
        if (signupCallback.isSuccess) {
            return <h3>Verified, redirecting to Home...</h3>;
        }

        // Error case
        if (signupCallback.isError) {
            return (
                <div className="flex flex-col items-center justify-center gap-2">
                    <h3>{signupCallback.error.message || "The authentication token has expired."}</h3>
                    <Button variant="primary" asChild className="rounded-none">
                        <Link href="/">Go to Home</Link>
                    </Button>
                </div>
            );
        }

        // Default case
        return <h3>Redirecting to Home...</h3>;
    };

    return (
        <section className="relative w-full h-full py-20">
            <div className="max-w-2xl mx-auto">
                <div className="flex items-center justify-center gap-2 text-2xl">
                    {renderContent()}
                </div>
            </div>
        </section>
    );
}