import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Grid } from "@stackshift-ui/grid";
import { GridItem } from "@stackshift-ui/grid-item";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { SocialIcons } from "@stackshift-ui/social-icons";
import { Text } from "@stackshift-ui/text";

import { FooterProps } from ".";
import { logoLink } from "helper";
import { ContactDetails, Images, Logo, SocialLink } from "types";
import { Socials } from "@stackshift-ui/footer/dist/types";
import { getImageDimensions } from "@sanity/asset-utils";
import React from "react";
import { Button } from "components/ui";

export default function Footer_F({
  logo,
  text,
  contacts,
  copyright,
  socialMedia,
  images,
  multipleMenus,
}: FooterProps) {
  return (
    <React.Fragment>
      <Section className="pt-32 pb-10 w-full bg-white relative border-t-4 border-primary flex flex-col items-center justify-center">
        <LogoSection logo={logo} />
        <Container maxWidth={1480} className="!px-0">
          <Grid
            gap={8}
            className="w-full grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4"
          >
            <ContactsContainer contacts={contacts} />
            <MultipleMenusContainer multipleMenus={multipleMenus} />
            {/* {images && <ImagesSection images={images} />} */}
          </Grid>
        </Container>
      </Section>
      <Flex
        md="row"
        justify="between"
        align="center"
        className="gap-4 md:gap-0 flex-col md:flex-row bg-primary"
      >
        <Container
          maxWidth={1480}
          className="py-5 !px-10 border-t border-primary"
        >
          <CopyrightContainer copyright={copyright} />
          <SocialMediaContainer socialMedia={socialMedia} />
        </Container>
      </Flex>
    </React.Fragment>
  );
}

function MultipleMenusContainer({ multipleMenus }: { multipleMenus?: any[] }) {
  if (!multipleMenus || multipleMenus.length === 0) return null;
  console.log(multipleMenus);

  return (
    <>
      {multipleMenus.map((menu, index) => (
        <GridItem key={index} className="px-10">
          <Text
            weight="bold"
            className="mb-6 text-xl md:text-2xl text-gray-800"
          >
            {menu.title.toUpperCase()}
          </Text>
          <Flex direction="col" className="gap-4">
            {menu.links?.map((link: any, linkIndex: number) => (
              <Button
                key={linkIndex}
                as="link"
                link={link}
                ariaLabel={link?.ariaLabel ?? link?.label}
                variant="unstyled"
                className="text-sm text-gray-800 hover:text-primary transition-colors"
              >
                {link.label}
              </Button>
            ))}
          </Flex>
        </GridItem>
      ))}
    </>
  );
}

function LogoSection({ logo }: { logo?: Logo }) {
  if (!logo?.image) return null;

  const width = getImageDimensions(logo?.image)?.width;
  const height = getImageDimensions(logo?.image)?.height;
  return (
    <div className="absolute -top-14 left-1/2 -translate-x-1/2">
      <Link
        aria-label={
          logoLink(logo) === "/" ? "Go to home page" : `Go to ${logoLink(logo)}`
        }
        className="bg-white inline-block p-2 rounded-full border-4 border-primary overflow-hidden"
        href={logoLink(logo)}
        target={logo?.linkTarget}
        rel={logo?.linkTarget === "_blank" ? "noopener noreferrer" : ""}
      >
        <Image
          src={`${logo?.image}`}
          width={width}
          height={height}
          alt={logo?.alt ?? "footer-logo"}
          className="w-20 h-auto mx-auto"
        />
      </Link>
    </div>
  );
}

function TextContainer({ text }: { text?: string }) {
  if (!text) return null;

  return (
    <Text className="text-sm md:text-sm leading-relaxed text-gray-800">
      {text}
    </Text>
  );
}

function ContactsContainer({ contacts }: { contacts?: ContactDetails[] }) {
  if (!contacts) return null;

  return (
    <GridItem className="px-10">
      {contacts?.map((contact, index) => (
        <div className="flex flex-col" key={index}>
          <Text
            weight="bold"
            className="mb-6 text-xl md:text-2xl text-gray-800"
          >
            CONTACT US
          </Text>

          {/* Email Section */}
          <div className="flex items-start mb-4">
            <div className="mr-4 p-3 bg-gray-100 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <Text weight="medium" className="text-base text-gray-800">
                E-mail:
              </Text>
              <Link
                href={`mailto:${contact?.emailInfo}`}
                className="text-sm text-gray-800 hover:text-primary transition-colors"
              >
                {contact?.emailInfo}
              </Link>
            </div>
          </div>

          {/* Phone Section */}
          <div className="flex items-start mb-4">
            <div className="mr-4 p-3 bg-gray-100 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                />
              </svg>
            </div>
            <div>
              <Text weight="medium" className="text-base text-gray-800">
                Tel:
              </Text>
              <Link
                href={`tel:${contact?.contactInfo}`}
                className="block text-sm text-gray-800 hover:text-primary transition-colors"
              >
                {contact?.contactInfo}
              </Link>
            </div>
          </div>

          {/* Fax Section (if available) */}
          {contact?.faxInfo && (
            <div className="flex items-start mb-4">
              <div className="mr-4 p-3 bg-gray-100 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-gray-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div>
                <Text weight="medium" className="text-base text-gray-800">
                  Fax:
                </Text>
                <Text className="text-sm text-gray-800">
                  {contact?.faxInfo}
                </Text>
              </div>
            </div>
          )}

          {/* Address Section */}
          <div className="flex items-start mb-4">
            <div className="mr-4 p-3 bg-gray-100 rounded-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            <div>
              <Text weight="medium" className="text-base text-gray-800">
                Address:
              </Text>
              <Text className="text-sm text-gray-800">
                {contact?.addressInfo}
              </Text>
            </div>
          </div>
        </div>
      ))}
    </GridItem>
  );
}

function ImagesSection({ images }: { images?: Images[] }) {
  if (!images || images.length === 0) return null;

  return (
    <GridItem colSpan={{ base: 1, lg: 1 }} className="flex flex-col">
      <Text weight="bold" className="mb-6 text-xl md:text-2xl text-gray-800">
        CERTIFICATIONS
      </Text>
      <Flex direction="row" wrap className="gap-10">
        {images?.map((image, index) => (
          <div key={index} className="flex items-center">
            <Image
              src={`${image?.image}`}
              width={getImageDimensions(image?.image || "")?.width}
              height={getImageDimensions(image?.image || "")?.height}
              alt={image?.alt ?? "partner-logo"}
              className="brightness-0 h-auto w-20 md:w-24"
            />
          </div>
        ))}
      </Flex>
    </GridItem>
  );
}

function CopyrightContainer({ copyright }: { copyright?: string }) {
  if (!copyright) return null;

  const currentYear = new Date().getFullYear();
  return (
    <Flex justify="between" align="center" className="w-full">
      <Text className="text-xs md:text-sm text-white">
        © Copyright {currentYear} {copyright}
      </Text>
      <Text className="text-xs md:text-sm text-white">
        Designed and Powered by&nbsp;
        <Link
          href="https://www.webriq.com/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-primary transition-colors"
        >
          WebriQ
        </Link>
      </Text>
    </Flex>
  );
}

function SocialMediaContainer({ socialMedia }: { socialMedia?: SocialLink[] }) {
  if (!socialMedia) return null;

  return (
    <Flex wrap className="gap-2 md:gap-4">
      {socialMedia?.map((social, index) => (
        <SocialMediaLink key={index} social={social} />
      ))}
    </Flex>
  );
}

function SocialMediaLink({ social }: { social?: SocialLink }) {
  if (!social?.socialMediaLink) return null;

  return (
    <Link
      aria-label={social?.socialMedia || social?.socialMediaPlatform || ""}
      className="p-2 rounded hover:bg-primary/80 transition-colors"
      target="_blank"
      rel="noopener noreferrer"
      href={social?.socialMediaLink}
    >
      {social?.socialMediaIcon?.image ? (
        <Image
          className="h-5 w-5 md:h-6 md:w-6"
          src={`${social?.socialMediaIcon?.image}`}
          width={24}
          height={24}
          alt={social?.socialMediaIcon?.alt ?? "social-media-icon"}
        />
      ) : (
        <SocialIcons social={social?.socialMedia as Socials} />
      )}
    </Link>
  );
}
