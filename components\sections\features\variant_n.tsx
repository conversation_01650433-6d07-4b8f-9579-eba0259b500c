import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button, Link } from "components/ui";
import { PortableText } from "@portabletext/react";

import { MyPortableTextComponents } from "types";
import { PortableTextBlock } from "@sanity/types";
import { IoDocumentOutline } from "react-icons/io5";
import { urlFor } from "lib/sanity";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-500 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary-foreground hover:text-secondary-foreground underline text-primary"
        href={value?.href}
        target={`${
          value?.href?.includes("wwww") || value?.href?.includes("https")
            ? "_blank"
            : ""
        }`}
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Features_N({
  caption,
  title,
  description,
  firstColumn,
  featuredItems,
}: FeaturesProps) {
  return (
    <Section className="pt-16 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex
          direction="col"
          gap={8}
          justify="between"
          className="lg:flex-row-reverse"
        >
          {/* TitleAndDescription first on small screens, second on larger screens */}
          <div className="relative  lg:-mt-48 w-full lg:w-2/3 lg:pl-10 ">
            <CaptionAndTitleSection title={title} firstColumn={firstColumn} />
          </div>

          {/* firstColumn below on small screens, left on larger screens */}
          <Flex direction="col" gap={8} className="w-full lg:basis-1/2">
            <FeatureItems features={featuredItems} />
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
}) {
  return (
    <div className="w-full lg:bg-white lg:rounded-lg lg:shadow-xl md:p-8">
      {title && (
        <Heading type="h2" className="text-xl lg:text-2xl mb-3">
          {title}
        </Heading>
      )}
      {firstColumn && (
        <div>
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </div>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center gap-2 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          size="lg"
          className="bg-white  hover:bg-white/80 text-primary rounded-full px-8 py-3"
        >
          {primaryButton.label}
        </Button>
      ) : null}
      {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="solid"
          className="bg-transparent border-2 border-white hover:bg-white/50 text-white rounded-full px-8 py-3"
        >
          <span>{secondaryButton.label}</span>
          {/* <FaArrowRightLong className="animate-blink" /> */}
        </Button>
      ) : null}
    </Flex>
  );
}
function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="px-4">
      <Heading type="h3" className="text-xl lg:text-2xl mb-3">
        Sections
      </Heading>
      <div className="flex flex-wrap -mx-2">
        {features.map((feature, index) => (
          <FeatureItem
            feature={feature}
            key={feature._key}
            isLast={index === features.length - 1 && features.length % 2 !== 0}
          />
        ))}
      </div>
    </div>
  );
}

function FeatureItem({ feature }: { feature: FeaturedItem }) {
  const pdfUrl = feature?.pdfFile?.asset?.url;
  const zipUrl = feature?.zipFile?.asset?.url;

  const handleDownload = (event: React.MouseEvent) => {
    if (zipUrl) {
      event.preventDefault(); // Prevent default link behavior
      const link = document.createElement("a");
      link.href = zipUrl;
      link.setAttribute("download", "");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="w-full md:w-1/2 lg:w-full relative mt-3 px-2">
      {zipUrl ? (
        // ZIP: Triggers auto-download
        <button
          onClick={handleDownload}
          className="inline-flex items-start space-x-2 group"
        >
          <IoDocumentOutline className="text-black transition-colors flex-shrink-0 text-xl group-hover:text-primary" />
          <Flex direction="col">
            <div
              className={`items-center gap-2 transition-colors group-hover:border-b border-primary ${
                feature.title.length > 37
                  ? ""
                  : feature.title.length > 30
                  ? "xl:inline-flex"
                  : "lg:inline-flex"
              }`}
            >
              <Text className="text-black font-semibold text-lg transition-colors group-hover:text-primary">
                {feature.title}
              </Text>
              {feature.subtitle && (
                <Text
                  muted
                  className="text-left text-primary font-normal text-sm italic"
                >
                  {feature.subtitle}
                </Text>
              )}
            </div>
          </Flex>
        </button>
      ) : (
        // PDF: Opens in a new tab
        <Link
          href={pdfUrl || "#"}
          target={pdfUrl ? "_blank" : undefined}
          rel={pdfUrl ? "noopener noreferrer" : undefined}
          className="inline-flex items-start space-x-2 group"
        >
          <IoDocumentOutline className="text-black transition-colors flex-shrink-0 text-xl group-hover:text-primary" />
          <Flex direction="col">
            <div
              className={`items-center gap-2 transition-colors group-hover:border-b border-primary ${
                feature.title.length > 37
                  ? ""
                  : feature.title.length > 30
                  ? "xl:inline-flex"
                  : "lg:inline-flex"
              }`}
            >
              <Text className="text-black font-semibold text-lg transition-colors group-hover:text-primary">
                {feature.title}
              </Text>
              {feature.subtitle && (
                <Text
                  muted
                  className="text-left text-primary font-normal text-sm italic"
                >
                  {feature.subtitle}
                </Text>
              )}
            </div>
          </Flex>
        </Link>
      )}
      <Text muted className="text-gray-700 text-sm mt-1">
        {feature.description}
      </Text>
    </div>
  );
}

export { Features_N };
