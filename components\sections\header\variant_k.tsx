import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { HeaderProps } from ".";
import { getImageDimensions } from "@sanity/asset-utils";
import { Button } from "components/ui";

const colorStyles = {
  green: {
    background: "bg-emerald-50",
    border: "border-emerald-200",
    title: "text-emerald-700",
    button: "hover:bg-emerald-100",
  },
  blue: {
    background: "bg-sky-50",
    border: "border-sky-200",
    title: "text-sky-700",
    button: "hover:bg-sky-100",
  },
  yellow: {
    background: "bg-amber-50",
    border: "border-amber-200",
    title: "text-amber-700",
    button: "hover:bg-amber-100",
  },
};

const noteBackgroundColors = {
  green: "bg-[#ACFFBC]",
  yellow: "bg-[#FFFDAD]",
  blue: "bg-[#CAE7FF]",
};

export default function Header_K({
  mainImage,
  title,
  description,
  notesSection,
  repairParts,
}: HeaderProps) {
  // Helper function to get background color class
  const getBackgroundColor = (color?: string) => {
    if (!color) return "";
    return (
      noteBackgroundColors[color as keyof typeof noteBackgroundColors] || ""
    );
  };

  return (
    <Section className="lg:pt-32 pt-20 pb-20 bg-white">
      <Container maxWidth={1536}>
        {/* Title Section */}
        <Flex direction="col" align="center" className="mb-14">
          <Heading weight="bold" fontSize="3xl" className="mb-2">
            {title}
          </Heading>
          <Text className="text-sm text-gray-600 max-w-2xl text-center">
            {description}
          </Text>
        </Flex>

        {/* Main Content Section */}
        <div className="grid lg:grid-cols-12 gap-8">
          {/* Left Side - Diagram */}
          <div className="lg:col-span-7 relative">
            <Image
              src={`${mainImage?.image}`}
              alt={mainImage?.alt || "Repair Parts Diagram"}
              width={getImageDimensions(mainImage?.image)?.width}
              height={getImageDimensions(mainImage?.image)?.height}
              className="w-full h-auto mx-auto"
            />
          </div>

          {/* Right Side - Parts Lists */}
          <div className="lg:col-span-5 space-y-6">
            {/* Parts Sections */}
            {repairParts?.map((group, index) => (
              <div
                key={index}
                className={`rounded-lg ${
                  colorStyles[group?.groupColor].background
                } ${
                  colorStyles[group?.groupColor].border
                } border p-4 shadow-sm`}
              >
                <Text
                  className={`font-semibold mb-3 ${
                    colorStyles[group?.groupColor].title
                  } capitalize`}
                >
                  {group?.groupColor} Parts
                </Text>
                <div className="grid grid-cols-2 gap-2">
                  {group?.parts.map((part, partIndex) => (
                    <Button
                      key={partIndex}
                      variant="unstyled"
                      as="link"
                      link={part}
                      ariaLabel={`View ${part.label}`}
                      target={part.linkTarget}
                      className={`text-sm p-2 rounded transition-colors ${
                        colorStyles[group?.groupColor].button
                      } text-gray-700 text-left`}
                    >
                      {part?.label}
                    </Button>
                  ))}
                </div>
              </div>
            ))}

            {/* Notes Section */}
            {notesSection && notesSection.length > 0 && (
              <div className="rounded-lg bg-gray-50 border border-gray-200 p-4">
                <Text className="font-semibold mb-3 text-gray-700">Notes:</Text>
                <ul className="space-y-2">
                  {notesSection.map((note, index) => (
                    <li key={index} className="flex items-start">
                      <span
                        className={`mr-2 text-xl -mt-1  ${
                          note?.title ? "text-gray-700" : "text-white/0"
                        }`}
                      >
                        •
                      </span>
                      <div>
                        {note.hasLink && note.primaryButton ? (
                          <>
                            <Button
                              variant="unstyled"
                              as="link"
                              link={note.primaryButton}
                              ariaLabel={`${note.primaryButton.label}`}
                              target={note.primaryButton.linkTarget}
                              className={`inline ${getBackgroundColor(
                                note.backgroundColor
                              )} px-2 py-1 rounded hover:bg-opacity-80 transition-colors`}
                            >
                              <span>{note.title}</span>
                            </Button>
                            <p className="text-gray-700 text-sm mt-1">
                              {note.description}
                            </p>
                          </>
                        ) : (
                          <>
                            {note?.title && (
                              <span className="text-blackw">{note.title}</span>
                            )}
                            <p
                              className={`text-gray-700 text-sm  ${
                                note?.title ? "mt-1" : ""
                              }`}
                            >
                              {note.description}
                            </p>
                          </>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </Container>
    </Section>
  );
}

export { Header_K };
