import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, LabeledRoute } from "../../../types";
import { ButtonProps } from "../header";
import { Button, Card, Link } from "components/ui";
import { PortableText } from "@portabletext/react";

import { MyPortableTextComponents } from "types";
import { PortableTextBlock } from "@sanity/types";
import { urlFor } from "lib/sanity";
import { FaArrowRightLong } from "react-icons/fa6";
import Image from "next/image";

const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-4 font-body text-base text-gray-900 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 underline"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Features_X({
  caption,
  title,
  description,
  firstColumn,
  secondColumn,
  features,
  arrayOfLinks,
  primaryButton,
  jobOpportunities,
  sideContent,
}: FeaturesProps) {
  return (
    <Section className="py-24 bg-background">
      <Container maxWidth={1280}>
        <Flex
          direction="col"
          justify="between"
          className="lg:flex-row  gap-6 lg:gap-16"
        >
          {/* First column */}
          <div className="relative w-full">
            <CaptionAndTitleSection
              title={title}
              firstColumn={firstColumn}
              secondColumn={secondColumn}
              caption={caption}
              description={description}
              primaryButton={primaryButton}
              arrayOfLinks={arrayOfLinks}
              jobOpportunities={jobOpportunities}
            />
          </div>
          {sideContent && (
            <Flex direction="col" gap={6} className="w-full mt-10">
              {/* <FeatureItems features={features} /> */}
              <div className="relative p-4 md:p-5 border border-black/40 rounded-lg shadow-lg">
                <PortableText
                  value={sideContent}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            </Flex>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
  secondColumn,
  caption,
  description,
  primaryButton,
  arrayOfLinks,
  jobOpportunities,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  caption?: string;
  description?: string;
  primaryButton?: LabeledRoute;
  arrayOfLinks?: LabeledRoute[];
  jobOpportunities?: any;
}) {
  const [activeTab, setActiveTab] = React.useState("");

  return (
    <div className="w-full">
      <div className="border-b border-gray-200 cursor-default">
        <div className="inline-flex items-center gap-2 group">
          {primaryButton?.label ? (
            <Link
              href={
                primaryButton?.type === "linkInternal"
                  ? primaryButton?.internalLink
                  : primaryButton?.externalLink
              }
              target={
                primaryButton?.type === "linkInternal" ? "self" : "_blank"
              }
              className="mb-2 text-primary uppercase w-full text-sm md:text-lg hover:underline peer"
            >
              {primaryButton?.label}
            </Link>
          ) : (
            <Text className="mb-2 text-primary uppercase w-full text-sm md:text-lg peer">
              {caption}
            </Text>
          )}
          {primaryButton?.label && (
            <FaArrowRightLong className="mb-2 text-primary animate-pulse scale-0 origin-left transition duration-300 ease-in-out peer-hover:scale-100 peer-hover:opacity-100 opacity-0" />
          )}
        </div>
        {title && (
          <Heading
            type="h2"
            className="!text-black text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
          >
            {title}
          </Heading>
        )}

        {description && (
          <Text className="mb-5 font-body text-lg">{description}</Text>
        )}
        <div className="w-10 h-2 bg-primary" />
      </div>

      <div className="w-full flex flex-row flex-wrap gap-4 pt-8 pb-8">
        <div className="mb-5 w-full space-y-2">
          {jobOpportunities?.jobOffers.length >= 1 && (
            <div>
              <Heading type="h3" className="uppercase mb-4 !text-lg">
                candidates for the following positions:
              </Heading>
            </div>
          )}
          {jobOpportunities && jobOpportunities.jobOffers ? (
            jobOpportunities?.jobOffers?.map((offer, idx) => (
              <div>
                <button
                  className={`w-full text-left flex items-start justify-between ${
                    idx !== 0 ? "border-b" : ""
                  } px-5 py-2 bg-primary transition`}
                  onClick={() => {
                    if (offer?.jobRole === activeTab) {
                      setActiveTab("");
                    } else {
                      setActiveTab(offer?.jobRole);
                      // window.scrollTo(
                      //   0,
                      //   document?.querySelector(`#${offer?.jobRole}`)
                      //     ?.offsetTop || 0
                      // );
                    }
                  }}
                >
                  <h4
                    id={offer?.jobRole}
                    className="text-lg text-white font-medium font-heading "
                  >
                    {offer?.jobRole}
                  </h4>

                  <svg
                    viewBox="0 0 24 24"
                    focusable="false"
                    className={`w-5 h-5 text-white ${
                      activeTab === offer?.jobRole ? "rotate-180" : "rotate-0"
                    }`}
                    aria-hidden="true"
                  >
                    <path
                      fill="currentColor"
                      d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"
                    ></path>
                  </svg>
                </button>

                {offer?.jobDescription && (
                  <div
                    className={`py-4 px-7 !bg-white ${
                      activeTab === offer?.jobRole ? "block" : "hidden"
                    }`}
                  >
                    <PortableText
                      value={offer?.jobDescription}
                      components={textComponentBlockStyling}
                    />
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="w-full flex flex-col items-center justify-center">
              <span className="uppercase">no positions currently open</span>
              <Heading type="h4" className="text-primary font-extrabold">
                Stay Tuned
              </Heading>
            </div>
          )}
        </div>
      </div>

      {secondColumn && (
        <div className="max-w-none">
          <PortableText
            value={secondColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </div>
  );
}

function Buttons({
  primaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex align="center" className="flex items-center justify-start flex-row">
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="maxtonPrimary"
          size="lg"
        >
          {primaryButton.label}
        </Button>
      ) : null}
    </Flex>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <div className="w-full">
      <Heading
        type="h3"
        className="text-lg sm:text-xl lg:text-2xl mb-3"
      ></Heading>
      <div className="gap-4">
        {features.map((feature, index) => (
          <FeatureItem feature={feature} key={feature._key} />
        ))}
      </div>
    </div>
  );
}

function FeatureItem({ feature }: { feature: ArrayOfImageTitleAndText }) {
  return (
    <div className="relative p-4 md:p-5 border border-black/40 rounded-lg shadow-lg">
      <Flex direction="col">
        <div className="items-center gap-2 transition-colors group-hover:border-b border-primary">
          <Text className="text-black font-semibold text-base md:text-lg transition-colors group-hover:text-primary mb-2">
            {feature.title}
          </Text>

          {feature?.firstColumn && (
            <div className="prose prose-sm md:prose-base">
              <PortableText
                value={feature?.firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          )}
        </div>
      </Flex>
    </div>
  );
}

export { Features_X };
