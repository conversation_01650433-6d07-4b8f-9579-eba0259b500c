import {
  SectionsProps,
  Logo,
  LabeledRoute,
  LabeledRouteWithKey,
  Form,
} from "../../../types";
import dynamic from "next/dynamic";
import * as CallToActionVariant from "@stackshift-ui/call-to-action";
import { PortableTextBlock } from "@sanity/types";

const Variants = {
  variant_a: CallToActionVariant.CallToAction_A,
  variant_b: CallToActionVariant.CallToAction_B,
  variant_c: CallToActionVariant.CallToAction_C,
  variant_d: CallToActionVariant.CallToAction_D,
  variant_e: CallToActionVariant.CallToAction_E,
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
};

export interface CTAProps {
  logo?: Logo;
  title?: string;
  plainText?: string;
  button?: LabeledRoute;
  features?: string[];
  formLinks?: LabeledRouteWithKey[];
  form?: Form;
  signInLink?: LabeledRoute;
  firstColumn?: PortableTextBlock[];
}

const displayName = "CallToAction";

export const CallToAction: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    logo: data?.variants?.logo ?? undefined,
    title: data?.variants?.title ?? undefined,
    plainText: data?.variants?.plainText ?? undefined,
    button: data?.variants?.primaryButton ?? undefined,
    features: data?.variants?.tags ?? undefined,
    formLinks: data?.variants?.formLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
    signInLink: data?.variants?.signInLink ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

CallToAction.displayName = displayName;
