import {
  logo,
  primaryButton,
  routes,
  secondaryButton,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const navigationSchema = [
  logo(),
  // routes(),
  {
    title: "Routes",
    description: "This will be your navigation menu",
    name: "routes",
    type: "array",
    of: [{ type: "multipleLinks" }],
  },
  primaryButton(hideIfVariantIn(["variant_f", "variant_g"])),
  secondaryButton(hideIfVariantIn(["variant_f", "variant_g"])),
];
