import React from "react";

import dynamic from "next/dynamic";

import {
  ArrayOfImageTitleAndText,
  FeaturedItem,
  Images,
  LabeledRoute,
  MainImage,
  QuickForms,
  SectionsProps,
} from "../../../types";

import * as FeaturesVariant from "@stackshift-ui/features";
import { PortableTextBlock } from "@sanity/types";

const Variants = {
  variant_a: FeaturesVariant.Features_A,
  variant_b: FeaturesVariant.Features_B,
  variant_c: FeaturesVariant.Features_C,
  variant_d: FeaturesVariant.Features_D,
  variant_e: FeaturesVariant.Features_E,
  variant_f: FeaturesVariant.Features_F,
  variant_g: FeaturesVariant.Features_G,
  variant_h: FeaturesVariant.Features_H,
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
  variant_k: dynamic(() => import("./variant_k")),
  variant_l: dynamic(() => import("./variant_l")),
  variant_m: dynamic(() => import("./variant_m")),
  variant_n: dynamic(() => import("./variant_n")),
  variant_o: dynamic(() => import("./variant_o")),
  variant_p: dynamic(() => import("./variant_p")),
  variant_q: dynamic(() => import("./variant_q")),
  variant_r: dynamic(() => import("./variant_r")),
  variant_s: dynamic(() => import("./variant_s")),
  variant_t: dynamic(() => import("./variant_t")),
  variant_u: dynamic(() => import("./variant_u")),
  variant_v: dynamic(() => import("./variant_v")),
  variant_w: dynamic(() => import("./variant_w")),
  variant_x: dynamic(() => import("./variant_x")),
};

export interface FeaturesProps {
  caption?: string;
  title?: string;
  description?: string;
  features?: ArrayOfImageTitleAndText[];
  tags?: string[];
  featuredItems?: FeaturedItem[];
  images?: Images[];
  primaryButton?: LabeledRoute;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  mainImage?: MainImage;
  arrayOfLinks?: LabeledRoute[];
  quickForms?: QuickForms;
  jobOpportunities?: any[];
  hasCalculator?: boolean;
  sideContent?: PortableTextBlock[];
}

export interface FeaturesProps {
  caption?: string;
  title?: string;
  description?: string;
  features?: ArrayOfImageTitleAndText[];
  tags?: string[];
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  featuredItems?: FeaturedItem[];
  images?: Images[];
  primaryButton?: LabeledRoute;
  secondaryButton?: LabeledRoute;
  mainImage?: MainImage;
  arrayOfLinks?: LabeledRoute[];
  quickForms?: QuickForms;
  jobOpportunities?: any[];
  hasCalculator?: boolean;
  sideContent?: PortableTextBlock[];
}

const displayName = "Features";

export const Features: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    caption: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    description: data?.variants?.description ?? undefined,
    features: data?.variants?.arrayOfImageTitleAndText ?? undefined,
    tags: data?.variants?.tags ?? undefined,
    featuredItems: data?.variants?.featuredItems ?? undefined,
    images: data?.variants?.images ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
    secondaryButton: data?.variants?.secondaryButton ?? undefined,
    mainImage: data?.variants?.mainImage ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    secondColumn: data?.variants?.secondColumn ?? undefined,
    arrayOfLinks: data?.variants?.arrayOfLinks ?? undefined,
    quickForms: data?.variants?.quickForms ?? undefined,
    jobOpportunities: data?.variants?.jobOpportunities ?? undefined,
    hasCalculator: data?.variants?.hasCalculator ?? undefined,
    sideContent: data?.variants?.sideContent ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Features.displayName = displayName;
