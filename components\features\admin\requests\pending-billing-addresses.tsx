import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge, BadgeProps } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { formatId } from "@/lib/utils/order-helpers";
import {
  useApproveBillingAddressMutation,
  useGetPendingBillingAddressesQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { BillingAddress } from "@/supabase/types";
import {
  ColumnDef,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Info, Loader2, MoreHorizontal, XCircle } from "lucide-react";
import { useMemo, useState } from "react";

// Define table columns
const columns: ColumnDef<BillingAddress>[] = [
  {
    header: "Customer Account#",
    accessorKey: "customer_id",
    cell: ({ row }) => {
      const customerId = row.original.customer_id;
      const shortCustomerId = formatId(customerId);
      const maxtonAccount =
        row.original.customer_data?.user_data?.business_details?.[0]?.maxton_account?.trim();

      return <div>{maxtonAccount || shortCustomerId}</div>;
    },
  },
  {
    header: "Company Name",
    accessorKey: "companyName",
    cell: ({ row }) => {
      const companyName = row.original.customer_data?.company_name;
      return <div>{companyName ?? "N/A"}</div>;
    },
  },
  {
    header: "Address",
    accessorKey: "address",
  },
  {
    header: "City",
    accessorKey: "city",
  },
  {
    header: "State/Province",
    accessorKey: "state",
  },
  {
    header: "Note",
    accessorKey: "note",
  },
  {
    header: "Date",
    accessorKey: "created_at",
    cell: ({ row }) => {
      const date = new Date(row.original.created_at);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });

      return <div>{formattedDate}</div>;
    },
  },
  {
    header: "Status",
    accessorKey: "approved",
    cell: ({ row }) => {
      const approved = row.original.approved;
      return <StatusBadge approved={approved} />;
    },
  },
  {
    header: "Actions",
    id: "actions",
    cell: ({ row }) => <BillingAddressActions row={row} />,
  },
];

export function PendingBillingAddressesDataTable() {
  const accessToken = useAuthStore((state) => state.token);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const { data, isLoading, isError } = useGetPendingBillingAddressesQuery(
    page,
    limit,
    accessToken
  );

  // Convert data to the correct format for the table
  const tableData = useMemo(() => data?.billingAddresses || [], [data]);
  const total = data?.total || 0;
  const totalPages = data?.totalPages || 1;

  // Create table instance
  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    enableColumnResizing: true,
    enableSorting: true,
    manualPagination: true,
    pageCount: totalPages,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize: limit,
        });
        setPage(newPagination.pageIndex + 1);
        setLimit(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setLimit(updater.pageSize);
      }
    },
    state: {
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize: limit,
      },
    },
  });

  return (
    <Card className="container mx-auto border-none">
      <CardContent>
        {isError ? (
          <div className="flex flex-col items-center justify-center py-10">
            <XCircle className="h-14 w-14 text-red-500 my-4" />
            <h3 className="text-xl font-semibold">
              Error loading pending billing addresses
            </h3>
            <p className="text-gray-500 mt-2">Please try again later</p>
          </div>
        ) : null}
        {isLoading ? (
          <DataTableSkeleton />
        ) : (
          <DataTable
            data={tableData}
            columns={columns}
            table={table}
            renderToolbar={(table) => (
              <DataTableToolbar table={table}>
                <DataTableFilter
                  table={table as any}
                  column="address"
                  placeholder="Search addresses..."
                  className="max-w-md rounded-full"
                />
              </DataTableToolbar>
            )}
          />
        )}
      </CardContent>
    </Card>
  );
}

function StatusBadge({ approved }: { approved: boolean }) {
  const status = approved ? "approved" : "pending";
  const variant = approved ? "success" : "pending";

  return (
    <Badge className="uppercase" variant={variant as BadgeProps["variant"]}>
      {status}
    </Badge>
  );
}

function BillingAddressDetails({
  address,
  onClose,
}: {
  address: BillingAddress & {
    customer_data?: {
      id: string;
      user_data?: {
        id: string;
        email: string;
      };
      defaultAddress?: BillingAddress | null;
    };
  };
  onClose: () => void;
}) {
  const maxtonAccount =
    // @ts-ignore
    address.customer_data?.user_data?.business_details?.[0]?.maxton_account?.trim() ||
    formatId(address.customer_id) ||
    "";
  // @ts-ignore
  const companyName = address.customer_data?.company_name || "";

  const defaultAddress = address.customer_data?.defaultAddress;
  const defaultAddressEffectiveDate = defaultAddress?.effective_date
    ? new Date(defaultAddress.effective_date)
    : new Date();

  const effectiveDate = address.effective_date
    ? new Date(address.effective_date)
    : new Date();

  return (
    <ScrollArea className="max-h-[70vh]">
      <div className="space-y-6">
        <div>
          {defaultAddress ? (
            <h3 className="text-lg font-medium">New Billing Address</h3>
          ) : (
            <h3 className="text-lg font-medium">Billing Address Details</h3>
          )}
          <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
            <div className="font-medium">Customer Account #:</div>
            <div>{maxtonAccount}</div>
            <div className="font-medium">Company Name:</div>
            <div>{companyName}</div>
            <div className="font-medium">Status:</div>
            <div>
              <StatusBadge approved={address.approved} />
            </div>
            <div className="font-medium">Created At:</div>
            <div>{new Date(address.created_at).toUTCString()}</div>
            <div className="font-medium">Address:</div>
            <div>{address.address}</div>
            <div className="font-medium">City:</div>
            <div>{address.city}</div>
            <div className="font-medium">State/Province:</div>
            <div>{address.state}</div>
            <div className="font-medium">Zip/Postal Code:</div>
            <div>{address.zip_code}</div>
            <div className="font-medium">Country:</div>
            <div>{address.country}</div>
            <div className="font-medium">Is Default:</div>
            <div>{address.default ? "Yes" : "No"}</div>
            <div className="font-medium">Note:</div>
            <div>{address.note}</div>
            <div className="font-medium">Effective Date:</div>
            <div>{effectiveDate.toLocaleString()}</div>
          </div>
        </div>

        {defaultAddress ? (
          <div className="border-t pt-4">
            <h3 className="text-lg font-medium">Old Billing Address</h3>
            <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
              <div className="font-medium">Address:</div>
              <div>{defaultAddress?.address}</div>
              <div className="font-medium">City:</div>
              <div>{defaultAddress?.city}</div>
              <div className="font-medium">State/Province:</div>
              <div>{defaultAddress?.state}</div>
              <div className="font-medium">Zip/Postal Code:</div>
              <div>{defaultAddress?.zip_code}</div>
              <div className="font-medium">Country:</div>
              <div>{defaultAddress?.country}</div>
              <div className="font-medium">Effective Date:</div>
              <div>{defaultAddressEffectiveDate.toLocaleString()}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-sm text-primary">
            <Info className="h-4 w-4" />
            This is a new billing address.
          </div>
        )}

        <div className="border-t pt-4">
          <h3 className="text-lg font-medium">Customer Details</h3>
          <div className="mt-2 grid grid-cols-2 gap-3 text-sm">
            <div className="font-medium">Email:</div>
            <div>{address.customer_data?.user_data?.email || "N/A"}</div>
          </div>
        </div>

        <DialogFooter>
          <Button onClick={onClose}>Close</Button>
        </DialogFooter>
      </div>
    </ScrollArea>
  );
}

function BillingAddressActions({ row }: { row: any }) {
  const accessToken = useAuthStore((state) => state.token);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const approveBillingAddressMutation =
    useApproveBillingAddressMutation(accessToken);

  const handleStatusUpdate = async () => {
    try {
      await approveBillingAddressMutation.mutateAsync({
        billing_address_id: row.original.id,
      });

      toast({
        title: "Billing address updated",
        description: `The billing address has been approved successfully`,
        variant: "success",
        duration: 3000,
      });

      setShowApproveDialog(false);
    } catch (e) {
      toast({
        title: "Error updating billing address",
        description: "Please try again later",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Billing Address Details</DialogTitle>
            <DialogDescription>
              Detailed information about this billing address
            </DialogDescription>
          </DialogHeader>
          <BillingAddressDetails
            address={row.original}
            onClose={() => setShowDetailsDialog(false)}
          />
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={showApproveDialog}
        onOpenChange={(open) => {
          // Only allow closing if not processing
          if (!approveBillingAddressMutation.isPending || !open) {
            setShowApproveDialog(open);
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Approve Billing Address</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to approve this billing address?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={approveBillingAddressMutation.isPending}
            >
              Cancel
            </AlertDialogCancel>
            <Button
              onClick={handleStatusUpdate}
              disabled={approveBillingAddressMutation.isPending}
              type="button"
            >
              {approveBillingAddressMutation.isPending
                ? "Processing..."
                : "Approve"}
              {approveBillingAddressMutation.isPending && (
                <Loader2 className="w-4 h-4 ml-2 animate-spin" />
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setShowDetailsDialog(true)}>
            View details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowApproveDialog(true)}>
            Approve
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
