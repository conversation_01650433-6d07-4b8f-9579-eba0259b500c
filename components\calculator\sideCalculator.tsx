import { useValveCalculator } from "../../hooks/useValveCalculator";
import type { ValveData, ValveProduct } from "../../types/valve";
import { useEffect, useRef, useState } from "react";
import { FaArrowRightLong } from "react-icons/fa6";
import Button from "../ui/button";
import { useGetImage } from "@/queries/customer-queries";
import { useGetProductByNameQuery } from "@/queries/product-queries";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";

export default function ValveCalculator() {
  const [showResults, setShowResults] = useState(false);
  const [upperUnits, setUpperUnits] = useState<"inch" | "metric">("inch");
  const [lowerUnits, setLowerUnits] = useState<"inch" | "metric">("inch");

  const {
    calculateValve,
    calculationResults,
    resetCalculator,
    upperFlashMessages,
    lowerFlashMessages,
    activeCalculator,
  } = useValveCalculator();

  const handleSubmit = (formData: ValveData) => {
    const results = calculateValve(formData);
    if (results) {
      setShowResults(true);
    }
  };

  const resetResults = () => {
    setShowResults(false);
    resetCalculator();
  };

  useEffect(
    function resetResultsOnFlashMessages() {
      if (upperFlashMessages.length > 0 || lowerFlashMessages.length > 0) {
        setShowResults(false);
      }
    },
    [upperFlashMessages, lowerFlashMessages]
  );

  return (
    <div className="bg-white shadow-md border border-gray-200 p-4">
      <div className="space-y-6">
        {/* Upper Calculator */}
        <div>
          <h3 className="text-sm font-bold text-gray-800 mb-3">
            Upper Calculator
          </h3>
          {upperFlashMessages.length > 0 && (
            <div className="p-3 mb-3 rounded border border-red-200 bg-red-50">
              {upperFlashMessages.map((msg, i) => (
                <p key={i} className="text-red-600 text-sm">
                  {msg.message}
                </p>
              ))}
            </div>
          )}
          {/* Units Selection for Upper Calculator */}
          <div className="mb-3">
            <select
              value={upperUnits}
              onChange={(e) =>
                setUpperUnits(e.target.value as "inch" | "metric")
              }
              className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
            >
              <option value="inch">English Units</option>
              <option value="metric">Metric Units</option>
            </select>
          </div>

          <UpperCalculatorForm
            units={upperUnits}
            onSubmit={handleSubmit}
            onReset={resetResults}
          />

          {showResults &&
            calculationResults &&
            activeCalculator === "upper" &&
            calculationResults.formType === "form1" && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <ValveCalculatorResults
                  results={calculationResults}
                  units={upperUnits}
                  onReset={resetResults}
                />
              </div>
            )}
        </div>

        {/* Lower Calculator */}
        <div className="pt-4 border-t border-gray-200">
          <h3 className="text-sm font-bold text-gray-800 mb-3">
            Lower Calculator
          </h3>
          {lowerFlashMessages.length > 0 && (
            <div className="p-3 mb-3 rounded border border-red-200 bg-red-50">
              {lowerFlashMessages.map((msg, i) => (
                <p key={i} className="text-red-600 text-sm">
                  {msg.message}
                </p>
              ))}
            </div>
          )}

          {/* Units Selection for Lower Calculator */}
          <div className="mb-3">
            <select
              value={lowerUnits}
              onChange={(e) =>
                setLowerUnits(e.target.value as "inch" | "metric")
              }
              className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
            >
              <option value="inch">English Units</option>
              <option value="metric">Metric Units</option>
            </select>
          </div>

          <LowerCalculatorForm
            units={lowerUnits}
            onSubmit={handleSubmit}
            onReset={resetResults}
          />

          {showResults &&
            calculationResults &&
            activeCalculator === "lower" &&
            calculationResults.formType !== "form1" && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <ValveCalculatorResults
                  results={calculationResults}
                  units={lowerUnits}
                  onReset={resetResults}
                />
              </div>
            )}
        </div>
      </div>
    </div>
  );
}

function UpperCalculatorForm({
  units,
  onSubmit,
  onReset,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
  onReset?: () => void;
}) {
  const formRef = useRef<HTMLFormElement>(null);

  const handleReset = () => {
    if (formRef.current) {
      formRef.current.reset();
      onReset?.();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) return;

    const form = formRef.current;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form1",
      units: units as "inch" | "metric",
      jackType: formData.get("jackType") as "direct" | "roped",
      numJack: Number(formData.get("numJack")),
      jackDiameter: Number(formData.get("jackDiameter")),
      carSpeed: Number(formData.get("carSpeed")),
      emptyWeight: Number(formData.get("emptyWeight")),
      emptyWeightUnits: formData.get("emptyWeightUnits") as
        | "PSI"
        | "lbs."
        | "kg"
        | "kg/cm2",
      capacity: Number(formData.get("capacity")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  // Prevent scroll from changing number input values
  const preventScrollChange = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  return (
    <form
      ref={formRef}
      id="upper-form"
      onSubmit={handleSubmit}
      className="space-y-3"
    >
      {/* Upper calculator fields - more compact */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Jack Type
        </label>
        <select
          name="jackType"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
        >
          <option value="">Select Jack Type</option>
          <option value="direct">Direct</option>
          <option value="roped">Roped</option>
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Jack Number
        </label>
        <select
          name="numJack"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
        >
          <option value="">Select Jack Number</option>
          <option value="1">Single Jack</option>
          <option value="2">Dual Jack</option>
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Piston Dia. ({units === "inch" ? "in" : "mm"})
        </label>
        <input
          type="number"
          name="jackDiameter"
          placeholder="Piston Diameter"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Car Speed ({units === "inch" ? "FPM" : "m/s"})
        </label>
        <input
          type="number"
          name="carSpeed"
          placeholder="Car Speed"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div className="flex flex-row gap-2">
        <div className="w-full flex flex-col">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Empty Car Weight or Static Pressure
          </label>
          <input
            type="number"
            name="emptyWeight"
            placeholder="Empty Car Weight"
            required
            className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
            step="any"
            onWheel={preventScrollChange}
          />
        </div>
        <div className="flex flex-col w-1/4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Unit
          </label>
          <select
            name="emptyWeightUnits"
            className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
          >
            {units === "inch" ? (
              <>
                <option value="lbs.">lbs.</option>
                <option value="PSI">PSI</option>
              </>
            ) : (
              <>
                <option value="kg">kg</option>
                <option value="kg/cm2">kg/cm²</option>
              </>
            )}
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Capacity ({units === "inch" ? "lbs" : "kg"})
        </label>
        <input
          type="number"
          name="capacity"
          placeholder="Capacity"
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div className="flex items-center gap-1 py-1">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="upperDSR-sidebar"
          value="true"
          className="w-3 h-3 text-primary"
        />
        <label htmlFor="upperDSR-sidebar" className="text-sm text-gray-700">
          Down Speed Regulation
        </label>
      </div>
      <div className="flex gap-2 pt-1">
        <Button
          as="button"
          type="submit"
          variant="maxtonPrimary"
          ariaLabel="Size Valve"
          className="px-2 py-1 text-sm font-medium"
        >
          <span>Size Valve</span>
        </Button>
        <Button
          as="button"
          type="button"
          variant="maxtonSecondary"
          onClick={handleReset}
          className="px-2 py-1 font-medium text-sm"
          ariaLabel="Reset"
        >
          <span>Reset</span>
        </Button>
      </div>
    </form>
  );
}

function LowerCalculatorForm({
  units,
  onSubmit,
  onReset,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
  onReset?: () => void;
}) {
  const formRef = useRef<HTMLFormElement>(null);

  const handleReset = () => {
    if (formRef.current) {
      formRef.current.reset();
      onReset?.();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) return;

    const form = formRef.current;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form2",
      units: units as "inch" | "metric",
      emptyStaticPressure: Number(formData.get("emptyStaticPressure")),
      loadedCarPressure: Number(formData.get("loadedCarPressure")),
      ratedFlow: Number(formData.get("ratedFlow")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  // Prevent scroll from changing number input values
  const preventScrollChange = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  return (
    <form
      ref={formRef}
      id="lower-form"
      onSubmit={handleSubmit}
      className="space-y-3"
    >
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Empty Static Pressure ({units === "inch" ? "PSI" : "kg/cm²"})
        </label>
        <input
          type="number"
          name="emptyStaticPressure"
          placeholder="Empty Static Pressure"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Loaded Car Pressure ({units === "inch" ? "PSI" : "kg/cm²"})
        </label>
        <input
          type="number"
          name="loadedCarPressure"
          placeholder="Loaded Car Pressure"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Rated Flow ({units === "inch" ? "GPM" : "LPM"})
        </label>
        <input
          type="number"
          name="ratedFlow"
          placeholder="Rated Flow"
          required
          className="w-full p-1 text-sm border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div className="flex items-center gap-1 py-1">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="lowerDSR-sidebar"
          value="true"
          className="w-3 h-3 text-primary"
        />
        <label htmlFor="lowerDSR-sidebar" className="text-sm text-gray-700">
          Down Speed Regulation
        </label>
      </div>
      <div className="flex gap-2 pt-1">
        <Button
          as="button"
          type="submit"
          variant="maxtonPrimary"
          ariaLabel="Size Valve"
          className="px-2 py-1 text-sm font-medium"
        >
          <span>Size Valve</span>
        </Button>
        <Button
          as="button"
          type="button"
          variant="maxtonSecondary"
          onClick={handleReset}
          className="px-2 py-1 font-medium text-sm"
          ariaLabel="Reset"
        >
          <span>Reset</span>
        </Button>
      </div>
    </form>
  );
}

function ValveCalculatorResults({
  results,
  units,
  onReset,
}: {
  results: ValveData | null;
  units: "inch" | "metric";
  onReset: () => void;
}) {
  if (!results) return null;

  return (
    <div className="text-sm">
      <h3 className="text-sm font-semibold mb-2 text-gray-800">
        Provided data:
      </h3>
      <div className="space-y-1 mb-3">
        {results.formType === "form1" ? (
          <>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">Jack Type:</span>
              <span className="text-right">
                {results.jackType === "direct" ? "Direct" : "Roped"}{" "}
                {results.numJack === 1 ? "Single" : "Dual"} Jack
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">
                Piston Dia.:
              </span>
              <span className="text-right">
                {results.jackDiameter} {units === "inch" ? "in." : "mm"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">Car Speed:</span>
              <span className="text-right">
                {results.carSpeed} {units === "inch" ? "FPM" : "m/sec"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">Empty Car:</span>
              <span className="text-right">
                {results.emptyWeight}
                {results.emptyWeightUnits}
              </span>
            </div>
            {results.capacity && (
              <div className="flex justify-between items-center">
                <span className="font-medium mr-2 flex-shrink-0">
                  Capacity:
                </span>
                <span className="text-right">
                  {results.capacity} {units === "inch" ? "lbs" : "kg"}
                </span>
              </div>
            )}
          </>
        ) : (
          <>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">
                Rated Flow:
              </span>
              <span className="text-right">
                {results.ratedFlow} {units === "inch" ? "GPM" : "LPM"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">
                Empty Static:
              </span>
              <span className="text-right">
                {results.emptyStaticPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium mr-2 flex-shrink-0">
                Loaded Car:
              </span>
              <span className="text-right">
                {results.loadedCarPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </span>
            </div>
          </>
        )}
        <div className="flex justify-between items-center">
          <span className="font-medium mr-2 flex-shrink-0">Down Speed:</span>
          <span className="text-right">
            {results.downSpeedRegulation ? "Yes" : "No"}
          </span>
        </div>
      </div>

      <h3 className="text-sm font-semibold mb-2 text-gray-800">Results:</h3>
      <div className="space-y-1 mb-3">
        <div className="flex justify-between items-center">
          <span className="font-medium mr-2 flex-shrink-0">Rated Flow:</span>
          <span className="text-blue-600 text-right">{results.sngGPM} GPM</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="font-medium mr-2 flex-shrink-0">Empty PSI:</span>
          <span className="text-blue-600 text-right">
            {results.sngOutMinPSI} PSI
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="font-medium mr-2 flex-shrink-0">Loaded PSI:</span>
          <span className="text-blue-600 text-right">
            {results.sngOutMaxPSI} PSI
          </span>
        </div>
      </div>

      <div className="mb-5">
        <h3 className="text-sm font-semibold mb-1 text-gray-800">
          Recommendations:
        </h3>
        <div className="space-y-2">
          {results.products?.map((product, index) => (
            <ProductCard key={`product-${index}`} product={product} />
          ))}
        </div>
      </div>

      <button
        onClick={onReset}
        className="inline-flex items-center gap-1 px-2 py-2 text-sm font-medium text-white bg-primary focus:outline-none"
        aria-label="Calculate another valve"
      >
        Calculate Another
      </button>
    </div>
  );
}

function ProductCard({ product }: { product: ValveProduct }) {
  const { data: productData, isLoading: isProductLoading } =
    useGetProductByNameQuery(product.text.replace("OSV Configuration: ", ""));

  if (isProductLoading) {
    return <Skeleton className="w-full h-12 animate-pulse" />;
  }

  if (!productData) {
    return <div className="text-blue-600 text-sm">{product.text}</div>;
  }

  const productUrl = `/store/products/${productData.sku}`;

  return (
    <div className="w-full bg-white rounded-lg shadow-md overflow-hidden mb-2 hover:shadow-lg transition-shadow duration-300">
      <div className="flex items-center">
        {/* Image section */}
        <div className="w-20 h-20 p-2 flex items-center justify-center">
          {productData.image ? (
            <Link href={productUrl}>
              <ProductImage image={productData.image} alt={productData.name} />
            </Link>
          ) : (
            <div className="bg-gray-200 h-14 w-14 flex items-center justify-center">
              <span className="text-gray-400 text-xs">No Image</span>
            </div>
          )}
        </div>

        {/* Content section */}
        <div className="flex-1 justify-start p-2">
          <Link href={productUrl}>
            <h3 className="text-sm font-bold text-primary cursor-pointer hover:underline line-clamp-1">
              {product.text}
            </h3>
          </Link>

          <p className="text-gray-600 text-xs mt-1 line-clamp-1">
            {productData.description}
          </p>

          <Link
            href={productUrl}
            className="w-auto inline-flex items-center mt-1 px-2 py-1 text-xs bg-primary text-white hover:bg-primary/80 transition-colors duration-300 gap-1"
          >
            <span>Buy Online</span>
            <FaArrowRightLong size={10} />
          </Link>
        </div>
      </div>
    </div>
  );
}

function ProductImage({ image, alt }: { image: string; alt: string }) {
  const { data: productImage, isLoading: isProductImageLoading } =
    image.startsWith("http")
      ? { data: image, isLoading: false }
      : useGetImage(image);

  if (isProductImageLoading) {
    return <Skeleton className="w-10 h-10 animate-pulse" />;
  }

  return (
    <img
      src={productImage}
      alt={alt}
      className="object-contain h-10 w-10 cursor-pointer"
    />
  );
}
