import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { CheckoutFormValues } from "@/pages/store/checkout";
import { Receipt } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { BillingAddresses } from "../customers/billing-address";

export function CheckoutBillingInformation({
    checkoutForm,
}: {
    checkoutForm: UseFormReturn<CheckoutFormValues>;
}) {
    return (
        <Card className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center gap-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Receipt className="h-5 w-5 text-primary" />
                </div>
                <div>
                    <CardTitle>Billing Information</CardTitle>
                    <CardDescription>Select your billing address</CardDescription>
                </div>
            </CardHeader>
            <CardContent className="space-y-6">
                <BillingAddresses checkoutForm={checkoutForm} />
            </CardContent>
        </Card>
    );
}
