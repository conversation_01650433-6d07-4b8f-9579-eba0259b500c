import React from "react";
import { useDrag, useDrop } from "react-dnd";
import { GripVertical, X, Edit, Check, X as Cancel } from "lucide-react";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { PlusCircle, Trash2 } from "lucide-react";

type DragItem = {
    index: number;
    id: string;
    type: string;
};

interface DraggableItemProps {
    id: string;
    index: number;
    moveItem: (dragIndex: number, hoverIndex: number) => void;
    renderItem: (ref: React.RefObject<HTMLDivElement>, opacity: number) => React.ReactNode;
    type?: string;
}

export function DraggableItem({
    id,
    index,
    moveItem,
    renderItem,
    type = "DRAGGABLE_ITEM",
}: DraggableItemProps) {
    const ref = React.useRef<HTMLDivElement>(null);

    const [{ handlerId }, drop] = useDrop({
        accept: type,
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item: DragItem, monitor) {
            if (!ref.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            // Don't replace items with themselves
            if (dragIndex === hoverIndex) {
                return;
            }

            // Determine rectangle on screen
            const hoverBoundingRect = ref.current?.getBoundingClientRect();

            // Get vertical middle
            const hoverMiddleY =
                (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

            // Determine mouse position
            const clientOffset = monitor.getClientOffset();

            // Get pixels to the top
            const hoverClientY = clientOffset!.y - hoverBoundingRect.top;

            // Only perform the move when the mouse has crossed half of the items height
            // When dragging downwards, only move when the cursor is below 50%
            // When dragging upwards, only move when the cursor is above 50%

            // Dragging downwards
            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }

            // Dragging upwards
            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }

            // Time to actually perform the action
            moveItem(dragIndex, hoverIndex);

            // Note: we're mutating the monitor item here!
            // Generally it's better to avoid mutations,
            // but it's good here for the sake of performance
            // to avoid expensive index searches.
            item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag] = useDrag({
        type,
        item: () => {
            return { id, index };
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const opacity = isDragging ? 0.4 : 1;
    drag(drop(ref));

    return renderItem(ref, opacity);
}

type OptionType = "select" | "text" | "number";

interface OptionValue {
    name: string;
    value: string;
    price?: number;
}

interface DraggableOptionProps {
    option: Record<string, any>;
    index: number;
    moveOption: (dragIndex: number, hoverIndex: number) => void;
    removeOption: (index: number) => void;
    updateOption: (index: number, option: Record<string, any>) => void;
}

export function DraggableOption({
    option,
    index,
    moveOption,
    removeOption,
    updateOption
}: DraggableOptionProps) {
    const [isEditing, setIsEditing] = React.useState(false);
    const [editedOption, setEditedOption] = React.useState({ ...option });

    const handleSave = () => {
        updateOption(index, editedOption);
        setIsEditing(false);
    };

    const handleCancel = () => {
        setEditedOption({ ...option });
        setIsEditing(false);
    };

    const updateOptionValue = (
        valueIndex: number,
        field: keyof OptionValue,
        value: string | number
    ) => {
        const updatedValues = [...editedOption.options];
        updatedValues[valueIndex] = {
            ...updatedValues[valueIndex],
            [field]: value,
        };
        setEditedOption({ ...editedOption, options: updatedValues });
    };

    const addOptionValue = () => {
        const updatedValues = [...(editedOption.options || []), { name: "", value: "" }];
        setEditedOption({ ...editedOption, options: updatedValues });
    };

    const removeOptionValue = (valueIndex: number) => {
        const updatedValues = [...editedOption.options];
        updatedValues.splice(valueIndex, 1);
        setEditedOption({ ...editedOption, options: updatedValues });
    };

    return (
        <DraggableItem
            id={`option-${index}`}
            index={index}
            moveItem={moveOption}
            type="OPTION"
            renderItem={(ref, opacity) => (
                <div
                    ref={ref}
                    style={{ opacity }}
                    className={`border rounded-md ${isEditing ? 'p-4' : 'p-2'} ${!isEditing && 'cursor-move'}`}
                >
                    {isEditing ? (
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor={`option-name-${index}`}>Option Name</Label>
                                    <Input
                                        id={`option-name-${index}`}
                                        value={editedOption.name}
                                        onChange={(e) =>
                                            setEditedOption({ ...editedOption, name: e.target.value })
                                        }
                                        placeholder="e.g. Size, Color, etc."
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor={`option-type-${index}`}>Option Type</Label>
                                    <Select
                                        value={editedOption.type}
                                        onValueChange={(value: OptionType) =>
                                            setEditedOption({
                                                ...editedOption,
                                                type: value,
                                                options:
                                                    value === "select"
                                                        ? editedOption.options?.length
                                                            ? editedOption.options
                                                            : [{ name: "", value: "" }]
                                                        : undefined,
                                            })
                                        }
                                    >
                                        <SelectTrigger id={`option-type-${index}`}>
                                            <SelectValue placeholder="Select type" />
                                        </SelectTrigger>
                                        <SelectContent className="rounded-lg">
                                            <SelectItem value="select">Select</SelectItem>
                                            <SelectItem value="text">Text</SelectItem>
                                            <SelectItem value="number">Number</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {editedOption.type === "select" && editedOption.options && (
                                <div className="flex flex-col gap-2">
                                    <Label className="text-sm font-semibold">Option Values</Label>
                                    <div className="flex flex-col gap-1">
                                        {editedOption.options.map((optVal: OptionValue, valueIndex: number) => (
                                            <div
                                                key={valueIndex}
                                                className="grid grid-cols-1 gap-2 md:grid-cols-3 items-center"
                                            >
                                                <Input
                                                    placeholder="Display Name"
                                                    value={optVal.name}
                                                    onChange={(e) =>
                                                        updateOptionValue(valueIndex, "name", e.target.value)
                                                    }
                                                />
                                                <Input
                                                    placeholder="Value"
                                                    value={optVal.value}
                                                    onChange={(e) =>
                                                        updateOptionValue(valueIndex, "value", e.target.value)
                                                    }
                                                />
                                                <div className="flex gap-2 items-center">
                                                    <Input
                                                        type="number"
                                                        placeholder="Price (optional)"
                                                        value={optVal.price || ""}
                                                        onChange={(e) => {
                                                            const value = e.target.value
                                                                ? parseFloat(e.target.value)
                                                                : undefined;
                                                            updateOptionValue(valueIndex, "price", value as number);
                                                        }}
                                                        onWheel={(e) => e.currentTarget.blur()}
                                                    />
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="icon"
                                                        onClick={() => removeOptionValue(valueIndex)}
                                                    >
                                                        <Trash2 className="h-4 w-4 text-red-500" />
                                                    </Button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={addOptionValue}
                                        className="mt-2 w-fit"
                                    >
                                        <PlusCircle className="h-4 w-4 mr-1" /> Add Value
                                    </Button>
                                </div>
                            )}

                            <div className="flex gap-2 justify-end">
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={handleCancel}
                                >
                                    <Cancel className="h-4 w-4 mr-1" /> Cancel
                                </Button>
                                <Button
                                    type="button"
                                    variant="primary"
                                    size="sm"
                                    onClick={handleSave}
                                    disabled={!editedOption.name}
                                >
                                    <Check className="h-4 w-4 mr-1" /> Save
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="flex justify-between items-center">
                            <div className="flex items-center">
                                <GripVertical className="h-4 w-4 mr-2 text-muted-foreground" />
                                <div>
                                    <span className="font-semibold">{option.name}</span>
                                    <span className="ml-2 text-sm text-muted-foreground">
                                        {option.type === "select"
                                            ? `(${option.options?.length || 0} values)`
                                            : `(${option.type})`}
                                    </span>
                                </div>
                            </div>
                            <div className="flex items-center">
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    className="mr-1"
                                    onClick={() => setIsEditing(true)}
                                >
                                    <Edit className="h-4 w-4 text-blue-500" />
                                </Button>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => removeOption(index)}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            )}
        />
    );
} 