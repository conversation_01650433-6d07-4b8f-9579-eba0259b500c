import { ImageIcon } from "@/components/common/icons";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { CategoryWithParent } from "@/pages/api/categories";
import { UseFormReturn } from "react-hook-form";
import { ACCEPTED_IMAGE_TYPES } from "../add-new-product";
import RichEditor from "../../block-editor/rich-editor";
import { useEffect, useState } from "react";

interface BasicInformationProps {
  form: UseFormReturn<any>;
  file: File | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  categories: CategoryWithParent[];
}

// Individual field components for reuse in grid layout
const NameField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="name"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Product Name *</FormLabel>
        <FormControl>
          <Input
            className=""
            placeholder="Enter product name here..."
            required
            {...field}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const BrandField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="brand"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Brand</FormLabel>
        <FormControl>
          <Input
            className=""
            placeholder="Enter product brand here..."
            value={field.value || ""}
            onChange={field.onChange}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const PriceField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="price"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Price ($) *</FormLabel>
        <FormControl>
          <Input
            type="number"
            placeholder="Enter product price here..."
            min="0"
            step="0.01"
            required
            className=""
            onWheel={(e) => e.currentTarget.blur()}
            {...field}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const SkuField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="sku"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Product Code</FormLabel>
        <FormControl>
          <Input
            className=""
            placeholder="Enter product code here..."
            value={field.value || ""}
            onChange={field.onChange}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const VariantField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="variant"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Variant</FormLabel>
        <FormControl>
          <Input
            className=""
            placeholder="Enter product variant here..."
            value={field.value || ""}
            onChange={field.onChange}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const CategoryField = ({
  form,
  categories,
}: {
  form: UseFormReturn<any>;
  categories: CategoryWithParent[];
}) => (
  <FormField
    control={form.control}
    name="category_id"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Product Category</FormLabel>
        <FormControl>
          <Select
            value={field.value}
            onValueChange={field.onChange}
            disabled={categories.length === 0}
          >
            <SelectTrigger className="">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent className="rounded-lg">
              {categories.length === 0 ? (
                <SelectItem value="loading">Loading categories...</SelectItem>
              ) : categories.length > 0 ? (
                categories.map((category: CategoryWithParent) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="none">No categories available</SelectItem>
              )}
            </SelectContent>
          </Select>
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

// Update the ImageFieldProps to include existingImageUrl and additional properties for multiple images
interface ImageFieldProps {
  file: File | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  existingImageUrl?: string | null;
  additionalFiles?: File[];
  existingAdditionalImageUrls?: string[];
}

// Update the ImageField component to show the existing image
const ImageField = ({
  file,
  handleFileChange,
  existingImageUrl,
  additionalFiles,
  existingAdditionalImageUrls,
}: ImageFieldProps) => {
  return (
    <div className="space-y-2">
      <Label htmlFor="image">Product Image</Label>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <Input
            type="file"
            id="image"
            accept={ACCEPTED_IMAGE_TYPES.join(",")}
            onChange={handleFileChange}
            className="cursor-pointer"
          />
          <p className="mt-2 text-sm text-gray-500">
            Upload a product image (PNG, JPG, GIF up to 10MB)
          </p>
        </div>
        <div className="flex items-center justify-center">
          {file && (
            <div className="relative h-32 w-32 overflow-hidden rounded-md border">
              <img
                src={URL.createObjectURL(file)}
                alt="Product preview"
                style={{ objectFit: "cover" }}
              />
            </div>
          )}
          {!file && existingImageUrl && (
            <div className="relative h-32 w-32 overflow-hidden rounded-md border">
              <img
                src={existingImageUrl}
                alt="Product image"
                style={{ objectFit: "cover" }}
              />
            </div>
          )}
          {!file && !existingImageUrl && (
            <div className="flex h-32 w-32 items-center justify-center rounded-md border bg-neutral-50">
              <ImageIcon className="h-12 w-12 text-gray-300" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const DescriptionField = ({ form }: { form: UseFormReturn<any> }) => {
  return (
    <FormField
      control={form.control}
      name="description"
      render={({ field }) => {
        return (
          <FormItem>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <RichEditor
                content={field.value}
                onChange={field.onChange}
                placeholder="Start creating rich content..."
              />
            </FormControl>
            <p className="text-xs text-muted-foreground">
              Set a description to the product for better visibility.
            </p>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

const TagsField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="tags"
    render={({ field }) => (
      <FormItem>
        <FormLabel>Tags</FormLabel>
        <FormControl>
          <Input
            placeholder="Enter tags separated by commas"
            value={field.value || ""}
            onChange={(e) => field.onChange(e.target.value.split(","))}
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

const AvailableField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="available"
    render={({ field }) => (
      <FormItem className="flex flex-row items-center space-x-2">
        <FormControl>
          <Switch checked={field.value} onCheckedChange={field.onChange} />
        </FormControl>
        <FormLabel>
          Available{" "}
          <span className="text-xs text-muted-foreground">
            (If not available, it will not be shown on the store)
          </span>
        </FormLabel>
      </FormItem>
    )}
  />
);

const QuoteField = ({ form }: { form: UseFormReturn<any> }) => (
  <FormField
    control={form.control}
    name="is_quote"
    render={({ field }) => (
      <FormItem className="flex flex-row items-center space-x-2">
        <FormControl>
          <Switch
            checked={field.value ?? false}
            onCheckedChange={field.onChange}
          />
        </FormControl>
        <FormLabel>
          Quote{" "}
          <span className="text-xs text-muted-foreground">
            (If a quote is active, the 'Request a Quote' option will be
            displayed instead of the shopping cart.)
          </span>
        </FormLabel>
      </FormItem>
    )}
  />
);

function BasicInformation({
  form,
  file,
  handleFileChange,
  categories,
}: BasicInformationProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <NameField form={form} />
          <BrandField form={form} />
          <PriceField form={form} />
          <SkuField form={form} />
          <VariantField form={form} />
          <CategoryField form={form} categories={categories} />
          <ImageField file={file} handleFileChange={handleFileChange} />
        </div>
        <DescriptionField form={form} />
        <AvailableField form={form} />
        <QuoteField form={form} />
      </CardContent>
    </Card>
  );
}

// Export individual fields for the grid layout
BasicInformation.NameField = NameField;
BasicInformation.BrandField = BrandField;
BasicInformation.PriceField = PriceField;
BasicInformation.SkuField = SkuField;
BasicInformation.VariantField = VariantField;
BasicInformation.CategoryField = CategoryField;
BasicInformation.ImageField = ImageField;
BasicInformation.DescriptionField = DescriptionField;
BasicInformation.AvailableField = AvailableField;
BasicInformation.TagsField = TagsField;
BasicInformation.QuoteField = QuoteField;

export default BasicInformation;
