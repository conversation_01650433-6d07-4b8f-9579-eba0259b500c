import dynamic from "next/dynamic";
import { BlogPost, LabeledRoute, SectionsProps } from "types";

import * as BlogVariant from "@stackshift-ui/blog";

const Variants = {
  variant_a: BlogVariant.Blog_A,
  variant_b: BlogVariant.Blog_B,
  variant_c: BlogVariant.Blog_C,
  variant_d: BlogVariant.Blog_D,
  variant_e: dynamic(() => import("./variant_e")),
};

export interface BlogProps {
  subtitle?: string;
  title?: string;
  posts?: BlogPost[];
  description?: string;
  primaryButton?: LabeledRoute;
}
const displayName = "Blog";

export const Blog: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    subtitle: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    description: data?.variants?.title ?? undefined,
    posts: (data?.variants?.posts || data?.variants?.blogPosts) ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
  };
  return Variant ? <Variant {...props} /> : null;
};

Blog.displayName = displayName;
