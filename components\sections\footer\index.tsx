import React, { lazy } from "react";
import dynamic from "next/dynamic";
import {
  SectionsProps,
  Logo,
  ContactDetails,
  SocialLink,
  LabeledRouteWithKey,
  Images,
} from "../../../types";
import * as FooterVariants from "@stackshift-ui/footer";

const Variants = {
  variant_a: FooterVariants.Footer_A,
  variant_b: FooterVariants.Footer_B,
  variant_c: FooterVariants.Footer_C,
  variant_d: FooterVariants.Footer_D,
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
};

export interface FooterProps {
  logo?: Logo;
  text?: string;
  contacts?: ContactDetails[];
  copyright?: string;
  socialMedia?: SocialLink[];
  menu?: LabeledRouteWithKey[];
  multipleMenus?: any;
  images?: Images[];
}

const displayName = "Footer";

export const Footer: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    logo: data?.variants?.logo ?? undefined,
    text: data?.variants?.plainText ?? undefined,
    contacts: data?.variants?.contactDetails ?? undefined,
    copyright: data?.variants?.copyright ?? undefined,
    socialMedia: data?.variants?.socialLinks ?? undefined,
    menu: data?.variants?.menu ?? undefined,
    multipleMenus: data?.variants?.multipleMenus ?? undefined,
    images: data?.variants?.images ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Footer.displayName = displayName;
