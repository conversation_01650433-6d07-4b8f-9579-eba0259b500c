import { ValveProduct } from "../types/valve";

//Sample Products Only for testing
interface ValveProductMap {
  [key: number]: ValveProduct[];
}

interface OSVProductMap {
  [key: string]: ValveProduct;
}

export const valveProducts: ValveProductMap = {
  1: [
    {
      text: "EMV10",
      link: "/products/index.php?route=product/product&path=59_73&product_id=53",
      image: "/products/image/cache/data/valves/emv10-80x80.jpg",
    },
  ],
  2: [
    {
      text: "UC4MS2",
      link: "/products/index.php?route=product/product&path=59_73&product_id=154",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4S2",
      link: "/products/index.php?route=product/product&path=59_73&product_id=155",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  3: [
    {
      text: "UC4ME1",
      link: "/products/index.php?route=product/product&path=59_68&product_id=61",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4E1",
      link: "/products/index.php?route=product/product&path=59_68&product_id=58",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  4: [
    {
      text: "UC4ME2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=54",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4E2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=59",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  5: [
    {
      text: "UC4MSTD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=60",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4STD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=57",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  6: [
    {
      text: "UC4MRE2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=66",
      image: "/products/image/cache/data/valves/new uc4mr-80x80.jpg",
    },
  ],
  7: [
    {
      text: "UC4MRSTD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=65",
      image: "/products/image/cache/data/valves/new uc4mr-80x80.jpg",
    },
  ],
  8: [
    {
      text: "UC1A",
      link: "/products/index.php?route=product/product&path=59_68&product_id=55",
      image:
        "/products/image/cache/data/valves/UC1A_NEW_COVER_500X500-228x228-80x80.jpg",
    },
  ],
  9: [
    {
      text: "UC2A",
      link: "/products/index.php?route=product/product&path=59_68&product_id=56",
      image: "/products/image/cache/data/valves/UC2ANewcover-80x80.jpg",
    },
  ],
  10: [
    {
      text: "UC4MRE1",
      link: "/products/index.php?route=product/product&path=59_68&product_id=67",
      image: "/products/image/cache/data/valves/new uc4mr-80x80.jpg",
    },
  ],
  11: [
    {
      text: "UC4ME1",
      link: "/products/index.php?route=product/product&path=59_68&product_id=61",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4E1",
      link: "/products/index.php?route=product/product&path=59_68&product_id=58",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  12: [
    {
      text: "UC4ME2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=54",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4E2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=59",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  13: [
    {
      text: "UC4MSTD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=60",
      image:
        "/products/image/cache/data/valves/uc4m with new coil cover-80x80.jpg",
    },
    {
      text: "UC4STD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=57",
      image: "/products/image/cache/data/valves/UC4_new_cover-80x80.jpg",
    },
  ],
  14: [
    {
      text: "UC4MRE2",
      link: "/products/index.php?route=product/product&path=59_68&product_id=66",
      image: "/products/image/cache/data/valves/new uc4mr-80x80.jpg",
    },
  ],
  15: [
    {
      text: "UC4MRSTD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=65",
      image: "/products/image/cache/data/valves/new uc4mr-80x80.jpg",
    },
  ],
  16: [
    {
      text: "UC4HD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=182",
      image: "/products/image/cache/data/valves/UC4%20HD-80x80.jpg",
    },
    {
      text: "UC4MHD",
      link: "/products/index.php?route=product/product&path=59_68&product_id=179",
      image: "/products/image/cache/data/valves/UC4M%20HD-80x80.jpg",
    },
  ],
  17: [
    {
      text: "UC2A",
      link: "/products/index.php?route=product/product&path=59_68&product_id=56",
      image: "/products/image/cache/data/valves/UC2ANewcover-80x80.jpg",
    },
  ],
};

export const osvProducts: OSVProductMap = {
  "OSV-STD": {
    text: "OSV Configuration: OSV STD",
    link: "/products/index.php?route=product/product&path=59_69&product_id=62",
    image:
      "/products/image/cache/data/valves/OSV%20New%20Handle%20web-80x80.jpg",
  },
  "OSV-E1": {
    text: "OSV Configuration: OSVE1",
    link: "/products/index.php?route=product/product&path=59_69&product_id=63",
    image: "/products/image/cache/data/valves/OSV-80x80.jpg",
  },
  OSVB25: {
    text: "OSV Configuration: OSVB25",
    link: "/products/index.php?route=product/product&path=59_74&product_id=64",
    image: "/wp-content/uploads/OSVB25-scaled-1.jpg",
  },
};
