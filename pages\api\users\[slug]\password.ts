import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { z } from "zod";
import { resetSessionCookie } from "../../auth/refresh";

export default checkAuth(
  matchRoute({
    POST: handler,
  }),
);

const changePasswordSchema = z.object({
  new_password: z.string().min(6, { message: "New password is required" }),
  confirm_password: z
    .string()
    .min(6, { message: "Confirm password is required" }),
});

export interface ChangePasswordResponse {
  error?: string;
  message?: string;
}

async function handler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<ChangePasswordResponse>,
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (userId !== id) {
    return res.status(403).json({ error: "Unauthorized" });
  }

  const data = changePasswordSchema.safeParse(req.body);

  if (!data.success) {
    return res.status(400).json({ error: data.error.issues.at(0)?.message });
  }

  const parsedData = data.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data: _user, error: userError } =
    await supabaseAdminClient.auth.admin.getUserById(userId);

  if (userError) {
    return res.status(400).json({ error: userError.message });
  }

  const { data: _updateData, error: updateError } =
    await supabaseAdminClient.auth.admin.updateUserById(userId, {
      password: parsedData.new_password,
    });

  if (updateError) {
    return res.status(400).json({ error: updateError.message });
  }

  const now = new Date();
  const updatedAt = now.toISOString();

  await supabaseAdminClient
    .schema("public")
    .from("users")
    .update({ updated_at: updatedAt })
    .eq("id", userId);

  await supabaseAdminClient
    .from("customers")
    .update({ updated_at: updatedAt })
    .eq("user_id", userId);

  resetSessionCookie(res);

  return res.status(200).json({ message: "Password updated successfully" });
}
