import InlineEditor from "components/InlineEditor";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Text } from "@stackshift-ui/text";
import { InlineEditorContext } from "context/InlineEditorContext";
import { format } from "date-fns";
import { urlFor } from "lib/sanity";
import { PortableText } from "@portabletext/react";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import React, { useState, useEffect, useMemo, useRef } from "react";
import { ArchivePageData } from "@/pages/archive/[year]/[month]";
import { useRouter } from "next/router";
import { PostCategory } from "types";
import { BlogsData } from "types";
import { FaReadme } from "react-icons/fa";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";

interface Comment {
  author: string;
  content: string;
  date: string;
}

interface ArchiveCombination {
  year: {
    _id: string;
    title: string;
    slug: string;
  };
  month: {
    _id: string;
    title: string;
    slug: string;
  };
}

interface ArchiveSectionProps {
  posts: ArchivePageData["posts"];
  archive: ArchivePageData["archive"];
  categories: ArchivePageData["categories"];
  allArchives: {
    years: ArchivePageData["allArchives"];
    months: ArchivePageData["allArchives"];
    validCombinations: ArchiveCombination[];
  };
}

const Navigation = dynamic(() =>
  import("components/sections/navigation").then((m) => m.Navigation)
);
const Footer = dynamic(() =>
  import("components/sections/footer").then((m) => m.Footer)
);

const ArchiveSection: React.FC<ArchiveSectionProps> = ({
  posts,
  archive,
  categories,
  allArchives,
}) => {
  const showInlineEditor = React.useContext(InlineEditorContext);
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 10;
  const totalPages = Math.ceil((posts?.length || 0) / postsPerPage);
  const router = useRouter();

  // State for collapsible sidebar sections
  const [isCategoryExpanded, setIsCategoryExpanded] = useState(false);
  const [isArchiveExpanded, setIsArchiveExpanded] = useState(false);
  const [isTagExpanded, setIsTagExpanded] = useState(false);

  // Check if current page is a category or archive page and set initial expansion state
  useEffect(() => {
    if (archive && (archive.year || archive.month)) {
      setIsArchiveExpanded(true);
    }
  }, [archive]);

  // Add state for share popup and likes
  const [postStates, setPostStates] = useState<
    Record<
      string,
      {
        showSharePopup: boolean;
        showComments: boolean;
        isLiked: boolean;
        likesCount: number;
        comments: Comment[];
      }
    >
  >({});

  // Refs for share popup click outside handling
  const sharePopupRefs = useRef<Record<string, HTMLDivElement>>({});
  const shareButtonRefs = useRef<Record<string, HTMLButtonElement>>({});

  useEffect(() => {
    const initialStates: Record<string, any> = {};
    if (posts) {
      posts.forEach((post) => {
        const postId = post._id;
        if (!postId) return;

        // Get total likes from localStorage
        const totalLikes = parseInt(
          localStorage.getItem(`post-${postId}-total-likes`) || "0"
        );
        // Get user-specific like status
        const userLiked =
          localStorage.getItem(`post-${postId}-user-liked`) === "true";
        const comments = JSON.parse(
          localStorage.getItem(`post-${postId}-comments`) || "[]"
        );

        initialStates[postId] = {
          showSharePopup: false,
          showComments: false,
          isLiked: userLiked,
          likesCount: totalLikes,
          comments: comments,
        };
      });
    }
    setPostStates(initialStates);
  }, [posts]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      Object.entries(postStates).forEach(([postId, state]) => {
        if (state.showSharePopup) {
          const popupRef = sharePopupRefs.current[postId];
          const buttonRef = shareButtonRefs.current[postId];

          if (
            popupRef &&
            !popupRef.contains(event.target as Node) &&
            buttonRef &&
            !buttonRef.contains(event.target as Node)
          ) {
            setPostStates((prev) => ({
              ...prev,
              [postId]: { ...prev[postId], showSharePopup: false },
            }));
          }
        }
      });
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [postStates]);

  // Sort posts by publishedAt date (latest to oldest)
  const sortedPosts = useMemo(() => {
    if (!posts) return [];
    return [...posts].sort((a, b) => {
      const dateA = a.publishedAt ? new Date(a.publishedAt).getTime() : 0;
      const dateB = b.publishedAt ? new Date(b.publishedAt).getTime() : 0;
      return dateB - dateA;
    });
  }, [posts]);

  // Calculate pagination
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = sortedPosts.slice(indexOfFirstPost, indexOfLastPost);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const toggleSharePopup = (postId: string) => {
    if (!postId) return;
    setPostStates((prev) => ({
      ...prev,
      [postId]: {
        ...prev[postId],
        showSharePopup: !prev[postId]?.showSharePopup,
      },
    }));
  };

  const handleLike = (postId: string) => {
    if (!postId) return;
    setPostStates((prev) => {
      const currentState = prev[postId] || {
        showSharePopup: false,
        isLiked: false,
        likesCount: 0,
        comments: [],
      };

      const newLikeState = !currentState.isLiked;
      const newLikeCount = currentState.likesCount + (newLikeState ? 1 : -1);

      // Update localStorage for user-specific like status
      localStorage.setItem(`post-${postId}-user-liked`, String(newLikeState));
      // Update localStorage for total likes
      localStorage.setItem(`post-${postId}-total-likes`, String(newLikeCount));

      return {
        ...prev,
        [postId]: {
          ...currentState,
          isLiked: newLikeState,
          likesCount: newLikeCount,
        },
      };
    });
  };

  if (!archive) {
    return null;
  }

  return (
    <InlineEditor
      document={{
        id: archive.year._id || "",
        type: "archive",
      }}
      showInlineEditor={showInlineEditor}
      key={archive.year._id}
    >
      {archive?.navigation && (
        <Navigation
          data={archive?.navigation}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}
      <section className="py-10 md:pt-12 md:pb-20">
        <Container className="!px-0">
          {/* Title Section */}
          <div className="mx-auto mb-8">
            <Container maxWidth={1380}>
              {archive.month.title && (
                <Heading
                  type="h1"
                  className="!text-xl md:!text-3xl !font-medium text-left uppercase !text-primary tracking-wide"
                >
                  Archives
                </Heading>
              )}
              {archive.month.title && (
                <Text className="text-[#999] !text-sm leading-snug mb-5">
                  Monthly Archive for: "{archive.month.title} {""}
                  {archive.year.title}"
                </Text>
              )}
            </Container>
            <hr className="w-full border-t border-primary/20 my-4" />
          </div>

          <Container maxWidth={1380}>
            <Flex className="flex-col lg:flex-row gap-8 mx-auto">
              {/* Main Content */}
              <div className="w-full lg:w-3/5">
                {/* Posts Grid */}
                <div className="grid grid-cols-1 gap-8">
                  {currentPosts && currentPosts.length > 0 ? (
                    currentPosts.map((post) => {
                      // Ensure we have required fields
                      const postId = post._id;
                      if (!postId || !post.slug?.current) return null;

                      // Get the current post state with default values
                      const postState = postStates[postId] || {
                        showSharePopup: false,
                        showComments: false,
                        isLiked: false,
                        likesCount: 0,
                        comments: [],
                      };

                      // Create refs for this post
                      const buttonRef = (el: HTMLButtonElement | null) => {
                        if (el) shareButtonRefs.current[postId] = el;
                      };

                      const popupRef = (el: HTMLDivElement | null) => {
                        if (el) sharePopupRefs.current[postId] = el;
                      };

                      return (
                        <article
                          key={postId}
                          className="overflow-hidden shadow-lg border border-gray-100 transition-all duration-300 hover:shadow-xl hover:border-gray-100/20 bg-white group"
                        >
                          {/* Full Width Image */}
                          {post?.mainImage && (
                            <Link
                              href={`/${
                                post?.slug?.current
                                  ? post.slug.current
                                  : "page-not-found"
                              }`}
                            >
                              <div className="relative group overflow-hidden">
                                <div className="relative h-[380px] w-full">
                                  <Image
                                    src={post.mainImage}
                                    alt={post.title || "Blog post image"}
                                    fill
                                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                                  />
                                  {/* Hover overlay effect */}
                                  <div className="absolute inset-0 bg-primary bg-opacity-70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <FaReadme className="text-white w-14 h-14" />
                                  </div>
                                </div>
                              </div>
                            </Link>
                          )}

                          {/* Content Container */}
                          <div className="p-7">
                            {/* Title */}
                            <Link href={`/${post.slug.current}`}>
                              <Heading
                                type="h2"
                                className="!text-xl md:!text-2xl mb-4 !leading-snug !font-bold !font-sans !text-black hover:!text-primary transition-colors duration-300"
                              >
                                {post.title}
                              </Heading>
                            </Link>

                            {/* Author and Categories Info */}
                            <div className="mb-2 text-xs !italic text-[#999]">
                              <span>By </span>
                              <span className="text-[#252525] hover:text-primary">
                                {Array.isArray(post.authors) &&
                                post.authors.length > 0
                                  ? post.authors.map((author, index) => (
                                      <span key={author._id}>
                                        {index > 0 && ", "}
                                        <Link
                                          href={`/author/${
                                            author?.slug?.current
                                              ? author.slug.current
                                              : "page-not-found"
                                          }`}
                                        >
                                          {author?.name}
                                        </Link>
                                      </span>
                                    ))
                                  : "Anonymous"}
                              </span>

                              <span> In </span>
                              <span className="text-[#252525] hover:text-primary">
                                {post.categories?.map((cat, index) => (
                                  <span key={cat._id}>
                                    {index > 0 && ", "}
                                    <Link
                                      href={`/category/${cat.slug?.current}`}
                                    >
                                      {cat.title}
                                    </Link>
                                  </span>
                                ))}
                              </span>

                              <span> Posted </span>
                              {post.publishedAt ? (
                                <Link
                                  href={`/archive/${format(
                                    new Date(post.publishedAt),
                                    "yyyy"
                                  )}/${format(
                                    new Date(post.publishedAt),
                                    "MM"
                                  )}`}
                                  className="text-[#252525] hover:text-primary"
                                >
                                  {format(
                                    new Date(post.publishedAt),
                                    "MMMM d, yyyy"
                                  )}
                                </Link>
                              ) : (
                                <span className="text-[#252525] hover:text-primary">
                                  No date
                                </span>
                              )}
                            </div>

                            {/* Tags - Add this section */}
                            {post.tags && (
                              <div className="mb-4 text-xs text-[#999]">
                                <span>Tags: </span>
                                {Array.isArray(post.tags) ? (
                                  // Handle array of tags
                                  post.tags.map((tag, index) => (
                                    <span
                                      key={tag._id}
                                      className="inline-block"
                                    >
                                      {index > 0 && ", "}
                                      <Link
                                        href={`/tag/${tag.slug?.current}`}
                                        className="text-[#252525] hover:text-primary transition-colors duration-200 capitalize"
                                      >
                                        {tag.title}
                                      </Link>
                                    </span>
                                  ))
                                ) : (
                                  // Handle single PostTag object
                                  <span className="inline-block">
                                    <Link
                                      href={`/tag/${post.tags.slug?.current}`}
                                      className="text-[#252525] hover:text-primary transition-colors duration-200"
                                    >
                                      {post.tags.title}
                                    </Link>
                                  </span>
                                )}
                              </div>
                            )}

                            {/* Excerpt */}
                            <Text className="text-secondary !text-sm mb-3">
                              {post?.excerpt}
                            </Text>

                            {/* Read More Button */}
                            <div className="flex justify-center md:block">
                              <Link
                                href={`/${post.slug.current}`}
                                className="relative text-xs uppercase font-semibold inline-flex flex-row items-center justify-center bg-primary text-white border-2 border-primary hover:border-primary/0 px-6 py-3 transition duration-200 overflow-hidden z-10 before:content-[''] before:absolute before:top-0 before:left-0 before:w-0 before:h-full before:bg-[#063a6b] before:transition-all before:duration-300 before:ease-in-out before:z-[-1] hover:before:w-full"
                              >
                                Read More
                              </Link>
                            </div>
                          </div>
                        </article>
                      );
                    })
                  ) : (
                    <div className="text-center py-12 bg-white rounded-lg shadow-md">
                      <Text className="text-gray-600 italic">
                        No posts found in this archive.
                      </Text>
                    </div>
                  )}
                </div>

                {/* Pagination */}
                <div className="lg:px-10">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              </div>

              {/* Sidebar - Categories and Archives */}
              <Flex className="flex-col md:flex-row lg:flex-col w-full lg:w-2/5 gap-8">
                {/* Categories Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsCategoryExpanded(!isCategoryExpanded)}
                    aria-expanded={isCategoryExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Categories
                    </Text>
                    {isCategoryExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isCategoryExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isCategoryExpanded ? "thin" : "none",
                      scrollbarColor: isCategoryExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {categories
                        ?.sort((a, b) =>
                          (a.title || "").localeCompare(b.title || "")
                        )
                        ?.map((cat) => {
                          return (
                            <li
                              key={cat._id}
                              className="group py-2 border-b border-gray-100 last:border-b-0"
                            >
                              <Link
                                href={`/category/${cat.slug?.current}`}
                                className="flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform"
                              >
                                <svg
                                  className="w-3 h-3 mr-2 text-primary/60"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                                {cat.title}
                              </Link>
                            </li>
                          );
                        })}
                    </ul>
                  </div>
                </div>

                {/* Tags Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsTagExpanded(!isTagExpanded)}
                    aria-expanded={isTagExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Tags
                    </Text>
                    {isTagExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isTagExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isTagExpanded ? "thin" : "none",
                      scrollbarColor: isTagExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {(() => {
                        // Extract all unique tags from posts
                        const allTags = new Map();

                        posts?.forEach((post) => {
                          if (post.tags) {
                            if (Array.isArray(post.tags)) {
                              // Handle array of tags
                              post.tags.forEach((tag) => {
                                if (tag && tag._id && !allTags.has(tag._id)) {
                                  allTags.set(tag._id, tag);
                                }
                              });
                            } else {
                              // Handle single PostTag object
                              if (
                                post.tags._id &&
                                !allTags.has(post.tags._id)
                              ) {
                                allTags.set(post.tags._id, post.tags);
                              }
                            }
                          }
                        });

                        // Convert to array and sort
                        const uniqueTags = Array.from(allTags.values()).sort(
                          (a, b) => (a.title || "").localeCompare(b.title || "")
                        );

                        return uniqueTags.length > 0 ? (
                          uniqueTags.map((tag) => {
                            return (
                              <li
                                key={tag._id}
                                className="group py-2 border-b border-gray-100 last:border-b-0"
                              >
                                <Link
                                  href={`/tag/${tag.slug?.current}`}
                                  className="flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform capitalize"
                                >
                                  <svg
                                    className="w-3 h-3 mr-2 text-primary/60"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                  {tag.title}
                                </Link>
                              </li>
                            );
                          })
                        ) : (
                          <li>
                            <Text className="text-gray-500 !text-sm italic">
                              No tags available
                            </Text>
                          </li>
                        );
                      })()}
                    </ul>
                  </div>
                </div>

                {/* Archives Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsArchiveExpanded(!isArchiveExpanded)}
                    aria-expanded={isArchiveExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Archives
                    </Text>
                    {isArchiveExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isArchiveExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isArchiveExpanded ? "thin" : "none",
                      scrollbarColor: isArchiveExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {(() => {
                        // Deduplicate combinations
                        const seen = new Set();
                        const uniqueCombinations =
                          allArchives?.validCombinations?.filter((combo) => {
                            const key = `${combo.year._id}-${combo.month._id}`;
                            if (seen.has(key)) return false;
                            seen.add(key);
                            return true;
                          });

                        return (
                          uniqueCombinations?.map((combo) => {
                            // Check if this is the current archive
                            const isActive =
                              archive?.year?.title === combo.year.title &&
                              archive?.month?.title === combo.month.title;

                            return (
                              <li
                                key={`${combo.year._id}-${combo.month._id}`}
                                className={`group py-2 border-b border-gray-100 last:border-b-0 ${
                                  isActive
                                    ? "bg-primary/5 -mx-3 px-3 rounded"
                                    : ""
                                }`}
                              >
                                <Link
                                  href={`/archive/${combo.year.slug}/${combo.month.slug}`}
                                  className={`flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform  ${
                                    isActive
                                      ? "!text-primary font-semibold"
                                      : ""
                                  }`}
                                >
                                  <svg
                                    className={`w-3 h-3 mr-2 ${
                                      isActive
                                        ? "text-primary"
                                        : "text-primary/60"
                                    }`}
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                  {combo.month.title} {combo.year.title}
                                  {isActive && (
                                    <span className="ml-auto bg-primary text-white text-xs py-0.5 px-2 rounded-full">
                                      Current
                                    </span>
                                  )}
                                </Link>
                              </li>
                            );
                          }) || (
                            <li>
                              <Text className="text-gray-500 !text-sm italic">
                                No archives available
                              </Text>
                            </li>
                          )
                        );
                      })()}
                    </ul>
                  </div>
                </div>
              </Flex>
            </Flex>
          </Container>
        </Container>
      </section>

      {archive.footer && (
        <Footer
          data={archive.footer}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}

      {/* Add custom scrollbar styles */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f3f4f6;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(0, 87, 168, 0.4);
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 87, 168, 0.6);
        }
      `}</style>
    </InlineEditor>
  );
};

function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) {
  const [maxVisiblePages, setMaxVisiblePages] = useState(9);
  const [startPage, setStartPage] = useState(1);

  useEffect(() => {
    const updateMaxPages = () => {
      setMaxVisiblePages(window.innerWidth < 768 ? 5 : 9);
    };

    updateMaxPages();
    window.addEventListener("resize", updateMaxPages);
    return () => window.removeEventListener("resize", updateMaxPages);
  }, []);

  if (totalPages <= 1) return null;

  const renderPageNumbers = () => {
    const pages = [];
    const endPage = Math.min(startPage + maxVisiblePages - 1, totalPages);
    const hasLeftEllipsis = startPage > 1;
    const hasRightEllipsis = endPage < totalPages;

    if (hasLeftEllipsis) pages.push("...");
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    if (hasRightEllipsis) pages.push("...");

    return pages;
  };

  const handlePageClick = (page: number | string) => {
    if (page === "...") {
      if (startPage === 1) {
        setStartPage(Math.max(1, totalPages - maxVisiblePages + 1));
        onPageChange(startPage + maxVisiblePages);
      } else {
        setStartPage(1);
        onPageChange(1);
      }
    } else {
      onPageChange(Number(page));
      if (page === startPage + maxVisiblePages - 1 && page < totalPages) {
        setStartPage(startPage + 2);
      } else if (page === startPage && startPage > 1) {
        setStartPage(Math.max(1, startPage - 2));
      }
    }
  };

  return (
    <div className="flex flex-col md:flex-row justify-between items-center mt-16 md:mt-24">
      <div className="flex items-center gap-3">
        {renderPageNumbers().map((page, index) => (
          <button
            key={index}
            onClick={() => handlePageClick(page)}
            className={`px-[14px] py-2 text-xs text-[#0006] border-2 ${
              currentPage === page
                ? "border-[#0006] !font-bold"
                : "border-transparent hover:border-[#0006]"
            }`}
          >
            {page}
          </button>
        ))}
        <button
          onClick={() =>
            currentPage < totalPages && onPageChange(currentPage + 1)
          }
          disabled={currentPage === totalPages}
          className={`p-2 ${
            currentPage === totalPages
              ? "opacity-50 cursor-not-allowed"
              : "hover:text-primary"
          }`}
          aria-label="Next page"
        >
          <svg
            className="w-4 h-4 fill-[#0006]"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 640 1792"
          >
            <path d="M595 960q0 13-10 23l-466 466q-10 10-23 10t-23-10l-50-50q-10-10-10-23t10-23l393-393-393-393q-10-10-10-23t10-23l50-50q10-10 23-10t23 10l466 466q10 10 10 23z" />
          </svg>
        </button>
      </div>
      <div className="items-center gap-3 mt-4 md:mt-0">
        <span className="text-[#0006] text-sm">
          page {currentPage} of {totalPages}
        </span>
      </div>
    </div>
  );
}

export default ArchiveSection;
