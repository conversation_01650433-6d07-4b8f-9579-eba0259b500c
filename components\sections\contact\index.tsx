import dynamic from "next/dynamic";
import { SectionsProps, SocialLink, Form } from "../../../types";
import * as ContactVariant from "@stackshift-ui/contact";
import { PortableTextBlock } from "@sanity/types";

const Variants = {
  variant_a: ContactVariant.Contact_A,
  variant_b: ContactVariant.Contact_B,
  variant_c: dynamic(() => import("./variant_c")),
  variant_d: dynamic(() => import("./variant_d")),
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
  variant_i: dynamic(() => import("./variant_i")),
};

export interface ContactProps {
  title?: string;
  contactDescription?: string;
  officeInformation?: string;
  officeHours?: string;
  contactEmail?: string;
  contactNumber?: string;
  contactFaxNumber?: string;
  socialLinks?: SocialLink[];
  form?: Form;
  block?: any;
  arrayOfTitleAndDescription?: any;
  firstColumn?: PortableTextBlock[];
  hasCalculator?: boolean;
}

const displayName = "Contact";

export const Contact: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    title: data?.variants?.title ?? undefined,
    contactDescription: data?.variants?.contactDescription ?? undefined,
    officeInformation: data?.variants?.officeInformation ?? undefined,
    officeHours: data?.variants?.officeHours ?? undefined,
    contactEmail: data?.variants?.contactEmail ?? undefined,
    contactNumber: data?.variants?.contactNumber ?? undefined,
    contactFaxNumber: data?.variants?.contactFaxNumber ?? undefined,
    socialLinks: data?.variants?.socialLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
    block: data?.variants?.block ?? undefined,
    arrayOfTitleAndDescription:
      data?.variants?.arrayOfTitleAndDescription ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    hasCalculator: data?.variants?.hasCalculator ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Contact.displayName = displayName;
