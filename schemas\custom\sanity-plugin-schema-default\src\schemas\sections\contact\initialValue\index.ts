import {
    defaultSocialLinks,
    defaultForm,
    defaultBlockContent,
  } from "@webriq-pagebuilder/sanity-plugin-schema-default"
  
  export default {
    title: "Contact",
    contactDescription: "Got any question? Let's talk about it.",
    officeInformation: "359 Hidden Valley Road, NY",
    contactEmail: "<EMAIL>",
    contactNumber: "+48 698 033 101",
    socialLinks: defaultSocialLinks([
      {
        socialMedia: "facebook",
        socialMediaLink: "https://www.facebook.com/",
      },
      {
        socialMedia: "twitter",
        socialMediaLink: "https://www.twitter.com/",
      },
      {
        socialMedia: "instagram",
        socialMediaLink: "https://www.instagram.com/",
      },
    ]),
    form: defaultForm(
      "",
      [
        {
          name: "Subject",
          placeholder: "Subject",
          type: "inputText",
        },
        {
          name: "Name",
          placeholder: "Name",
          type: "inputText",
        },
        {
          name: "<EMAIL>",
          placeholder: "<EMAIL>",
          type: "inputEmail",
        },
        {
          name: "Message...",
          placeholder: "Message...",
          type: "textarea",
        },
        {
          name: "Add file",
          type: "inputFile",
        },
      ],
      "",
      "Get Started"
    ),
    block: defaultBlockContent([
      {
        // default - single children array with one element
        // add more elements when markDefs (link) or marks/decorators (i.e. strong, em/italic, underline) is applied to text element
        children: [
          {
            text: "I agree to terms and conditions.",
            marks: [], // leave empty if no marks/decorators (i.e. strong, em, underline) are applied to text element, else add marks/decorators strings here
          },
        ],
      },
    ]),
  }
  