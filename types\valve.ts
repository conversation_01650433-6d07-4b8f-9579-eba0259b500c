export interface ValveProduct {
  text: string;
  link: string;
  image: string;
}

export interface ValveData {
  formType: 'form1' | 'form2';
  units: 'inch' | 'metric';
  // Upper calculator fields
  jackType?: 'direct' | 'roped';
  numJack?: number;
  jackDiameter?: number;
  carSpeed?: number;
  emptyWeight?: number;
  emptyWeightUnits?: 'PSI' | 'lbs.' | 'kg' | 'kg/cm2';
  capacity?: number;
  // Lower calculator fields
  emptyStaticPressure?: number;
  loadedCarPressure?: number;
  ratedFlow?: number;
  // Common fields
  downSpeedRegulation: boolean;
  // Calculation results
  sngPisArea?: number;
  sngGPM?: number;
  sngLPM?: number;
  sngOutMinPSI?: number;
  sngOutMaxPSI?: number;
  sngInMinP?: number;
  sngInMaxP?: number;
  // Valve selection fields
  valveType?: number;
  osvValve?: string;
  noteText?: string;
  // GPM calculation fields
  S2GPM?: number;
  E1GPM?: number;
  E2GPM?: number;
  STDGPM?: number;
  UC1AGPM?: number;
  HDSTDGPM?: number;
  // Product information
  products?: ValveProduct[];
}

export interface FlashMessage {
  message: string;
  type?: 'error' | 'warning' | 'success';
} 