import React, { useRef, useState } from "react";
import Link from "next/link";
import { urlFor } from "lib/sanity";
import { ConditionalLink, logoLink } from "helper";
import * as Accordion from "@radix-ui/react-accordion";
import styles from "/styles/components/navigation-f.module.css";
import bannerStyles from "/styles/components/navigation-banner.module.css";
import {
  IoCall,
  IoLanguage,
  IoSearch,
  IoCart,
  IoPerson,
} from "react-icons/io5";
import {
  useGoogleTranslate,
  createManualTranslator,
} from "../../language-translate/LanguageOptions";
import { Container } from "@stackshift-ui/container";
import useAuthStore from "@/stores/auth-store";
import { NavUser } from "@/components/features/store/nav-user";
import { CartContent } from "@/components/features/store/customers/cart";

function VariantF({ links, primaryButton, secondaryButton, logo }) {
  const [menu, setMenu] = React.useState(false);
  const [mobileTranslatorReady, setMobileTranslatorReady] = useState(false);

  // Create a ref for the mobile translator container
  const mobileTranslatorRef = useRef<HTMLDivElement | null>(null);

  // Initialize Google Translate
  useGoogleTranslate(mobileTranslatorRef, setMobileTranslatorReady);

  const showMenu = () => {
    setMenu((prevState) => {
      if (!prevState) {
        // When opening the menu, create the manual translator
        setTimeout(() => createManualTranslator(mobileTranslatorRef), 100);
      }
      return !prevState;
    });
  };

  const InternalLink = ({ link, styling }) => {
    return (
      <Link
        href={`${
          link.linkInternal === "Home" || link.linkInternal === "home"
            ? "/"
            : `/${!link.linkInternal ? "page-not-found" : link.linkInternal}`
        }`}
        legacyBehavior
      >
        <a
          aria-label={`Navigation ${
            link?.label ?? "Menu"
          } links which directs to ${
            link?.linkInternal === undefined
              ? "page-not-found"
              : link?.linkInternal
          }`}
          className={styling}
          target={link?.linkTarget}
          rel={
            link?.linkTarget === "_blank" ? "noopener noreferrer" : undefined
          }
        >
          {link?.label}
        </a>
      </Link>
    );
  };

  const ExternalLink = ({ link, styling }) => {
    return (
      <a
        aria-label={`Navigation ${
          link?.label ?? "Menu"
        } links which directs to ${
          link?.externalLink === undefined
            ? "link-not-found"
            : link?.externalLink
        }`}
        className={styling}
        target={link?.linkTarget}
        href={`${
          !link?.externalLink && !link?.linkExternal
            ? "link-not-found"
            : link?.externalLink || link?.linkExternal
        }`}
        rel={link?.linkTarget === "_blank" ? "noopener noreferrer" : undefined}
      >
        {link?.label}
      </a>
    );
  };

  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const userData = useAuthStore((state) => state.data);

  return (
    <>
      {/* Top Banner - Not sticky */}
      <div className={`${bannerStyles.topBanner} py-3 relative`}>
        <Container maxWidth={1840} className="w-full  mx-auto">
          <div className="flex justify-between items-center">
            {/* Left side - Language Selector */}
            <div className="flex items-center gap-x-4">
              {/* Language Selector - Desktop Only */}
              <div className="hidden xs:flex items-center">
                <div lang="en" className="relative notranslate z-50">
                  <div id="google_translate_element" className=""></div>
                </div>
              </div>
              <div className="xs:hidden flex gap-4">
                {/* Search and Cart Icons */}
                <button
                  aria-label="Search"
                  className="text-white hover:text-white/80"
                >
                  <IoSearch size={22} />
                </button>
              </div>
            </div>

            {/* Right side - Sign In/Up and Profile */}
            <div className="flex items-center space-x-4">
              <div className="hidden xs:flex space-x-2"></div>
              {/* <Link
              href="/cart"
              aria-label="Shopping Cart"
              className="text-white hover:text-white/80 xs:hidden block"
            >
              <div className="relative">
                <IoCart size={22} />
                <span className="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  0
                </span>
              </div>
            </Link> */}
            </div>
          </div>
        </Container>
      </div>

      {/* Main Navigation - Sticky */}
      <div className="sticky top-0 w-full left-0 bg-white py-[5px] xs:px-[10px] shadow-md z-40">
        <Container maxWidth={1840} className="w-full  mx-auto">
          <nav className="flex items-center py-[10px] gap-4">
            {/* Logo - Left */}
            {logo?.image && (
              <div className="relative z-10 flex-shrink-0 max-w-[180px] sm:max-w-[220px] lg:max-w-[200px] xl:max-w-[280px]">
                <Link href={logoLink(logo)} legacyBehavior>
                  <a
                    aria-label={`Go to ${
                      logoLink(logo) === "/" ? "home page" : logoLink(logo)
                    }`}
                    className="text-3xl font-bold flex items-center justify-center"
                  >
                    <img
                      className="w-full h-auto"
                      style={{ transition: "width 1s" }}
                      src={logo?.image}
                      alt={logo?.alt ?? "navigation-logo"}
                    />
                  </a>
                </Link>
              </div>
            )}

            {/* Navigation Links - Center */}
            <div className="hidden lg:flex items-center justify-center gap-4 xl:gap-8 flex-grow">
              <div className="flex items-center gap-6 xl:gap-8">
                {links?.map((link, index) => (
                  <React.Fragment key={link?._key ?? `link-${index}`}>
                    <div className="group relative">
                      <div className="flex items-center space-x-[10px] relative">
                        {!link?.linkInternal && !link?.linkExternal ? (
                          <span className="font-futura font-semibold tracking-tight text-black py-[13px] text-base relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-primary after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0 cursor-default">
                            {link?.label}
                          </span>
                        ) : (
                          <div className="relative py-[13px] after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-primary after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0">
                            {link?.type === "linkInternal" ? (
                              <InternalLink
                                {...{
                                  link,
                                  styling:
                                    "font-futura font-semibold text-black tracking-tight text-base w-full",
                                }}
                              />
                            ) : (
                              <ExternalLink
                                {...{
                                  link,
                                  styling:
                                    "font-futura font-semibold text-black tracking-tight text-base w-full",
                                }}
                              />
                            )}
                          </div>
                        )}

                        {link?.routeType === "multipleRoute" && (
                          <>
                            {link?.multipleRoutes && (
                              <>
                                {/* Invisible bridge to maintain hover state */}
                                <div className="absolute w-full h-[35%] bottom-0 left-0 translate-y-full z-40"></div>

                                <div
                                  className={`absolute top-[135%] bg-white p-2 opacity-0 group-hover:opacity-100 group-hover:visible invisible transition-all duration-200 w-[270px] flex flex-col shadow-[0_0_10px_rgba(0,0,0,0.2)] z-50 ${
                                    index === links.length - 1
                                      ? "right-0"
                                      : "-left-2.5"
                                  }`}
                                >
                                  {link?.multipleRoutes?.map((mpr, idx) => (
                                    <div
                                      key={mpr?._key ?? `mpr-${idx}`}
                                      className="group/inner"
                                    >
                                      <div className="relative flex justify-between items-center hover:bg-primary/10 hover:text-primary transition-all duration-200">
                                        {!mpr?.linkInternal &&
                                        !mpr?.linkExternal ? (
                                          <span className="w-full font-medium text-sm p-3 cursor-default">
                                            {mpr?.label}
                                          </span>
                                        ) : mpr?.type === "linkInternal" ? (
                                          <InternalLink
                                            {...{
                                              link: mpr,
                                              styling:
                                                "w-full font-medium text-sm p-3",
                                            }}
                                          />
                                        ) : (
                                          <ExternalLink
                                            {...{
                                              link: mpr,
                                              styling:
                                                "w-full font-medium text-sm p-3",
                                            }}
                                          />
                                        )}
                                        {mpr?.routeType === "multipleRoute" && (
                                          <svg
                                            width="20px"
                                            height="20px"
                                            fill="currentColor"
                                            className="-rotate-90 ml-auto flex-none"
                                            viewBox="0 0 320 512"
                                            xmlns="http://www.w3.org/2000/svg"
                                          >
                                            <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                          </svg>
                                        )}
                                        {mpr?.routeType === "multipleRoute" &&
                                          mpr?.multipleInnerRoutes && (
                                            <div className="absolute top-0 left-[106%] bg-white opacity-0 group-hover/inner:opacity-100 group-hover/inner:visible invisible transition-all duration-200 w-[280px] flex flex-col shadow-lg p-2 z-50">
                                              {mpr?.multipleInnerRoutes?.map(
                                                (innerRoute, idx) => (
                                                  <div
                                                    key={
                                                      innerRoute?._key ??
                                                      `inner-${idx}`
                                                    }
                                                    className="hover:bg-primary/10 transition-all duration-200 text-black hover:text-primary"
                                                  >
                                                    {innerRoute?.type ===
                                                    "linkInternal" ? (
                                                      <InternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-medium p-3 text-sm w-full block",
                                                        }}
                                                      />
                                                    ) : (
                                                      <ExternalLink
                                                        {...{
                                                          link: innerRoute,
                                                          styling:
                                                            "font-futura font-medium p-3 text-sm w-full block",
                                                        }}
                                                      />
                                                    )}
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </React.Fragment>
                ))}
              </div>
            </div>

            {/* Right Side - CTA Button, Search and Cart */}
            <div className="flex items-center gap-4 ml-auto">
              {primaryButton?.label && (
                <div className="hidden lg:inline-flex flex-shrink-0">
                  <ConditionalLink
                    link={primaryButton}
                    ariaLabel={primaryButton?.label}
                    target={primaryButton?.linkTarget}
                    className="btn-primary text-black !h-auto whitespace-nowrap"
                  >
                    {primaryButton?.label}
                  </ConditionalLink>
                </div>
              )}
              <div className="hidden xs:flex gap-4">
                {/* Search and Cart Icons */}
                <button
                  aria-label="Search"
                  className="text-gray-700 hover:text-primary"
                >
                  <IoSearch size={22} />
                </button>
                {isAuthenticated ? (
                  <div className="flex h-16 items-center justify-between w-fit place-self-center">
                    {/* User and cart */}
                    <div className="flex items-center space-x-4">
                      <CartContent />
                      <div className="hidden md:block">
                        <NavUser
                          user={{
                            name: userData?.email?.split("@")[0] || "Customer",
                            email: userData?.email || "",
                            avatar: "",
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <Link href="/log-in" className="text-sm hover:underline">
                      Sign in
                    </Link>
                    <span>/</span>
                    <Link href="/sign-up" className="text-sm hover:underline">
                      Sign up
                    </Link>
                  </>
                )}
                {/* <Link
                  href="/cart"
                  aria-label="Shopping Cart"
                  className="text-gray-700 hover:text-primary"
                >
                  <div className="relative">
                    <IoCart size={22} />
                    <span className="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      0
                    </span>
                  </div>
                </Link> */}
              </div>

              {/* Mobile Translator (hidden) */}
              {mobileTranslatorReady && (
                <div ref={mobileTranslatorRef} className="w-0 mb-4"></div>
              )}

              {/* Mobile Menu Button */}
              <div className="inline-flex lg:hidden">
                <button
                  aria-label="Menu"
                  className="text-black"
                  onClick={showMenu}
                >
                  {menu ? (
                    <svg
                      width="22"
                      height="22"
                      fill="currentColor"
                      aria-hidden="true"
                      viewBox="0 0 1000 1000"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z" />
                    </svg>
                  ) : (
                    <svg
                      width="22"
                      height="22"
                      fill="currentColor"
                      viewBox="0 0 1000 1000"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M104 333H896C929 333 958 304 958 271S929 208 896 208H104C71 208 42 237 42 271S71 333 104 333ZM104 583H896C929 583 958 554 958 521S929 458 896 458H104C71 458 42 487 42 521S71 583 104 583ZM104 833H896C929 833 958 804 958 771S929 708 896 708H104C71 708 42 737 42 771S71 833 104 833Z" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </nav>
        </Container>
      </div>

      {/* Mobile Navigation */}
      {menu && (
        <div className={`${menu ? "nav-open" : "nav-close"} relative z-50`}>
          <div
            className="fixed inset-0 bg-gray-800 opacity-25 navbar-backdrop"
            onClick={showMenu}
          ></div>
          <nav className="sidenav fixed top-0 bottom-0 left-0 flex flex-col w-5/6 max-w-sm px-6 py-6 overflow-y-auto bg-primary border-r border-r-white/50">
            {logo?.image && (
              <div className="relative z-10 flex-shrink-0 max-w-[180px] sm:max-w-[220px] md:max-w-[280px]">
                <Link href={logoLink(logo)} legacyBehavior>
                  <a
                    aria-label={`Go to ${
                      logoLink(logo) === "/" ? "home page" : logoLink(logo)
                    }`}
                    className="text-3xl font-bold flex items-center justify-center mb-10"
                  >
                    <img
                      className="w-full h-auto brightness-0 invert-[1]"
                      style={{ transition: "width 1s" }}
                      src={logo?.image}
                      alt={logo?.alt ?? "navigation-logo"}
                    />
                  </a>
                </Link>
              </div>
            )}

            {/* Mobile Language Selector */}
            <div className="flex w-full mb-6">
              <div lang="en" className="relative notranslate w-full">
                <p className="text-sm mb-1 items-center notranslate flex font-medium font-global text-white">
                  Language Selected
                </p>
                <div
                  ref={mobileTranslatorRef}
                  className="mobile-translator"
                  style={{
                    backgroundColor: "rgba(255, 255, 255, 0.2)",
                    padding: "4px",
                    borderRadius: "4px",
                  }}
                ></div>
              </div>
            </div>

            {/* Mobile Navigation Links */}
            <div className="w-full max-w-[250px]">
              <Accordion.Root type="multiple" className="flex flex-col">
                {links?.map((link, i) => (
                  <React.Fragment key={link?._key ?? `link-${i}`}>
                    {link?.routeType === "multipleRoute" ? (
                      <Accordion.Item
                        value={`mainRoute-${i}`}
                        className={`${styles["AccordionItem"]} group`}
                      >
                        <Accordion.Header
                          className={`font-futura font-thin px-3 py-1 text-xl w-full text-white transition justify-start flex relative whitespace-nowrap
                            [&[data-state=open]]:after:w-full [&[data-state=open]]:after:left-0`}
                        >
                          {!link?.linkInternal && !link?.linkExternal ? (
                            <span className="relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0 cursor-default">
                              {link?.label}
                            </span>
                          ) : link?.type === "linkInternal" ? (
                            <InternalLink
                              {...{
                                link,
                                styling:
                                  "relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0",
                              }}
                            />
                          ) : (
                            <ExternalLink
                              {...{
                                link,
                                styling:
                                  "relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0",
                              }}
                            />
                          )}
                          <Accordion.Trigger
                            className={styles["AccordionTrigger"]}
                          >
                            <svg
                              width="20px"
                              height="20px"
                              fill="currentColor"
                              className={`${styles["AccordionChevron"]} transition-transform duration-300`}
                              viewBox="0 0 320 512"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                            </svg>
                          </Accordion.Trigger>
                        </Accordion.Header>
                        <Accordion.Content
                          className={`${styles["AccordionContent"]} bg-white/10 rounded-lg mt-2`}
                        >
                          <Accordion.Root
                            type="multiple"
                            className="flex flex-col"
                          >
                            {link?.multipleRoutes?.map((mpr, ii) => (
                              <>
                                {mpr?.routeType === "multipleRoute" ? (
                                  <Accordion.Item
                                    value={`innerRoute-${ii}`}
                                    className={styles["AccordionItem"]}
                                  >
                                    <Accordion.Header className="font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition justify-start flex">
                                      {!mpr?.linkInternal &&
                                      !mpr?.linkExternal ? (
                                        <span className="cursor-default">
                                          {mpr?.label}
                                        </span>
                                      ) : mpr?.type === "linkInternal" ? (
                                        <InternalLink
                                          {...{
                                            link: mpr,
                                            styling: "",
                                          }}
                                        />
                                      ) : (
                                        <ExternalLink
                                          {...{
                                            link: mpr,
                                            styling: "",
                                          }}
                                        />
                                      )}
                                      <Accordion.Trigger
                                        className={styles["AccordionTrigger"]}
                                      >
                                        <svg
                                          width="20px"
                                          height="20px"
                                          fill="currentColor"
                                          className={`${styles["AccordionChevron"]}`}
                                          viewBox="0 0 320 512"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path d="M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z" />
                                        </svg>
                                      </Accordion.Trigger>
                                    </Accordion.Header>
                                    <Accordion.Content
                                      className={styles["AccordionContent"]}
                                    >
                                      <div className="flex flex-col">
                                        {mpr?.multipleInnerRoutes?.map(
                                          (innerRoute, iii) => (
                                            <>
                                              {!innerRoute?.linkInternal &&
                                              !innerRoute?.linkExternal ? (
                                                <span className="font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition cursor-default">
                                                  {innerRoute?.label}
                                                </span>
                                              ) : innerRoute?.type ===
                                                "linkInternal" ? (
                                                <InternalLink
                                                  {...{
                                                    link: innerRoute,
                                                    styling:
                                                      "font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition",
                                                  }}
                                                />
                                              ) : (
                                                <ExternalLink
                                                  {...{
                                                    link: innerRoute,
                                                    styling:
                                                      "font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition",
                                                  }}
                                                />
                                              )}
                                            </>
                                          )
                                        )}
                                      </div>
                                    </Accordion.Content>
                                  </Accordion.Item>
                                ) : (
                                  <>
                                    {!mpr?.linkInternal &&
                                    !mpr?.linkExternal ? (
                                      <span className="font-futura font-lg px-3 py-1 text-xl w-full text-white hover:text-white/80 hover:bg-brand transition cursor-default">
                                        {mpr?.label}
                                      </span>
                                    ) : (
                                      <>
                                        {mpr?.type === "linkInternal" ? (
                                          <InternalLink
                                            {...{
                                              link: mpr,
                                              styling:
                                                "font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition",
                                            }}
                                          />
                                        ) : (
                                          <ExternalLink
                                            {...{
                                              link: mpr,
                                              styling:
                                                "font-futura font-thin px-3 py-1 text-lg w-full text-white hover:text-white/80 hover:bg-brand transition",
                                            }}
                                          />
                                        )}
                                      </>
                                    )}
                                  </>
                                )}
                              </>
                            ))}
                          </Accordion.Root>
                        </Accordion.Content>
                      </Accordion.Item>
                    ) : (
                      <div className="group">
                        {!link?.linkInternal && !link?.linkExternal ? (
                          <span className="font-futura font-thin px-3 py-1 text-xl w-full text-white block relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0">
                            {link?.label}
                          </span>
                        ) : (
                          <>
                            {link?.type === "linkInternal" ? (
                              <InternalLink
                                {...{
                                  link,
                                  styling:
                                    "font-futura font-thin px-3 py-1 text-xl w-full text-white block relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0",
                                }}
                              />
                            ) : (
                              <ExternalLink
                                {...{
                                  link,
                                  styling:
                                    "font-futura font-thin px-3 py-1 text-xl w-full text-white block relative after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:w-0 after:h-[2px] after:bg-white after:transition-all after:duration-300 group-hover:after:w-full group-hover:after:left-0",
                                }}
                              />
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </Accordion.Root>
            </div>

            {/* Mobile Sign In/Up Links */}
            <div className="mt-6 pt-6 border-t border-white/20">
              <div className="flex flex-col space-y-3">
                <Link
                  href="/log-in"
                  className="text-white hover:text-white/80 font-thin text-lg"
                >
                  Sign In
                </Link>
                <Link
                  href="/sign-up"
                  className="text-white hover:text-white/80 font-thin text-lg"
                >
                  Sign Up
                </Link>
                <Link
                  href="/profile"
                  className="text-white hover:text-white/80 font-thin text-lg"
                >
                  My Profile
                </Link>
              </div>
            </div>
          </nav>
        </div>
      )}
    </>
  );
}

// Global declaration
declare global {
  interface Window {
    googleTranslateElementInit: any;
    google: any;
  }
}

export default React.memo(VariantF);
