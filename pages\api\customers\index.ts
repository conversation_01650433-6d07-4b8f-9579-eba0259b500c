import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Customer, Group } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: checkAdmin(getCustomersHandler),
  POST: createCustomerHandler,
});

export interface CreateCustomerResponse {
  error?: string;
  customer?: Customer;
}

const createCustomerSchema = z
  .object({
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
    confirm_password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters" }),
  })
  .refine(({ password, confirm_password }) => password === confirm_password, {
    message: "Passwords do not match",
    path: ["confirm_password"],
  });

async function createCustomerHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateCustomerResponse>
) {}

export interface GetCustomersResponse {
  error?: string;
  customers?: Partial<CustomerWithGroups>[];
  total?: number;
  totalPages?: number;
}

export interface CustomerWithGroups extends Customer {
  group?: Partial<Group>;
}

async function getCustomersHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetCustomersResponse>
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, count, error } = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select(
      "*, catgegories:customer_categories(*, category_data:categories(*)), group:customer_groups(*)",
      { count: "exact" }
    )
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  const total = count ?? 0;
  const totalPages = Math.ceil(total / limit);

  const orderByUpdatedAtDesc =
    data?.sort((a, b) => {
      const aDate = new Date(a.updated_at || Date.now());
      const bDate = new Date(b.updated_at || Date.now());
      return bDate.getTime() - aDate.getTime();
    }) || [];

  return res.status(200).json({
    customers: orderByUpdatedAtDesc as CustomerWithGroups[],
    total,
    totalPages,
  });
}
