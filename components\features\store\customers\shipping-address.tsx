import { SvgSpinners90Ring } from "@/components/common/icons";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem } from "@/components/ui/form";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { CheckoutFormValues } from "@/pages/store/checkout";
import {
  useDeleteShippingAddressMutation,
  useGetShippingAddressesQuery,
  useUpdateShippingAddressMutation,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { ShippingAddress } from "@/supabase/types";
import { <PERSON><PERSON><PERSON>, <PERSON>, Trash2 } from "lucide-react";
import { useMemo, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { AddShippingAddressDialog } from "./add-shipping-address";
import { EditShippingAddressDialog } from "./edit-shipping-address";

interface ShippingAddressCardProps {
  address: ShippingAddress;
  onClick: () => void;
  isSelected: boolean;
}

export function ShippingAddressesSkeleton() {
  return (
    <Card className="w-[240px] h-[240px]">
      <CardHeader>
        <Skeleton className="w-full h-full object-cover rounded-t-lg" />
      </CardHeader>
      <CardContent>
        <div>
          <div className="space-y-1">
            <Skeleton className="w-2/3 h-4" />
            <Skeleton className="w-1/3 h-4" />
          </div>
          <div className="space-y-1">
            <Skeleton className="w-2/3 h-4" />
            <Skeleton className="w-1/3 h-4" />
            <Skeleton className="w-1/3 h-4" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ShippingAddresses({
  checkoutForm,
}: {
  checkoutForm: UseFormReturn<CheckoutFormValues>;
}) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const [selectedAddressId, setSelectedAddressId] = useState<
    string | undefined
  >(undefined);
  const shippingAddresses = useGetShippingAddressesQuery(userId);

  const sortByDefaultAddress = useMemo(
    () =>
      shippingAddresses?.data?.sort((a, b) => {
        if (a.default) return -1;
        if (b.default) return 1;
        return 0;
      }),
    [shippingAddresses.data]
  );

  return (
    <section>
      {shippingAddresses.isLoading ? (
        <div>
          <ShippingAddressesSkeleton />
        </div>
      ) : (
        <div className="w-full h-full flex flex-col items-start gap-2">
          {shippingAddresses.data && shippingAddresses.data?.length === 0 && (
            <div>No Shipping Addresses.</div>
          )}
          <div className="relative w-full h-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {shippingAddresses.data &&
              shippingAddresses.data?.length > 0 &&
              sortByDefaultAddress?.map((address, index) => (
                <FormField
                  key={`shipping-address-${address.id}-${index}`}
                  control={checkoutForm.control}
                  name="shippingAddressId"
                  render={({ field }) => {
                    const isSelected =
                      address.id === selectedAddressId ||
                      (selectedAddressId === undefined && address.default);

                    return (
                      <FormItem>
                        <FormControl>
                          <ShippingAddressCard
                            address={address}
                            isSelected={isSelected ?? false}
                            onClick={() => {
                              if (address.id) {
                                setSelectedAddressId(address.id);
                                field.onChange(address.id);
                              }
                            }}
                          />
                        </FormControl>
                      </FormItem>
                    );
                  }}
                />
              ))}
            <AddShippingAddressDialog />
          </div>
        </div>
      )}
    </section>
  );
}

export function ShippingAddressCard({
  address,
  onClick,
  isSelected,
}: ShippingAddressCardProps) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const updateShippingAddressMutation = useUpdateShippingAddressMutation(
    userId,
    address?.id?.toString() ?? ""
  );
  const deleteShippingAddressMutation = useDeleteShippingAddressMutation(
    userId,
    address?.id?.toString() ?? ""
  );

  const setShippingAddressAsDefault = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the card click handler from firing
    updateShippingAddressMutation
      .mutateAsync({
        state: address.state ?? "",
        address: address.address ?? "",
        address_type: address.address_type ?? "commercial",
        city: address.city ?? "",
        country: address.country ?? "",
        zip_code: address.zip_code ?? "",
        default: true,
        address_2: address.address_2 ?? undefined,
        option_name: address.option_name ?? undefined,
        contact_name: address.contact_name ?? undefined,
        contact_email: address.contact_email ?? undefined,
        contact_number: address.contact_number ?? undefined,
      })
      .then(() => {
        toast({
          title: "Success",
          description: "Shipping address set as default",
          duration: 3000,
          variant: "success",
        });
      })
      .catch((e) => {
        console.log(e);
      });
  };

  const deleteShippingAddress = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the card click handler from firing
    deleteShippingAddressMutation
      .mutateAsync()
      .then(() => {
        toast({
          title: "Success",
          description: "Shipping address deleted",
          duration: 3000,
          variant: "success",
        });
      })
      .catch((e) => {
        console.log(e);
      });
  };

  const openEditDialog = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the card click handler from firing
    setIsEditDialogOpen(true);
  };

  return (
    <div className="relative group">
      <Card
        key={address.id}
        className={cn(
          "relative w-[240px] h-[240px] flex flex-col cursor-pointer",
          isSelected ? "border-primary shadow-lg" : "border-gray-300"
        )}
        onClick={onClick}
      >
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute top-2 right-2">
                <Button
                  variant="ghost"
                  size="icon"
                  disabled={
                    isSelected || updateShippingAddressMutation.isPending
                  }
                  className="hover:bg-transparent"
                  onClick={setShippingAddressAsDefault}
                  type="button"
                >
                  {updateShippingAddressMutation.isPending ? (
                    <SvgSpinners90Ring />
                  ) : (
                    <Star
                      size={16}
                      className={cn(
                        isSelected
                          ? "text-yellow-500 fill-yellow-500"
                          : "text-gray-500"
                      )}
                    />
                  )}
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              {address.default ? <p>Default</p> : <p>Set as Default</p>}
            </TooltipContent>
          </Tooltip>

          {/* Edit Button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute top-2 left-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="w-8 h-8 rounded-full bg-white shadow-sm hover:bg-gray-100"
                  onClick={openEditDialog}
                  type="button"
                >
                  <Pencil
                    size={14}
                    className="text-gray-600 hover:text-primary"
                  />
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit Shipping Address</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <div className="absolute bottom-2 right-2">
                <Button
                  variant="ghost"
                  size="icon"
                  disabled={deleteShippingAddressMutation.isPending}
                  className="hover:bg-transparent"
                  onClick={deleteShippingAddress}
                  type="button"
                >
                  <Trash2
                    size={16}
                    className={cn(
                      isSelected ? "text-red-500" : "text-gray-500"
                    )}
                  />
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete Shipping Address</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <CardHeader className="flex-none p-4 pt-12 uppercase">
          <CardTitle className="text-base truncate">
            {/* {address.city}, {address.state} */}
            {address.address?.split(",")[0] ?? "Billing Address"}
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center px-4 uppercase">
          <div className="text-sm font-medium flex flex-col items-start justify-center gap-2">
            {address.contact_name ? <p>{address.contact_name}</p> : null}
            <p className="truncate">
              {address.city?.substring(0, 12)},{" "}
              {address.state?.substring(0, 12)}
            </p>
            <p>{address.country}</p>
            <p>{address.zip_code}</p>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      {isEditDialogOpen && (
        <EditShippingAddressDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          address={address}
        />
      )}
    </div>
  );
}
