import { SvgSpinners90Ring } from "@/components/common/icons";
import { Button } from "@/components/ui/shadcn-button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useResetPasswordWithTokenMutation } from "@/queries/user-queries";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Head from "next/head";
import { useNavigationQuery } from "@/queries/component-queries";
import { useFooterQuery } from "@/queries/component-queries";
import { Footer } from "@/components/sections/footer";
import { Navigation } from "@/components/sections/navigation";
import React from "react";

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type ResetPasswordForm = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const { toast } = useToast();
  const router = useRouter();
  const { token } = router.query;
  const resetPasswordMutation = useResetPasswordWithTokenMutation();
  const navigationData = useNavigationQuery();
  const footerData = useFooterQuery();

  const resetPasswordForm = useForm<ResetPasswordForm>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const resetPassword = async (data: ResetPasswordForm) => {
    if (typeof token !== "string") {
      toast({
        title: "Error",
        description: "Invalid token",
        variant: "destructive",
      });
      return;
    }

    await resetPasswordMutation
      .mutateAsync({ ...data, token })
      .then(() => {
        toast({
          title: "Password reset successful",
          description: "You can now login with your new password.",
          variant: "success",
          duration: 3000,
        });
      })
      .catch((error: any) => {
        toast({
          title: "Something went wrong, please try again later.",
          description: error.message,
          variant: "destructive",
          duration: 3000,
        });
      });
  };

  return (
    <React.Fragment>
      {navigationData.data && (
        <div className="sticky top-0 left-0 w-full h-full z-50">
          <Navigation data={navigationData.data?.data} />
        </div>
      )}
      <section className="relative w-full py-40">
        <Head>
          <title>Reset Password</title>
          <meta name="description" content="Reset your password here." />
        </Head>
        <div className="w-fit min-w-lg h-full mx-auto max-w-6xl  gap-6 border rounded-sm shadow-lg px-12 py-10">
          <div className="w-full flex flex-col items-center justify-center gap-2 pb-10">
            <h1 className="text-2xl font-medium">Reset Password</h1>
            <p className="text-base">Enter your new password below.</p>
          </div>
          <Form {...resetPasswordForm}>
            <form
              onSubmit={resetPasswordForm.handleSubmit(resetPassword)}
              className="w-full h-full flex flex-col gap-4"
            >
              <FormField
                control={resetPasswordForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel aria-required>New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Enter new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <FormField
                control={resetPasswordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel aria-required>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="Confirm new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <div className="w-full h-full flex flex-col items-end">
                <Button
                  type="submit"
                  size="lg"
                  className="w-full bg-primary"
                  disabled={resetPasswordForm.formState.isSubmitting}
                  aria-disabled={resetPasswordForm.formState.isSubmitting}
                >
                  <span>Submit</span>
                  {resetPasswordForm.formState.isSubmitting && (
                    <SvgSpinners90Ring />
                  )}
                </Button>
              </div>
            </form>
          </Form>
          <div className="flex items-center justify-center mt-4">
            <Link
              href="/log-in"
              className="text-sm text-gray-500 hover:underline"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </section>
      {footerData.data && (
        <div className="w-full h-full">
          <Footer data={footerData.data?.data} />
        </div>
      )}
    </React.Fragment>
  );
}
