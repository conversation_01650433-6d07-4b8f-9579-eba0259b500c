import { cn } from "@/lib/utils";
import { SettingsIcon, Users2, Calculator } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";

interface SettingsLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

const settingsNavItems = [
  {
    title: "Users",
    href: "/admin/settings/users",
    icon: Users2,
    description: "Manage user roles and permissions",
  },
  {
    title: "Tax Rates",
    href: "/admin/settings/tax-rates",
    icon: Calculator,
    description: "Manage Nevada tax rates",
  },
];

export function SettingsLayout({ 
  children, 
  title = "Settings", 
  description = "Manage your application settings" 
}: SettingsLayoutProps) {
  const router = useRouter();

  const isActive = (href: string) => {
    return router.pathname === href || router.pathname.startsWith(href + '/');
  };

  return (
    <div className="space-y-6">
      {/* Settings Header */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <SettingsIcon className="h-6 w-6 text-gray-600" />
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        </div>
        <p className="text-gray-600">{description}</p>
      </div>

      {/* Settings Navigation + Content Layout */}
      <div className="flex gap-8">
        {/* Main Content */}
        <main className="flex-1 min-w-0">
          {children}
        </main>
      </div>
    </div>
  );
}