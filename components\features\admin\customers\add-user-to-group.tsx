import { Badge } from "@/components/ui/badge";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    <PERSON><PERSON>Footer,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
    DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
    useCreateCustomerGroupMutation
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Group } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    Loader2,
    UserPlus
} from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export function AddUserToGroupDialog({
    user,
    groups,
}: {
    user: PublicUserWithCustomer;
    groups: Group[];
}) {
    groups = groups.filter(group => group.name !== "VISITOR");

    const [open, setOpen] = useState(false);
    const token = useAuthStore((state) => state.token);

    // Get current user group
    const firstCustomerData = user.customer_data?.[0];
    const groupName = firstCustomerData?.group_data?.data?.name;
    const currentGroupName = groupName || "None";
    const hasGroup = !!groupName;

    const formSchema = z.object({
        group_id: z.string().min(1, "Please select a group"),
    });

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            group_id: firstCustomerData?.group_data?.data?.id || "",
        },
    });

    // Reset form when dialog opens/closes
    const handleOpenChange = (newOpen: boolean) => {
        const isVisitor = firstCustomerData?.group_data?.data?.name === "VISITOR";
        const defaultGroupId = isVisitor ? groups[0]?.id : firstCustomerData?.group_data?.data?.id || "";

        if (!newOpen) {
            form.reset({ group_id: firstCustomerData?.group_data?.data?.id || "" });
        } else {
            if (isVisitor) {
                form.setValue("group_id", defaultGroupId);
            } else {
                form.setValue("group_id", firstCustomerData?.group_data?.data?.id || "");
            }
        }
        setOpen(newOpen);
    };

    const createCustomerGroupMutation = useCreateCustomerGroupMutation(token);

    const onSubmit = (data: z.infer<typeof formSchema>) => {
        if (!data.group_id) {
            toast({
                title: "Error",
                description: "Please select a group",
                variant: "destructive",
            });
            return;
        }

        const customerId = firstCustomerData?.id;
        if (!customerId) {
            toast({
                title: "Error",
                description: "Customer ID not found.",
                variant: "destructive",
            });
            return;
        }

        const values = {
            customer_ids: [customerId],
            group_id: data.group_id,
        };

        createCustomerGroupMutation.mutate(values, {
            onSuccess: () => {
                toast({
                    title: "Success",
                    description: `User group ${hasGroup ? "updated" : "added"
                        } successfully`,
                    variant: "default",
                    duration: 2000,
                });
                setOpen(false);
            },
            onError: (error) => {
                toast({
                    title: "Error",
                    description:
                        error?.message ??
                        `Failed to ${hasGroup ? "update" : "add"} user group`,
                    variant: "destructive",
                    duration: 2000,
                });
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    <span>{hasGroup ? "Edit User Group" : "Add to Group"}</span>
                </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>
                        {hasGroup ? "Edit User Group" : "Add User to Group"}
                    </DialogTitle>
                    <DialogDescription>
                        Select a group for {user.first_name} {user.last_name}.
                    </DialogDescription>
                </DialogHeader>

                {hasGroup && (
                    <div className="py-2 text-sm">
                        <p className="font-medium">
                            This user is currently in the{" "}
                            <Badge variant="outline">{currentGroupName}</Badge> group.
                        </p>
                    </div>
                )}

                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="group_id"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Group</FormLabel>
                                    <Select
                                        onValueChange={field.onChange}
                                        defaultValue={field.value}
                                        disabled={createCustomerGroupMutation.isPending}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select group" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {groups.map((group) => (
                                                <SelectItem key={group.id} value={group.id}>
                                                    {group.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button
                                variant="outline"
                                onClick={() => setOpen(false)}
                                type="button"
                                disabled={createCustomerGroupMutation.isPending}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={createCustomerGroupMutation.isPending}
                            >
                                {createCustomerGroupMutation.isPending
                                    ? hasGroup
                                        ? "Updating..."
                                        : "Adding..."
                                    : hasGroup
                                        ? "Update Group"
                                        : "Add to Group"}
                                {createCustomerGroupMutation.isPending && (
                                    <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                                )}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}