import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { contactVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantCImage from "./images/variant_c.png";
import variantDImage from "./images/variant_d.png";
import variantEImage from "./images/variant_d.png";
import variantGImage from "./images/variant_g.png";
import variantHImage from "./images/variant_h.png";
import initialValue from "./initialValue";
import { contactSchema } from "./schema";

export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows

  {
    title: "Variant C ",
    description: "A new variant for header component",
    value: "variant_c",
    image: variantCImage.src,
  },
  {
    title: "Variant D ",
    description: "A new variant for header component",
    value: "variant_d",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    description: "A new variant for header component",
    value: "variant_e",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    description: "A new variant for header component",
    value: "variant_f",
    image: variantDImage.src,
  },
  {
    title: "Variant G",
    description: "A new variant for contact component",
    value: "variant_g",
    image: variantGImage.src,
  },
  {
    title: "Variant H",
    description: "A new variant for contact component",
    value: "variant_h",
    image: variantHImage.src,
  },
  {
    title: "Variant I",
    description: "A new variant for contact component",
    value: "variant_i",
    image: variantHImage.src,
  },
];

export default rootSchema(
  "contact",
  "Contact",
  MdVerticalAlignTop,
  variantsList,
  contactSchema,
  initialValue
);
