import AdminLayout from "@/components/features/admin/layout";
import ProductGroupPricesExport from "@/components/features/admin/products/product-group-prices-export";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import {
  useBulkCreateGroupsMutation,
  useBulkUpdateProductGroupPricesMutation,
  useGetGroupsQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import {
  AlertCircle,
  Download,
  Edit3,
  Eye,
  FileText,
  Upload,
} from "lucide-react";
import Head from "next/head";
import { useCallback, useMemo, useRef, useState } from "react";

interface ParsedCSVData {
  product_code: string;
  group_name: string;
  custom_price: number;
  is_empty_price?: boolean; // Flag to indicate if this is an empty price for display purposes
}

interface TransformedData {
  product_code: string;
  group_prices: {
    group_name: string;
    custom_price: number;
  }[];
}

interface ValidationError {
  row: number;
  column: string;
  message: string;
  value: string;
}

export default function BulkPriceUpdate() {
  const permissions = useAuthStore((state) => state.permissions);
  const token = useAuthStore((state) => state.token);
  const hasUpdatePermission = checkUserHasPermission(
    permissions,
    "update:products"
  );

  const groups = useGetGroupsQuery(1, 100, token);
  const groupNames = useMemo(() => {
    if (!groups.data?.groups) return [];
    if (groups.isRefetching) return [];

    return (
      groups.data?.groups
        ?.map((group) => group.name)
        .filter((name) => name !== null) || []
    );
  }, [groups.data?.groups, groups.isRefetching]);

  const expectedPriceGroups = useMemo(() => {
    return groupNames;
  }, [groupNames]);

  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<ParsedCSVData[]>([]);
  const [editableCsvData, setEditableCsvData] = useState<ParsedCSVData[]>([]);
  const [transformedData, setTransformedData] = useState<TransformedData[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>(
    []
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [csvTextData, setCsvTextData] = useState("");
  const [showAllData, setShowAllData] = useState(false);
  const [nonExistentGroups, setNonExistentGroups] = useState<string[]>([]);
  const [showCreateGroupsDialog, setShowCreateGroupsDialog] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Convert CSV data array to text format for editing (new long format)
  const csvDataToText = useCallback((data: ParsedCSVData[]) => {
    if (data.length === 0) return "";

    const header = "Part_Nbr,PC,Qty,Price,Weight";
    const rows = data
      .map((row) => {
        // Only include non-empty prices in the text output
        if (row.is_empty_price) return null;
        return `${row.product_code},${row.group_name},,${row.custom_price},`;
      })
      .filter(Boolean);

    return [header, ...rows].join("\n");
  }, []);

  // Parse CSV text back to data array (new long format)
  const parseCSVText = useCallback((text: string): ParsedCSVData[] => {
    if (!text.trim()) return [];

    const lines = text.trim().split("\n");
    if (lines.length < 2) return [];

    const headers = lines[0].split(",").map((h) => h.trim());

    // Check for duplicate headers
    const headerCounts = new Map<string, number>();
    const duplicateHeaders: string[] = [];

    headers.forEach((header) => {
      const count = headerCounts.get(header) || 0;
      headerCounts.set(header, count + 1);
      if (count === 1) {
        duplicateHeaders.push(header);
      }
    });

    if (duplicateHeaders.length > 0) {
      toast({
        title: "Error",
        description: `Duplicate headers found: ${duplicateHeaders.join(
          ", "
        )}. Each column header must be unique.`,
        variant: "destructive",
      });
      return [];
    }

    // Validate headers for new long format: Part_Nbr,PC,Qty,Price,Weight
    const requiredHeaders = ["Part_Nbr", "PC", "Price"];
    const missingHeaders = requiredHeaders.filter(
      (header) => !headers.includes(header)
    );

    if (missingHeaders.length > 0) {
      toast({
        title: "Error",
        description: `Missing required headers: ${missingHeaders.join(
          ", "
        )}. Required format: Part_Nbr,PC,Qty,Price,Weight`,
        variant: "destructive",
      });
      return [];
    }

    const data: ParsedCSVData[] = [];
    const partNbrIndex = headers.indexOf("Part_Nbr");
    const pcIndex = headers.indexOf("PC");
    const priceIndex = headers.indexOf("Price");

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(",").map((v) => v.trim());
      if (values.length !== headers.length) continue;

      const partNbr = values[partNbrIndex];
      const pc = values[pcIndex];
      const priceValue = values[priceIndex];

      // Skip rows with empty Part_Nbr or PC
      if (!partNbr || !pc) continue;

      // Convert PC to uppercase for consistency
      const pcUpperCase = pc.toUpperCase();

      // Handle empty prices - include them for preview but mark as empty
      if (!priceValue || priceValue.trim() === "") {
        data.push({
          product_code: partNbr,
          group_name: pcUpperCase, // Use uppercase version
          custom_price: 0,
          is_empty_price: true,
        });
        continue;
      }

      // Validate non-empty price
      const custom_price = parseFloat(priceValue);
      if (!isNaN(custom_price)) {
        data.push({
          product_code: partNbr,
          group_name: pcUpperCase, // Use uppercase version
          custom_price,
          is_empty_price: false,
        });
      }
    }

    return data;
  }, []);

  // Transform data for display
  const transformData = useCallback((data: ParsedCSVData[]) => {
    // Filter out empty prices for API submission, but keep them for preview
    const validData = data.filter((item) => !item.is_empty_price);

    const grouped = validData.reduce(
      (acc, item) => {
        if (!acc[item.product_code]) {
          acc[item.product_code] = [];
        }
        acc[item.product_code].push({
          group_name: item.group_name,
          custom_price: item.custom_price,
        });
        return acc;
      },
      {} as Record<string, { group_name: string; custom_price: number }[]>
    );

    return Object.entries(grouped).map(([product_code, group_prices]) => ({
      product_code,
      group_prices,
    }));
  }, []);

  const bulkUpdateMutation = useBulkUpdateProductGroupPricesMutation(
    token || ""
  );
  const createGroupMutation = useBulkCreateGroupsMutation(token || "");

  // Function to create missing groups
  const handleCreateGroups = async () => {
    try {
      const groups = nonExistentGroups.map((name) => ({
        name,
        description: `Auto-created group from CSV import: ${name}`,
      }));

      await createGroupMutation.mutateAsync({
        groups,
      });

      toast({
        title: "Success",
        description: `Created ${nonExistentGroups.length} new price groups`,
      });

      setShowCreateGroupsDialog(false);
      setNonExistentGroups([]);
      setValidationErrors([]);

      // manually add the new groups to the expected price groups
      // since the query client is not immediately updated
      expectedPriceGroups.push(...groups.map((group) => group.name));

      // Re-parse the CSV now that groups exist
      if (csvFile) {
        parseCSV(csvFile);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create some price groups",
        variant: "destructive",
      });
      console.error("Group creation error:", error);
    }
  };

  // Function to skip invalid groups and proceed with valid data
  const handleSkipInvalidGroups = () => {
    // Filter out data with non-existent groups
    const validData = csvData.filter(
      (item) => !nonExistentGroups.includes(item.group_name)
    );
    const validEditableData = editableCsvData.filter(
      (item) => !nonExistentGroups.includes(item.group_name)
    );

    // Update state with filtered data
    setCsvData(validData);
    setEditableCsvData(validEditableData);
    setCsvTextData(csvDataToText(validData));

    // Remove validation errors for invalid groups
    const filteredErrors = validationErrors.filter(
      (error) =>
        error.column !== "PC" || !nonExistentGroups.includes(error.value)
    );
    setValidationErrors(filteredErrors);

    // Transform the valid data
    const transformed = transformData(validData);
    setTransformedData(transformed);

    // Clear invalid groups state
    setNonExistentGroups([]);
    setShowCreateGroupsDialog(false);

    toast({
      title: "Invalid groups skipped",
      description: `Proceeding with ${validData.length} valid price records`,
    });
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileSelection = (file: File) => {
    if (!file.name.toLowerCase().endsWith(".csv")) {
      toast({
        title: "Error",
        description: "Please select a CSV file",
        variant: "destructive",
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      // 5MB limit
      toast({
        title: "Error",
        description: "File size must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    setCsvFile(file);
    parseCSV(file);
  };

  const parseCSV = async (file: File) => {
    setIsProcessing(true);
    try {
      const text = await file.text();
      const lines = text
        .replace(/\r\n/g, "\n")
        .replace(/\r/g, "\n")
        .split("\n")
        .filter((line) => line.trim());

      if (lines.length < 2) {
        toast({
          title: "Error",
          description:
            "CSV file must contain at least a header row and one data row",
          variant: "destructive",
        });
        return;
      }

      const headers = lines[0]
        .split(",")
        .map((h) => h.trim().replace(/^"|"$/g, ""));

      // Check for duplicate headers
      const headerCounts = new Map<string, number>();
      const duplicateHeaders: string[] = [];

      headers.forEach((header) => {
        const count = headerCounts.get(header) || 0;
        headerCounts.set(header, count + 1);
        if (count === 1) {
          duplicateHeaders.push(header);
        }
      });

      if (duplicateHeaders.length > 0) {
        toast({
          title: "Error",
          description: `Duplicate headers found: ${duplicateHeaders.join(
            ", "
          )}. Each column header must be unique.`,
          variant: "destructive",
        });
        return;
      }

      // Validate headers for new long format: Part_Nbr,PC,Qty,Price,Weight
      const requiredHeaders = ["Part_Nbr", "PC", "Price"];
      const optionalHeaders = ["Qty", "Weight"];

      // Check for required headers
      const missingHeaders = requiredHeaders.filter(
        (header) => !headers.includes(header)
      );

      if (missingHeaders.length > 0) {
        toast({
          title: "Error",
          description: `Missing required headers: ${missingHeaders.join(
            ", "
          )}. Required format: Part_Nbr,PC,Qty,Price,Weight`,
          variant: "destructive",
        });
        return;
      }

      const partNbrIndex = headers.indexOf("Part_Nbr");
      const pcIndex = headers.indexOf("PC");
      const priceIndex = headers.indexOf("Price");

      const parsedData: ParsedCSVData[] = [];
      const errors: ValidationError[] = [];
      const invalidGroups = new Set<string>();

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i]
          .split(",")
          .map((v) => v.trim().replace(/^"|"$/g, ""));

        if (values.length !== headers.length) {
          errors.push({
            row: i + 1,
            column: "general",
            message: "Row has incorrect number of columns",
            value: lines[i],
          });
          continue;
        }

        const partNbr = values[partNbrIndex];
        const pc = values[pcIndex];
        const priceStr = values[priceIndex];

        // Validate Part_Nbr (maps to product_code)
        if (!partNbr || partNbr.trim() === "") {
          errors.push({
            row: i + 1,
            column: "Part_Nbr",
            message: "Part_Nbr is required",
            value: partNbr,
          });
          continue;
        }

        // Validate PC (maps to group_name)
        if (!pc || pc.trim() === "") {
          errors.push({
            row: i + 1,
            column: "PC",
            message: "PC (price group) is required",
            value: pc,
          });
          continue;
        }

        // Convert PC to uppercase for consistency
        const pcUpperCase = pc.trim().toUpperCase();

        // Check if PC exists in expected price groups (case-insensitive)
        if (
          !expectedPriceGroups
            .map((group) => group.toUpperCase())
            .includes(pcUpperCase)
        ) {
          invalidGroups.add(pcUpperCase);
          // Don't add to errors yet - we'll handle this separately
        }

        // Handle empty prices - include them for preview but mark as empty
        if (!priceStr || priceStr.trim() === "") {
          parsedData.push({
            product_code: partNbr.trim(),
            group_name: pcUpperCase, // Use uppercase version
            custom_price: 0, // Use 0 as placeholder for empty prices
            is_empty_price: true,
          });
          continue;
        }

        // Validate non-empty price
        const price = parseFloat(priceStr);
        if (isNaN(price) || price < 0) {
          errors.push({
            row: i + 1,
            column: "Price",
            message: "Price must be a non-negative number",
            value: priceStr,
          });
          continue;
        }

        parsedData.push({
          product_code: partNbr.trim(),
          group_name: pcUpperCase, // Use uppercase version
          custom_price: price,
          is_empty_price: false,
        });
      }

      // Handle invalid groups
      const invalidGroupsArray = Array.from(invalidGroups);
      if (invalidGroupsArray.length > 0) {
        setNonExistentGroups(invalidGroupsArray);
        setShowCreateGroupsDialog(true);

        // Add validation errors for invalid groups
        parsedData.forEach((item, index) => {
          if (invalidGroupsArray.includes(item.group_name)) {
            const originalRow =
              Math.floor(index / invalidGroupsArray.length) + 2; // Approximate row
            errors.push({
              row: originalRow,
              column: "PC",
              message: `Invalid price group. Expected one of: ${expectedPriceGroups.join(
                ", "
              )}`,
              value: item.group_name,
            });
          }
        });
      } else {
        setNonExistentGroups([]);
      }

      setCsvData(parsedData);
      setEditableCsvData(parsedData);
      setCsvTextData(csvDataToText(parsedData));
      setValidationErrors(errors);

      if (errors.length === 0) {
        const transformed = transformData(parsedData);
        setTransformedData(transformed);
        toast({
          title: "Success",
          description: `Successfully parsed ${parsedData.length} price records`,
        });
      } else {
        toast({
          title: "Error",
          description: `Found ${errors.length} validation errors`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to parse CSV file",
        variant: "destructive",
      });
      console.error("CSV parsing error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle CSV text changes in edit mode
  const handleCsvTextChange = useCallback(
    (text: string) => {
      setCsvTextData(text);

      try {
        if (text.length === 0) {
          setEditableCsvData([]);
          setTransformedData([]);
          setValidationErrors([]);
          return;
        }

        const parsedData = parseCSVText(text);
        setEditableCsvData(parsedData);

        const transformed = transformData(parsedData);
        setTransformedData(transformed);
      } catch (error) {
        console.error("Error parsing CSV text:", error);
      }
    },
    [parseCSVText, transformData]
  );

  const toggleEditMode = () => {
    if (isEditMode) {
      // Switching from edit to preview - parse current CSV text to ensure latest changes are captured
      try {
        const parsedData = parseCSVText(csvTextData);
        setEditableCsvData(parsedData);
        const transformed = transformData(parsedData);
        setTransformedData(transformed);
      } catch (error) {
        console.error(
          "Error parsing CSV text when switching to preview:",
          error
        );
        // Fall back to using existing editableCsvData
        const transformed = transformData(editableCsvData);
        setTransformedData(transformed);
      }
    } else {
      // Switching to edit mode - only initialize CSV text if it's empty
      if (!csvTextData.trim()) {
        setCsvTextData(csvDataToText(editableCsvData));
      }
    }
    setIsEditMode(!isEditMode);
  };

  const handleSubmit = async () => {
    if (validationErrors.length > 0) {
      toast({
        title: "Error",
        description: "Please fix all validation errors before submitting",
        variant: "destructive",
      });
      return;
    }

    if (transformedData.length === 0) {
      toast({
        title: "Error",
        description: "No valid data to submit",
        variant: "destructive",
      });
      return;
    }

    try {
      await bulkUpdateMutation.mutateAsync({ data: transformedData });
      toast({
        title: "Success",
        description: "Successfully updated product group prices",
      });

      // Reset form
      setCsvFile(null);
      setCsvData([]);
      setEditableCsvData([]);
      setCsvTextData("");
      setTransformedData([]);
      setValidationErrors([]);
      setShowConfirmDialog(false);
      setIsEditMode(false);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update product group prices",
        variant: "destructive",
      });
      console.error("Update error:", error);
    }
  };

  const downloadTemplate = () => {
    // Generate new long format template with multiple rows per product
    const csvContent = `Part_Nbr,PC,Qty,Price,Weight
00041,LIST,,30.0,
00041,FILCV,,32.5,
00041,CT1,,28.0,
00042,LIST,,45.0,
00042,FILCV,,47.5,
00042,CT1,,42.0,`;
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "bulk_price_update_template.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (!hasUpdatePermission) {
    return (
      <AdminLayout>
        <Head>
          <title>Bulk Price Update - Access Denied</title>
        </Head>
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Access Denied
          </h1>
          <p className="text-gray-600">
            You don't have permission to update product prices.
          </p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Bulk Price Update</title>
      </Head>

      <div className="flex flex-col gap-2">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold tracking-tight">
              Bulk Price Update
            </h1>
            <p className="text-sm text-muted-foreground">
              Upload a CSV file to update product group prices in bulk.
            </p>
          </div>
          <div className="flex items-center gap-2">
            <ProductGroupPricesExport />
            <Button onClick={downloadTemplate} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download Template
            </Button>
          </div>
        </div>

        {/* File Upload Area */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 grid-flow-row pt-10">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">1. Upload CSV File</h2>

            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? "border-primary bg-primary/5"
                  : "border-gray-300 hover:border-gray-400"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Drop your CSV file here, or click to browse
              </p>
              <p className="text-sm text-gray-500 mb-4">
                Supports CSV files up to 5MB
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={(e) =>
                  e.target.files?.[0] && handleFileSelection(e.target.files[0])
                }
                className="hidden"
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
                disabled={isProcessing}
              >
                {isProcessing ? "Processing..." : "Choose File"}
              </Button>
            </div>

            {csvFile && (
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <FileText className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  {csvFile.name} ({(csvFile.size / 1024).toFixed(1)} KB)
                </span>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">2. Required CSV Format</h2>
            <div className="bg-gray-50 border rounded-lg p-4">
              <p className="text-sm font-medium text-gray-900 mb-2">
                The CSV file must have the following format:
              </p>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  <code className="bg-gray-200 px-1 rounded">Part_Nbr</code> -
                  Product SKU/Code (required)
                </li>
                <li>
                  <code className="bg-gray-200 px-1 rounded">PC</code> - Price
                  group name (required)
                </li>
                <li>
                  <code className="bg-gray-200 px-1 rounded">Qty</code> -
                  Quantity (optional, ignored)
                </li>
                <li>
                  <code className="bg-gray-200 px-1 rounded">Price</code> -
                  Price value (required)
                </li>
                <li>
                  <code className="bg-gray-200 px-1 rounded">Weight</code> -
                  Weight (optional, ignored)
                </li>
              </ul>
              <p className="text-xs text-gray-500 mt-3">
                * Each row represents one price for one product in one price
                group.
              </p>
              <p className="text-xs text-gray-500">
                * Multiple rows per product are expected (one per price group).
              </p>
              <p className="text-xs text-gray-500">
                * Valid price groups: {expectedPriceGroups.join(", ")}
              </p>
              <p className="text-xs text-gray-500 overflow-hidden">
                * Example format: Part_Nbr,PC,Qty,Price,Weight
              </p>
            </div>
          </div>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="space-y-4">
            {/* Non-existent Groups Alert */}
            {nonExistentGroups.length > 0 && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-amber-800 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    Non-existent Price Groups ({nonExistentGroups.length})
                  </h3>
                  <Button
                    size="sm"
                    onClick={() => setShowCreateGroupsDialog(true)}
                    className="bg-amber-600 hover:bg-amber-700 text-white mr-2"
                  >
                    Create Groups
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleSkipInvalidGroups}
                  >
                    Skip Invalid
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mb-2">
                  {nonExistentGroups.map((group) => (
                    <span
                      key={group}
                      className="px-2 py-1 bg-amber-100 text-amber-800 rounded text-sm font-mono"
                    >
                      {group}
                    </span>
                  ))}
                </div>
                <p className="text-sm text-amber-700">
                  These price groups don't exist in the system. Click "Create
                  Groups" to add them.
                </p>
              </div>
            )}

            {/* Validation Errors Table */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-red-800 mb-3 flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Validation Errors ({validationErrors.length})
              </h3>
              <div className="max-h-60 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-red-200">
                      <th className="text-left p-2">Row</th>
                      <th className="text-left p-2">Column</th>
                      <th className="text-left p-2">Error</th>
                      <th className="text-left p-2">Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {validationErrors.map((error, index) => (
                      <tr key={index} className="border-b border-red-100">
                        <td className="p-2 font-mono">{error.row}</td>
                        <td className="p-2 font-mono">{error.column}</td>
                        <td className="p-2">{error.message}</td>
                        <td className="p-2 font-mono max-w-xs truncate">
                          {error.value}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Data Preview */}
        {validationErrors.length === 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-10">
            {/* CSV Data Preview */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">CSV Data Preview</h3>
                <div className="flex items-center gap-2">
                  <Button
                    variant={isEditMode ? "outline" : "default"}
                    size="sm"
                    onClick={toggleEditMode}
                    className="flex items-center gap-2"
                  >
                    {isEditMode ? (
                      <>
                        <Eye className="w-4 h-4" />
                        Preview
                      </>
                    ) : (
                      <>
                        <Edit3 className="w-4 h-4" />
                        Edit
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {isEditMode && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-sm text-blue-800">
                    <strong>Edit Mode:</strong> Edit the CSV data directly in
                    the textarea below. Changes will be reflected in the
                    preview.
                  </p>
                </div>
              )}

              {isEditMode ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    CSV Data (Edit directly):
                  </label>
                  <textarea
                    value={csvTextData}
                    onChange={(e) => handleCsvTextChange(e.target.value)}
                    className="w-full h-96 px-3 py-2 border border-gray-300 rounded-lg text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    placeholder="Part_Nbr,PC,Qty,Price,Weight"
                  />
                  <p className="text-xs text-gray-500">
                    Format: Part_Nbr,PC,Qty,Price,Weight (one row per price
                    group per product)
                  </p>
                </div>
              ) : (
                <div className="bg-gray-50 border rounded-lg p-4 max-h-96 overflow-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left p-2">Product Code</th>
                        <th className="text-left p-2">Group Name</th>
                        <th className="text-left p-2">Price</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editableCsvData.length > 0 ? (
                        editableCsvData?.map((row, index) => (
                          <tr key={index} className="border-b border-gray-100">
                            <td className="p-2 font-mono">
                              {row.product_code}
                            </td>
                            <td className="p-2">{row.group_name}</td>
                            <td className="p-2 font-mono">
                              {row.is_empty_price ? (
                                <span className="text-gray-400 italic">
                                  Empty price
                                </span>
                              ) : (
                                `$${row.custom_price.toFixed(2)}`
                              )}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={3}
                            className="p-4 text-center text-gray-500"
                          >
                            No data available. Import a CSV file or switch to
                            edit mode to add data.
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* JSON Preview */}
            <div className="space-y-4 h-fit">
              <h3 className="text-lg font-semibold">JSON Preview</h3>
              <div className="bg-gray-900 text-green-400 border rounded-lg p-4 max-h-96 overflow-auto">
                <pre className="text-xs">
                  {transformedData.length > 0
                    ? JSON.stringify(
                        {
                          data: showAllData
                            ? transformedData
                            : transformedData.slice(0, 3),
                        },
                        null,
                        2
                      )
                    : JSON.stringify(
                        {
                          data: [],
                          message:
                            "No data available. Import a CSV file or add data in edit mode to see the API format.",
                        },
                        null,
                        2
                      )}
                </pre>
                {transformedData.length > 3 && (
                  <p className="text-xs text-gray-400 mt-2 text-center">
                    Showing first 3 of {transformedData.length} products{" "}
                    <Button
                      onClick={() => setShowAllData(true)}
                      size="sm"
                      className="text-xs hover:bg-transparent outline-none border-none bg-transparent"
                    >
                      Show all
                    </Button>
                  </p>
                )}
              </div>

              {/* Submit Button */}
              {(csvData.length > 0 || editableCsvData.length > 0) &&
              validationErrors.length === 0 ? (
                <div className="flex justify-end pb-10">
                  <Button
                    onClick={() => setShowConfirmDialog(true)}
                    className="bg-primary text-white"
                    disabled={bulkUpdateMutation.isPending}
                  >
                    {bulkUpdateMutation.isPending
                      ? "Updating..."
                      : `Update ${transformedData.length} Products`}
                  </Button>
                </div>
              ) : null}
            </div>
          </div>
        )}
      </div>

      {/* Create Groups Dialog */}
      <Dialog
        open={showCreateGroupsDialog}
        onOpenChange={setShowCreateGroupsDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-amber-500" />
              Non-existent Price Groups Detected
            </DialogTitle>
            <DialogDescription>
              The following price groups don't exist in the system. Would you
              like to create them?
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <h4 className="font-medium text-amber-800 mb-2">
                Groups to create:
              </h4>
              <div className="flex flex-wrap gap-2">
                {nonExistentGroups.map((group) => (
                  <span
                    key={group}
                    className="px-2 py-1 bg-amber-100 text-amber-800 rounded text-sm font-mono"
                  >
                    {group}
                  </span>
                ))}
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-600">
              <p>
                Creating these groups will allow the CSV import to proceed. You
                can configure group details later in the Groups management
                section.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleSkipInvalidGroups}>
              Skip Invalid Groups
            </Button>
            <Button
              onClick={handleCreateGroups}
              disabled={createGroupMutation.isPending}
              className="bg-amber-600 hover:bg-amber-700 text-white"
            >
              {createGroupMutation.isPending
                ? "Creating..."
                : "Create Groups & Continue"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Bulk Price Update</DialogTitle>
            <DialogDescription>
              You are about to update prices for {transformedData.length}{" "}
              products with a total of{" "}
              {csvData.length || editableCsvData.length} price group
              assignments. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm text-yellow-800">
                <strong>Warning:</strong> This will overwrite existing price
                group assignments for the specified products.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              className="bg-primary text-white"
              disabled={bulkUpdateMutation.isPending}
            >
              {bulkUpdateMutation.isPending ? "Updating..." : "Confirm Update"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}
