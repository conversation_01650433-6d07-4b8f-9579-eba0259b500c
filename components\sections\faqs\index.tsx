import { AskedQuestion, FaqsWithCategory, SectionsProps } from "../../../types";
import dynamic from "next/dynamic";
import * as FaqsVariant from "@stackshift-ui/faqs";

const Variants = {
  variant_a: FaqsVariant.Faqs_A,
  variant_b: FaqsVariant.Faqs_B,
  variant_c: FaqsVariant.Faqs_C,
  variant_d: dynamic(() => import("./variant_d")),
  variant_e: dynamic(() => import("./variant_e")),
};

export interface FAQProps {
  subtitle?: string;
  title?: string;
  faqs?: AskedQuestion[];
  faqsWithCategories?: FaqsWithCategory[];
}

const displayName = "Faqs";

export const Faqs: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    subtitle: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    faqs: (data?.variants?.faqs || data?.variants?.askedQuestions) ?? undefined,
    faqsWithCategories: data?.variants?.faqsWithCategories ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Faqs.displayName = displayName;
