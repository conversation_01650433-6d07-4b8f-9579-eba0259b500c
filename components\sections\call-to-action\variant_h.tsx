import { PortableText } from "@portabletext/react";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";
import { MyPortableTextComponents } from "types";
import { CTAProps } from ".";
import { useValveCalculator } from "../../../hooks/useValveCalculator";
import type { ValveData, ValveProduct } from "../../../types/valve";

import Button from "components/ui/button";
import { FaArrowRightLong } from "react-icons/fa6";
import { FiCheckCircle } from "react-icons/fi";
import { useGetProductByNameQuery } from "@/queries/product-queries";
import { Product } from "@/supabase/types";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { useGetImage } from "@/queries/customer-queries";
import { CalculatorData } from "@/pages/api/orders";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return (
        <h2 className="mb-4 text-2xl md:text-2xl lg:text-[38px]   text-primary">
          {children}
        </h2>
      );
    },
    h3: ({ children, index }) => {
      return (
        <h3
          className={`${
            index !== 0 ? "pt-8" : "pt-0"
          } mb-8 text-xl md:text-2xl  text-gray-800`}
        >
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      // <li className="mb-3 leading-loose list-none text-gray-900 before:content-['✔'] before:text-primary before:mr-2">
      //   {children}
      // </li>

      <div className="flex flex-row items-start justify-start gap-4 mb-4">
        {/* <Image
          src="/assets/elements/downloads/checkmark.png"
          width={40}
          height={40}
          alt="checkmark"
        /> */}
        <div>
          <FiCheckCircle size={40} className="text-primary pt-[6px]" />
        </div>

        <li className="mb-3 leading-loose list-none text-gray-900">
          {children}
        </li>
      </div>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addTable: ({ value }) => {
      if (!value?.columns || !value?.rows) {
        console.error("Missing table data:", value);
        return null;
      }

      return (
        <div className="overflow-x-auto my-6">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {value.columns.map(
                  (column: { title: string }, index: number) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-semibold text-gray-800 uppercase tracking-wider"
                    >
                      {column.title}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {value.rows.map((row: { cells: string[] }, rowIndex: number) => (
                <tr key={rowIndex}>
                  {row.cells.map((cell: string, cellIndex: number) => (
                    <td
                      key={cellIndex}
                      className="px-6 py-4 text-xs md:text-sm text-gray-500"
                      dangerouslySetInnerHTML={{
                        __html: cell
                          .replace(
                            /\n\n/g,
                            "<hr class='my-2 border-gray-300' />"
                          )
                          .replace(/\n/g, "<br />"),
                      }}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    },
  },
};

export default function CallToAction_H({
  title,
  plainText,
  firstColumn,
}: CTAProps) {
  const router = useRouter();
  const [showResults, setShowResults] = useState(false);
  const [upperUnits, setUpperUnits] = useState<"inch" | "metric">("inch");
  const [lowerUnits, setLowerUnits] = useState<"inch" | "metric">("inch");

  const {
    calculateValve,
    calculationResults,
    resetCalculator,
    upperFlashMessages,
    lowerFlashMessages,
    activeCalculator,
    resetFormFields,
  } = useValveCalculator();

  const handleSubmit = (formData: ValveData) => {
    const results = calculateValve(formData);
    if (results) {
      setShowResults(true);
    }
  };

  const resetResults = () => {
    setShowResults(false);
    resetCalculator();
  };

  useEffect(
    function resetResultsOnFlashMessages() {
      if (upperFlashMessages.length > 0 || lowerFlashMessages.length > 0) {
        setShowResults(false);
      }
    },
    [upperFlashMessages, lowerFlashMessages]
  );

  return (
    <Section className="pt-24 pb-32 bg-background">
      <Container maxWidth={1280}>
        <Flex className="flex-col md:flex-row " gap={12}>
          <div className="w-full md:pr-10 space-y-8">
            <div className="border-b border-gray-300">
              {title && (
                <Heading
                  type="h2"
                  className="!text-black text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
                >
                  {title}
                </Heading>
              )}

              {plainText && <Text className="mb-4 text-lg">{plainText}</Text>}
              <div className="w-10 h-2 bg-primary" />
            </div>

            <div className="prose">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          </div>

          <div className="w-full md:pl-10 md:pt-36 lg:pt-10">
            <div className="space-y-2 lg:space-y-16">
              {/* Upper Calculator */}
              <div className="p-6 bg-white shadow-md border border-gray-200">
                {upperFlashMessages.length > 0 && (
                  <div className="p-3 mb-3 border border-red-200 bg-red-50">
                    {upperFlashMessages.map((msg, i) => (
                      <p key={i} className="text-red-600">
                        {msg.message}
                      </p>
                    ))}
                  </div>
                )}

                {router.asPath === "/calculator-2" ||
                router.asPath === "/calculator" ? (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h2"
                  >
                    Upper Calculator
                  </Heading>
                ) : (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h3"
                  >
                    Upper Calculator
                  </Heading>
                )}

                <div className="mb-6">
                  <select
                    value={upperUnits}
                    onChange={(e) =>
                      setUpperUnits(e.target.value as "inch" | "metric")
                    }
                    className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
                  >
                    <option value="inch">English Units</option>
                    <option value="metric">Metric Units</option>
                  </select>
                </div>

                <UpperCalculatorForm
                  units={upperUnits}
                  onSubmit={handleSubmit}
                  onReset={resetResults}
                />
                {showResults && calculationResults && (
                  <div className="mt-8 border-t pt-6 border-gray-200">
                    {activeCalculator === "upper" &&
                      calculationResults.formType === "form1" && (
                        <ValveCalculatorResults
                          results={calculationResults}
                          units={upperUnits}
                          onReset={resetResults}
                        />
                      )}
                  </div>
                )}
              </div>

              {/* Lower Calculator */}
              <div className="p-6 bg-white shadow-md border border-gray-200">
                {lowerFlashMessages.length > 0 && (
                  <div className="p-3 mb-3 border border-red-200 bg-red-50">
                    {lowerFlashMessages.map((msg, i) => (
                      <p key={i} className="text-red-600">
                        {msg.message}
                      </p>
                    ))}
                  </div>
                )}

                {router.asPath === "/calculator-2" ||
                router.asPath === "/calculator" ? (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h2"
                  >
                    Lower Calculator
                  </Heading>
                ) : (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h3"
                  >
                    Lower Calculator
                  </Heading>
                )}

                <div className="mb-6">
                  <select
                    value={lowerUnits}
                    onChange={(e) =>
                      setLowerUnits(e.target.value as "inch" | "metric")
                    }
                    className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
                  >
                    <option value="inch">English Units</option>
                    <option value="metric">Metric Units</option>
                  </select>
                </div>

                <LowerCalculatorForm
                  units={lowerUnits}
                  onSubmit={handleSubmit}
                  onReset={resetResults}
                />

                {showResults && calculationResults && (
                  <div className="mt-8 border-t pt-6 border-gray-200">
                    {activeCalculator === "lower" &&
                      calculationResults.formType !== "form1" && (
                        <ValveCalculatorResults
                          results={calculationResults}
                          units={lowerUnits}
                          onReset={resetResults}
                        />
                      )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function UpperCalculatorForm({
  units,
  onSubmit,
  onReset,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
  onReset?: () => void;
}) {
  const formRef = useRef<HTMLFormElement>(null);
  const handleReset = () => {
    if (formRef.current) {
      formRef?.current.reset();
      onReset?.();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formRef.current) return;

    const form = formRef.current;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form1",
      units: units as "inch" | "metric",
      jackType: formData.get("jackType") as "direct" | "roped",
      numJack: Number(formData.get("numJack")),
      jackDiameter: Number(formData.get("jackDiameter")),
      carSpeed: Number(formData.get("carSpeed")),
      emptyWeight: Number(formData.get("emptyWeight")),
      emptyWeightUnits: formData.get("emptyWeightUnits") as
        | "PSI"
        | "lbs."
        | "kg"
        | "kg/cm2",
      capacity: Number(formData.get("capacity")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  // Prevent scroll from changing number input values
  const preventScrollChange = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  return (
    <form
      ref={formRef}
      id="upper-calculator-form"
      name="upper-calculator-form"
      onSubmit={handleSubmit}
      className="space-y-4"
    >
      {/* Upper calculator fields */}
      <div className="flex flex-col gap-2">
        <label htmlFor="jackType" className="text-sm font-bold">
          Jack Type
        </label>
        <select
          id="jackType"
          name="jackType"
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
        >
          <option value="">Select Jack Type</option>
          <option value="direct">Direct</option>
          <option value="roped">Roped</option>
        </select>
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="numJack" className="text-sm font-bold">
          Jack Number
        </label>
        <select
          id="numJack"
          name="numJack"
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
        >
          <option value="">Select Jack Number</option>
          <option value="1">Single Jack</option>
          <option value="2">Dual Jack</option>
        </select>
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="jackDiameter" className="text-sm font-bold">
          Piston Diameter
        </label>
        <input
          id="jackDiameter"
          type="number"
          name="jackDiameter"
          placeholder={`Piston Diameter (${
            units === "inch" ? "in" : "mm"
          }, e.g. ${units === "inch" ? "1.5 in" : "15 mm"})`}
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="carSpeed" className="text-sm font-bold">
          Car Speed
        </label>
        <input
          id="carSpeed"
          type="number"
          name="carSpeed"
          placeholder={`Car Speed (${units === "inch" ? "FPM" : "m/s"}, e.g. ${
            units === "inch" ? "100 FPM" : "0.5 m/s"
          })`}
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>
      <div className="flex flex-row justify-between items-center gap-4">
        <div className="w-full flex flex-col gap-2">
          <label htmlFor="emptyWeight" className="text-sm font-bold">
            Empty Car Weight or Static Pressure
          </label>
          <input
            id="emptyWeight"
            type="number"
            name="emptyWeight"
            placeholder={`Empty Car Weight (${
              units === "inch" ? "lbs" : "kg"
            }, e.g. ${units === "inch" ? "1000 lbs" : "500 kg"})`}
            required
            className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
            step="any"
            onWheel={preventScrollChange}
          />
        </div>
        <div className="flex flex-col gap-2">
          <label htmlFor="emptyWeightUnits" className="text-sm font-bold">
            Unit
          </label>
          <select
            id="emptyWeightUnits"
            name="emptyWeightUnits"
            className="w-32 p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary"
          >
            {units === "inch" ? (
              <>
                <option value="lbs.">lbs.</option>
                <option value="PSI">PSI</option>
              </>
            ) : (
              <>
                <option value="kg">kg</option>
                <option value="kg/cm2">kg/cm²</option>
              </>
            )}
          </select>
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="capacity" className="text-sm font-bold">
          Capacity
        </label>
        <input
          id="capacity"
          type="number"
          name="capacity"
          placeholder={`Capacity (${units === "inch" ? "lbs" : "kg"}, e.g. ${
            units === "inch" ? "1000 lbs" : "500 kg"
          })`}
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>

      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="upperDSR"
          value="true"
          className="w-4 h-4 text-primary focus:ring-1 focus:ring-primary"
        />
        <label htmlFor="upperDSR" className="text-sm font-bold">
          Down Speed Regulation
        </label>
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-start items-center w-full justify-center gap-4">
        <Button
          as="button"
          type="submit"
          variant="maxtonPrimary"
          ariaLabel="Size Valve"
          className="w-full sm:w-fit"
        >
          <span>Size Valve</span>
          <FaArrowRightLong />
        </Button>

        <Button
          type="button"
          variant="maxtonSecondary"
          onClick={handleReset}
          ariaLabel="Reset"
          className="w-full sm:w-fit justify-center"
        >
          <span>Reset</span>
          <FaArrowRightLong />
        </Button>
      </div>
    </form>
  );
}

function LowerCalculatorForm({
  units,
  onSubmit,
  onReset,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
  onReset?: () => void;
}) {
  const formRef = useRef<HTMLFormElement>(null);
  const handleReset = () => {
    if (formRef.current) {
      formRef?.current.reset();
      onReset?.();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formRef.current) return;

    const form = formRef.current;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form2",
      units: units as "inch" | "metric",
      emptyStaticPressure: Number(formData.get("emptyStaticPressure")),
      loadedCarPressure: Number(formData.get("loadedCarPressure")),
      ratedFlow: Number(formData.get("ratedFlow")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  // Prevent scroll from changing number input values
  const preventScrollChange = (e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  };

  return (
    <form
      ref={formRef}
      id="lower-calculator-form"
      name="lower-calculator-form"
      onSubmit={handleSubmit}
      className="space-y-4"
    >
      <div className="flex flex-col gap-2">
        <label htmlFor="emptyStaticPressure" className="text-sm font-bold">
          Empty Static Pressure
        </label>
        <input
          id="emptyStaticPressure"
          type="number"
          name="emptyStaticPressure"
          placeholder={`Empty Static Pressure (${
            units === "inch" ? "PSI" : "kg/cm²"
          }, e.g. ${units === "inch" ? "135 PSI" : "10 kg/cm²"})`}
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="loadedCarPressure" className="text-sm font-bold">
          Loaded Car Pressure
        </label>
        <input
          id="loadedCarPressure"
          type="number"
          name="loadedCarPressure"
          placeholder={`Loaded Car Pressure (${
            units === "inch" ? "PSI" : "kg/cm²"
          }, e.g. ${units === "inch" ? "337 PSI" : "22 kg/cm²"})`}
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="ratedFlow" className="text-sm font-bold">
          Rated Flow
        </label>
        <input
          id="ratedFlow"
          type="number"
          name="ratedFlow"
          placeholder={`Rated Flow (${units === "inch" ? "GPM" : "LPM"}, e.g. ${
            units === "inch" ? "100 GPM" : "200 LPM"
          })`}
          required
          className="w-full p-2 border bg-white focus:outline-2 focus:ring-1 focus:outline-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          step="any"
          onWheel={preventScrollChange}
        />
      </div>

      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="lowerDSR"
          value="true"
          className="w-4 h-4 text-primary focus:ring-1 focus:ring-primary"
        />
        <label htmlFor="lowerDSR" className="text-sm font-bold">
          Down Speed Regulation
        </label>
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-start items-center w-full justify-center gap-4">
        <Button
          type="submit"
          variant="maxtonPrimary"
          ariaLabel="Size Valve"
          className="w-full sm:w-fit justify-center"
        >
          <span>Size Valve</span>
          <FaArrowRightLong />
        </Button>
        <Button
          type="button"
          variant="maxtonSecondary"
          onClick={handleReset}
          ariaLabel="Reset"
          className="w-full sm:w-fit justify-center"
        >
          <span>Reset</span>
          <FaArrowRightLong />
        </Button>
      </div>
    </form>
  );
}

function ValveCalculatorResults({
  results,
  units,
  onReset,
}: {
  results: ValveData | null;
  units: "inch" | "metric";
  onReset: () => void;
}) {
  if (!results) return null;

  return (
    <Container maxWidth={1280}>
      <Heading fontSize="xl" weight="bold" className="mb-4" type="h3">
        Provided data:
      </Heading>
      <Flex direction="col" className="md:gap-2 mb-6">
        {results.formType === "form1" ? (
          <>
            <Flex>
              <Text className="!font-bold">Jack Type:</Text>
              <Text className="ml-2">
                {results.jackType === "direct" ? "Direct" : "Roped"}{" "}
                {results.numJack === 1 ? "Single" : "Dual"} Jack
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Piston Dia.:</Text>
              <Text className="ml-2">
                {results.jackDiameter} {units === "inch" ? "in." : "mm"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Car Speed:</Text>
              <Text className="ml-2">
                {results.carSpeed} {units === "inch" ? "FPM" : "m/sec"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Empty Car:</Text>
              <Text className="ml-2">
                {results.emptyWeight}
                {results.emptyWeightUnits}
              </Text>
            </Flex>
            {results.capacity && (
              <Flex>
                <Text className="!font-bold">Capacity:</Text>
                <Text className="ml-2">
                  {results.capacity} {units === "inch" ? "lbs" : "kg"}
                </Text>
              </Flex>
            )}
          </>
        ) : (
          <>
            <Flex>
              <Text className="!font-bold">Rated Flow:</Text>
              <Text className="ml-2">
                {results.ratedFlow} {units === "inch" ? "GPM" : "LPM"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Empty Static Pressure:</Text>
              <Text className="ml-2">
                {results.emptyStaticPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Loaded Static Pressure:</Text>
              <Text className="ml-2">
                {results.loadedCarPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </Text>
            </Flex>
          </>
        )}
        <Flex>
          <Text className="!font-bold">Down Speed Regulation:</Text>
          <Text className="ml-2">
            {results.downSpeedRegulation ? "Yes" : "No"}
          </Text>
        </Flex>
      </Flex>

      <Heading fontSize="xl" weight="bold" className="mb-4" type="h3">
        Results:
      </Heading>
      <Flex direction="col" className="md:gap-2 mb-6">
        <Flex>
          <Text className="!font-bold">Rated Flow:</Text>
          <Text className="ml-2 text-blue-600">{results.sngGPM} GPM</Text>
        </Flex>
        <Flex>
          <Text className="!font-bold">Empty Static Pressure:</Text>
          <Text className="ml-2 text-blue-600">{results.sngOutMinPSI} PSI</Text>
        </Flex>
        <Flex>
          <Text className="!font-bold">Loaded Car Pressure:</Text>
          <Text className="ml-2 text-blue-600">{results.sngOutMaxPSI} PSI</Text>
        </Flex>
      </Flex>

      <Flex direction="col" className="md:gap-2 mb-6">
        <Heading fontSize="xl" weight="bold" className="mb-2" type="h3">
          Valve Recommendations:
        </Heading>
        <Text as="ul" className="list-disc">
          {results.products?.map((product, index) => (
            <ProductCard
              key={`product-${index}`}
              product={product}
              results={results}
            />
          ))}
        </Text>
      </Flex>

      <Button
        onClick={onReset}
        className="mt-6 px-4 py-2"
        variant="maxtonPrimary"
        ariaLabel="Calculate another valve"
      >
        Calculate Another
      </Button>
    </Container>
  );
}

function ProductCard({
  product,
  results,
}: {
  product: ValveProduct;
  results: any;
}) {
  const { data: productData, isLoading: isProductLoading } =
    useGetProductByNameQuery(product.text.replace("OSV Configuration: ", ""));

  if (isProductLoading) {
    return <Skeleton className="w-full h-48 animate-pulse" />;
  }

  if (!productData) {
    return (
      <Text as="li" className="text-blue-600">
        {product.text}
      </Text>
    );
  }

  const calculator: CalculatorData = {
    providedData: {
      "Jack Type":
        results.jackType === "direct" ? "Direct Single Jack" : "Unknown",
      "Piston Dia.": `${results.jackDiameter} in.`,
      "Car Speed": `${results.carSpeed} FPM`,
      "Empty Car": `${results.emptyWeight} lbs`,
      Capacity: `${results.capacity} lbs`,
      "Down Speed Regulation": results.downSpeedRegulation ? "Yes" : "No",
    },
    results: {
      "Rated Flow": results.sngGPM,
      "Empty Static Pressure": results.sngOutMinPSI,
      "Loaded Car Pressure": results.sngOutMaxPSI,
    },
  };

  const query = encodeURIComponent(JSON.stringify(calculator));
  const productUrl = `/store/products/${productData.sku}?calculator=${query}`;

  return (
    <div className="w-full bg-white rounded-lg shadow-md overflow-hidden mb-4 hover:shadow-lg transition-shadow duration-300">
      <div className="flex flex-col md:flex-row">
        {/* Image section */}
        <div className="w-full md:w-1/2 p-4 flex items-center justify-center">
          {productData.image ? (
            <Link href={productUrl}>
              <ProductImage image={productData.image} alt={productData.name} />
            </Link>
          ) : (
            <div className="bg-gray-200 h-32 w-32 flex items-center justify-center">
              <span className="text-gray-400">No Image</span>
            </div>
          )}
        </div>

        {/* Content section */}
        <div className="w-full md:w-3/4 p-4">
          <Link href={productUrl}>
            <h3 className="text-lg font-bold text-primary cursor-pointer hover:underline">
              {product.text}
            </h3>
          </Link>

          <p className="text-gray-600 text-sm mt-2 line-clamp-2">
            {productData.description}
          </p>

          <Link
            href={productUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block mt-4 px-6 py-2 bg-primary text-white font-normal border-2 border-primary hover:bg-[#063a6b] transition-colors duration-300 flex justify-center items-center gap-2 text-center"
          >
            <span>Buy Online</span>
            <FaArrowRightLong />
          </Link>
        </div>
      </div>
    </div>
  );
}

function ProductImage({ image, alt }: { image: string; alt: string }) {
  const { data: productImage, isLoading: isProductImageLoading } =
    image.startsWith("http")
      ? { data: image, isLoading: false }
      : useGetImage(image);

  if (isProductImageLoading) {
    return <Skeleton className="w-full h-48 animate-pulse" />;
  }

  return (
    <img
      src={productImage}
      alt="Product Image"
      className="object-contain h-32 w-32 cursor-pointer"
    />
  );
}
