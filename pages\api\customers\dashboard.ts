import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Order, OrderItem, OrderStatus } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: getDashboardData,
  }),
);

interface RecentOrder extends Pick<Order, "id"> {
  order_items: Pick<OrderItem, "quantity">[];
  order_statuses: Pick<OrderStatus, "status">[];
  total_amount: number | null;
}

export interface GetDashboardDataResponse {
  totalProducts?: number;
  totalOrders?: number;
  recentOrders?: Partial<RecentOrder>[];
  memberSince?: string;
  error?: string;
}

async function getDashboardData(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetDashboardDataResponse>,
) {
  const user_id = req.query.id?.toString();
  const userId = req.user?.id;

  if (!userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (user_id !== userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const userData = await supabaseAdminClient
    .from("users")
    .select("id, created_at")
    .eq("id", userId);

  if (userData.error) {
    return res.status(400).json({ error: "user not found" });
  }

  const assignedCategories = await supabaseAdminClient
    .from("customer_categories")
    .select("category_id")
    .eq("user_id", userId);

  const assignedCategoriesIds =
    assignedCategories.data?.map((category) => category.category_id) || [];

  const productCategories = await supabaseAdminClient
    .from("product_categories")
    .select("product_id(id)", { count: "exact" })
    .in("category_id", assignedCategoriesIds);

  const totalProductsCount = productCategories.count || 0;

  const totalOrders = await supabaseAdminClient
    .from("orders")
    .select("id", { count: "exact" })
    .eq("user_id", userId);

  const totalOrdersCount = totalOrders.count || 0;

  const recentOrders = await supabaseAdminClient
    .from("orders")
    .select(
      "id, total_amount, order_statuses(status,created_at), order_items(id, quantity, products(price))",
    )
    .eq("user_id", userId)
    .order("created_at", { ascending: false })
    .limit(5);

  const recentOrdersData = recentOrders.data || [];

  return res.status(200).json({
    totalProducts: totalProductsCount,
    totalOrders: totalOrdersCount,
    recentOrders: recentOrdersData,
    memberSince: userData.data?.[0].created_at,
  });
}
