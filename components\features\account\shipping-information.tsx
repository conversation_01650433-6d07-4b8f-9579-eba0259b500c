import { Skeleton } from "@/components/ui/skeleton";
import { useGetShippingAddressesQuery } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { useMemo } from "react";
import { AddShippingAddressDialog } from "../store/customers/add-shipping-address";
import { ShippingAddressCard, ShippingAddressesSkeleton } from "../store/customers/shipping-address";

export function ShippingInformation() {
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;
    const shippingAddresses = useGetShippingAddressesQuery(userId);

    const sortByDefaultAddress = useMemo(() => shippingAddresses?.data?.sort((a, b) => {
        if (a.default) return -1;
        if (b.default) return 1;
        return 0;
    }), [shippingAddresses.data]);

    if (shippingAddresses.isLoading) {
        return <ShippingInformationSkeleton />
    }

    return (
        <div className="flex flex-col gap-4">
            <div>
                <h2 className="text-2xl font-bold">Shipping Addresses</h2>
                <p className="text-sm text-gray-500">
                    Manage your saved addresses.
                </p>
            </div>
            <div className="w-full h-full flex flex-wrap items-center justify-start gap-4 pt-6">
                {shippingAddresses.data
                    && shippingAddresses.data?.length > 0
                    && sortByDefaultAddress?.map((address, index) => (
                        <ShippingAddressCard
                            key={`shipping-address-${address.id}-${index}`}
                            address={address}
                            isSelected={address.default ?? false}
                            onClick={() => { }} // Empty click handler since we don't need it here
                        />
                    ))}

                <div className="w-full h-full flex items-center justify-end relative max-w-[240px] max-h-[240px]">
                    <AddShippingAddressDialog />
                </div>
            </div>
        </div>
    )
}

function ShippingInformationSkeleton() {
    return (
        <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
                <Skeleton className="w-64 h-4" />
                <Skeleton className="w-96 h-4" />
            </div>
            <div className="w-full h-full flex flex-wrap items-center justify-start gap-4 pt-6">
                <ShippingAddressesSkeleton />
            </div>
        </div>
    )
}
