import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdArtTrack } from "react-icons/md";

// Images
import variantAImage from "./images/variant_a.jpg";
import variantBImage from "./images/variant_b.jpg";
import variantCImage from "./images/variant_c.jpg";
import variantDImage from "./images/variant_d.jpg";
import variantEImage from "./images/variant_e.png";
import initialValue from "./initialValue";
import { blogSchema } from "./schema";

export const variantsList = [
  {
    title: "Variant A",
    description: "",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    value: "variant_b",
    description: "",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    value: "variant_c",
    image: variantCImage.src,
    description: "",
  },
  {
    title: "Variant D",
    description: "",
    value: "variant_d",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    description:
      "Displays all blog posts from StackShift automatically. Click on the Blog Tab to manage your blog posts.",
    value: "variant_e",
    image: variantEImage.src,
  },
];

export default rootSchema(
  "blog",
  "Blog",
  MdArtTrack,
  variantsList,
  blogSchema,
  initialValue
);
