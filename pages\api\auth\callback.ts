import { matchRoute } from "@/middlewares/match-route";
import { serialize } from "cookie";
import { NextApiRequest, NextApiResponse } from "next";
import { createSupabaseAdminClient, supabaseClient } from "supabase";
import { z } from "zod";

export default matchRoute({
    POST: handler
});

const signupCallbackSessionSchema = z.object({
    access_token: z.string().min(1),
    expires_in: z.string().min(1),
    expires_at: z.string().optional(),
    refresh_token: z.string().min(1),
    token_type: z.string().min(1),
    type: z.string().min(1)
});

export type SignUpCallbackSession = z.infer<typeof signupCallbackSessionSchema>;

async function handler(req: NextApiRequest, res: NextApiResponse) {
    const signupSession = signupCallbackSessionSchema.safeParse(req.body);

    if (!signupSession.success) {
        return res.status(400).json({
            error: signupSession.error.issues[0].message,
        });
    }

    const { data } = signupSession;

    const user = await supabaseClient.auth.getUser(data.access_token);

    if (user.error && (user.error.message.includes("invalid JWT") || user.error.message.includes("token is expired"))) {
        return res.status(403).json({
            error: "Token is expired",
        });
    }

    if (user.error) {
        return res.status(400).json({
            error: user.error.message,
        });
    }

    const { data: userData } = user;

    // check if confirmed at Date is > 5 mins
    if (!!userData.user?.email_confirmed_at) {
        const now = new Date();
        const confirmedAt = new Date(userData.user?.email_confirmed_at);

        const diff = now.getTime() - confirmedAt.getTime();
        const diffInMinutes = Math.floor(diff / 1000 / 60);

        if (diffInMinutes >= 5) {
            return res.status(400).json({
                error: "User already confirmed",
            });
        }
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    const isApproved = await supabaseAdminClient
        .schema("public")
        .from("users")
        .select("status, role")
        .eq("id", userData.user?.id)
        .single();

    if (isApproved.error) {
        return res.status(400).json({
            error: isApproved.error.message,
        });
    }

    const { status, role } = isApproved.data;

    if (status !== "approved") {
        return res.status(400).json({
            error: "Please wait for your account to be approved by the admin.",
        });
    }

    const newSessionStr = JSON.stringify({
        refresh_token: data.refresh_token,
        expires_in: data.expires_in,
        expires_at: data.expires_at,
        access_token: undefined,
        token_type: data.token_type,
    });

    const authCookie = serialize('maxton-auth-session', newSessionStr, {
        httpOnly: true,
        secure: process.env.NODE_ENV !== 'development',
        sameSite: 'strict',
        maxAge: 60 * 60 * 24 * 365,
        path: '/',
    });

    const permissions = await supabaseAdminClient
        .schema("public")
        .from("permissions")
        .select("allowed_actions")
        .eq("role", role === "manager" ? "customer" : role) // Managers have customer permissions
        .single();

    res.setHeader('Set-Cookie', authCookie);

    return res.status(200).json({
        user: userData.user,
        role,
        permissions: permissions.data?.allowed_actions || [],
        access_token: data?.access_token,
        access_token_expires_in: data?.expires_in,
        access_token_expires_at: data?.expires_at,
    });
}