import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import Image from "next/image";
import React from "react";
import { FeaturesProps } from ".";
import { FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button, Card } from "components/ui";
import { FaArrowRightLong } from "react-icons/fa6";

export default function Features_S({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
  quickForms,
}: FeaturesProps) {
  return (
    <Section className="py-24 bg-gray-100">
      <Container maxWidth={1280}>
        <Flex
          align="center"
          justify="center"
          className="w-full flex-col gap-y-24"
        >
          <div className="w-full h-full flex flex-col md:flex-row gap-y-12 md:gap-x-10 lg:gap-x-16 justify-center items-start  mb-4">
            <div className="md:w-5/12 w-full h-full flex items-start flex-col max-w-xl">
              <CaptionAndTitleSection
                caption={caption}
                title={title}
                description={description}
              />
              <div className="mt-4">
                <Buttons
                  primaryButton={primaryButton}
                  secondaryButton={secondaryButton}
                />
              </div>
            </div>

            <div className="md:w-7/12 w-full flex flex-col">
              <FeatureItems features={featuredItems} />
            </div>
          </div>

          {/* <div className="w-full flex flex-col gap-10  lg:items-start items-center">
            <div className="w-full flex flex-col items-center gap-8 md:gap-4 ">
              <div className="w-full flex flex-col lg:items-start items-center max-w-xl">
                {quickForms?.title && (
                  <Heading
                    type="h2"
                    className="!text-black text-2xl md:text-2xl text-center lg:text-3xl uppercase font-extrabold mb-4"
                  >
                    {quickForms?.title}
                  </Heading>
                )}

                {quickForms?.description && (
                  <Text className=" text-center">
                    {quickForms?.description}
                  </Text>
                )}
              </div>

              <div className="w-full flex flex-row gap-4">
                {quickForms?.quickLinks &&
                  quickForms?.quickLinks?.map((form, idx) => (
                    <div className="w-full p-4 md:w-1/2 bg-white rounded-md">
                      <div className="flex flex-col h-full">
                        <div className="flex flex-col h-full">
                          <div className="flex-1">
                            <Text
                              fontSize="xl"
                              weight="bold"
                              className="text-primary mb-3 md:text-left text-center"
                            >
                              {form.title}
                            </Text>
                            <Text muted className="text-sm text-gray-700">
                              {form.description}
                            </Text>
                          </div>

                          {form?.primaryButton?.label ? (
                            <div className="pt-6 ">
                              <Button
                                as="link"
                                link={form?.primaryButton}
                                ariaLabel={
                                  form?.primaryButton?.ariaLabel ??
                                  form?.primaryButton?.label
                                }
                                size="lg"
                                className="text-white bg-primary rounded-sm w-max py-2 px-4 hover:bg-primary/80  flex items-center space-x-2 transition-all duration-200  origin-left"
                              >
                                <span>{form?.primaryButton?.label}</span>
                                <FaArrowRightLong />
                              </Button>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div> */}
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  subtitle,
  description,
}: {
  caption?: string;
  title?: string;
  subtitle?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Heading
          type="h3"
          className="text-lg lg:text-xl uppercase font-semibold text-primary mb-2"
        >
          {caption}
        </Heading>
      )}

      {title && (
        <div className="mb-8">
          <Heading
            type="h2"
            className="!text-black text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
          >
            {title}
          </Heading>
          <div className="w-10 bg-primary h-2" />
        </div>
      )}
      {description && <Text className="mb-4 text-lg">{description}</Text>}
    </>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="relative flex items-center justify-center gap-2 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="maxtonPrimary"
        >
          <span>{primaryButton?.label}</span>
          <FaArrowRightLong />
        </Button>
      ) : null}

      {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="maxtonSecondary"
        >
          <span>{secondaryButton?.label}</span>
          <FaArrowRightLong />
        </Button>
      ) : null}
    </Flex>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    // <Flex justify="center" className="-mx-4 flex flex-row">
    //   <div className="w-full flex flex-col">
    //     {features.slice(0, 2).map((feature, index) => (
    //       <FeatureItem feature={feature} key={feature._key} index={index + 1} />
    //     ))}
    //   </div>
    //   <div className="w-full flex flex-col lg:translate-y-12">
    //     {features.slice(2, 4).map((feature, index) => (
    //       <FeatureItem feature={feature} key={feature._key} index={index + 3} />
    //     ))}
    //   </div>
    // </Flex>
    <Flex justify="center" className="-mx-4 grid grid-cols-2">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </Flex>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  return (
    <div
      className={`w-full h-full p-2 lg:p-4 relative group transition duration-300 ease-linear ${
        index % 2 === 0 ? "lg:translate-y-12" : ""
      }`}
    >
      <Card variant="normalCard">
        <div className="absolute top-6 right-10 text-4xl font-bold group-hover:!text-white !text-secondary group-hover:opacity-100 opacity-10">
          {index < 10 ? `0${index}` : index}
        </div>

        {feature?.mainImage?.image && (
          <div>
            <Image
              src={feature?.mainImage?.image}
              width={50}
              height={50}
              alt={feature?.mainImage?.alt ?? feature?.title}
              className="group-hover:invert brightness-0 group-hover:opacity-100 opacity-20"
            />
          </div>
        )}

        <div>
          <Text
            weight="bold"
            className="px-0 lg:text-lg text-base group-hover:text-white uppercase font-bold text-gray-800"
          >
            {feature.title}
          </Text>

          <div className="group-hover:scale-100 scale-0 origin-left transition duration-300 ease-linear w-10 h-1 bg-white" />

          {/* <Text
            muted
            className="px-0 !text-sm group-hover:!text-white !text-gray-700"
          >
            {feature.description}
          </Text> */}
        </div>
      </Card>
    </div>
  );
}

export { Features_S };
