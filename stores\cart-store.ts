import { CalculatorData } from "@/pages/api/orders";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface SelectedOption {
  name: string;
  value: string | number;
  price?: number | undefined;
}

export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  sku: string;
  selected: boolean;
  discount?: number;
  selectedOptions?: SelectedOption[];
  calculatorData?: CalculatorData;
}

interface CartState {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (itemId: string, selectedOptions?: SelectedOption[]) => void;
  updateQuantity: (
    itemId: string,
    quantity: number,
    selectedOptions?: SelectedOption[]
  ) => void;
  clearCart: () => void;
  getSubtotal: () => number;
  getAdditionalAmount: () => number;
}

const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      getSubtotal() {
        const items = get().items;
        const prices = items.map((item) => {
          const totalPrice = item.price * item.quantity;

          return totalPrice;
        });
        const subtotal =
          prices.length === 0 ? 0 : prices?.reduce((acc, curr) => acc + curr);

        return subtotal;
      },
      getAdditionalAmount() {
        const items = get().items;
        const additionalAmount = items.reduce((acc, curr) => {
          return (
            acc +
            (curr.selectedOptions?.reduce((optAcc, option) => {
              // Multiply option price by quantity to get the total
              return optAcc + (option.price ?? 0) * curr.quantity;
            }, 0) ?? 0)
          );
        }, 0);

        return additionalAmount;
      },
      addItem: (item) =>
        set((state) => {
          const existingItemIndex = state.items.findIndex((i) => {
            // Check if IDs match
            if (i.id !== item.id) return false;

            // If no options on either item, just match by product ID
            if (!i.selectedOptions?.length && !item.selectedOptions?.length)
              return true;

            // If one has options and other doesn't, they're different
            if (
              (!i.selectedOptions?.length && item.selectedOptions?.length) ||
              (i.selectedOptions?.length && !item.selectedOptions?.length)
            )
              return false;

            // If different number of options, they're different
            if (i.selectedOptions?.length !== item.selectedOptions?.length)
              return false;

            // Check if all options match (both name and value)
            return i.selectedOptions?.every((existingOption) => {
              return item.selectedOptions?.some(
                (newOption) =>
                  newOption.name === existingOption.name &&
                  newOption.value === existingOption.value
              );
            });
          });

          // If item doesn't exist with these exact options, add it as new
          if (existingItemIndex === -1) {
            return { items: [...state.items, item] };
          }

          // If item exists with same options, update quantity
          const updatedItems = [...state.items];
          updatedItems[existingItemIndex] = {
            ...updatedItems[existingItemIndex],
            quantity: updatedItems[existingItemIndex].quantity + item.quantity,
          };

          return { items: updatedItems };
        }),
      removeItem: (itemId, selectedOptions) =>
        set((state) => {
          // If no selectedOptions provided, remove all items with this ID
          if (!selectedOptions) {
            return {
              items: state.items.filter((i) => i.id !== itemId),
            };
          }

          // Find the specific item with matching ID and options
          const itemIndex = state.items.findIndex((i) => {
            if (i.id !== itemId) return false;

            // If one has options and other doesn't, they're different
            const hasItemOptions =
              i.selectedOptions && i.selectedOptions.length > 0;
            const hasSelectedOptions =
              selectedOptions && selectedOptions.length > 0;

            if (
              (!hasItemOptions && hasSelectedOptions) ||
              (hasItemOptions && !hasSelectedOptions)
            )
              return false;

            // If neither has options, they're the same basic product
            if (!hasItemOptions && !hasSelectedOptions) return true;

            // At this point, both have options, we can safely use selectedOptions.length
            // If different number of options, they're different
            if (i.selectedOptions!.length !== selectedOptions.length)
              return false;

            // Check if all options match
            return i.selectedOptions!.every((existingOption) =>
              selectedOptions.some(
                (option) =>
                  option.name === existingOption.name &&
                  option.value === existingOption.value
              )
            );
          });

          // If no matching item found, return unchanged
          if (itemIndex === -1) return state;

          // Remove the specific item
          const updatedItems = [...state.items];
          updatedItems.splice(itemIndex, 1);
          return { items: updatedItems };
        }),
      updateQuantity: (itemId, quantity, selectedOptions) =>
        set((state) => {
          // If no selectedOptions provided, update all items with this ID
          if (!selectedOptions) {
            return {
              items: state.items.map((i) =>
                i.id === itemId ? { ...i, quantity } : i
              ),
            };
          }

          // Find the specific item with matching ID and options
          const itemIndex = state.items.findIndex((i) => {
            if (i.id !== itemId) return false;

            // If one has options and other doesn't, they're different
            const hasItemOptions =
              i.selectedOptions && i.selectedOptions.length > 0;
            const hasSelectedOptions =
              selectedOptions && selectedOptions.length > 0;

            if (
              (!hasItemOptions && hasSelectedOptions) ||
              (hasItemOptions && !hasSelectedOptions)
            )
              return false;

            // If neither has options, they're the same basic product
            if (!hasItemOptions && !hasSelectedOptions) return true;

            // At this point, both have options, we can safely use selectedOptions.length
            // If different number of options, they're different
            if (i.selectedOptions!.length !== selectedOptions.length)
              return false;

            // Check if all options match
            return i.selectedOptions!.every((existingOption) =>
              selectedOptions.some(
                (option) =>
                  option.name === existingOption.name &&
                  option.value === existingOption.value
              )
            );
          });

          // If no matching item found, return unchanged
          if (itemIndex === -1) return state;

          // Update the specific item
          const updatedItems = [...state.items];
          updatedItems[itemIndex] = {
            ...updatedItems[itemIndex],
            quantity,
          };
          return { items: updatedItems };
        }),
      clearCart: () => set({ items: [] }),
    }),
    {
      name: "maxton-cart-data",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

export default useCartStore;
