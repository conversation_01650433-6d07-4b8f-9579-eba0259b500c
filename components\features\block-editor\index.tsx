"use client"

import { useState, useEffect } from "react"
import { useE<PERSON><PERSON>, EditorContent, BubbleMenu, type Editor } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Link from "@tiptap/extension-link"
import { cn } from "@/lib/utils"
import {
    Bold,
    Italic,
    List,
    ListOrdered,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Heading1,
    Heading2,
    Heading3,
    Code,
    Quote,
    Undo,
    Redo,
    Link as LinkIcon,
} from "lucide-react"

interface TipTapEditorProps {
    content?: string
    onChange?: (html: string) => void
    placeholder?: string
    className?: string
    disabled?: boolean
}

export function TipTapEditor({
    content = "",
    onChange,
    placeholder = "Start typing...",
    className,
    disabled = false,
}: TipTapEditorProps) {
    const [isFocused, setIsFocused] = useState(false)

    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                heading: {
                    levels: [1, 2, 3],
                },
            }),
            Link.configure({
                openOnClick: true,
                linkOnPaste: true,
                protocols: ["http", "https", "mailto", "tel"],
                HTMLAttributes: {
                    class: 'text-blue-600 underline hover:text-blue-800 transition-colors',
                },
            }),
        ],
        content,
        editable: !disabled,
        onUpdate: ({ editor }) => {
            onChange?.(editor.getHTML())
        },
        onFocus: () => setIsFocused(true),
        onBlur: () => setIsFocused(false),
        editorProps: {
            attributes: {
                class: cn(
                    "prose max-w-none dark:prose-invert focus:outline-none",
                    "min-h-[150px] w-full rounded-md border border-input bg-background px-3 py-2",
                    isFocused && "ring-2 ring-ring ring-offset-2",
                    disabled && "cursor-not-allowed opacity-50",
                    !content && !isFocused && "before:text-muted-foreground before:content-[attr(data-placeholder)]"
                ),
                "data-placeholder": placeholder,
            },
        },
    })

    useEffect(() => {
        if (editor && content !== editor.getHTML()) {
            editor.commands.setContent(content)
        }
    }, [content, editor])

    const setLink = () => {
        if (!editor) return

        const previousUrl = editor.getAttributes('link').href
        const url = window.prompt('URL', previousUrl)

        if (url === null) return

        if (url === '') {
            editor.chain().focus().extendMarkRange('link').unsetLink().run()
            return
        }

        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
    }

    return (
        <div className={cn("relative", className)}>
            {editor && (
                <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
                    <ToolbarGroup>
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleBold().run()}
                            isActive={editor.isActive("bold")}
                            icon={<Bold className="h-4 w-4" />}
                            title="Bold"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleItalic().run()}
                            isActive={editor.isActive("italic")}
                            icon={<Italic className="h-4 w-4" />}
                            title="Italic"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleCode().run()}
                            isActive={editor.isActive("code")}
                            icon={<Code className="h-4 w-4" />}
                            title="Code"
                        />
                        <ToolbarButton
                            onClick={setLink}
                            isActive={editor.isActive("link")}
                            icon={<LinkIcon className="h-4 w-4" />}
                            title="Link"
                        />
                    </ToolbarGroup>
                    <ToolbarGroup>
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                            isActive={editor.isActive("heading", { level: 1 })}
                            icon={<Heading1 className="h-4 w-4" />}
                            title="Heading 1"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                            isActive={editor.isActive("heading", { level: 2 })}
                            icon={<Heading2 className="h-4 w-4" />}
                            title="Heading 2"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                            isActive={editor.isActive("heading", { level: 3 })}
                            icon={<Heading3 className="h-4 w-4" />}
                            title="Heading 3"
                        />
                    </ToolbarGroup>
                    <ToolbarGroup>
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleBulletList().run()}
                            isActive={editor.isActive("bulletList")}
                            icon={<List className="h-4 w-4" />}
                            title="Bullet List"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleOrderedList().run()}
                            isActive={editor.isActive("orderedList")}
                            icon={<ListOrdered className="h-4 w-4" />}
                            title="Ordered List"
                        />
                        <ToolbarButton
                            onClick={() => editor.chain().focus().toggleBlockquote().run()}
                            isActive={editor.isActive("blockquote")}
                            icon={<Quote className="h-4 w-4" />}
                            title="Blockquote"
                        />
                    </ToolbarGroup>
                </BubbleMenu>
            )}

            <EditorContent editor={editor} className={cn(
                "overflow-hidden",
                "[&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:transition-colors",
            )} />

            {!disabled && editor && (
                <div className="mt-2 flex items-center justify-end gap-2">
                    <ToolbarButton
                        onClick={() => editor.chain().focus().undo().run()}
                        disabled={!editor.can().undo()}
                        icon={<Undo className="h-4 w-4" />}
                        title="Undo"
                        variant="ghost"
                    />
                    <ToolbarButton
                        onClick={() => editor.chain().focus().redo().run()}
                        disabled={!editor.can().redo()}
                        icon={<Redo className="h-4 w-4" />}
                        title="Redo"
                        variant="ghost"
                    />
                </div>
            )}
        </div>
    )
}

interface ToolbarButtonProps {
    onClick: () => void
    isActive?: boolean
    disabled?: boolean
    icon: React.ReactNode
    title: string
    variant?: "default" | "ghost"
}

function ToolbarButton({
    onClick,
    isActive,
    disabled,
    icon,
    title,
    variant = "default"
}: ToolbarButtonProps) {
    return (
        <button
            type="button"
            onClick={onClick}
            disabled={disabled}
            className={cn(
                "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium ring-offset-background transition-colors",
                "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
                "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                "disabled:pointer-events-none disabled:opacity-50",
                isActive && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50",
                variant === "ghost" && "hover:bg-transparent dark:hover:bg-transparent"
            )}
            title={title}
        >
            {icon}
        </button>
    )
}

interface ToolbarGroupProps {
    children: React.ReactNode
}

function ToolbarGroup({ children }: ToolbarGroupProps) {
    return (
        <div className="flex items-center border-r border-neutral-200 pr-2 last:border-r-0 dark:border-neutral-700">
            {children}
        </div>
    )
}

export function BlockEditor() {
    const [content, setContent] = useState("<p>Hello World!</p>")

    return (
        <div className="w-full space-y-4">
            <h2 className="text-xl font-semibold">Block Editor</h2>
            <TipTapEditor
                content={content}
                onChange={setContent}
                placeholder="Start writing..."
            />
            <div className="mt-4 rounded-md bg-neutral-100 p-4 dark:bg-neutral-800">
                <h3 className="mb-2 text-sm font-medium">HTML Output</h3>
                <pre className="text-xs">{content}</pre>
            </div>
        </div>
    )
}

export default BlockEditor
