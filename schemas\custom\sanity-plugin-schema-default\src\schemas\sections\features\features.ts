import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { featuresVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantIImage from "./images/variant_i.png";
import variantJImage from "./images/variant_j.png";
import variantKImage from "./images/variant_k.png";
import variantLImage from "./images/variant_l.png";
import variantMImage from "./images/variant_m.png";
import variantNImage from "./images/variant_n.png";
import variantOImage from "./images/variant_o.png";
import variantPImage from "./images/variant_p.png";
import variantQImage from "./images/variant_q.png";
import variantRImage from "./images/variant_r.png";
import variantSImage from "./images/variant_r.png";
import variantTImage from "./images/variant_t.png";
import variantUImage from "./images/variant_k.png";
import variantVImage from "./images/variant_v.png";
import variantWImage from "./images/variant_w.png";
import variantXImage from "./images/variant_x.png";

import initialValue from "./initialValue";
import { featuresSchema } from "./schema";

export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows

  {
    title: "Variant I",
    description: "A new variant for header component",
    value: "variant_i",
    image: variantIImage.src,
  },
  {
    title: "Variant J",
    description: "A new variant for header component",
    value: "variant_j",
    image: variantJImage.src,
  },
  {
    title: "Variant K",
    description: "A new variant for header component",
    value: "variant_k",
    image: variantKImage.src,
  },
  {
    title: "Variant L",
    description: "A new variant for header component",
    value: "variant_l",
    image: variantLImage.src,
  },
  {
    title: "Variant M",
    description: "A new variant for header component",
    value: "variant_m",
    image: variantMImage.src,
  },
  {
    title: "Variant N",
    description: "A new variant for header component",
    value: "variant_n",
    image: variantNImage.src,
  },
  {
    title: "Variant O",
    description: "A new variant for header component",
    value: "variant_o",
    image: variantOImage.src,
  },
  {
    title: "Variant P",
    description: "A new variant for features component",
    value: "variant_p",
    image: variantPImage.src,
  },
  {
    title: "Variant Q",
    description: "A new variant for header component",
    value: "variant_q",
    image: variantQImage.src,
  },
  {
    title: "Variant R",
    description: "A new variant for header component",
    value: "variant_r",
    image: variantRImage.src,
  },
  {
    title: "Variant S",
    description: "A new variant for header component",
    value: "variant_s",
    image: variantSImage.src,
  },
  {
    title: "Variant T",
    description: "A new variant for header component",
    value: "variant_t",
    image: variantTImage.src,
  },
  {
    title: "Variant U",
    description: "A new variant for header component",
    value: "variant_u",
    image: variantUImage.src,
  },
  {
    title: "Variant V",
    description: "A new variant for feature component",
    value: "variant_v",
    image: variantVImage.src,
  },
  {
    title: "Variant W",
    description: "A new variant for feature component",
    value: "variant_w",
    image: variantWImage.src,
  },
  {
    title: "Variant X",
    description: "A new variant for feature component",
    value: "variant_x",
    image: variantXImage.src,
  },
];

export default rootSchema(
  "features",
  "Features",
  MdVerticalAlignTop,
  variantsList,
  featuresSchema,
  initialValue
);
