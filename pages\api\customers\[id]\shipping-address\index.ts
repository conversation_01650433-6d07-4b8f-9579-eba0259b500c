import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { ShippingAddress } from "@/supabase/types";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
  matchRoute({
    GET: getShippingAddressesHandler,
    POST: addShippingAddressHandler,
  }),
);

export interface GetShippingAddressResponse {
  shipping_address?: ShippingAddress[];
  error?: string;
}

async function getShippingAddressesHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<GetShippingAddressResponse>,
) {
  const userId = req.query.id?.toString();

  if (!userId) {
    return res.status(400).json({ error: "Missing user id" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customerId = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customerId.error) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const shipping_addresses = await supabaseAdminClient
    .from("shipping_addresses")
    .select("*")
    .eq("customer_id", customerId.data.id);

  if (shipping_addresses.error) {
    return res.status(400).json({ error: shipping_addresses.error.message });
  }

  return res.status(200).json({
    shipping_address: shipping_addresses.data,
  });
}

export const addShippingAddressSchema = z.object({
  address: z.string().min(1, "Address is required"),
  address_2: z.string().optional(),
  address_type: z.enum(["residential", "commercial"], {
    required_error: "Address type is required",
  }),
  city: z.string().min(1, "City is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  zip_code: z.string().min(1, "Zip code is required"),
  contact_email: z.string().email("Invalid email address").optional().or(z.literal('')),
  contact_name: z.string().optional(),
  contact_number: z.string().optional().or(z.literal('')),
  option_name: z.string().optional(),
  default: z.boolean().default(false),
});

export type AddShippingAddress = z.infer<typeof addShippingAddressSchema>;

export interface AddShippingAddressResponse {
  error?: string;
  shipping_address?: ShippingAddress;
}

async function addShippingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<AddShippingAddressResponse>,
) {
  const user_id = req.query.id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const result = addShippingAddressSchema.safeParse(req.body);

  if (result.error) {
    return res.status(400).json({ error: result.error.message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const {
    address,
    address_2,
    address_type,
    city,
    country,
    state,
    zip_code,
    contact_email,
    contact_name,
    contact_number,
    option_name,
    default: isDefault,
  } = result.data;

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;

  if (isDefault) {
    await supabaseAdminClient
      .from("shipping_addresses")
      .update({ default: false })
      .eq("customer_id", customerId);
  }

  const newShippingAddress = await supabaseAdminClient
    .from("shipping_addresses")
    .insert({
      address,
      address_2,
      address_type,
      city,
      country,
      state,
      zip_code,
      contact_email,
      contact_name,
      contact_number,
      option_name,
      customer_id: customerId,
      default: isDefault,
    })
    .select()
    .single();

  if (newShippingAddress.error) {
    return res.status(400).json({ error: newShippingAddress.error.message });
  }

  return res.status(200).json({
    shipping_address: newShippingAddress.data,
  });
}
