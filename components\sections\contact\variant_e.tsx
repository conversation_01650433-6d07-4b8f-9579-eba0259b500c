import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ContactProps } from ".";

// import { SocialIcons } from "@stackshift-ui/social-icons";
// import { SocialLink } from "../../../types";

export default function Contact_E({
  title,
  contactDescription,
  officeInformation,
  contactNumber,
  contactEmail,
  socialLinks,
}: ContactProps) {
  return (
    <Section className="pt-20 bg-background">
      <Container maxWidth={1280}>
        <ContactHeader title={title} description={contactDescription} />
        <Container maxWidth={1280}>
          <ContactDetails
            officeInformation={officeInformation}
            contactNumber={contactNumber}
            contactEmail={contactEmail}
            socialLinks={socialLinks}
          />
        </Container>
      </Container>
    </Section>
  );
}

function ContactHeader({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <div className="mb-6 text-center">
      {title ? (
        <Heading type="h2" fontSize="3xl">
          {title}
        </Heading>
      ) : null}
      {description ? (
        <Text className="text-left font-semibold">{description}</Text>
      ) : null}
    </div>
  );
}

function ContactDetails({
  officeInformation,
  contactEmail,
  contactNumber,
  socialLinks,
}: {
  officeInformation?: string;
  contactEmail?: string;
  contactNumber?: string;
  socialLinks?: any;
}) {
  return (
    <Flex
      className="flex-col md:flex-row"
      gap={4}
      justify="end"
      align="stretch"
    >
      {officeInformation ? (
        <OfficeInformationCard officeInformation={officeInformation} />
      ) : null}

      {contactEmail ? <ContactsCard contactEmail={contactEmail} /> : null}

      {contactNumber ? <NumberCard contactNumber={contactNumber} /> : null}

      {/* {socialLinks ? <SocialLinksCard socialLinks={socialLinks} /> : null} */}
    </Flex>
  );
}

function OfficeInformationCard({
  officeInformation,
}: {
  officeInformation: string;
}) {
  return (
    <div className="w-full pt-4 text-center md:pt-6 lg:pt-6  px-10 lg:px-10">
      <Heading type="h2" className="md:mb-4 mb-2 text-base lg:text-xl">
        Postal Address:
      </Heading>
      <Text muted>{officeInformation}</Text>
    </div>
  );
}

function ContactsCard({ contactEmail }: { contactEmail?: string }) {
  return (
    <div className="w-full pt-4 text-center md:pt-6 lg:pt-6  px-10 lg:px-10">
      <Heading type="h2" className="md:mb-4 mb-2 text-base lg:text-xl">
        E-mail:
      </Heading>
      <Text className="mb-2 hover:underline hover:text-primary">
        {contactEmail ? (
          <Link href={`mailto:${contactEmail}`}>{contactEmail}</Link>
        ) : null}
      </Text>
    </div>
  );
}
function NumberCard({ contactNumber }: { contactNumber?: string }) {
  return (
    <div className="w-full pt-4 text-center md:pt-6 lg:pt-6  px-10 lg:px-10">
      <Heading type="h2" className="md:mb-4 mb-2 text-base lg:text-xl">
        Telephone:
      </Heading>

      <Text className="hover:underline hover:text-primary">
        {contactNumber ? (
          <Link href={`tel:${contactNumber}`}>{contactNumber}</Link>
        ) : null}
      </Text>
    </div>
  );
}

// function SocialLinksCard({ socialLinks }: any) {
//   return (
//     <Card className="w-full p-12 text-center bg-white md:p-16 lg:p-20">
//       <Heading className="mb-16" fontSize="2xl" type="h2">
//         Socials
//       </Heading>
//       <Flex justify="center" wrap>
//         <SocialLinks socialLinks={socialLinks} />
//       </Flex>
//     </Card>
//   );
// }

// function SocialLinks({ socialLinks }: { socialLinks?: SocialLink[] }) {
//   if (!socialLinks) return null;

//   return (
//     <React.Fragment>
//       {socialLinks?.map((social) => (
//         <Link
//           aria-label={social?.socialMedia || social?.socialMediaPlatform || ""}
//           className="inline-block mr-4 mb-4 rounded"
//           target="_blank"
//           rel="noopener noreferrer"
//           href={social?.socialMediaLink ?? "/page-not-found"}
//           key={social?._key}
//         >
//           {social?.socialMediaIcon?.image ? (
//             <Image
//               src={social?.socialMediaIcon?.image}
//               width={24}
//               height={24}
//               alt={social?.socialMediaIcon?.alt ?? "contact-socialMedia-icon"}
//             />
//           ) : (
//             <SocialIcons social={social.socialMedia as any} />
//           )}
//         </Link>
//       ))}
//     </React.Fragment>
//   );
// }

export { Contact_E };
