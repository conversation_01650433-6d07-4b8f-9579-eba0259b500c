import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdNavigation } from "react-icons/md";
import { navigationVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";

import variantAImage from "./images/variant_a.jpg";
import variantBImage from "./images/variant_b.jpg";
import variantCImage from "./images/variant_c.jpg";
import variantDImage from "./images/variant_d.jpg";
import variantFImage from "./images/variant_f.png";
import initialValue from "./initialValue";
import { navigationSchema } from "./schema";

export const variantLists = [
  // {
  //   title: "Variant A",
  //   description:
  //     "This variant consists of Logo, Menu and Button as seen on the image - Responsive Navigation",
  //   value: "variant_a",
  //   image: variantAImage.src,
  // },
  // {
  //   title: "Variant B",
  //   value: "variant_b",
  //   description:
  //     "This variant consists of <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> as seen on the image - Responsive Navigation",
  //   image: variantBImage.src,
  // },
  // {
  //   title: "Variant C",
  //   value: "variant_c",
  //   image: variantCImage.src,
  //   description:
  //     "This variant consists of Logo, Menu and Button as seen on the image - Responsive Navigation",
  // },
  // {
  //   title: "Variant D",
  //   description:
  //     "This variant consists of Logo, Menu and Button as seen on the image - Responsive Navigation",
  //   value: "variant_d",
  //   image: variantDImage.src,
  // },
  ...baseVariantsList,
  {
    title: "Variant F",
    description:
      "This variant consists of Logo, Menu and Button as seen on the image - Responsive Navigation",
    value: "variant_f",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    description:
      "This variant consists of Logo, Menu and Button as seen on the image - Responsive Navigation",
    value: "variant_g",
    image: variantFImage.src,
  },
];

export default rootSchema(
  "navigation",
  "Navigation",
  MdNavigation,
  variantLists,
  navigationSchema,
  initialValue
);
