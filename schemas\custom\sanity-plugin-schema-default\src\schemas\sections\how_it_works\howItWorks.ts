import {   rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { howItWorksVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantFImage from "./images/variant_f.png";

import initialValue from "./initialValue";
import { howItWorksSchema } from "./schema";



export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows
  
  {
    title: "Variant F",
    description: "A new variant for header component",
    value: "variant_f", 
    image: variantFImage.src, 
  },
 


];

export default rootSchema(
  "howItWorks",
  "How It Works",
  MdVerticalAlignTop,
  variantsList,
  howItWorksSchema,
  initialValue
);

