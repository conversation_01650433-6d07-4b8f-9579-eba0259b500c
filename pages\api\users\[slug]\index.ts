import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin, checkAuth, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
  matchRoute({
    GET: handler,
    PUT: updateUserHandler,
    DELETE: checkAdmin(checkPermission("delete:users", deleteUserHandler))
  }),
);

async function handler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse,
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (userId !== id) {
    return res.status(403).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("users")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({ data });
}

const updateUserSchema = z.object({
  first_name: z
    .string()
    .min(1, { message: "First name is required" })
    .optional(),
  last_name: z.string().min(1, { message: "Last name is required" }).optional(),
  phone: z.string().min(1, { message: "Phone number is required" }).optional(),
  email: z
    .string()
    .email({ message: "Please enter a valid email address" })
    .optional(),
});

export interface UpdateUserResponse {
  error?: string;
  userId?: string;
  message?: string;
}

async function updateUserHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<UpdateUserResponse>,
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId || userId !== id) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const data = updateUserSchema.safeParse(req.body);

  if (!data.success) {
    // Just return the Zod validation errors directly
    return res.status(400).json({ error: data.error.issues.at(0)?.message });
  }

  const parsedData = data.data;
  const supabaseAdminClient = createSupabaseAdminClient();

  const updateAuthData = {
    email: parsedData.email,
    phone: parsedData.phone,
  };

  const publicUserData = {
    first_name: parsedData.first_name,
    last_name: parsedData.last_name,
    phone: parsedData.phone,
  };

  // Only update auth data if there are changes to make
  if (parsedData.email) {
    const filteredAuthData = Object.entries(updateAuthData)
      .filter(([_, value]) => value !== undefined)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredAuthData).length > 0) {
      const updateAuth = await supabaseAdminClient.auth.admin.updateUserById(
        userId,
        filteredAuthData,
      );

      if (updateAuth.error) {
        // Return the raw error from Supabase
        return res.status(400).json({ error: updateAuth.error.message });
      }
    }
  }

  // Only update public user data if there are changes to make
  if (parsedData.first_name || parsedData.last_name || parsedData.phone) {
    const filteredPublicUserData = Object.entries(publicUserData)
      .filter(([_, value]) => value !== undefined)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    if (Object.keys(filteredPublicUserData).length > 0) {
      const now = new Date();
      const updatedAt = now.toISOString();

      const updatePublicUser = await supabaseAdminClient
        .schema("public")
        .from("users")
        .update({
          ...filteredPublicUserData,
          updated_at: updatedAt,
        })
        .eq("id", userId);

      await supabaseAdminClient
        .from("customers")
        .update({
          updated_at: updatedAt,
        })
        .eq("user_id", userId);

      if (updatePublicUser.error) {
        // Return the raw error from Supabase
        return res.status(400).json({ error: updatePublicUser.error.message });
      }
    }
  }

  return res.status(200).json({ userId, message: "User updated successfully" });
}

export interface DeleteUserResponse {
  error?: string;
  message?: string;
}

async function deleteUserHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<DeleteUserResponse>,
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { error } = await supabaseAdminClient.auth.admin.deleteUser(id);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.status(200).json({ message: "User deleted successfully" });
}