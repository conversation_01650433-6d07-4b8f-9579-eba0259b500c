import emailTemplate from ".";

interface RegistrationConfirmationProps {
  to: string;
  name: string;
  email: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  companyName?: string;
  businessType?: string;
  businessNature?: string;
  website?: string;
  maxtonAccount?: string;
  numberOfElevators?: number;
  registrationDate: string;
  loginUrl: string;
  additionalNotes?: string;
}

export default function createRegistrationConfirmationTemplate({
  to,
  name,
  email,
  address,
  city,
  state,
  zip_code,
  country,
  companyName,
  businessType,
  businessNature,
  website,
  maxtonAccount,
  numberOfElevators,
  registrationDate,
  loginUrl,
  additionalNotes,
}: RegistrationConfirmationProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Manufacturing Company";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

  // Format date to be more user-friendly
  const formattedDate = new Date(registrationDate).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Full address in a single line
  const fullAddress =
    address && city && state && zip_code && country
      ? `${address}, ${city}, ${state} ${zip_code}, ${country}`
      : "";

  return emailTemplate({
    to,
    subject: `Registration Approval - ${APP_NAME}`,
    from: from,
    bcc: "<EMAIL>",
    body: `
      <h2 style="color: #ff8c00; background-color: #fff4e6; padding: 10px; border-left: 4px solid #ff8c00;">Registration Approval</h2>
      <p>Dear ${name} ${companyName ? `(${companyName})` : ""},</p>
      
      <p>Welcome and thank you for registering at Maxton Manufacturing Company!</p>
      
      <p><strong>Important:</strong> Your account must be approved before you can log in. Account approvals are processed within 24 hours (except on holidays and weekends).</p>
      
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="padding: 12px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd; font-size: 18px; font-weight: 600;" colspan="2">Account Information</th>
          </tr>
          <tr>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                <li style="margin-bottom: 4px;"><strong>Name:</strong> ${name}</li>
                <li style="margin-bottom: 4px;"><strong>Email:</strong> ${email}</li>
                <li style="margin-bottom: 4px;"><strong>Registration Date:</strong> ${formattedDate}</li>
              </ul>
            </td>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                ${
                  companyName
                    ? `<li style="margin-bottom: 4px;"><strong>Company Name:</strong> ${companyName}</li>`
                    : ""
                }
                ${
                  businessType
                    ? `<li style="margin-bottom: 4px;"><strong>Business Type:</strong> ${businessType}</li>`
                    : ""
                }
                ${
                  businessNature
                    ? `<li style="margin-bottom: 4px;"><strong>Nature of Business:</strong> ${businessNature}</li>`
                    : ""
                }
                ${
                  website
                    ? `<li style="margin-bottom: 4px;"><strong>Website:</strong> <a href="${
                        website.startsWith("http")
                          ? website
                          : "https://" + website
                      }" style="color: #ff8c00; text-decoration: none;">${website}</a></li>`
                    : ""
                }
                ${
                  maxtonAccount
                    ? `<li style="margin-bottom: 4px;"><strong>Maxton Account:</strong> ${maxtonAccount}</li>`
                    : ""
                }
                ${
                  numberOfElevators !== undefined
                    ? `<li style="margin-bottom: 4px;"><strong>Number of Elevators:</strong> ${numberOfElevators}</li>`
                    : ""
                }
              </ul>
            </td>
          </tr>
        </table>
      </div>
      
      ${
        fullAddress
          ? `
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="padding: 12px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd; font-size: 18px; font-weight: 600;">Address</th>
          </tr>
          <tr>
            <td style="padding: 15px; border: 1px solid #ddd;">${fullAddress}</td>
          </tr>
        </table>
      </div>
      `
          : ""
      }
      
      ${
        additionalNotes
          ? `
      <div style="margin: 20px 0;">
        <h3 style="font-size: 18px; margin-bottom: 8px;">Additional Information</h3>
        <p>${additionalNotes}</p>
      </div>
      `
          : ""
      }
      
      <p>Once your account is approved, you'll receive a confirmation email. You can then log in using your email address and password by visiting our website or at the following URL:</p>
      
      <div style="margin: 25px 0; text-align: center;">
        <a href="${loginUrl}" 
           style="display: inline-block; padding: 12px 24px; background-color: #ff8c00; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Login Page
        </a>
      </div>
      
      <p>Upon approval and logging in, you will be able to:</p>
      <ul>
        <li>Place orders directly on our website</li>
        <li>View product prices</li>
        <li>Add products to your cart</li>
        <li>Proceed to checkout</li>
        <li>Access your order history</li>
        <li>Edit your account information</li>
      </ul>
      
      <p>If you have any questions or need assistance, please don't hesitate to contact our customer service team.</p>
      
      <p>Thank you for choosing Maxton Manufacturing Company.</p>
    `,
  });
}
