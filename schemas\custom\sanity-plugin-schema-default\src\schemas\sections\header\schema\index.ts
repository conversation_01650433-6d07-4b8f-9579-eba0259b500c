import {
  arrayOfImages,
  description,
  formLinks,
  signInLink,
  mainImage,
  plainText,
  primaryButton,
  secondaryButton,
  title,
  webriqForms,
  youtubeLink,
  subtitle,
  blockContentNormalStyle,
  arrayOfImagesCopy,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const headerSchema = [
  title(),
  subtitle(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_q",
    ])
  ),
  description(
    hideIfVariantIn([
      "variant_c",
      "variant_f",
      "variant_j",
      "variant_l",
      "variant_o",
    ])
  ),
  primaryButton(hideIfVariantIn(["variant_i", "variant_k", "variant_p"])),
  secondaryButton(
    hideIfVariantIn([
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_m",
      "variant_o",
      "variant_p",
    ])
  ),
  arrayOfImages(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_p",
      "variant_q",
    ])
  ),
  arrayOfImagesCopy(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
    ])
  ),
  mainImage(
    hideIfVariantIn([
      "variant_b",
      "variant_c",
      "variant_e",
      "variant_i",
      "variant_o",
    ])
  ),
  webriqForms(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
    ])
  ),
  formLinks(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
    ])
  ),

  youtubeLink(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
    ])
  ),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_i",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
    ])
  ),
  {
    name: "notesSection",
    title: "Notes Section",
    description:
      "Click the 'Add item' button to add a title and link. If you want to edit added title and link, click this ⋮ icon found on its right.",
    type: "array",
    of: [
      {
        type: "object",
        name: "titleAndLink",
        title: "Title and Link",
        fields: [
          title(),
          description(),
          {
            name: "hasLink",
            title: "Add Link",
            type: "boolean",
            initialValue: false,
          },
          {
            name: "primaryButton",
            title: "Primary Button",
            hidden: ({ parent }) => !parent?.hasLink,
            description:
              "Click ▶ above to expand and fill out the details to add a primary button.",
            type: "conditionalLink",
            options: {
              collapsible: true,
              collapsed: true,
            },
          },
          {
            name: "backgroundColor",
            title: "Background Color",
            type: "string",
            hidden: ({ parent }) => !parent?.hasLink,
            options: {
              list: [
                { title: "Green", value: "green" },
                { title: "Yellow", value: "yellow" },
                { title: "Blue", value: "blue" },
              ],
            },
          },
        ],
      },
    ],
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_o",
      "variant_p",
      "variant_q",
    ]),
  },
  {
    name: "repairParts",
    title: "Repair Parts with Link",
    description:
      "Group repair parts by color. Click 'Add item' to create a new color group, then add parts within that group.",
    type: "array",
    of: [
      {
        type: "object",
        name: "colorGroup",
        title: "Color Group",
        fields: [
          {
            name: "groupColor",
            title: "Group Color",
            type: "string",
            options: {
              list: [
                { title: "Green", value: "green" },
                { title: "Blue", value: "blue" },
                { title: "Yellow", value: "yellow" },
              ],
            },
          },
          {
            name: "parts",
            title: "Parts in this color group",
            type: "array",
            of: [primaryButton()],
          },
        ],
      },
    ],
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_o",
      "variant_p",
      "variant_q",
    ]),
  },
];
