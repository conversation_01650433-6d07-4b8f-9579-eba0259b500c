import AdminLayout from "@/components/features/admin/layout";
import { CancellationRequestsDataTable } from "@/components/features/admin/requests/cancelation-request";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { XCircle } from "lucide-react";
import Head from "next/head";

export default function Requests() {
    return (
        <AdminLayout>
            <Head>
                <title>Orders | Cancellation Requests</title>
            </Head>
            <div className="min-h-screen bg-white">
                <div className="flex-1 space-y-8 p-8 pt-6">
                    {/* Header Section */}
                    <div className="flex flex-col space-y-6">
                        <div className="flex flex-col space-y-2">
                            <h2 className="text-3xl font-bold tracking-tight">Order Cancellations</h2>
                            <p className="text-muted-foreground">
                                Review and manage customer order cancellation requests
                            </p>
                        </div>

                        <div className="flex flex-col md:flex-row gap-4">
                            <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                                <XCircle className="mr-2 h-4 w-4" />
                                <span className="text-primary">Cancellation Requests</span>
                            </Badge>
                        </div>
                    </div>

                    {/* Cancellation Requests Table */}
                    <Card className="hover:shadow-md transition-all">
                        <CardHeader>
                            <CardTitle>Cancellation Requests</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <CancellationRequestsDataTable />
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AdminLayout>
    );
}
