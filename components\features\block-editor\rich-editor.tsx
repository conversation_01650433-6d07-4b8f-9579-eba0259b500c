"use client"

import { useState, useEffect } from "react"
import { useE<PERSON><PERSON>, Editor<PERSON>ontent, FloatingMenu } from "@tiptap/react"
import StarterKit from "@tiptap/starter-kit"
import Placeholder from "@tiptap/extension-placeholder"
import Link from "@tiptap/extension-link"
import Image from "@tiptap/extension-image"
import TextAlign from "@tiptap/extension-text-align"
import { cn } from "@/lib/utils"
import {
    Bold,
    Italic,
    Link as LinkIcon,
    Image as ImageIcon,
    List,
    ListOrdered,
    AlignLeft,
    AlignCenter,
    AlignRight,
    Heading1,
    Heading2,
    Heading3,
    Code,
    Quote,
    Undo,
    Redo,
    Check,
} from "lucide-react"

interface RichEditorProps {
    content?: string
    onChange?: (html: string) => void
    placeholder?: string
    className?: string
    disabled?: boolean
}

export function RichEditor({
    content = "",
    onChange,
    placeholder = "Type '/' for commands...",
    className,
    disabled = false,
}: RichEditorProps) {
    const [linkUrl, setLinkUrl] = useState("")
    const [showLinkForm, setShowLinkForm] = useState(false)
    const [imageUrl, setImageUrl] = useState("")
    const [showImageForm, setShowImageForm] = useState(false)

    const editor = useEditor({
        extensions: [
            StarterKit,
            Placeholder.configure({
                placeholder,
            }),
            Link.configure({
                openOnClick: true,
                linkOnPaste: true,
                protocols: ["http", "https", "mailto", "tel"],
                HTMLAttributes: {
                    class: 'text-blue-600 underline hover:text-blue-800 transition-colors',
                },
            }),
            Image.configure({
                allowBase64: true,
                inline: true,
            }),
            TextAlign.configure({
                types: ["heading", "paragraph"],
                alignments: ["left", "center", "right"],
            }),
        ],
        content,
        editable: !disabled,
        onUpdate: ({ editor }) => {
            onChange?.(editor.getHTML())
        },
        immediatelyRender: true,
    })

    // Update content when it changes from parent component
    useEffect(() => {
        if (editor && content !== editor.getHTML()) {
            editor.commands.setContent(content)
        }
    }, [content, editor])

    const handleLink = () => {
        if (!editor) return

        if (showLinkForm) {
            if (linkUrl) {
                editor
                    .chain()
                    .focus()
                    .extendMarkRange("link")
                    .setLink({ href: linkUrl })
                    .run()
            }
            setShowLinkForm(false)
            setLinkUrl("")
        } else {
            setShowLinkForm(true)
            const previousUrl = editor.getAttributes("link").href
            setLinkUrl(previousUrl || "")
        }
    }

    const handleImage = () => {
        if (!editor) return

        if (showImageForm) {
            if (imageUrl) {
                editor
                    .chain()
                    .focus()
                    .setImage({ src: imageUrl })
                    .run()
            }
            setShowImageForm(false)
            setImageUrl("")
        } else {
            setShowImageForm(true)
        }
    }

    return (
        <div className={cn("rich-editor relative", className)}>
            <div className="mb-2 flex flex-wrap items-center gap-1 rounded-md border border-input bg-background p-1">
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleBold().run()}
                    isActive={editor?.isActive("bold")}
                    icon={<Bold className="h-4 w-4" />}
                    title="Bold"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleItalic().run()}
                    isActive={editor?.isActive("italic")}
                    icon={<Italic className="h-4 w-4" />}
                    title="Italic"
                />
                <ToolbarButton
                    onClick={handleLink}
                    isActive={editor?.isActive("link") || showLinkForm}
                    icon={<LinkIcon className="h-4 w-4" />}
                    title="Link"
                />
                <ToolbarButton
                    onClick={handleImage}
                    isActive={showImageForm}
                    icon={<ImageIcon className="h-4 w-4" />}
                    title="Image"
                />
                <ToolbarSeparator />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleHeading({ level: 1 }).run()}
                    isActive={editor?.isActive("heading", { level: 1 })}
                    icon={<Heading1 className="h-4 w-4" />}
                    title="Heading 1"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
                    isActive={editor?.isActive("heading", { level: 2 })}
                    icon={<Heading2 className="h-4 w-4" />}
                    title="Heading 2"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
                    isActive={editor?.isActive("heading", { level: 3 })}
                    icon={<Heading3 className="h-4 w-4" />}
                    title="Heading 3"
                />
                <ToolbarSeparator />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().setTextAlign("left").run()}
                    isActive={editor?.isActive({ textAlign: "left" })}
                    icon={<AlignLeft className="h-4 w-4" />}
                    title="Align Left"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().setTextAlign("center").run()}
                    isActive={editor?.isActive({ textAlign: "center" })}
                    icon={<AlignCenter className="h-4 w-4" />}
                    title="Align Center"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().setTextAlign("right").run()}
                    isActive={editor?.isActive({ textAlign: "right" })}
                    icon={<AlignRight className="h-4 w-4" />}
                    title="Align Right"
                />
                <ToolbarSeparator />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleBulletList().run()}
                    isActive={editor?.isActive("bulletList")}
                    icon={<List className="h-4 w-4" />}
                    title="Bullet List"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleOrderedList().run()}
                    isActive={editor?.isActive("orderedList")}
                    icon={<ListOrdered className="h-4 w-4" />}
                    title="Ordered List"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleBlockquote().run()}
                    isActive={editor?.isActive("blockquote")}
                    icon={<Quote className="h-4 w-4" />}
                    title="Blockquote"
                />
                <ToolbarButton
                    onClick={() => editor?.chain().focus().toggleCode().run()}
                    isActive={editor?.isActive("code")}
                    icon={<Code className="h-4 w-4" />}
                    title="Code"
                />
                <div className="ml-auto flex items-center gap-1">
                    <ToolbarButton
                        onClick={() => editor?.chain().focus().undo().run()}
                        disabled={!editor?.can().undo()}
                        icon={<Undo className="h-4 w-4" />}
                        title="Undo"
                    />
                    <ToolbarButton
                        onClick={() => editor?.chain().focus().redo().run()}
                        disabled={!editor?.can().redo()}
                        icon={<Redo className="h-4 w-4" />}
                        title="Redo"
                    />
                </div>
            </div>

            {showLinkForm && (
                <div className="mb-2 flex items-center gap-2 rounded-md border border-input bg-background p-2">
                    <input
                        type="url"
                        value={linkUrl}
                        onChange={(e) => setLinkUrl(e.target.value)}
                        placeholder="https://example.com"
                        className="flex-1 bg-transparent p-1 text-sm outline-none"
                    />
                    <button
                        onClick={handleLink}
                        className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-primary p-1 text-primary-foreground hover:bg-primary/90"
                    >
                        <Check className="h-4 w-4" />
                    </button>
                </div>
            )}

            {showImageForm && (
                <div className="mb-2 flex items-center gap-2 rounded-md border border-input bg-background p-2">
                    <input
                        type="url"
                        value={imageUrl}
                        onChange={(e) => setImageUrl(e.target.value)}
                        placeholder="https://example.com/image.jpg"
                        className="flex-1 bg-transparent p-1 text-sm outline-none"
                    />
                    <button
                        onClick={handleImage}
                        className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-primary p-1 text-primary-foreground hover:bg-primary/90"
                    >
                        <Check className="h-4 w-4" />
                    </button>
                </div>
            )}

            <EditorContent
                editor={editor}
                className={cn(
                    "prose max-w-none min-h-[200px] dark:prose-invert focus:outline-none overflow-hidden",
                    "w-full rounded-md border border-input bg-background p-3",
                    disabled && "cursor-not-allowed opacity-50",
                    // Style for links within the editor content
                    "[&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:transition-colors",
                )}
            />

            {editor && (
                <FloatingMenu
                    editor={editor}
                    tippyOptions={{ duration: 100 }}
                    className="flex overflow-hidden rounded-md border border-border bg-background shadow-md"
                >
                    <FloatingButton
                        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                        icon={<Heading1 className="h-4 w-4" />}
                    />
                    <FloatingButton
                        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                        icon={<Heading2 className="h-4 w-4" />}
                    />
                    <FloatingButton
                        onClick={() => editor.chain().focus().toggleBulletList().run()}
                        icon={<List className="h-4 w-4" />}
                    />
                    <FloatingButton
                        onClick={() => editor.chain().focus().toggleOrderedList().run()}
                        icon={<ListOrdered className="h-4 w-4" />}
                    />
                    <FloatingButton
                        onClick={() => editor.chain().focus().toggleBlockquote().run()}
                        icon={<Quote className="h-4 w-4" />}
                    />
                </FloatingMenu>
            )}
        </div>
    )
}

interface ToolbarButtonProps {
    onClick: () => void
    isActive?: boolean
    disabled?: boolean
    icon: React.ReactNode
    title: string
}

function ToolbarButton({ onClick, isActive, disabled, icon, title }: ToolbarButtonProps) {
    return (
        <button
            type="button"
            onClick={onClick}
            disabled={disabled}
            className={cn(
                "inline-flex h-8 w-8 items-center justify-center rounded-md p-1 text-sm font-medium transition-colors",
                "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
                "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
                "disabled:pointer-events-none disabled:opacity-50",
                isActive && "bg-neutral-200 text-neutral-900 dark:bg-neutral-700 dark:text-neutral-50",
            )}
            title={title}
        >
            {icon}
        </button>
    )
}

function ToolbarSeparator() {
    return <div className="mx-1 h-6 w-px bg-border" />
}

interface FloatingButtonProps {
    onClick: () => void
    icon: React.ReactNode
}

function FloatingButton({ onClick, icon }: FloatingButtonProps) {
    return (
        <button
            type="button"
            onClick={onClick}
            className="p-2 hover:bg-neutral-100 dark:hover:bg-neutral-800"
        >
            {icon}
        </button>
    )
}

export default RichEditor 