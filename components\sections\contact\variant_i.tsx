import { PortableText, PortableTextComponents } from "@portabletext/react";
import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { SocialIcons } from "@stackshift-ui/social-icons";
import React from "react";
import { IoLocationSharp } from "react-icons/io5";
import { FaPhoneAlt, FaFax, FaBusinessTime } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import { ContactProps } from ".";
import { thankYouPageLink } from "../../../helper";
import { textComponentBlockStyling } from "./variant_g";

// Helper function to clean field names by removing the (Bill To) or (Ship To) suffix
const cleanFieldName = (fieldName: string | undefined): string => {
  if (!fieldName) return "";
  return fieldName
    .replace(/\s*\(Bill To\)\s*/g, "")
    .replace(/\s*\(Ship To\)\s*/g, "")
    .trim();
};

export default function Contact_I({
  officeInformation,
  contactEmail,
  contactNumber,
  officeHours,
  contactFaxNumber,
  socialLinks,
  form,
  block, // Added block prop
  title,
}: ContactProps) {
  return (
    <Section className="pt-10 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Heading type="h2" className="text-center mb-10">
          {title}
        </Heading>

        <div className="flex flex-col md:flex-row bg-transparent rounded-xl overflow-hidden shadow-lg mx-auto">
          {/* Left sidebar with contact information */}
          <div className="w-full md:w-80 bg-primary p-8 flex flex-col rounded-l-xl">
            <h2 className="text-xl font-medium text-white mb-8">
              Contact Information
            </h2>

            {/* Contact information */}
            <div className="space-y-6">
              {officeInformation && (
                <div className="flex items-start">
                  <IoLocationSharp className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Office</h3>
                    <p className="text-white/80">{officeInformation}</p>
                  </div>
                </div>
              )}

              {contactNumber && (
                <div className="flex items-start">
                  <FaPhoneAlt className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Phone</h3>
                    <Link
                      href={`tel:${contactNumber}`}
                      className="text-white/80 hover:text-white"
                    >
                      {contactNumber}
                    </Link>
                  </div>
                </div>
              )}

              {contactFaxNumber && (
                <div className="flex items-start">
                  <FaFax className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Fax</h3>
                    <p className="text-white/80">{contactFaxNumber}</p>
                  </div>
                </div>
              )}

              {contactEmail && (
                <div className="flex items-start">
                  <MdEmail className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Email</h3>
                    <Link
                      href={`mailto:${contactEmail}`}
                      className="text-white/80 hover:text-white"
                    >
                      {contactEmail}
                    </Link>
                  </div>
                </div>
              )}

              {officeHours && (
                <div className="flex items-start">
                  <FaBusinessTime className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">
                      Office Hours
                    </h3>
                    <p className="text-white/80">{officeHours}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Social links */}
            {socialLinks && socialLinks.length > 0 && (
              <div className="mt-8">
                <h3 className="text-white font-medium mb-4">Connect With Us</h3>
                <div className="flex space-x-4">
                  {socialLinks.map((social) => (
                    <Link
                      key={social?._key}
                      aria-label={
                        social?.socialMedia || social?.socialMediaPlatform || ""
                      }
                      href={social?.socialMediaLink ?? "/page-not-found"}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-white/80"
                    >
                      <SocialIcons social={social.socialMedia as any} />
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Main content area - Contact Form */}
          <div className="flex-1 p-8 md:p-12 bg-white/5 backdrop-blur-sm rounded-r-xl">
            <div className="max-w-2xl mx-auto">
              {/* Form Header */}
              {form && (
                <div className="mb-14">
                  {form.name && (
                    <h2 className="text-3xl font-medium text-gray-800 mb-2">
                      {form.name}
                    </h2>
                  )}
                  {/* Replaced form.subtitle with PortableText */}
                  {block && (
                    <div className="text-sm text-gray-600">
                      <PortableText
                        value={block}
                        components={textComponentBlockStyling}
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Contact Form */}
              {form?.fields && (
                <Form
                  id={form?.id ?? undefined}
                  name="Contact-Form"
                  thankyouPage={thankYouPageLink(form?.thankYouPage)}
                >
                  {/* Restructured form with 6 sections */}
                  {form.fields && (
                    <>
                      {/* Section 1: Company Details */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          1. Company Details
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                          {form.fields
                            .slice(0, 4) // Company Details fields (0-3)
                            .map((field, index) => {
                              const isFullWidth = field.type === "textarea";
                              return (
                                <div
                                  key={`company-${index}`}
                                  className={isFullWidth ? "sm:col-span-2" : ""}
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {field.name}
                                  </label>
                                  {field.type === "textarea" ? (
                                    <textarea
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      rows={4}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    ></textarea>
                                  ) : field.type === "inputEmail" ? (
                                    <input
                                      type="email"
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    />
                                  ) : (
                                    <input
                                      type="text"
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    />
                                  )}
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {/* Section 2: Bill To */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          2. Bill To
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                          {form.fields
                            .slice(4, 8) // Bill To fields (5-8)
                            .map((field, index) => {
                              const isFullWidth = field.type === "textarea";
                              return (
                                <div
                                  key={`bill-${index}`}
                                  className={isFullWidth ? "sm:col-span-2" : ""}
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {cleanFieldName(field.name)}
                                  </label>
                                  {field.type === "textarea" ? (
                                    <textarea
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      rows={4}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    ></textarea>
                                  ) : (
                                    <input
                                      type={
                                        field.type === "inputEmail"
                                          ? "email"
                                          : "text"
                                      }
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    />
                                  )}
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {/* Section 3: Ship To */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          3. Ship To
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                          {form.fields
                            .slice(8, 12) // Ship To fields (10-13)
                            .map((field, index) => {
                              const isFullWidth = field.type === "textarea";
                              return (
                                <div
                                  key={`ship-${index}`}
                                  className={isFullWidth ? "sm:col-span-2" : ""}
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {cleanFieldName(field.name)}
                                  </label>
                                  {field.type === "textarea" ? (
                                    <textarea
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      rows={4}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    ></textarea>
                                  ) : (
                                    <input
                                      type={
                                        field.type === "inputEmail"
                                          ? "email"
                                          : "text"
                                      }
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    />
                                  )}
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {/* Section 4: Shipping Details */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          4. Shipping Details
                        </h3>
                        <div className="grid grid-cols-1 gap-6">
                          {form.fields
                            .slice(12, 14) // Shipping Details fields (15-16)
                            .map((field, index) => {
                              return (
                                <div
                                  key={`shipping-${index}`}
                                  className="sm:col-span-2"
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {field.name}
                                  </label>
                                  <textarea
                                    id={field.name}
                                    name={field.name}
                                    placeholder={field.placeholder}
                                    required={field?.isRequired}
                                    rows={4}
                                    className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                  ></textarea>
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {/* Section 5: Return Request Details */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          5. Return Request Details
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                          {form.fields
                            .slice(14, 18) // Return Request Details fields (18-21)
                            .map((field, index) => {
                              const isFullWidth = field.type === "textarea";
                              return (
                                <div
                                  key={`return-request-${index}`}
                                  className={isFullWidth ? "sm:col-span-2" : ""}
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {field.name}
                                  </label>
                                  {field.type === "textarea" ? (
                                    <textarea
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      rows={4}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    ></textarea>
                                  ) : (
                                    <input
                                      type={
                                        field.type === "inputEmail"
                                          ? "email"
                                          : "text"
                                      }
                                      id={field.name}
                                      name={field.name}
                                      placeholder={field.placeholder}
                                      required={field?.isRequired}
                                      className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                    />
                                  )}
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {/* Section 6: Return Reason */}
                      <div className="mb-8">
                        <h3 className="text-lg font-medium text-primary border-b border-gray-200 pb-2 mb-4">
                          6. Return Reason
                        </h3>
                        <div className="grid grid-cols-1 gap-6">
                          {form.fields
                            .slice(18) // Return Reason field (23 to end)
                            .map((field, index) => {
                              return (
                                <div
                                  key={`return-reason-${index}`}
                                  className="sm:col-span-2"
                                >
                                  <label
                                    htmlFor={field.name}
                                    className="block text-left text-md mb-2"
                                  >
                                    {field?.isRequired && (
                                      <span className="text-red-600">*</span>
                                    )}{" "}
                                    {field.name}
                                  </label>
                                  <textarea
                                    id={field.name}
                                    name={field.name}
                                    placeholder={field.placeholder}
                                    required={field?.isRequired}
                                    rows={6}
                                    className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                                  ></textarea>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    </>
                  )}

                  <div className="mt-6">
                    <div className="webriq-recaptcha" />
                  </div>

                  {/* Submit Button */}
                  <div className="border-t border-gray-200 pt-8 mt-8">
                    <div className="flex justify-end">
                      <Button
                        as="button"
                        type="submit"
                        variant="maxtonPrimary"
                        ariaLabel={form?.buttonLabel || "Submit"}
                      >
                        {form?.buttonLabel || "Submit"}
                      </Button>
                    </div>
                  </div>
                </Form>
              )}
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
}

// block styling as props to `components` of the PortableText component
const blockCustomization: PortableTextComponents = {
  marks: {
    internalLink: ({ children, value }) => (
      <Link
        aria-label={value.href ?? "internal link"}
        style={{ color: "red" }}
        href={value.slug.current}
      >
        {children}
      </Link>
    ),
    link: ({ children, value }) =>
      value.blank ? (
        <Link
          aria-label={value.href ?? "external link"}
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      ) : (
        <Link
          aria-label={value.href ?? "external link"}
          style={{ color: "blue" }}
          href={value.href}
        >
          {children}
        </Link>
      ),
  },
};

export { Contact_I };
