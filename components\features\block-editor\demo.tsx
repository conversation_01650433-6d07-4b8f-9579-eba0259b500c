"use client"

import { useState } from "react"
import { use<PERSON><PERSON>, FormProvider } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { BlockEditor } from "./index"
import { RichEditor } from "./rich-editor"
import { FormTipTapEditor, FormEditorExample, TipTapWrapper } from "./form-editor"
import { Button } from "@/components/ui/shadcn-button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const formSchema = z.object({
  content: z.string().min(10, {
    message: "Content must be at least 10 characters",
  }),
})

type FormValues = z.infer<typeof formSchema>

export function TipTapDemo() {
  const [standardContent, setStandardContent] = useState("<p>Edit this content to test the basic editor</p>")
  const [richContent, setRichContent] = useState("<h2>Rich Editor Demo</h2><p>This is a more feature-rich editor with floating menu, link and image support.</p>")

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "<p>This editor is integrated with react-hook-form and zod validation</p>",
    },
  })

  function onSubmit(data: FormValues) {
    alert(JSON.stringify(data, null, 2))
  }

  // Test function to update content dynamically
  const updateContentSamples = () => {
    setStandardContent("<h1>Updated Basic Editor</h1><p>The content was updated programmatically via setState!</p>")
    setRichContent("<h1>Updated Rich Editor</h1><p>This content was updated from outside the component.</p><ul><li>It should reflect immediately</li><li>Without losing focus</li></ul>")
    form.setValue("content", "<h2>Form Updated</h2><p>This form field was updated programmatically with form.setValue</p>", {
      shouldValidate: true,
      shouldDirty: true
    })
  }

  return (
    <div className="container mx-auto max-w-4xl py-10">
      <h1 className="mb-6 text-3xl font-bold">TipTap Editor Components</h1>

      <div className="mb-6">
        <Button onClick={updateContentSamples}>
          Test Content Update
        </Button>
        <p className="mt-2 text-sm text-neutral-500">
          Click this button to test updating content programmatically in all editors
        </p>
      </div>

      <Tabs defaultValue="basic">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Editor</TabsTrigger>
          <TabsTrigger value="rich">Rich Editor</TabsTrigger>
          <TabsTrigger value="form">Form Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6 space-y-6">
          <div className="rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">Basic TipTap Editor</h2>
            <p className="mb-4 text-sm text-neutral-500">
              A simple implementation with bubble menu that appears when text is selected
            </p>
            <BlockEditor />
          </div>
        </TabsContent>

        <TabsContent value="rich" className="mt-6 space-y-6">
          <div className="rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">Rich TipTap Editor</h2>
            <p className="mb-4 text-sm text-neutral-500">
              Enhanced editor with fixed toolbar, floating menu, link and image support
            </p>
            <RichEditor
              content={richContent}
              onChange={setRichContent}
              placeholder="Start creating rich content..."
            />
            <div className="mt-4 rounded-md bg-neutral-100 p-4 dark:bg-neutral-800">
              <h3 className="mb-2 text-sm font-medium">HTML Output</h3>
              <pre className="text-xs">{richContent}</pre>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="form" className="mt-6 space-y-6">
          <div className="rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">Form Integration</h2>
            <p className="mb-4 text-sm text-neutral-500">
              TipTap editor integrated with react-hook-form and zod validation
            </p>

            <FormProvider {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormTipTapEditor
                  name="content"
                  label="Content"
                  placeholder="Enter at least 10 characters to pass validation..."
                />
                <Button type="submit">Submit Form</Button>
              </form>
            </FormProvider>

            <div className="mt-6">
              <h3 className="mb-2 text-sm font-medium">Form State</h3>
              <pre className="rounded-md bg-neutral-100 p-4 text-xs dark:bg-neutral-800">
                {JSON.stringify({
                  values: form.getValues(),
                  errors: form.formState.errors,
                  isDirty: form.formState.isDirty,
                  isValid: form.formState.isValid,
                }, null, 2)}
              </pre>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="mt-10 rounded-lg border p-6">
        <FormEditorExample />
      </div>
    </div>
  )
}

export default TipTapDemo 