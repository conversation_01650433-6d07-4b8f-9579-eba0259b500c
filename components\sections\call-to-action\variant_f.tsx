import { useState } from "react";
import { Container } from "@stackshift-ui/container";
import { Section } from "@stackshift-ui/section";
import { Form } from "@stackshift-ui/form";
import { Input } from "@stackshift-ui/input";
import { Select } from "@stackshift-ui/select";
import { Button } from "@stackshift-ui/button";
import { useValveCalculator } from "../../../hooks/useValveCalculator";
import type { ValveData, FlashMessage } from "../../../types/valve";
import { PortableTextBlock } from "@sanity/types";
import { MyPortableTextComponents } from "types";
import { CTAProps } from ".";
import { Heading } from "@stackshift-ui/heading";
import { PortableText } from "@portabletext/react";
import { Text } from "@stackshift-ui/text";
import { Flex } from "@stackshift-ui/flex";
import { useRouter } from "next/router";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-xl  text-gray-800">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addTable: ({ value }) => {
      if (!value?.columns || !value?.rows) {
        console.error("Missing table data:", value);
        return null;
      }

      return (
        <div className="overflow-x-auto my-6">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {value.columns.map(
                  (column: { title: string }, index: number) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-semibold text-gray-800 uppercase tracking-wider"
                    >
                      {column.title}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {value.rows.map((row: { cells: string[] }, rowIndex: number) => (
                <tr key={rowIndex}>
                  {row.cells.map((cell: string, cellIndex: number) => (
                    <td
                      key={cellIndex}
                      className="px-6 py-4 text-xs md:text-sm text-gray-500"
                      dangerouslySetInnerHTML={{
                        __html: cell
                          .replace(
                            /\n\n/g,
                            "<hr class='my-2 border-gray-300' />"
                          )
                          .replace(/\n/g, "<br />"),
                      }}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    },
  },
};

export default function CallToAction_F({ title, firstColumn }: CTAProps) {
  const [showResults, setShowResults] = useState(false);
  const [units, setUnits] = useState<"inch" | "metric">("inch");
  const {
    calculateValve,
    calculationResults,
    resetCalculator,
    upperFlashMessages,
    lowerFlashMessages,
    activeCalculator,
  } = useValveCalculator();

  const handleSubmit = (formData: ValveData) => {
    const results = calculateValve(formData);
    if (results) {
      setShowResults(true);
    }
  };

  const router = useRouter();

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        {title &&
          (router.asPath === "/calculator" ? (
            <Heading className="mb-16 text-center text-3xl md:text-5xl">
              {title}
            </Heading>
          ) : (
            <Heading
              type="h2"
              className="mb-16 text-center text-3xl md:text-4xl"
            >
              {title}
            </Heading>
          ))}
        <Flex className="flex-col md:flex-row " gap={8}>
          <div className="w-full md:w-1/3">
            <div className="prose">
              <PortableText
                value={firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          </div>

          <div className="w-full md:w-2/3 md:pl-10">
            <div className="space-y-8">
              {/* Upper Calculator */}
              <div className="p-6 bg-white rounded-lg shadow-md">
                {upperFlashMessages.length > 0 && (
                  <div className="p-4 mb-4 bg-red-100 rounded">
                    {upperFlashMessages.map((msg, i) => (
                      <p key={i} className="text-red-600">
                        {msg.message}
                      </p>
                    ))}
                  </div>
                )}

                {router.asPath === "/calculator" ? (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h2"
                  >
                    Upper Calculator
                  </Heading>
                ) : (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h3"
                  >
                    Upper Calculator
                  </Heading>
                )}

                <div className="mb-6">
                  <select
                    value={units}
                    onChange={(e) =>
                      setUnits(e.target.value as "inch" | "metric")
                    }
                    className="w-full p-2 border rounded"
                  >
                    <option value="inch">English Units</option>
                    <option value="metric">Metric Units</option>
                  </select>
                </div>
                <UpperCalculatorForm units={units} onSubmit={handleSubmit} />
                {showResults && calculationResults && (
                  <div className="mt-8 border-t pt-6">
                    {activeCalculator === "upper" &&
                      calculationResults.formType === "form1" && (
                        <ValveCalculatorResults
                          results={calculationResults}
                          units={units}
                          onReset={() => {
                            resetCalculator();
                            setShowResults(false);
                          }}
                        />
                      )}
                  </div>
                )}
              </div>

              {/* Lower Calculator */}
              <div className="p-6 bg-white rounded-lg shadow-md">
                {lowerFlashMessages.length > 0 && (
                  <div className="p-4 mb-4 bg-red-100 rounded">
                    {lowerFlashMessages.map((msg, i) => (
                      <p key={i} className="text-red-600">
                        {msg.message}
                      </p>
                    ))}
                  </div>
                )}
                {router.asPath === "/calculator" ? (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h2"
                  >
                    Lower Calculator
                  </Heading>
                ) : (
                  <Heading
                    fontSize="2xl"
                    weight="bold"
                    className="mb-4 !text-gray-800"
                    type="h3"
                  >
                    Lower Calculator
                  </Heading>
                )}

                <div className="mb-6">
                  <select
                    value={units}
                    onChange={(e) =>
                      setUnits(e.target.value as "inch" | "metric")
                    }
                    className="w-full p-2 border rounded"
                  >
                    <option value="inch">English Units</option>
                    <option value="metric">Metric Units</option>
                  </select>
                </div>
                <LowerCalculatorForm units={units} onSubmit={handleSubmit} />
                {showResults && calculationResults && (
                  <div className="mt-8 border-t pt-6">
                    {activeCalculator === "lower" &&
                      calculationResults.formType !== "form1" && (
                        <ValveCalculatorResults
                          results={calculationResults}
                          units={units}
                          onReset={() => {
                            resetCalculator();
                            setShowResults(false);
                          }}
                        />
                      )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function UpperCalculatorForm({
  units,
  onSubmit,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
}) {
  const handleReset = () => {
    const form = document.querySelector("form") as HTMLFormElement;
    form?.reset();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form1",
      units,
      jackType: formData.get("jackType") as "direct" | "roped",
      numJack: Number(formData.get("numJack")),
      jackDiameter: Number(formData.get("jackDiameter")),
      carSpeed: Number(formData.get("carSpeed")),
      emptyWeight: Number(formData.get("emptyWeight")),
      emptyWeightUnits: formData.get("emptyWeightUnits") as
        | "PSI"
        | "lbs."
        | "kg"
        | "kg/cm2",
      capacity: Number(formData.get("capacity")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Upper calculator fields */}
      <select name="jackType" required className="w-full p-2 border rounded">
        <option value="">Select Jack Type</option>
        <option value="direct">Direct</option>
        <option value="roped">Roped</option>
      </select>

      <select name="numJack" required className="w-full p-2 border rounded">
        <option value="">Select Jack Number</option>
        <option value="1">Single Jack</option>
        <option value="2">Dual Jack</option>
      </select>

      <input
        type="number"
        name="jackDiameter"
        placeholder={`Piston Diameter (${units === "inch" ? "in" : "mm"})`}
        required
        className="w-full p-2 border rounded"
        step="any"
      />

      <input
        type="number"
        name="carSpeed"
        placeholder={`Car Speed (${units === "inch" ? "FPM" : "m/s"})`}
        required
        className="w-full p-2 border rounded"
        step="any"
      />

      <div className="flex gap-2">
        <input
          type="number"
          name="emptyWeight"
          placeholder="Empty Car Weight"
          required
          className="flex-1 p-2 border rounded"
          step="any"
        />
        <select name="emptyWeightUnits" className="w-32 p-2 border rounded">
          {units === "inch" ? (
            <>
              <option value="lbs.">lbs.</option>
              <option value="PSI">PSI</option>
            </>
          ) : (
            <>
              <option value="kg">kg</option>
              <option value="kg/cm2">kg/cm²</option>
            </>
          )}
        </select>
      </div>

      <input
        type="number"
        name="capacity"
        placeholder={`Capacity (${units === "inch" ? "lbs" : "kg"})`}
        className="w-full p-2 border rounded"
        step="any"
      />

      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="upperDSR"
          value="true"
          className="w-4 h-4"
        />
        <label htmlFor="upperDSR">Down Speed Regulation</label>
      </div>

      <div className="flex gap-4">
        <Button type="submit" variant="solid" ariaLabel="Size Valve">
          Size Valve
        </Button>

        <Button
          type="button"
          variant="solid"
          onClick={handleReset}
          ariaLabel="Reset"
          className="!bg-secondary !text-white"
        >
          Reset
        </Button>
      </div>
    </form>
  );
}

function LowerCalculatorForm({
  units,
  onSubmit,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
}) {
  const handleReset = () => {
    const form = document.querySelector("form") as HTMLFormElement;
    form?.reset();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form2",
      units,
      emptyStaticPressure: Number(formData.get("emptyStaticPressure")),
      loadedCarPressure: Number(formData.get("loadedCarPressure")),
      ratedFlow: Number(formData.get("ratedFlow")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input
        type="number"
        name="emptyStaticPressure"
        placeholder={`Empty Static Pressure (${
          units === "inch" ? "PSI" : "kg/cm²"
        })`}
        required
        className="w-full p-2 border rounded"
        step="any"
      />

      <input
        type="number"
        name="loadedCarPressure"
        placeholder={`Loaded Car Pressure (${
          units === "inch" ? "PSI" : "kg/cm²"
        })`}
        required
        className="w-full p-2 border rounded"
        step="any"
      />

      <input
        type="number"
        name="ratedFlow"
        placeholder={`Rated Flow (${units === "inch" ? "GPM" : "LPM"})`}
        required
        className="w-full p-2 border rounded"
        step="any"
      />

      <div className="flex items-center gap-2 mb-4">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="lowerDSR"
          value="true"
          className="w-4 h-4"
        />
        <label htmlFor="lowerDSR">Down Speed Regulation</label>
      </div>

      <div className="flex gap-4">
        <Button type="submit" variant="solid" ariaLabel="Size Valve">
          Size Valve
        </Button>
        <Button
          type="button"
          variant="solid"
          onClick={handleReset}
          ariaLabel="Reset"
          className="!bg-secondary !text-white"
        >
          Reset
        </Button>
      </div>
    </form>
  );
}

function ValveCalculatorResults({
  results,
  units,
  onReset,
}: {
  results: ValveData | null;
  units: "inch" | "metric";
  onReset: () => void;
}) {
  if (!results) return null;

  return (
    <Container maxWidth={1280}>
      <Heading fontSize="xl" weight="bold" className="mb-4" type="h3">
        Provided data:
      </Heading>
      <Flex direction="col" className="md:gap-2 mb-6">
        {results.formType === "form1" ? (
          <>
            <Flex>
              <Text className="!font-bold">Jack Type:</Text>
              <Text className="ml-2">
                {results.jackType === "direct" ? "Direct" : "Roped"}{" "}
                {results.numJack === 1 ? "Single" : "Dual"} Jack
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Piston Dia.:</Text>
              <Text className="ml-2">
                {results.jackDiameter} {units === "inch" ? "in." : "mm"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Car Speed:</Text>
              <Text className="ml-2">
                {results.carSpeed} {units === "inch" ? "FPM" : "m/sec"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Empty Car:</Text>
              <Text className="ml-2">
                {results.emptyWeight}
                {results.emptyWeightUnits}
              </Text>
            </Flex>
            {results.capacity && (
              <Flex>
                <Text className="!font-bold">Capacity:</Text>
                <Text className="ml-2">
                  {results.capacity} {units === "inch" ? "lbs" : "kg"}
                </Text>
              </Flex>
            )}
          </>
        ) : (
          <>
            <Flex>
              <Text className="!font-bold">Rated Flow:</Text>
              <Text className="ml-2">
                {results.ratedFlow} {units === "inch" ? "GPM" : "LPM"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Empty Static Pressure:</Text>
              <Text className="ml-2">
                {results.emptyStaticPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </Text>
            </Flex>
            <Flex>
              <Text className="!font-bold">Loaded Static Pressure:</Text>
              <Text className="ml-2">
                {results.loadedCarPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </Text>
            </Flex>
          </>
        )}
        <Flex>
          <Text className="!font-bold">Down Speed Regulation:</Text>
          <Text className="ml-2">
            {results.downSpeedRegulation ? "Yes" : "No"}
          </Text>
        </Flex>
      </Flex>

      <Heading fontSize="xl" weight="bold" className="mb-4" type="h3">
        Results:
      </Heading>
      <Flex direction="col" className="md:gap-2 mb-6">
        <Flex>
          <Text className="!font-bold">Rated Flow:</Text>
          <Text className="ml-2 text-blue-600">{results.sngGPM} GPM</Text>
        </Flex>
        <Flex>
          <Text className="!font-bold">Empty Static Pressure:</Text>
          <Text className="ml-2 text-blue-600">{results.sngOutMinPSI} PSI</Text>
        </Flex>
        <Flex>
          <Text className="!font-bold">Loaded Car Pressure:</Text>
          <Text className="ml-2 text-blue-600">{results.sngOutMaxPSI} PSI</Text>
        </Flex>
      </Flex>

      <Flex direction="col" className="md:gap-2 mb-6">
        <Heading fontSize="xl" weight="bold" className="mb-2" type="h3">
          Valve Recommendations:
        </Heading>
        <Text as="ul" className="list-disc pl-6">
          {results.products?.map((product, index) => (
            <Text as="li" key={index} className="text-blue-600">
              {product.text}
            </Text>
          ))}
        </Text>
      </Flex>

      <Button
        onClick={onReset}
        className="mt-6 px-4 py-2"
        variant="solid"
        ariaLabel="Calculate another valve"
      >
        Calculate Another
      </Button>
    </Container>
  );
}
