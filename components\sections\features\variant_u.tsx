import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem, Images } from "../../../types";
import { ButtonProps } from "../header";
import { Button, Card } from "components/ui";
import { FaArrowRightLong } from "react-icons/fa6";
import Image from "next/image";
import Marquee from "react-fast-marquee";

export default function Features_U({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
  mainImage,
  images,
}: FeaturesProps) {
  return (
    <Section
      className="relative py-24"
      style={{
        backgroundImage: `linear-gradient(rgba(1, 84, 162, 0.9) 0%, rgba(1, 84, 162, 0.5) 40%, rgba(1, 84, 162, 0.0) 60%, rgba(1, 84, 162, 0.5) 85%, rgba(1, 84, 162, 0.9) 100%), url(${mainImage?.image})`,
        // backgroundAttachment: "fixed",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "bottom",
      }}
    >
      <Container maxWidth={1280} className="z-50">
        <div className="flex flex-col gap-x-5 justify-between items-center">
          <div className="w-full flex flex-col justify-center text-center max-w-xl mb-8">
            <CaptionAndTitleSection
              caption={caption}
              title={title}
              description={description}
            />
          </div>

          <div className="w-full lg:w-2/3 mb-20">
            <FeatureItems features={featuredItems} />
          </div>

          <div className="relative max-w-5xl mx-auto overflow-hidden">
            {/* <div className="absolute left-0 top-0 h-full w-16 bg-gradient-to-r from-primary to-transparent pointer-events-none"></div> */}

            <Flex
              wrap
              justify="center"
              align="center"
              className="w-full"
              gap={3}
            >
              <LogoCloudImages images={images} />
            </Flex>

            {/* <div className="absolute right-0 top-0 h-full w-16 bg-gradient-to-l from-primary to-transparent pointer-events-none"></div> */}
          </div>
        </div>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Heading
          type="h3"
          className="text-lg lg:text-xl uppercase font-semibold text-white mb-2"
        >
          {caption}
        </Heading>
      )}

      {title && (
        <div className="">
          <Heading
            type="h2"
            className="!text-white text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
          >
            {title}
          </Heading>
        </div>
      )}
      {description && (
        <Text className="mb-4 text-white text-base">{description}</Text>
      )}
    </>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="flex flex-wrap justify-start gap-y-5 lg:gap-y-0">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </div>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  return (
    <div className="w-full px-4 md:w-1/2">
      <Card variant="normalCard" className="h-full md:h-full">
        {feature?.mainImage?.image && (
          <Image
            src={feature?.mainImage.image}
            width={50}
            height={50}
            alt={feature.mainImage.alt ?? `features-image-`}
          />
        )}

        <div className="flex flex-col h-full">
          <div className="flex-1">
            <Text
              fontSize="xl"
              weight="bold"
              className="text-gray-800 uppercase mb-3"
            >
              {feature.title}
            </Text>
            <Text muted className="text-sm text-gray-700">
              {feature.description}
            </Text>
          </div>

          {feature?.primaryButton?.label ? (
            <div className="pt-6">
              <Button
                as="link"
                variant="maxtonPrimary"
                link={feature?.primaryButton}
                ariaLabel={feature?.primaryButton?.label}
                size="lg"
              >
                <span>{feature?.primaryButton?.label}</span>
                <FaArrowRightLong />
              </Button>
            </div>
          ) : null}
        </div>
      </Card>
    </div>
  );
}

function LogoCloudImages({ images }: { images?: Images[] }) {
  if (!images) return null;

  return (
    <React.Fragment>
      {/* <Marquee speed={50} autoFill={true} className="logo-marquee gap-20"> */}
      {images?.map((image, index) => (
        <div key={index}>
          {image?.image ? (
            <Flex
              align="center"
              justify="center"
              className="h-[192px] w-[192px]"
            >
              <Image
                className="object-scale-down brightness-0 invert-[1]"
                src={`${image?.image}`}
                sizes="192px"
                width={192}
                height={192}
                alt={image?.alt ?? `logoCloud-image${index}`}
              />
            </Flex>
          ) : null}
        </div>
      ))}
      {/* </Marquee> */}
    </React.Fragment>
  );
}
export { Features_U };
