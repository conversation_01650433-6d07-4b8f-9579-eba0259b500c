import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button } from "components/ui";
import { FaArrowRightLong } from "react-icons/fa6";

export default function Features_K({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
}: FeaturesProps) {
  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        <div className="flex flex-col lg:flex-row gap-x-5 justify-between items-center mb-8">
          <div className="w-full lg:w-1/3 flex flex-col justify-center text-center lg:text-left max-w-xl lg:max-w-sm mb-8 lg:mb-0">
            <CaptionAndTitleSection
              caption={caption}
              title={title}
              description={description}
            />
          </div>

          <div className="w-full lg:w-2/3">
            <FeatureItems features={featuredItems} />
          </div>
        </div>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Text fontSize="sm" className="text-gray-600 mb-2">
          {caption}
        </Text>
      )}
      {title && (
        <Heading fontSize="3xl" type="h2" className="mb-4">
          {title}
        </Heading>
      )}
      {description && (
        <Text fontSize="md" className="">
          {description}
        </Text>
      )}
    </>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="flex flex-wrap justify-start gap-y-5 lg:gap-y-0">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </div>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  return (
    <div className="w-full px-4 md:w-1/2">
      <div className="flex flex-col h-full">
        <div className="rounded-lg mb-5">
          {feature?.mainImage?.image && (
            <Image
              className="object-cover h-[250px] w-full border-primary border-[1px]"
              src={feature?.mainImage.image}
              width={350}
              height={350}
              alt={feature.mainImage.alt ?? `features-image-`}
            />
          )}
        </div>

        <div className="flex flex-col h-full">
          <div className="flex-1">
            <Text fontSize="xl" weight="bold" className="text-gray-800 mb-3">
              {feature.title}
            </Text>
            <Text muted className="text-sm text-gray-700">
              {feature.description}
            </Text>
          </div>

          {feature?.primaryButton?.label ? (
            <div className="pt-6">
              <Button
                as="link"
                link={feature?.primaryButton}
                ariaLabel={
                  feature?.primaryButton?.ariaLabel ??
                  feature?.primaryButton?.label
                }
                size="lg"
                className="text-primary flex items-center space-x-2 transition-all duration-200 hover:text-primary hover:scale-110 origin-left"
              >
                <span>{feature?.primaryButton?.label}</span>
                <FaArrowRightLong />
              </Button>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

export { Features_K };
