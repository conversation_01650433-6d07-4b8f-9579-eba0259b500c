import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    POST: checkPermission("create:groups", bulkCreateGroupsHandler),
  })
);

export const bulkCreateGroupsSchema = z.object({
  groups: z.array(
    z.object({
      name: z.string().min(1, "Name is required"),
      description: z.string().optional(),
    })
  ),
});

export type BulkCreateGroupsRequest = z.infer<typeof bulkCreateGroupsSchema>;

export interface BulkCreateGroupsResponse {
  error?: string;
  data?: any;
}

async function bulkCreateGroupsHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<BulkCreateGroupsResponse>
) {
  const parsedData = bulkCreateGroupsSchema.safeParse(req.body);

  if (!parsedData.success) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const { groups } = parsedData.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .from("groups")
    .insert(groups);

  if (error) {
    console.error("Error creating groups:", error);
    return res.status(400).json({ error: "Failed to create groups" });
  }

  return res.status(200).json({ data });
}
