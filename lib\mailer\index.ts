import nodemailer from "nodemailer";

export interface MailerOptions {
    host: string;
    port: number;
    secure: boolean;
    debug?: boolean;
    auth: MailerAuth
}

interface MailerAuth {
    user: string;
    pass: string;
}

export interface MailerSenderOptions {
    from: string;
    to: string | string[];
    subject: string;
    cc?: string | string[];
    bcc?: string | string[];
    html: string;
    attachments?: any[];
}

export interface SendMailOption {
    to: string | string[];
    from: string;
    subject: string;
    cc?: string | string[];
    bcc?: string | string[];
    body: string;
    attachments?: any[];
}

export interface MailerClient {
    sendMail(options: MailerSenderOptions): Promise<any>;
}

export function createMailerOptions(): MailerOptions {
    const MAIL_HOST = process.env.MAIL_HOST ?? "smtp.zeptomail.com";
    const MAIL_PORT = process.env.MAIL_PORT ?? 587;
    const MAIL_SECURE = process.env.MAIL_SECURE === "true";
    const MAIL_USER = process.env.MAIL_USER ?? "";
    const MAIL_PASS = process.env.MAIL_PASS ?? "";

    return {
        host: MAIL_HOST,
        port: Number(MAIL_PORT),
        secure: MAIL_SECURE,
        auth: {
            user: MAIL_USER,
            pass: MAIL_PASS,
        }
    }
}

export function createMailer(options: MailerOptions): MailerClient {
    const client = nodemailer.createTransport(options);

    return client;
}