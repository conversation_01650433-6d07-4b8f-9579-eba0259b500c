import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import Image from "next/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableTextBlock } from "sanity";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";
import ValveCalculator from "../../calculator/sideCalculator";
import { useRouter } from "next/router";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-6 leading-loose text-5xl sm:text-7xl">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return (
        <Heading type="h2" className="mb-4 text-xl">
          {children}
        </Heading>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-6 text-xl md:text-3xl leading-loose text-gray-900">
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-2 text-base text-start sm:text-xl font-semibold leading-loose text-gray-900">
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <Text className="mb-3 font-body text-sm sm:text-base text-gray-900 lg:text-left text-balance text-left">
          {children}
        </Text>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-2 w-fit leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-secondary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Header_Q({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  firstColumn,
}: HeaderProps) {
  const router = useRouter();
  const showCalculator = router.asPath === "/mobile-mechanic";

  return (
    <Section className="py-24 bg-background">
      <Container maxWidth={1280}>
        {showCalculator ? (
          <Flex direction="col" justify="between" className="lg:flex-row">
            <div className="relative w-full lg:w-[70%] lg:pr-8 lg:border-r border-gray-300">
              <Flex
                align="start"
                justify="start"
                className="md:gap-10 gap-20 flex-col md:flex-row"
              >
                <Flex
                  align="start"
                  justify="start"
                  direction="col"
                  className="w-full"
                >
                  <div className="w-full text-center lg:text-left">
                    <TitleAndDescription
                      title={title}
                      description={description}
                      firstColumn={firstColumn}
                    />

                    <Buttons
                      primaryButton={primaryButton}
                      secondaryButton={secondaryButton}
                    />
                  </div>
                </Flex>

                <MainImage mainImage={mainImage} />
              </Flex>
            </div>
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
              <ValveCalculator />
            </div>
          </Flex>
        ) : (
          <Flex
            align="start"
            justify="start"
            className="md:gap-10 gap-20 flex-col md:flex-row"
          >
            <Flex
              align="start"
              justify="start"
              direction="col"
              className="w-full"
            >
              <div className="w-full text-center lg:text-left">
                <TitleAndDescription
                  title={title}
                  description={description}
                  firstColumn={firstColumn}
                />

                <Buttons
                  primaryButton={primaryButton}
                  secondaryButton={secondaryButton}
                />
              </div>
            </Flex>

            <MainImage mainImage={mainImage} />
          </Flex>
        )}
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  firstColumn,
  description,
}: {
  title?: string;
  description?: string;
  firstColumn?: PortableTextBlock[];
}) {
  return (
    <Flex gap={10} className="w-full flex flex-col">
      <div className="border-b border-gray-300">
        {title && (
          <Heading
            type="h2"
            className="!text-black text-left text-2xl md:text-2xl lg:text-[38px] uppercase font-extrabold mb-4"
          >
            {title}
          </Heading>
        )}

        {description && (
          <Text className="mb-4 text-lg text-left">{description}</Text>
        )}
        <div className="w-10 h-2 bg-primary" />
      </div>

      {firstColumn && (
        <div className="mb-2 text-xs md:mb-0 lg:text-base">
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </Flex>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="start"
      justify="start"
      gap={2}
      direction="col"
      className="justify-start items-start !flex-row"
    >
      {primaryButton?.label && (
        <Button
          variant="unstyled"
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.label}
          className="hover:opacity-90"
        >
          <Image
            src="/assets/elements/downloads/download.png"
            alt="Maxton App store"
            width={160}
            height={130}
            quality={100}
            priority
          />
          {/* {primaryButton?.label} */}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          variant="unstyled"
          className="hover:opacity-90"
          link={secondaryButton}
          ariaLabel={secondaryButton?.label}
        >
          <Image
            src="/assets/elements/downloads/googleplay.png"
            alt="Maxton App store"
            width={140}
            height={100}
            quality={100}
            priority
          />
          {/* {secondaryButton?.label} */}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full flex items-center justify-center">
      <Image
        className="object-cover relative"
        src={`${mainImage.image}`}
        sizes="(min-width: 420px) 448px, 80vw"
        width={448}
        height={448}
        alt={mainImage.alt ?? "header-main-image"}
        property=""
        quality={100}
      />
    </div>
  );
}

export { Header_Q };
