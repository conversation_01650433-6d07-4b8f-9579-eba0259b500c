import React, { lazy, Suspense } from "react";
import dynamic from "next/dynamic";
import { Testimonial as ITestimonial, SectionsProps, MainImage } from "types";

// import TestimonialA from "./testimonial_a";
// import TestimonialB from "./testimonial_b";
// import TestimonialC from "./testimonial_c";
// import TestimonialD from "./testimonial_d";
import * as TestimonialsVariant from "@stackshift-ui/testimonial";

export interface TestimonialProps {
  caption?: string;
  title?: string;
  testimonials?: ITestimonial[];
  mainImage?: MainImage;
}

const Variants = {
  variant_a: TestimonialsVariant.Testimonial_A,
  variant_b: TestimonialsVariant.Testimonial_B,
  variant_c: TestimonialsVariant.Testimonial_C,
  variant_d: TestimonialsVariant.Testimonial_D,
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
};

export const Testimonial: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant as keyof typeof Variants;
  const Variant = variant && Variants[variant];

  const props = {
    caption: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    testimonials: data?.variants?.testimonials ?? undefined,
    mainImage: data?.variants?.mainImage ?? undefined,
  };

  if (!Variant) return null;

  return (
    <Suspense fallback={null}>
      <Variant {...props} />
    </Suspense>
  );
};

Testimonial.displayName = "Testimonial";
