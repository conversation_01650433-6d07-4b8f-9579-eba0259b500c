import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

const resetPasswordSchema = z.object({
    password: z.string().min(6, "Password must be at least 6 characters long"),
    confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters long"),
}).refine(data => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "POST") {
        return res.status(405).json({ message: "Method not allowed" });
    }

    const { token } = req.query;

    if (!token || typeof token !== "string") {
        return res.status(400).json({ error: "Invalid token" });
    }

    const data = resetPasswordSchema.safeParse(req.body);

    if (!data.success) {
        return res.status(400).json({
            error: data.error.issues[0].message
        });
    }

    const { password } = data.data;

    const supabaseAdminClient = createSupabaseAdminClient();

    const password_reset = await supabaseAdminClient
        .from("password_resets")
        .select("user_id, created_at")
        .eq("token", token)
        .single();

    if (password_reset.error) {
        return res.status(400).json({ error: "Invalid token" });
    }

    // check if token is expired (older than 5 minutes)
    const createdAt = new Date(password_reset.data.created_at);
    const now = new Date();
    const diff = now.getTime() - createdAt.getTime();
    const minutes = Math.floor(diff / 1000 / 60);
    if (minutes > 5) {
        return res.status(400).json({ error: "Token expired" });
    }

    const { user_id } = password_reset.data;

    const { error } = await supabaseAdminClient.auth.admin.updateUserById(
        user_id as string,
        { password: password }
    );

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    // Delete password reset record
    await supabaseAdminClient
        .from("password_resets")
        .delete()
        .eq("token", token);

    return res.status(200).json({ success: true, message: "Password updated successfully" });
}