import InlineEditor from "components/InlineEditor";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Text } from "@stackshift-ui/text";
import { InlineEditorContext } from "context/InlineEditorContext";
import { format } from "date-fns";
import { PortableText, urlFor } from "lib/sanity";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { BlogsData, MyPortableTextComponents } from "types";
import { getImageDimensions } from "@sanity/asset-utils";
import { allPostsQuery } from "pages/api/query";
import { sanityClient } from "lib/sanity.client";

const Navigation = dynamic(() =>
  import("components/sections/navigation").then((m) => m.Navigation)
);
const Footer = dynamic(() =>
  import("components/sections/footer").then((m) => m.Footer)
);
// block styling as props to `components` of the PortableText component
const blockStyle: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-8 leading-tight text-gray-800 text-4xl md:text-5xl font-bold">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return (
        <Heading
          type="h2"
          fontSize="3xl"
          className="mb-6 mt-10 leading-tight text-gray-800 font-bold"
        >
          {children}
        </Heading>
      );
    },
    h3: ({ children }) => {
      return (
        <Heading
          type="h3"
          fontSize="2xl"
          className="mb-4 mt-8 leading-tight text-gray-800 font-bold"
        >
          {children}
        </Heading>
      );
    },
    h4: ({ children }) => {
      return (
        <Heading
          fontSize="xl"
          type="h4"
          className="mb-4 mt-6 leading-tight text-gray-800 font-semibold"
        >
          {children}
        </Heading>
      );
    },
    h5: ({ children }) => {
      return (
        <Heading
          fontSize="lg"
          type="h5"
          className="mb-3 mt-5 leading-tight text-gray-800 font-semibold"
        >
          {children}
        </Heading>
      );
    },
    h6: ({ children }) => {
      return (
        <Heading
          fontSize="base"
          type="h6"
          className="mb-3 mt-4 leading-tight text-gray-800 font-semibold"
        >
          {children}
        </Heading>
      );
    },
    normal: ({ children }) => {
      return (
        <Text className="mb-4 leading-relaxed text-gray-700 text-base md:text-base">
          {children}
        </Text>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-8 italic leading-relaxed text-gray-600 px-6 py-3 border-l-4 border-primary my-6 bg-gray-50 rounded-r">
          {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre
        className="bg-gray-900 text-gray-100 p-4 rounded-md overflow-x-auto mb-6 text-sm md:text-base"
        data-language={value.language}
      >
        <code>{value.code}</code>
      </pre>
    );
  },
  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-relaxed text-gray-700 list-disc space-y-2">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="pl-10 mb-6 leading-relaxed text-gray-700 list-decimal space-y-2">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="leading-relaxed text-gray-700">{children}</li>
    ),
    number: ({ children }) => (
      <li className="leading-relaxed text-gray-700">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => (
      <strong className="font-bold text-gray-900">{children}</strong>
    ),
    em: ({ children }) => <em className="italic text-gray-800">{children}</em>,
    code: ({ children }) => (
      <code className="bg-gray-100 text-primary px-1 py-0.5 rounded text-sm font-mono">
        {children}
      </code>
    ),
    link: ({ children, value }) => (
      <Link
        className="text-primary hover:text-primary-dark underline decoration-1 underline-offset-2 transition-colors duration-200"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </Link>
    ),
  },
  types: {
    addImage: ({ value }) => (
      <div className="my-10">
        <Image
          className="w-auto h-auto shadow-md"
          width={getImageDimensions(urlFor(value?.image))?.width}
          height={getImageDimensions(urlFor(value?.image))?.height}
          src={urlFor(value?.image)}
          alt={value?.alt ?? value?.image?.asset?._ref}
        />
        {value?.alt && (
          <p className="sm:ml-10 text-left text-gray-500 text-sm mt-2 italic">
            {value.alt}
          </p>
        )}
      </div>
    ),
  },
};

interface BlogSectionsProps {
  data: BlogsData;
}

// Define a type for blog posts
interface BlogPost {
  _id: string;
  title?: string;
  slug?: {
    current?: string;
  };
  publishedAt?: string;
  _createdAt?: string;
}

function BlogSections({ data }: BlogSectionsProps) {
  const blogData: BlogsData = data || data?.[0];
  const [allPosts, setAllPosts] = React.useState<BlogPost[]>([]);

  const showInlineEditor = React.useContext(InlineEditorContext);
  if (!blogData) {
    return null;
  }

  const {
    _id,
    _type,
    authors,
    categories,
    body,
    mainImage,
    publishedAt,
    title,
    navigation,
    footer,
    slug,
    tags,
  } = blogData;

  React.useEffect(() => {
    async function getAllPosts() {
      try {
        // fetch all blog posts
        await sanityClient.fetch(allPostsQuery).then((posts) => {
          // Sort posts so that null publishedAt values go to the end
          const sortedPosts = [...posts].sort((a, b) => {
            // If both have publishedAt, compare them
            if (a.publishedAt && b.publishedAt) {
              return (
                new Date(b.publishedAt).getTime() -
                new Date(a.publishedAt).getTime()
              );
            }
            // If only a has publishedAt, a should come first
            if (a.publishedAt && !b.publishedAt) {
              return -1;
            }
            // If only b has publishedAt, b should come first
            if (!a.publishedAt && b.publishedAt) {
              return 1;
            }
            // If neither has publishedAt, sort by _createdAt
            return (
              new Date(b._createdAt).getTime() -
              new Date(a._createdAt).getTime()
            );
          });

          setAllPosts(sortedPosts);
        });
      } catch (err) {
        console.log("Error: ", err);
      }
    }
    getAllPosts();
  }, []);

  console.log("authors", authors);
  console.log("tags", tags);
  console.log("categories", categories);

  // Find current blog post index
  const currentBlogIndex = allPosts
    ?.map((post) => post?.slug?.current)
    .indexOf(slug?.current);

  // Check if current post is the first or last post
  const isFirstPost = currentBlogIndex === 0;
  const isLastPost = currentBlogIndex === allPosts.length - 1;

  return (
    <InlineEditor
      document={{
        id: _id || "",
        type: _type || "",
      }}
      showInlineEditor={showInlineEditor}
      key={_id}
    >
      {navigation && (
        <Navigation
          data={navigation}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}
      <section className="pb-20">
        <div
          className="relative p-20 mb-12"
          style={{
            ...(mainImage
              ? {
                  backgroundImage: `url(${urlFor(mainImage)})`,
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }
              : {
                  // backgroundImage: `linear-gradient(to bottom right, #0154a2, rgba(255, 255, 255, 1))`,
                  backgroundImage: `linear-gradient(135deg, #1155a3, #2a7dd2, #0d3b6f)`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }),
          }}
        >
          {/* Overlay for better readability when image is present */}
          {mainImage && (
            <div
              className="absolute inset-0"
              style={{
                backgroundColor: "rgba(0, 0, 0, 0.5)",
              }}
            ></div>
          )}

          <Container className="relative z-10">
            <Container className="text-center !max-w-5xl">
              <div className="flex flex-wrap items-center justify-center gap-4">
                {/* Categories - with icon */}
                {categories && categories.length > 0 && (
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                      />
                    </svg>
                    <div className="flex flex-wrap items-center">
                      {categories?.map((category, index) => (
                        <React.Fragment key={category._id}>
                          {index > 0 && (
                            <span className="mx-1 text-white">,</span>
                          )}
                          <Link
                            href={`/category/${category.slug?.current}`}
                            className="text-base uppercase text-white lg:text-xl hover:text-gray-300 transition-colors duration-200"
                          >
                            {category.title}
                          </Link>
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                )}

                {/* Published date - with icon */}
                {publishedAt && (
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <Link
                      href={`/archive/${format(
                        new Date(publishedAt),
                        "yyyy"
                      )}/${format(new Date(publishedAt), "MM")}`}
                      className="text-base lg:text-xl text-white hover:text-gray-300 transition-colors duration-200"
                    >
                      {format(new Date(publishedAt), "MMMM d, yyyy")}
                    </Link>
                  </div>
                )}
              </div>

              <div className="mt-4">
                {title && (
                  <Heading
                    weight="bold"
                    className="mb-6 text-white text-3xl md:text-5xl !leading-tight"
                  >
                    {title}
                  </Heading>
                )}

                {/* Authors section - with icon */}
                {authors && authors.length > 0 && (
                  <div className="flex flex-wrap justify-center gap-5">
                    {authors?.map((author, index) => (
                      <Flex
                        justify="center"
                        className="mr-2"
                        align="center"
                        key={index}
                      >
                        <div className="mr-4">
                          {author?.profile?.image ? (
                            <Image
                              className="object-cover object-top w-12 h-12 rounded-full border-2 border-white"
                              width={48}
                              height={48}
                              src={urlFor(author?.profile?.image)}
                              alt={author?.profile?.alt ?? author?.name}
                            />
                          ) : (
                            <svg
                              className="text-white"
                              xmlns="http://www.w3.org/2000/svg"
                              width="48"
                              height="48"
                              viewBox="0 0 24 24"
                              fill="white"
                            >
                              <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm0 22c-3.123 0-5.914-1.441-7.749-3.69.259-.588.783-.995 1.867-1.246 2.244-.518 4.459-.981 3.393-2.945-3.155-5.82-.899-9.119 2.489-9.119 3.322 0 5.634 3.177 2.489 9.119-1.035 1.952 1.1 2.416 3.393 2.945 1.082.25 1.61.655 1.871 1.241-1.836 2.253-4.628 3.695-7.753 3.695z" />
                            </svg>
                          )}
                        </div>
                        <div className="text-left flex flex-col items-start">
                          <Link href={`/author/${author.slug?.current}`}>
                            <Heading
                              type="h3"
                              weight="bold"
                              fontSize="base"
                              className="text-white hover:text-gray-300 transition-colors duration-200"
                            >
                              {author.name}
                            </Heading>
                          </Link>
                          <span className="text-xs italic text-gray-200">
                            {authors?.length > 1 ? "Authors" : "Author"}
                          </span>
                        </div>
                      </Flex>
                    ))}
                  </div>
                )}
              </div>
            </Container>
          </Container>
        </div>
        <Container>
          {/* Tags section */}
          {tags && (
            <div className="max-w-4xl mx-auto mb-8 flex flex-wrap gap-2 items-center">
              <span className="text-sm font-semibold text-gray-700">Tags:</span>
              {Array.isArray(tags) ? (
                // Handle array of tags
                tags.map((tag) => (
                  <Link
                    key={tag._id}
                    href={`/tag/${tag.slug?.current}`}
                    className="inline-block px-3 py-1 bg-gray-100 hover:bg-primary hover:text-white transition-colors duration-200 text-sm text-gray-700"
                  >
                    {tag.title}
                  </Link>
                ))
              ) : (
                // Handle single tag object
                <Link
                  key={tags._id}
                  href={`/tag/${tags.slug?.current}`}
                  className="inline-block px-3 py-1 bg-gray-100 hover:bg-primary hover:text-white transition-colors duration-200 text-sm text-gray-700"
                >
                  {tags.title}
                </Link>
              )}
            </div>
          )}
          {body && (
            <div className="max-w-4xl mx-auto">
              <PortableText
                value={body}
                components={blockStyle}
                onMissingComponent={false} // Disabling warnings / handling unknown types
              />
            </div>
          )}
          <div className="max-w-4xl mx-auto overflow-hidden mt-20 border-t border-primary pt-8">
            <div className="grid grid-cols-2 gap-4 lg:gap-8">
              <div className="text-left mb-5 md:mb-0">
                {!isFirstPost && allPosts && allPosts.length > 0 && (
                  <>
                    <span className="block font-bold uppercase text-xs text-primary mb-2">
                      Next
                    </span>
                    <Link
                      className="text-sm md:text-base font-medium text-gray-700 hover:text-primary transition-colors duration-200"
                      href={`/${allPosts?.[currentBlogIndex - 1]?.slug
                        ?.current}`}
                    >
                      {allPosts?.[currentBlogIndex - 1]?.title}
                    </Link>
                  </>
                )}
              </div>
              <div className="text-left lg:text-right mb-5 md:mb-0">
                {!isLastPost && allPosts && allPosts.length > 0 && (
                  <>
                    <span className="block font-bold uppercase text-xs text-primary mb-2">
                      Prev
                    </span>
                    <Link
                      className="text-sm md:text-base font-medium text-gray-700 hover:text-primary transition-colors duration-200"
                      href={`/${allPosts?.[currentBlogIndex + 1]?.slug
                        ?.current}`}
                    >
                      {allPosts?.[currentBlogIndex + 1]?.title}
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        </Container>
      </section>
      {footer && (
        <Footer
          data={footer}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}
    </InlineEditor>
  );
}

export default React.memo(BlogSections);
