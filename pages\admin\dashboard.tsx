import AdminLayout from "@/components/features/admin/layout";
import { SalesOverview } from "@/components/features/admin/sales-overview";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetDashboardDataQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Clock, CreditCard, Package, ShoppingCart, Users } from "lucide-react";
import Head from "next/head";

export default function AdminDashboard() {
  const userData = useAuthStore(state => state.data);
  const token = useAuthStore(state => state.token);
  const { data, error, isLoading } = useGetDashboardDataQuery(token);
  const currentYear = new Date().getFullYear();

  return (
    <AdminLayout>
      <Head>
        <title>Admin Dashboard</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section with Welcome */}
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">Welcome back, {userData?.email?.split('@')[0] || 'Admin'}</h2>
              <p className="text-muted-foreground">
                Here's an overview of your store's performance and activity
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <span className="text-primary">Admin Dashboard</span>
              </Badge>
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <Clock className="mr-2 h-4 w-4" /> Last login: {new Date().toLocaleDateString()}
              </Badge>
            </div>
          </div>

          {/* Quick Stats Grid */}
          {isLoading ? (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <StatCardSkeleton />
              <StatCardSkeleton />
              <StatCardSkeleton />
              <StatCardSkeleton />
            </div>
          ) : error ? (
            <Card className="p-6">
              <div className="text-red-500">Error loading dashboard data: {error.message}</div>
            </Card>
          ) : (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <Card className="hover:shadow-md transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${data?.totalSales?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">Total revenue generated in {currentYear}</p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.totalOrders?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">Orders processed in {currentYear}</p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.totalCustomers?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">+{data?.totalPendingCustomers || '0'} pending in {currentYear}</p>
                </CardContent>
              </Card>

              <Card className="hover:shadow-md transition-all">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{data?.totalProducts?.toLocaleString() || '0'}</div>
                  <p className="text-xs text-muted-foreground">Products in catalog</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Sales Overview Chart */}
          <Card className="hover:shadow-md transition-all">
            <CardHeader>
              <CardTitle>Sales Overview <span className="text-muted text-sm">({currentYear})</span></CardTitle>
            </CardHeader>
            <CardContent className="pl-2">
              {isLoading ? (
                <div className="h-[350px] w-full">
                  <Skeleton className="h-full w-full" />
                </div>
              ) : (
                <SalesOverview salesData={data?.salesData || []} />
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}

function StatCardSkeleton() {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-4 w-4 rounded-full" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-7 w-[60px] mb-1" />
        <Skeleton className="h-4 w-[100px]" />
      </CardContent>
    </Card>
  );
}
