import { NextApiRequest, NextApiResponse } from "next";
import { createSupabaseAdminClient } from "@/supabase";
import { matchRoute } from "@/middlewares/match-route";
import { z } from "zod";
import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import {
  createQuoteConfirmationTemplate,
  createQuoteAdminNotificationTemplate,
} from "@/lib/mailer/templates";

const quoteSchema = z.object({
  product_id: z.string().min(1, "Product ID is required"),
  user_id: z.string().min(1, "User ID is required"),
  quote_information: z.string().optional(),
  quantity: z.number().min(1, "Quantity must be at least 1"),
});

interface QuoteResponse {
  success?: boolean;
  message?: string;
  error?: string;
  quote_id?: string;
}

interface UserData {
  email: string;
  first_name?: string;
  last_name?: string;
  customers?: Array<{
    company_name?: string;
  }>;
}

interface ProductData {
  name: string;
}

interface QuoteData {
  id: string;
  product_id: string;
  user_id: string;
  quote_text?: string;
  quantity: number;
}

export default matchRoute({
  POST: submitQuote,
});

async function submitQuote(
  req: NextApiRequest,
  res: NextApiResponse<QuoteResponse>
) {
  try {
    // Validate request body
    const result = quoteSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: "Invalid quote data",
        message: result.error.message,
      });
    }

    const quoteData = result.data;
    const supabase = createSupabaseAdminClient();

    const { data, error } = await supabase
      .from("quotes" as any)
      .insert({
        product_id: quoteData.product_id,
        user_id: quoteData.user_id,
        quote_text: quoteData.quote_information,
        quantity: quoteData.quantity,
      })
      .select()
      .single();

    if (error) {
      console.error("Error submitting quote:", error);
      return res.status(500).json({
        success: false,
        error: "Failed to submit quote",
        message: error.message,
      });
    }

    const quoteRecord = data as unknown as QuoteData;
    const quoteId = quoteRecord.id;

    if (!quoteId) {
      return res.status(500).json({
        success: false,
        error: "Failed to retrieve quote ID",
        message: "Quote was created but ID was not returned",
      });
    }

    const [userResponse, productResponse] = await Promise.all([
      supabase
        .from("users" as any)
        .select("email, first_name, last_name, customers(company_name)")
        .eq("id", quoteData.user_id)
        .single(),
      supabase
        .from("products" as any)
        .select("name")
        .eq("id", quoteData.product_id)
        .single(),
    ]);

    if (userResponse.error || !userResponse.data) {
      console.error("Error fetching user data:", userResponse.error);
      return res.status(500).json({
        success: false,
        error: "Failed to fetch user data",
        message: userResponse.error?.message || "User data not found",
      });
    }

    if (productResponse.error || !productResponse.data) {
      console.error("Error fetching product data:", productResponse.error);
      return res.status(500).json({
        success: false,
        error: "Failed to fetch product data",
        message: productResponse.error?.message || "Product data not found",
      });
    }

    const userData = userResponse.data as unknown as UserData;
    const productData = productResponse.data as unknown as ProductData;

    const companyName = userData.customers?.[0]?.company_name || "";

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    const ADMIN_EMAIL = process.env.ADMIN_EMAIL || "<EMAIL>";

    await Promise.all([
      // Send email to user
      sendEmail(
        mailer,
        createQuoteConfirmationTemplate({
          to: userData.email,
          firstName: userData.first_name,
          lastName: userData.last_name,
          companyName,
          productName: productData.name,
          quantity: quoteData.quantity,
          quoteText: quoteData.quote_information,
        })
      ),

      // Send email to admin
      sendEmail(
        mailer,
        createQuoteAdminNotificationTemplate({
          to: ADMIN_EMAIL,
          userName: `${userData.first_name || ""} ${
            userData.last_name || ""
          }`.trim(),
          userEmail: userData.email,
          companyName,
          productName: productData.name,
          quantity: quoteData.quantity,
          quoteText: quoteData.quote_information,
          userId: quoteData.user_id,
          productId: quoteData.product_id,
        })
      ),
    ]);

    return res.status(200).json({
      success: true,
      message: "Quote submitted successfully",
      quote_id: quoteId,
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    return res.status(500).json({
      success: false,
      error: "An unexpected error occurred",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
