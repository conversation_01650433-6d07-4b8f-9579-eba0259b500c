import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { BusinessDetail } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: getBusinessDetailsHandler,
  }),
);

export interface GetBusinessDetailsResponse {
  data?: BusinessDetail;
  error?: string;
}

async function getBusinessDetailsHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse,
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  if (userId !== id) {
    return res.status(403).json({ error: "Unauthorized" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const businessDetails = await supabaseAdminClient
    .from("business_details")
    .select(
      `
      id,
      account_payable_contact,
      account_payable_phone,
      authorized_contact_name,
      business_nature,
      business_type,
      buyer_name,
      buyer_phone,
      maxton_account,
      number_of_elevators,
      technical_contact,
      technical_contact_phone,
      technician,
      user_id,
      website,
      created_at
  `,
    )
    .eq("user_id", userId)
    .single();

  if (businessDetails.error) {
    return res.status(404).json({ error: businessDetails.error });
  }

  return res.status(200).json({ data: businessDetails.data });
}
