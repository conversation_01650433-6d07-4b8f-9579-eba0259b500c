import { SignInResponse } from "@/pages/api/auth/sign-in";
import { UpdateUserResponse } from "@/pages/api/users/[slug]";
import { ChangePasswordResponse } from "@/pages/api/users/[slug]/password";
import { PublicUser } from "@/supabase/types";
import { AuthResponse, User } from "@supabase/supabase-js";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/router";
import { SignUpCallbackSession } from "pages/api/auth/callback";
import { CreateUserDTO, CreateUserResponse } from "pages/api/users";
import useAuthStore from "stores/auth-store";
import { supabaseClient } from "../supabase";
import { GetBusinessDetailsResponse } from "@/pages/api/users/[slug]/business-details";

interface GetPublicUserResponse {
  data: PublicUser;
  error?: string;
}

async function getPublicUser(id: string, token: String): Promise<PublicUser> {
  const res = await fetch("/api/users/" + id, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });
  const response = (await res.json()) as GetPublicUserResponse;

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

export function useGetPublicUserQuery(id: string, token: string) {
  return useQuery({
    enabled: id !== undefined && !!id,
    queryKey: ["get-public-user", id],
    queryFn: async () => await getPublicUser(id, token),
  });
}

type CreateUserResponseData = CreateUserResponse["data"];

async function createUser(
  data: CreateUserDTO,
): Promise<CreateUserResponseData> {
  const createPublicUser = await fetch("/api/users", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const response = (await createPublicUser.json()) as CreateUserResponse;
  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

async function deletePublicUserById(id: string): Promise<boolean> {
  const user = await supabaseClient
    .from("users")
    .delete()
    .eq("id", id)
    .select("id")
    .single();

  return !!user.data?.id;
}

type OnSignUpErrorParam = AuthResponse["error"];

export function useSignupMutation() {
  return useMutation({
    mutationKey: ["create-user"],
    mutationFn: (dto: CreateUserDTO) => createUser(dto),
    onSuccess: async (data: CreateUserResponseData) => {
      return data;
    },
    onError: (error: OnSignUpErrorParam) => {
      return error;
    },
  });
}

interface SignInDTO {
  email: string;
  password: string;
}

async function signIn(data: SignInDTO): Promise<SignInResponse> {
  const res = await fetch("/api/auth/sign-in", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const signInResponse = (await res.json()) as SignInResponse;

  if (signInResponse.error) {
    throw new Error(signInResponse.error);
  }

  return signInResponse;
}

export function useSigninMutation() {
  const authStore = useAuthStore();

  return useMutation({
    mutationKey: ["signin"],
    mutationFn: signIn,
    onSuccess: (data: SignInResponse) => {
      authStore.setToken(data.access_token || "");
      authStore.setData(data.user || ({} as User));
      authStore.setExpiresAt(data.access_token_expires_at || 0);
      authStore.setExpiresIn(data.access_token_expires_in || 0);
      authStore.setRole(data.role || "manager");
      authStore.setPermissions(data.permissions || []);
    },
    onError: (error) => {
      return error;
    },
  });
}

export async function autoSignInAfterSignUp(
  data: SignUpCallbackSession,
): Promise<SignInResponse> {
  const res = await fetch("/api/auth/callback", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  const signInResponse = (await res.json()) as SignInResponse;

  if (signInResponse.error) {
    throw new Error(signInResponse.error);
  }

  return signInResponse;
}

export function useSignUpCallbackMutation() {
  const authStore = useAuthStore();

  return useMutation({
    mutationKey: ["signup-callback"],
    mutationFn: autoSignInAfterSignUp,
    onSuccess: (data: SignInResponse) => {
      authStore.setToken(data.access_token || "");
      authStore.setData(data.user || ({} as User));
      authStore.setExpiresAt(data.access_token_expires_at || 0);
      authStore.setExpiresIn(data.access_token_expires_in || 0);
      authStore.setRole(data.role || "manager");
      authStore.setPermissions(data.permissions || []);
    },
    onError: (error) => {
      return error;
    },
  });
}

interface ResetPasswordResponse {
  error?: string;
  success?: boolean;
  message?: string;
}

async function resetPasswordForEmail(email: string): Promise<boolean> {
  const res = await fetch("/api/auth/forgot-password", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email }),
  });

  const reset = (await res.json()) as ResetPasswordResponse;

  if (reset.error) {
    throw new Error(reset.error);
  }

  return true;
}

export function useResetPasswordMutation() {
  return useMutation({
    mutationFn: resetPasswordForEmail,
    onSuccess: () => {
      return true;
    },
    onError: (error) => {
      return error;
    },
  });
}

interface SignOutResponse {
  error?: string;
  success?: boolean;
  message?: string;
}

async function signOut(accessToken: string) {
  const res = await fetch("/api/auth/sign-out", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ access_token: accessToken }),
  });

  const signOutResponse = (await res.json()) as SignOutResponse;

  if (signOutResponse.error) {
    throw new Error(signOutResponse.error);
  }

  return signOutResponse;
}

export function useSignOutMutation() {
  const authStore = useAuthStore();

  return useMutation({
    mutationFn: signOut,
    onSuccess: () => {
      authStore.logout();
    },
    onError: () => {
      authStore.logout();
    },
  });
}

interface ResetPasswordWithTokenDTO {
  password: string;
  confirmPassword: string;
  token: string;
}

async function resetPasswordWithToken(
  data: ResetPasswordWithTokenDTO,
): Promise<ResetPasswordResponse> {
  const res = await fetch(`/api/forgot-password/${data.token}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      password: data.password,
      confirmPassword: data.confirmPassword,
    }),
  });

  const resetResponse = (await res.json()) as ResetPasswordResponse;

  if (resetResponse.error) {
    throw new Error(resetResponse.error);
  }

  return resetResponse;
}

export function useResetPasswordWithTokenMutation() {
  const router = useRouter();
  return useMutation({
    mutationFn: resetPasswordWithToken,
    onSuccess: async () => {
      await router.push("/log-in");
    },
    onError: (error) => {
      return error;
    },
  });
}

interface UpdateUserDTO {
  first_name?: string;
  last_name?: string;
  phone?: string;
  email?: string;
  password?: string;
  confirm_password?: string;
}

async function updateUser(
  id: string,
  data: UpdateUserDTO,
  token: string,
): Promise<UpdateUserResponse> {
  const res = await fetch(`/api/users/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });

  const response = (await res.json()) as UpdateUserResponse;

  // Check if the response contains an error
  if (response.error) {
    throw new Error(response.error);
  }

  return response;
}

export function useUpdateUserMutation(id: string, token: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-user", id],
    mutationFn: (data: UpdateUserDTO) => updateUser(id, data, token),
    onSuccess: async () => {
      // Invalidate and refetch the user data
      await queryClient.invalidateQueries({
        queryKey: ["get-public-user", id],
      });
      await queryClient.invalidateQueries({
        queryKey: ["get-business-details", id],
      });

      // Force refetch the user data
      await queryClient.refetchQueries({ queryKey: ["get-public-user", id] });
    },
    onError: (error) => {
      return error;
    },
  });
}

interface ChangePasswordDTO {
  new_password: string;
  confirm_password: string;
}

async function changePassword(
  id: string,
  data: ChangePasswordDTO,
  token: string,
): Promise<ChangePasswordResponse> {
  const res = await fetch(`/api/users/${id}/password`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });

  const response = (await res.json()) as ChangePasswordResponse;

  if (response.error) {
    throw new Error(response.error);
  }

  return response;
}

export function useChangePasswordMutation(id: string, token: string) {
  const authStore = useAuthStore();

  return useMutation({
    mutationKey: ["change-password", id],
    mutationFn: (data: ChangePasswordDTO) => changePassword(id, data, token),
    onSuccess: () => {
      return true;
    },
    onError: (error) => {
      return error;
    },
  });
}

async function getBusinessDetails(
  userId: string,
  token: string,
): Promise<GetBusinessDetailsResponse> {
  const res = await fetch(`/api/users/${userId}/business-details`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  const response = (await res.json()) as GetBusinessDetailsResponse;

  if (response.error) {
    throw new Error(response.error);
  }

  return response;
}

export function useGetBusinessDetailsQuery(userId: string, token: string) {
  return useQuery({
    queryKey: ["get-business-details", userId],
    queryFn: () => getBusinessDetails(userId, token),
    enabled: !!userId && !!token,
  });
}
