import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import { CategoryWithParent } from "@/pages/api/categories";
import { useGetAllCategoriesQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { ColumnDef, ColumnFiltersState, SortingState, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table";
import { useState } from "react";
import { CreateCategoryDialog } from "./create-category-dialog";
import { DeleteCategoryDialog } from "./delete-category-dialog";
import { UpdateCategoryDialog } from "./update-category-dialog";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";

const categoryColumns: ColumnDef<CategoryWithParent>[] = [
    {
        header: "Name",
        accessorKey: "name",
    },
    {
        header: "Value",
        accessorKey: "value",
    },
    {
        header: "Parent Category",
        accessorKey: "parent_category_id",
        cell: ({ row }) => {
            const parentCategory = row.original.parent_category_data;
            return parentCategory?.name || "None";
        }
    },
    {
        header: "Created",
        accessorKey: "created_at",
        cell: ({ row }) => {
            const date = new Date(row.original.created_at);
            return <div>{date.toISOString().split('T')[0]}</div>;
        }
    },
    {
        id: "actions",
        cell: ({ row }) => {
            const category = row.original;
            const permissions = useAuthStore((state) => state.permissions);
            const hasDeletePermission = checkUserHasPermission(permissions, "delete:categories");

            return (
                <div className="flex items-center gap-2">
                    <UpdateCategoryDialog category={category} />
                    {hasDeletePermission && <DeleteCategoryDialog category={category} />}
                </div>
            );
        },
    },
];

export function CategoriesDataTable() {
    const accessToken = useAuthStore((state) => state.token);
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const { data, isLoading } = useGetAllCategoriesQuery(page, limit, accessToken);
    const permissions = useAuthStore((state) => state.permissions);
    const hasCreatePermission = checkUserHasPermission(permissions, "create:categories");

    const table = useReactTable({
        data: data?.categories ?? [],
        columns: categoryColumns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true,
        pageCount: Math.ceil((data?.total ?? 0) / limit),
        onPaginationChange: updater => {
            if (typeof updater === 'function') {
                const newPagination = updater({
                    pageIndex: page - 1,
                    pageSize: limit,
                });
                setPage(newPagination.pageIndex + 1);
                setLimit(newPagination.pageSize);
            } else {
                setPage(updater.pageIndex + 1);
                setLimit(updater.pageSize);
            }
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        state: {
            sorting,
            columnFilters,
            pagination: {
                pageIndex: page - 1,
                pageSize: limit,
            },
        },
    });

    if (isLoading) return <DataTableSkeleton />;

    return (
        <Card className="container mx-auto border-none">
            <CardHeader className="px-0 py-0">
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className="text-4xl font-bold">All Categories</h3>
                        <p className="text-sm text-muted-foreground">
                            Manage your product and customer categories.
                        </p>
                    </div>
                    {
                        hasCreatePermission && (
                            <CreateCategoryDialog />
                        )
                    }
                </div>
            </CardHeader>
            <CardContent className="px-0">
                <DataTable
                    columns={categoryColumns}
                    data={data?.categories ?? []}
                    filterColumn="name"
                    filterPlaceholder="Search categories..."
                    table={table}
                />
            </CardContent>
        </Card>
    );
}
