import AdminLayout from "@/components/features/admin/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useGetSalesReportQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { format, subDays } from "date-fns";
import { ArrowLeft } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import { useState } from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  <PERSON>C<PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

// Skeleton component for the chart
const ChartSkeleton = () => (
  <div className="h-96 w-full">
    <Skeleton className="w-full h-full" />
  </div>
);

// Skeleton component for the table
const TableSkeleton = () => (
  <div className="space-y-2">
    {/* Header skeleton */}
    <Skeleton className="h-10 w-full rounded" />

    {/* Row skeletons */}
    {[...Array(6)].map((_, index) => (
      <Skeleton key={index} className="h-12 w-full rounded" />
    ))}

    {/* Summary row skeleton */}
    <Skeleton className="h-12 w-full rounded mt-4" />
  </div>
);

export default function SalesByWeekPage() {
  const router = useRouter();
  const token = useAuthStore((state) => state.token);
  const [dateRange, setDateRange] = useState("84"); // Default to 12 weeks

  // Calculate date range based on selection
  const today = new Date();
  const endDate = format(today, "yyyy-MM-dd");
  const startDate = format(subDays(today, parseInt(dateRange)), "yyyy-MM-dd");

  const { data, isLoading, isError } = useGetSalesReportQuery(
    token,
    "week",
    dateRange,
    startDate,
    endDate
  );

  const handleDateRangeChange = (value: string) => {
    setDateRange(value);
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Get chart data
  const getChartData = () => {
    if (!data?.data) return [];

    return data.data.map((item: any) => {
      // Use week number for display
      const label = `W${item.label.split("-W")[1]}`;

      return {
        name: label,
        grossSales: item.gross_sales,
        netSales: item.net_sales,
      };
    });
  };

  return (
    <AdminLayout>
      <Head>
        <title>Reports | Sales by Week</title>
      </Head>
      <div className="p-6">
        {/* Back button */}
        <div className="mb-4">
          <Link href="/admin/reports">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Reports
            </Button>
          </Link>
        </div>

        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Sales by week</h1>
            <p className="text-gray-500">
              View detailed reports on your weekly sales
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="28">Last 4 weeks</SelectItem>
                <SelectItem value="56">Last 8 weeks</SelectItem>
                <SelectItem value="84">Last 12 weeks</SelectItem>
                <SelectItem value="168">Last 24 weeks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Sales Chart</CardTitle>
            <CardDescription>
              Weekly sales performance from{" "}
              {format(new Date(startDate), "dd MMM yyyy")} to{" "}
              {format(new Date(endDate), "dd MMM yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              {isLoading ? (
                <ChartSkeleton />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={getChartData()}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis tickFormatter={(value) => `$${value}`} />
                    <Tooltip
                      formatter={(value: number, name: string) => {
                        return [
                          formatCurrency(value),
                          name === "Gross Sales" ? "Gross Sales" : "Net Sales",
                        ];
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="grossSales"
                      name="Gross Sales"
                      stroke="#6366f1"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="netSales"
                      name="Net Sales"
                      stroke="#22c55e"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Weekly Sales Data</CardTitle>
            <CardDescription>
              Detailed breakdown of weekly sales
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <TableSkeleton />
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Week</TableHead>
                    <TableHead>Period</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Gross Sales</TableHead>
                    <TableHead>Discounts</TableHead>
                    <TableHead>Net Sales</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.data?.map((week: any) => (
                    <TableRow key={week.label}>
                      <TableCell>{week.label}</TableCell>
                      <TableCell>
                        {format(new Date(week.week_start), "dd MMM")} -{" "}
                        {format(new Date(week.week_end), "dd MMM yyyy")}
                      </TableCell>
                      <TableCell>{week.orders}</TableCell>
                      <TableCell>{formatCurrency(week.gross_sales)}</TableCell>
                      <TableCell>
                        {formatCurrency(week.item_discounts)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(week.net_sales)}
                      </TableCell>
                    </TableRow>
                  ))}

                  {/* Summary row */}
                  {data?.summary && (
                    <TableRow className="bg-gray-50 font-bold">
                      <TableCell colSpan={2}>Summary</TableCell>
                      <TableCell>{data.summary.orders}</TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.gross_sales)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.item_discounts)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.net_sales)}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
