import { NextApiResponse } from "next";
import { createSupabaseAdminClient, supabaseClient } from "supabase";
import { PermissionAction, RoleAdmin, RoleStaff } from "supabase/types";
import { NextApiFunction, NextApiRequestWithUserContext } from ".";

/**
 * #### Middleware function to check if the user is authenticated.
 * User object is attached to the request object if the user is authenticated.
 * ##### Example:
 * ```js
 * export default checkAuth(async function(req: NextApiRequestWithUserContext, res: NextApiResponse) {
 *  // ...
 * });
 * ```
 */
export function checkAuth(next: NextApiFunction): NextApiFunction {
    return async function (req: NextApiRequestWithUserContext, res: NextApiResponse) {
        const authorization = req.headers.authorization;
        const accessToken = authorization?.split(" ").at(1);

        if (!accessToken) {
            return res.status(401).json({
                message: "Access token is missing",
            });
        }

        const user = await supabaseClient.auth.getUser(accessToken);

        if (user.error) {
            return res.status(401).json({
                message: "JWT is invalid or expired",
            });
        }

        req.user = user.data.user;

        await next(req, res);
    }
}

/**
 * #### Middleware function to check if the user is an admin
 * User object is attached to the request object if the user is authenticated.
 * ##### Example:
 * ```js
 * export default checkAdmin(async function(req, res) {
 *  // ...
 * });
 * ```
 */
export function checkAdmin(next: NextApiFunction): NextApiFunction {
    return async function (req: NextApiRequestWithUserContext, res: NextApiResponse) {
        const authorization = req.headers.authorization;
        const accessToken = authorization?.split(" ").at(1);

        if (!accessToken) {
            return res.status(401).json({
                message: "Access token is missing",
            });
        }
        const user = await supabaseClient.auth.getUser(accessToken);

        if (user.error) {
            return res.status(401).json({
                message: "JWT is invalid or expired",
            });
        }

        const userData = user.data.user;

        const supabaseAdminClient = createSupabaseAdminClient();

        const publicUser = await supabaseAdminClient
            .schema("public")
            .from("users")
            .select("role")
            .eq("id", userData.id)
            .single();

        if (publicUser.data?.role !== RoleAdmin && publicUser.data?.role !== RoleStaff) {
            return res.status(403).json({
                message: "Forbidden, you are not an admin",
            });
        }

        await next(req, res);
    }
}

/**
 * #### Middleware function to check if the user has permission to perform an action
 * User object is attached to the request object if the user is authenticated and has the required permission.
 * ##### Example:
 * ```js
 * export default checkPermission("read:products", async function(req, res) {
 *  // ...
 * });
 * ```
 */
export function checkPermission(requiredPermission: PermissionAction, next: NextApiFunction): NextApiFunction {
    return async function (req: NextApiRequestWithUserContext, res: NextApiResponse) {
        const authorization = req.headers.authorization;
        const accessToken = authorization?.split(" ").at(1);

        if (!accessToken) {
            return res.status(401).json({
                message: "Access token is missing",
            });
        }

        const user = await supabaseClient.auth.getUser(accessToken);

        if (user.error) {
            return res.status(401).json({
                message: "JWT is invalid or expired",
            });
        }

        const userData = user.data.user;
        req.user = userData;

        const supabaseAdminClient = createSupabaseAdminClient();

        // Get user role
        const publicUser = await supabaseAdminClient
            .schema("public")
            .from("users")
            .select("role")
            .eq("id", userData.id)
            .single();

        if (publicUser.error || !publicUser.data) {
            return res.status(403).json({
                message: "Forbidden, unable to determine user role",
            });
        }

        // Admin has all permissions
        if (publicUser.data.role === RoleAdmin) {
            return await next(req, res);
        }

        // Get permissions for the user's role from customer_role
        // Since user role and customer role might be different, we need to map them
        const userRoleToCustomerRole: Record<string, "admin" | "customer" | "staff"> = {
            "admin": "admin",
            "staff": "staff",
            "manager": "customer", // Managers have customer permissions,
            "customer": "customer"
        };

        const customerRole = userRoleToCustomerRole[publicUser.data.role];

        if (!customerRole) {
            return res.status(403).json({
                message: "Forbidden, invalid user role",
            });
        }

        const permissionData = await supabaseAdminClient
            .schema("public")
            .from("permissions")
            .select("allowed_actions")
            .eq("role", customerRole)
            .single();

        if (permissionData.error || !permissionData.data) {
            return res.status(403).json({
                message: "Forbidden, no permissions found for your role",
            });
        }

        const allowedActions = permissionData.data.allowed_actions;

        // Check if user has the required permission
        const hasPermission = checkUserHasPermission(allowedActions, requiredPermission);

        if (!hasPermission) {
            return res.status(403).json({
                message: `Forbidden, you don't have permission to ${requiredPermission}`,
            });
        }

        await next(req, res);
    }
}

/**
 * Helper function to check if a user has a specific permission
 */
export function checkUserHasPermission(permissions: string[], requiredPermission: PermissionAction): boolean {
    // Check for exact match
    if (permissions.includes(requiredPermission)) return true;

    // Check for wildcard permissions (e.g., "read:all")
    const [operation, resource] = requiredPermission.split(':');
    if (permissions.includes(`${operation}:all`)) return true;

    // Check for multi-resource permissions (e.g., "read:products,orders")
    return permissions.some(perm => {
        const [permOperation, permResources] = perm.split(':');
        if (permOperation === operation && permResources) {
            return permResources.split(',').includes(resource);
        }
        return false;
    });
}