import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/shadcn-button";
import { ChevronDown, ChevronUp } from "lucide-react";

interface CitiesListProps {
  cities: string[];
  maxVisible?: number;
  variant?: "default" | "secondary" | "outline";
  className?: string;
  onCityClick?: (city: string) => void;
}

export function CitiesList({
  cities,
  maxVisible = 3,
  variant = "secondary",
  className = "",
  onCityClick
}: CitiesListProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!cities || cities.length === 0) {
    return null;
  }

  const visibleCities = isExpanded ? cities : cities.slice(0, maxVisible);
  const hasMore = cities.length > maxVisible;

  return (
    <div className={`max-w-96 flex flex-wrap items-center gap-1 ${className}`}>
      {visibleCities.map((city) => (
        <Badge
          key={city}
          variant={variant}
          className={`text-xs ${
            onCityClick ? "cursor-pointer hover:opacity-80" : ""
          }`}
          onClick={() => onCityClick?.(city)}
        >
          {city}
        </Badge>
      ))}
      
      {hasMore && (
        <Button
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? (
            <>
              <ChevronUp className="h-3 w-3 mr-1" />
              Show Less
            </>
          ) : (
            <>
              <ChevronDown className="h-3 w-3 mr-1" />
              +{cities.length - maxVisible} more
            </>
          )}
        </Button>
      )}
    </div>
  );
}

export default CitiesList;