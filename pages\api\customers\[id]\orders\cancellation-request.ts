import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
    matchRoute({
        POST: createCancellationRequestHandler
    })
)

export const cancellationRequestSchema = z.object({
    orderId: z.string().min(1, "Order ID is required"),
    comment: z.string().min(1, "Comment is required").max(500, "Comment must be less than 500 characters"),
});

export type CancellationRequestDTO = z.infer<typeof cancellationRequestSchema>;

export interface CreateCancellationRequestResponse {
    success?: boolean;
    data?: {
        id: string;
    };
    error?: string;
}

async function createCancellationRequestHandler(
    req: NextApiRequestWithUserContext,
    res: NextApiResponse<CreateCancellationRequestResponse>
) {
    const { id: user_id } = req.query;

    const validationResult = cancellationRequestSchema.safeParse(req.body);

    if (!validationResult.success) {
        return res.status(400).json({
            error: validationResult.error.issues[0].message
        });
    }

    const { orderId, comment } = validationResult.data;

    const userId = req.user?.id.toString();

    if (!user_id || !orderId) {
        return res.status(400).json({ error: "Missing required fields" });
    }

    if (!userId || userId !== user_id) {
        return res.status(401).json({ error: "Unauthorized" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    const customer = await supabaseAdminClient
        .from("customers")
        .select("id")
        .eq("user_id", user_id)
        .single();

    if (customer.error) {
        return res.status(400).json({ error: customer.error.message });
    }

    const customerId = customer.data.id;

    const order = await supabaseAdminClient
        .from("orders")
        .select("id, user_id, customer_id")
        .eq("id", orderId)
        .eq("user_id", user_id)
        .eq("customer_id", customerId)
        .single();

    if (order.error) {
        return res.status(400).json({ error: order.error.message });
    }

    const { data, error } = await supabaseAdminClient
        .from("cancellation_requests")
        .insert({
            user_id: userId,
            customer_id: customerId,
            order_id: orderId,
            comment: comment,
            status: "pending"
        })
        .select("id")
        .single();

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    return res.status(201).json({
        success: true,
        data
    });
} 