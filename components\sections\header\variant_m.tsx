import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableTextBlock } from "sanity";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";
import { getImageDimensions } from "@sanity/asset-utils";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-6 leading-loose text-5xl sm:text-7xl">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return (
        <Heading type="h2" className="mb-4 text-xl">
          {children}
        </Heading>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-6 text-xl md:text-3xl leading-loose text-gray-900">
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-2 text-base sm:text-xl font-semibold leading-loose text-gray-900">
          {children}
        </h4>
      );
    },

    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-500 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="pl-10 mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary-foreground hover:text-secondary-foreground"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Header_M({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  firstColumn,
}: HeaderProps) {
  return (
    <Section className="lg:pt-32 pt-20 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex className="lg:gap-8 gap-4 flex-col md:flex-row flex items-center justify-center">
          <MainImage mainImage={mainImage} />

          <Flex direction="col" className="w-full md:items-start items-center">
            <div className="md:max-w-xl px-10 text-left flex items-center md:items-start justify-start flex-col space-y-2">
              <TitleAndDescription title={title} description={description} />
              <Buttons
                primaryButton={primaryButton}
                secondaryButton={secondaryButton}
              />
            </div>
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}
function TitleAndDescription({
  title,
  description,
}: {
  title?: string;
  description?: string;
}) {
  return (
    <React.Fragment>
      {title && (
        <div>
          <Heading
            type="h2"
            className="mb-4 lg:text-2xl text-xl text-center md:text-left"
          >
            {title}
          </Heading>
        </div>
      )}

      {description && (
        <div className="!mb-4 text-xs md:mb-0 lg:text-base">
          <Text muted>{description}</Text>
        </div>
      )}
    </React.Fragment>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="lg:justify-start items-center !flex-row"
    >
      {primaryButton?.label && (
        <Button
          variant="solid"
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.label}
        >
          {primaryButton?.label}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full flex items-end md:justify-end justify-center">
      <Image
        className="object-cover relative"
        src={`${mainImage.image}`}
        width={getImageDimensions(mainImage?.image)?.width}
        height={getImageDimensions(mainImage?.image)?.width}
        alt={mainImage.alt ?? "header-main-image"}
      />
    </div>
  );
}

export { Header_M };
