import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface UrlLabelItem {
    url: string;
    label: string;
}

interface WarrantyTabProps {
    warranty: UrlLabelItem
    setWarranty: React.Dispatch<React.SetStateAction<UrlLabelItem>>
}

export default function WarrantyTab({
    warranty,
    setWarranty
}: WarrantyTabProps) {
    return (
        <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                    <Label htmlFor="warranty-label">Warranty Information</Label>
                    <Input
                        id="warranty-label"
                        placeholder="Warranty information"
                        value={warranty.label}
                        onChange={(e) => setWarranty({ ...warranty, label: e.target.value })}
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="warranty-url">Warranty Policy URL (Optional)</Label>
                    <Input
                        id="warranty-url"
                        placeholder="Link to warranty policy"
                        value={warranty.url}
                        onChange={(e) => setWarranty({ ...warranty, url: e.target.value })}
                    />
                </div>
            </div>

            <p className="text-sm text-muted-foreground mt-1">
                Add warranty information and an optional link to warranty policy.
            </p>
        </div>
    )
} 