import { SendMailOption } from "..";

type Props = {
  from: string;
  to: string | string[];
  subject: string;
  cc?: string | string[];
  bcc?: string | string[];
  body: string;
  attachments?: any[];
};

const emailTemplate = (props: Props): SendMailOption => {
  return {
    to: props.to,
    subject: props.subject,
    cc: props?.cc,
    bcc: props?.bcc,
    from: props.from,
    body: `
                <div style="background: #f6f6f6;">
                    <div
                        style="padding: 40px;margin-top: 25px;background: #ffffff;width: 600px; margin-left: auto; margin-right: auto;"
                    >
                        <img style="margin-left: -10px;" src="cid:footer-mail-logo-jpg" width="200px" height="auto"/>
                        <br/>
                        <br/>
                        ${props.body}
                        <br/>
                        <br/>
                        <br/>
                        For Support Please Contact
                        <br/>
                        <h3 style="margin: 0; font-weight: 800; color: #0355A0;"><b>Maxton Manufacturing Company</b></h3>
                        1728 Orbit Way | Minden, NV 89423-4114
                        <br>
                        <a 
                            href="tel:**************" 
                            target="_blank" 
                            title="tel:**************" 
                            style="text-decoration: none; color: #0045d8;" 
                            rel="noopener"
                        >
                            **************
                        </a> or 
                        <a 
                            href="mailto:<EMAIL>" 
                            target="_blank" 
                            title="mailto:<EMAIL>" 
                            style="text-decoration: none; color: #0045d8;" 
                            rel="noopener"
                        >
                            <EMAIL>
                        </a>
                        <br/>
                    </div>
                </div>
            `,
    attachments: [
      {
        filename: "footer-mail-logo.jpg",
        path: "https://cdn.sanity.io/images/s0dti8ee/production/a8f2bc50ca133532581f01c50cdf630da0ea1bb9-321x65.png",
        cid: "footer-mail-logo-jpg",
      },
      ...(props?.attachments?.length ? props.attachments : []),
    ],
  };
};

// Export the base email template
export default emailTemplate;

// Export all template creators
export { default as createAccountApprovalTemplate } from "./account-approval";
export { default as createAccountStatusTemplate } from "./account-approval";
export { default as createCancellationApprovalTemplate } from "./cancellation-approval";
export { default as createForgotPasswordTemplate } from "./forgot-password";
export { default as createBillingAddressApprovalTemplate } from "./billing-address-approval";
export { default as createOrderConfirmationTemplate } from "./order-confirmation";
export { default as createOrderUpdateTemplate } from "./order-update";
export { default as createQuoteConfirmationTemplate } from "./quote-confirmation";
export { default as createQuoteAdminNotificationTemplate } from "./quote-admin-notification";
export { default as createRegistrationConfirmationTemplate } from "./registration-confirmation";
