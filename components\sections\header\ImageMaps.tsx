import React, { useEffect, useRef, useState } from "react";
import { MainImage } from "types";
import { getImageDimensions } from "@sanity/asset-utils";

interface ImageMapProps {
  currentPath: string;
  title: string;
  image: MainImage;
}

const ImageMaps: React.FC<ImageMapProps> = ({ currentPath, title, image }) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const [scale, setScale] = useState(1);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const originalWidth = getImageDimensions(image?.image)?.width || 1;

  // Function to scale coordinates
  const scaleCoords = (coords: string) => {
    return coords
      .split(",")
      .map((coord) => Math.round(parseInt(coord) * scale))
      .join(",");
  };

  // Function to calculate scale
  const calculateScale = () => {
    if (imageRef.current) {
      const currentWidth = imageRef.current.clientWidth;
      setScale(currentWidth / originalWidth);
    }
  };

  // Handle image load
  const handleImageLoad = () => {
    setIsImageLoaded(true);
    calculateScale();
  };

  useEffect(() => {
    // Calculate scale on mount and window resize
    calculateScale();

    const handleResize = () => {
      calculateScale();
    };

    window.addEventListener("resize", handleResize);

    // Force browser to recognize image map
    const maps = document.getElementsByTagName("map");
    for (let i = 0; i < maps.length; i++) {
      maps[i].addEventListener("click", (e) => e.stopPropagation());
    }

    return () => window.removeEventListener("resize", handleResize);
  }, [originalWidth, isImageLoaded]);

  const renderImageMap = () => {
    switch (currentPath) {
      case "/repair/uc2":
        return (
          <div className="text-center">
            <img
              ref={imageRef}
              src={`${image?.image}`}
              alt={image?.alt || "Repair Parts Diagram"}
              className="max-w-full h-auto mx-auto inline-block"
              useMap="#image-map"
              onLoad={handleImageLoad}
            />
            {/* <map name="image-map" id="image-map">
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("119,1037,137,1019")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("204,990,221,1008")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("228,922,247,939")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("297,904,315,923")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("412,890,431,905")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("517,954,536,972")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("475,1089,496,1107")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("593,623,612,643")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Seal Kit(Click To Purchase Online)"
                title="Model#291510 Seal Kit(Click To Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("682,806,921,823")}
                shape="rect"
              />
            </map> */}
          </div>
        );

      case "/repair/uc1":
        return (
          <div className="text-center">
            <img
              ref={imageRef}
              src={`${image?.image}`}
              alt={image?.alt || "Repair Parts Diagram"}
              className="max-w-full h-auto mx-auto inline-block"
              useMap="#image-map"
              onLoad={handleImageLoad}
            />
            {/* <map name="image-map" id="image-map">
              <area
                target="_blank"
                alt="Model#205900 Coil Cover(Click to Purchase Online)"
                title="Model#205900 Coil Cover(Click to Purchase Online)"
                href="/repair/uc1"
                coords={scaleCoords("743,48,823,87")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#208880 C-Frame(Click to Purchase Online)"
                title="Model#208880 C-Frame(Click to Purchase Online)"
                href="/store/products/c45de61d-244e-40d2-ba9b-1020428e107e"
                coords={scaleCoords("943,206,996,243")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#28082B 115 VAC/24VDC (Red) with C-Frame (Click to Purchase Online)"
                title="Model#28082B 115 VAC/24VDC (Red) with C-Frame (Click to Purchase Online)"
                href="/repair/uc1"
                coords={scaleCoords("606,203,711,285")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#281630 Coils Base Plate Assembly(Click to Purchase Online)"
                title="Model#281630 Coils Base Plate Assembly(Click to Purchase Online)"
                href="/store/products/d8bc394e-bcfa-4aaf-9b10-b1483053bec8"
                coords={scaleCoords("878,355,960,396")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Flanges(Click to Purchase Online)"
                title="Flanges(Click to Purchase Online)"
                href="/store/products?category=Flanges"
                coords={scaleCoords("171,952,301,1030")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Solenoid Tube Assembly(Click to Purchase Online)"
                title="Model#291510 Solenoid Tube Assembly(Click to Purchase Online)"
                href="/store/products/42315225-2717-473e-bb19-a2b1babaffe6"
                coords={scaleCoords("858,400,960,447")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#292950 Ball Cage Kit(Click to Purchase Online)"
                title="Model#292950 Ball Cage Kit(Click to Purchase Online)"
                href="/store/products/12150523-b4f5-4fe3-8aa0-7d99b5d87dba"
                coords={scaleCoords("849,457,917,495")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#29298A-2 Solenoid Seat kit(Click to Purchase Online)"
                title="Model#29298A-2 Solenoid Seat kit(Click to Purchase Online)"
                href="/store/products/150204ba-b791-4f88-872c-c9c4785dc91a"
                coords={scaleCoords("878,497,947,535")}
                shape="rect"
              />
              {[
                "271,158,287,175",
                "266,199,281,217",
                "238,206,258,223",
                "523,282,541,299",
                "439,311,458,328",
                "515,359,532,375",
                "834,376,851,392",
                "486,400,505,417",
                "325,500,342,513",
                "476,495,494,513",
                "213,497,232,515",
                "219,575,240,592",
                "23,586,45,603",
                "224,657,244,674",
                "367,754,389,773",
                "417,1006,439,1024",
                "425,1050,446,1068",
                "624,995,640,1012",
                "839,1105,858,1122",
                "593,576,613,593",
                "646,702,663,719",
                "725,730,743,746",
                "842,729,861,747",
              ].map((coords, index) => (
                <area
                  key={index}
                  target="_blank"
                  alt="Model#291510 Solenoid kit(Click to Purchase Online)"
                  title="Model#291510 Solenoid kit(Click to Purchase Online)"
                  href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                  coords={scaleCoords(coords)}
                  shape="rect"
                />
              ))}
              <area
                target="_blank"
                alt="Model#29292A Seat kit(Click to Purchase Online)"
                title="Model#29292A Seat kit(Click to Purchase Online)"
                href="/store/products/42315225-2717-473e-bb19-a2b1babaffe6"
                coords={scaleCoords("28,1156,368,1174")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291510 Solenoid kit(Click to Purchase Online)"
                title="Model#291510 Solenoid kit(Click to Purchase Online)"
                href="/store/products/52bc70e7-436a-46d8-b4e6-cd6295bf6242"
                coords={scaleCoords("32,1120,344,1143")}
                shape="rect"
              />
            </map> */}
          </div>
        );

      case "/repair/uc4mr":
        return (
          <div className="text-center">
            <img
              ref={imageRef}
              src={`${image?.image}`}
              alt={image?.alt || "Repair Parts Diagram"}
              className="max-w-full h-auto mx-auto inline-block"
              useMap="#image-map"
              onLoad={handleImageLoad}
            />
            {/* <map name="image-map" id="image-map">
              <area
                target="_blank"
                alt="Model#291540 Seal Kit(Click to Purchase Online)"
                title="Model#291540 Seal Kit(Click to Purchase Online)"
                href="/store/products/6bf9cb4f-deb2-416f-b45a-be8fc04c46ad"
                coords={scaleCoords("7,1208,332,1232")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291540 Seal Kit(Click to Purchase Online)"
                title="Model#291540 Seal Kit(Click to Purchase Online)"
                href="/store/products/6bf9cb4f-deb2-416f-b45a-be8fc04c46ad"
                coords={scaleCoords("620,912,643,929")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#291540 Seal Kit(Click to Purchase Online)"
                title="Model#291540 Seal Kit(Click to Purchase Online)"
                href="/store/products/6bf9cb4f-deb2-416f-b45a-be8fc04c46ad"
                coords={scaleCoords("671,938,691,956")}
                shape="rect"
              />
            </map> */}
          </div>
        );

      case "/repair/uc4":
        return (
          <div className="text-center">
            <img
              ref={imageRef}
              src={`${image?.image}`}
              alt={image?.alt || "Repair Parts Diagram"}
              className="max-w-full h-auto mx-auto inline-block"
              useMap="#image-map"
              onLoad={handleImageLoad}
            />
            {/* <map name="image-map" id="image-map">
              <area
                target="_blank"
                alt="Model#292920 Solenoid Kit(Click to Purchase Online)"
                title="Model#292920 Solenoid Kit(Click to Purchase Online)"
                href="/store/products/e5c0f4ca-c090-4e11-ab8c-ed2c949ede82"
                coords={scaleCoords("460,119,530,164")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#292950 Ball Cage Kit(Click to Purchase Online)"
                title="Model#292950 Ball Cage Kit(Click to Purchase Online)"
                href="/store/products/12150523-b4f5-4fe3-8aa0-7d99b5d87dba"
                coords={scaleCoords("315,231,387,275")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#29298A-2 Seat Kit(Click to Purchase Online)"
                title="Model#29298A-2 Seat Kit(Click to Purchase Online)"
                href="/store/products/150204ba-b791-4f88-872c-c9c4785dc91a"
                coords={scaleCoords("317,290,387,333")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#237500 Coil Cover(Click to Purchase Online)"
                title="Model#237500 Coil Cover(Click to Purchase Online)"
                href="/repair/uc4"
                coords={scaleCoords("613,50,695,81")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#28082B 115 Vac/24 VDC(RED) with C-Frame(Click to Purchase Online)"
                title="Model#28082B 115 Vac/24 VDC(RED) with C-Frame(Click to Purchase Online)"
                href="/repair/uc4"
                coords={scaleCoords("625,235,791,279")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#208880 C-Frame(Click to Purchase Online)"
                title="Model#208880 C-Frame(Click to Purchase Online)"
                href="/store/products/c45de61d-244e-40d2-ba9b-1020428e107e"
                coords={scaleCoords("945,297,996,324")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#28390B Base Plate Assembly(Click to Purchase Online)"
                title="Model#28390B Base Plate Assembly(Click to Purchase Online)"
                href="/store/products/5c1981a5-ea17-4173-bff9-2bd525d2e60a"
                coords={scaleCoords("735,548,819,606")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#283430 Grooved Flange Assembly 2 inch(Click to Purchase Online)"
                title="Model#283430 Grooved Flange Assembly 2 inch(Click to Purchase Online)"
                href="/store/products/529b2be5-ce2f-480a-ab50-ec630fe7fe37"
                coords={scaleCoords("201,1045,291,1099")}
                shape="rect"
              />
              <area
                target="_blank"
                alt="Model#283580 Threaded Flange Assembly 2 inch(Click to Purchase Online)"
                title="Model#283580 Threaded Flange Assembly 2 inch(Click to Purchase Online)"
                href="/store/products/16ba3f94-5f13-4999-b869-b2d55d6e1bb1"
                coords={scaleCoords("202,1101,289,1156")}
                shape="rect"
              />
              {[
                "22,1214,356,1231",
                "22,1177,332,1199",
                "442,95,460,113",
                "494,221,512,237",
                "493,241,510,258",
                "596,342,616,358",
                "435,364,455,380",
                "444,392,465,411",
                "285,535,303,553",
                "424,536,442,554",
                "506,604,527,622",
                "270,623,288,638",
                "410,614,427,634",
                "694,644,715,661",
                "766,713,786,732",
                "866,771,884,789",
                "515,888,535,906",
                "361,918,384,938",
                "297,995,319,1013",
                "425,1056,444,1074",
                "474,1135,495,1157",
                "624,911,642,929",
                "649,982,671,1000",
                "598,1010,616,1023",
                "888,1101,909,1123",
                "935,1128,955,1148",
              ].map((coords, index) => (
                <area
                  key={index}
                  target="_blank"
                  alt="Model#291540 Seal Kit(Click to Purchase Online)"
                  title="Model#291540 Seal Kit(Click to Purchase Online)"
                  href="/store/products/6bf9cb4f-deb2-416f-b45a-be8fc04c46ad"
                  coords={scaleCoords(coords)}
                  shape="rect"
                />
              ))}
            </map> */}
          </div>
        );

      default:
        return null;
    }
  };

  return <div className="image-map-container w-full">{renderImageMap()}</div>;
};

export default ImageMaps;
