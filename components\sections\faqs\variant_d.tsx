import { <PERSON><PERSON> } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState } from "react";
import { FAQProps } from ".";
import { AskedQuestion, FaqsWithCategory } from "../../../types";
import { Input } from "@stackshift-ui/input";
import { CiSearch } from "react-icons/ci";
import { MyPortableTextComponents } from "../../../types";
import { urlFor } from "lib/sanity";
import { PortableText, PortableTextComponents } from "@portabletext/react";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-xl  text-gray-800">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-xs md:text-sm text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className=" pl-10 mb-6 text-xs md:text-sm text-gray-800 leading-loose list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Faqs_B({
  subtitle,
  title,
  faqsWithCategories,
}: FAQProps) {
  const [show, setShow] = useState<boolean>(false);
  const [activeQA, setActiveQA] = useState<number | null>(null);
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Prepare FAQ array with all questions
  const allQuestions = faqsWithCategories
    ?.reduce((acc: AskedQuestion[], curr) => {
      return [...acc, ...(curr.askedQuestions || [])];
    }, [])
    .sort((a, b) => a.question.localeCompare(b.question));

  // Filter questions based on search and category
  const getFilteredQuestions = () => {
    let questions =
      activeCategory === "all"
        ? allQuestions
        : faqsWithCategories?.find((cat) => cat.category === activeCategory)
            ?.askedQuestions || [];

    if (searchQuery) {
      questions = questions.filter((q) =>
        q.question.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return questions;
  };

  const filteredQuestions = getFilteredQuestions();

  // toggle view or hide answers on click for each FAQ items
  const toggleView = (position: number) => {
    if (activeQA === position) {
      setShow(!show);
    } else {
      setActiveQA(position);
      setShow(true);
    }
  };

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        {/* Categories tabs aligned left */}
        <div className="max-w-3xl mx-auto mb-8 border-b border-gray-200">
          <Flex className="gap-8">
            <Button
              as="button"
              variant="unstyled"
              onClick={() => setActiveCategory("all")}
              className={`px-4 py-2 font-medium ${
                activeCategory === "all"
                  ? "border-b-2 border-primary text-primary"
                  : "text-gray-500 hover:text-primary"
              }`}
            >
              All
            </Button>
            {faqsWithCategories?.map((tab, index) => (
              <Button
                as="button"
                key={index}
                variant="unstyled"
                onClick={() => setActiveCategory(tab?.category ?? "")}
                className={`px-4 py-2 font-medium ${
                  activeCategory === tab?.category
                    ? "border-b-2 border-primary text-primary"
                    : "text-gray-500 hover:text-primary"
                }`}
              >
                {tab?.category}
              </Button>
            ))}
          </Flex>
        </div>

        {/* Centered search field */}
        <div className="flex justify-center mb-8">
          <div className="relative max-w-md w-full">
            <Input
              type="text"
              placeholder="Search questions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full border border-primary/80 rounded-lg pr-10 focus:border-primary focus:ring-1 focus:ring-primary"
            />
            <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {/* Centered Questions and Answers section */}
        <div className="max-w-3xl mx-auto space-y-4">
          {filteredQuestions.length === 0 ? (
            <Text className="text-gray-500 text-center">
              No questions found in{" "}
              {activeCategory === "all" ? "any category" : activeCategory}
            </Text>
          ) : (
            filteredQuestions.map((content, index) => (
              <div key={index} className="border rounded-lg overflow-hidden">
                <Button
                  as="button"
                  variant="unstyled"
                  className={`w-full text-left flex justify-between items-center px-4 py-4 transition-colors group ${
                    activeQA === index && show
                      ? "bg-primary text-white"
                      : "hover:bg-primary hover:text-white text-gray-900"
                  }`}
                  onClick={() => toggleView(index)}
                >
                  <Heading
                    type="h2"
                    fontSize="xs"
                    weight="semibold"
                    className={`lg:text-base  ${
                      activeQA === index && show
                        ? " !text-white"
                        : " group-hover:!text-white !text-gray-900"
                    }`}
                  >
                    {content?.question}
                  </Heading>
                  <svg
                    className={`w-5 h-5 transition-transform ${
                      activeQA === index && show ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </Button>

                {/* Dropdown answer */}
                {activeQA === index && show && (
                  <div className="p-4 bg-white border-t">
                    <PortableText
                      value={content.firstColumn}
                      components={textComponentBlockStyling}
                      onMissingComponent={false}
                    />
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </Container>
    </Section>
  );
}

function SubtitleAndTitle({
  subtitle,
  title,
}: {
  subtitle?: string;
  title?: string;
}) {
  return (
    <React.Fragment>
      {subtitle ? (
        <Text weight="bold" className="text-secondary">
          {subtitle}
        </Text>
      ) : null}
      {title ? <Heading fontSize="3xl">{title}</Heading> : null}
    </React.Fragment>
  );
}

function ArrowIcon({
  show,
  activeQA,
  index,
}: {
  show: boolean;
  activeQA: number | null;
  index: number;
}) {
  return (
    <svg
      className="w-4 h-4 text-primary lg:h-6 lg:w-6 xl:h-6 xl:w-6 2xl:h-6 2xl:w-6"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d={
          show && activeQA === index
            ? "M5 10l7-7m0 0l7 7m-7-7v18"
            : "M19 14l-7 7m0 0l-7-7m7 7V3"
        }
      />
    </svg>
  );
}

export { Faqs_B };
