import { extractLink } from "helper";
import Link from "next/link";
import React from "react";
import { LabeledRoute } from "types";
import { cn } from "utils/cn";
import { StyleVariants } from "types";
import { Spinner } from "@sanity/ui";

type Variant =
  | "outline"
  | "ghost"
  | "link"
  | "custom"
  | "solid"
  | "addToWishlist"
  | "unstyled"
  | "swiper_pagination"
  | "tab"
  | "solidPrimary"
  | "outlinePrimary"
  | "outlineWhite"
  | "navSidebar"
  | "maxtonPrimary"
  | "maxtonSecondary"
  | "primaryHover";
type TextSize = "xs" | "sm" | "md" | "lg";
type RadiusSize = "sm" | "md" | "lg" | "xl" | "2xl" | "none";

interface BaseType {
  /** Defines the classname of the button. */
  className?: string;
  variant?: Variant;
  /** String value that labels the interactive element e.g. "Submit" */
  ariaLabel: string;
  /** Defines the content inside the button. */
  children?: React.ReactNode;
  /** Set button to link component */
  size?: TextSize;
  borderRadius?: RadiusSize;
  [key: string]: any;
}

interface Link extends BaseType {
  /** Link data pass to the button */
  link: LabeledRoute;
  as?: "link";
}

interface Button extends BaseType {
  as: "button";
  /** Sets the button in a loading state. */
  loading?: boolean;
  /** Sets the button in a disabled state. */
  disabled?: boolean;
  /** Custom loading component. */
  loadingComponent?: React.ReactNode;
  /** Function that runs when the button is clicked. */
  onClick?: (...args: any) => any;
  /** Set button type. Defaults to button */
  type?: "button" | "submit";
  isActive?: any;
}

type ButtonProps = Button | Link;

export default function Button(props: ButtonProps) {
  const sizes = {
    xs: "py-1 px-3 text-xs",
    sm: "py-2 px-4 text-sm",
    md: "py-3 px-6 text-base",
    lg: "py-4 px-7 text-lg",
  };

  const borderRadiusMap = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl",
    "2xl": "rounded-2xl",
  };

  const {
    borderRadius,
    size,
    variant,
    ariaLabel,
    className,
    children,
    isActive,
  } = props;

  const buttonRadius = borderRadiusMap[borderRadius];
  const buttonSize = sizes[size] || sizes["md"];

  const commonStyles =
    "inline-block rounded-l-xl rounded-t-xl font-bold transition duration-200";
  const solid = `${commonStyles} ${buttonSize} rounded-full bg-primary text-white hover:bg-primary/70`;
  const maxtonPrimary = `${commonStyles} ${buttonSize} ${className} relative flex flex-row items-center gap-4 md:gap-2 lg:gap-4 justify-center font-normal bg-primary text-white border-2 border-primary hover:border-primary/0 !rounded-none px-8 md:px-4 lg:px-8 py-3 transition duration-200 overflow-hidden z-10 before:content-[''] before:absolute before:top-0 before:left-0 before:w-0 before:h-full before:bg-[#063a6b] before:transition-all before:duration-300 before:ease-in-out before:z-[-1] hover:before:w-full`;

  const maxtonSecondary = `${commonStyles} ${buttonSize} ${className} relative flex flex-row items-center  gap-4 md:gap-2 lg:gap-4 font-normal bg-white-50 border-2 border-primary hover:border-primary/80 text-primary !rounded-none px-8 md:px-4 lg:px-8 py-3 transition duration-200 overflow-hidden z-10 before:content-[''] before:absolute before:top-0 before:left-0 before:w-0 before:h-full before:bg-[#fff] before:transition-all before:duration-300 before:ease-in-out before:z-[-1] hover:before:w-full`;

  const solidPrimary = `font-avenir bg-primary text-white hover:bg-primary/70  ${buttonSize}  font-semibold`;
  const outlinePrimary = `rounded-full font-avenir bg-transparent border-2 border-primary text-primary  hover:text-white hover:border-white  ${buttonSize}  font-semibold`;
  const outlineWhite = `rounded-full font-avenir bg-transparent border-2 text-white border-white  hover:border-primary hover:text-primary  ${buttonSize}  font-semibold`;
  const custom = `inline-block bg-primary hover:bg-primary-foreground ${buttonSize} ${buttonRadius || "rounded-md"
    } text-gray-50 font-bold transition duration-200`;
  const outline = `${commonStyles} ${buttonSize} ${buttonRadius} bg-white hover:bg-primary-foreground/20 outline outline-1 text-primary outline-primary`;
  const ghost = `${commonStyles}  ${buttonRadius} ${buttonSize} bg-transparent hover:text-primary text-primary`;
  const link = `transition-200 text-primary hover:text-primary-foreground underline  ${buttonRadius} ${cn(
    buttonSize,
    "px-0 py-0"
  )} `;
  const unstyled = ``;
  const swiper_pagination = `mr-1 ${isActive ? "bg-primary" : "bg-gray-200"
    } rounded-full p-1 focus:outline-none`;
  const tab = `mx-auto mb-1 w-auto px-4 py-2  duration-200 transition focus:outline-none font-bold  ${isActive
    ? " bg-primary text-white shadow "
    : "  text-primary/40 bg-gray-200 hover:bg-secondary-foreground hover:text-primary-foreground hover:shadow"
    }`;
  const addToWishlist = ` ${commonStyles} ${buttonRadius} ${buttonSize} classNames="ml-auto sm:ml-0 flex-shrink-0 inline-flex items-center justify-center w-full  rounded-md border hover:border-primary`;
  const navSidebar = `self-center navbar-burger lg:hidden text-primary border-2 border-primary rounded-full p-2`;

  const primaryHover = `${commonStyles} bg-background text-primary hover:bg-primary hover:text-background transition-colors duration-300 px-12 py-4`;

  const variants: StyleVariants<Variant> = {
    outline,
    ghost,
    link,
    custom,
    solid,
    solidPrimary,
    outlinePrimary,
    outlineWhite,
    addToWishlist,
    unstyled,
    swiper_pagination,
    tab,
    navSidebar,
    maxtonPrimary,
    maxtonSecondary,
    primaryHover,
  };

  const variantClass = variants[variant] ?? solid;

  if (props.as === "link") {
    const { link, ariaLabel, ...rest } = props;

    return (
      <Link href={extractLink(link)} passHref legacyBehavior>
        <a
          className={cn(variantClass, className)}
          aria-label={ariaLabel}
          target={link?.linkTarget}
          rel={link?.linkTarget === "_blank" ? "noopener noreferrer" : ""}
          {...rest}
        >
          {children}
        </a>
      </Link>
    );
  }

  const { loadingComponent, onClick, loading, disabled, type } = props;
  const Loader = loadingComponent ?? (
    <div className="flex gap-x-5 items-center justify-center">
      <Spinner size={20} />
      <span>{children}</span>
    </div>
  );

  return (
    <button
      onClick={onClick}
      disabled={disabled ?? loading}
      className={cn(variantClass, className)}
      aria-label={ariaLabel}
      type={type}
    >
      {loading ? Loader : children}
    </button>
  );
}
