import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import createOrderUpdateTemplate from "@/lib/mailer/templates/order-update";
import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { CalculatorData } from "@/pages/api/orders";
import { NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  PUT: checkAdmin(updateOrderStatusHandler),
});

const orderStatusEnum = z.enum([
  "pending",
  "awaiting_payment",
  "shipped",
  "delivered",
  "processing",
  "completed",
  "canceled",
  "denied",
  "canceled_reversal",
  "failed",
  "refunded",
  "reversed",
  "chargeback",
  "expired",
  "processed",
  "voided",
]);

const updateOrderStatusSchema = z.object({
  status: orderStatusEnum,
  trackingNumber: z.string().nullish(),
  trackingUrl: z.string().nullish(),
  estimatedDeliveryDate: z.string().nullish(),
  additionalInfo: z.string().nullish(),
});

export type UpdateOrderStatusRequest = z.infer<typeof updateOrderStatusSchema>;
export type OrderStatusType = z.infer<typeof orderStatusEnum>;

export interface UpdateOrderStatusResponse {
  success?: boolean;
  error?: string;
}

interface OrderOption {
  name?: string;
  value?: string;
  price?: number;
}

async function updateOrderStatusHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<UpdateOrderStatusResponse>,
) {
  const { id } = req.query;

  if (!id || typeof id !== "string") {
    return res.status(400).json({ error: "Order ID is required" });
  }

  const data = updateOrderStatusSchema.safeParse(req.body);

  if (data.error) {
    return res.status(400).json({ error: data.error.issues[0].message });
  }

  const {
    status,
    trackingNumber,
    trackingUrl,
    estimatedDeliveryDate,
    additionalInfo,
  } = data.data;
  const supabaseAdminClient = createSupabaseAdminClient();

  // Get the order details with customer info and previous status
  const orderResult = await supabaseAdminClient
    .from("orders")
    .select(
      `
            *,
            customer:customers(
                id,
                company_name,
                customer_number,
                users(
                    id,
                    email,
                    first_name,
                    last_name,
                    phone,
                    business_details(maxton_account)
                )
            ),
            order_items(
                id,
                quantity,
                options,
                item_price,
                calculator_data,
                products(
                    id,
                    name,
                    price
                )
            ),
            order_statuses(
                status,
                created_at
            ),
            billing_address:billing_addresses(
                id,
                address,
                city,
                state,
                zip_code,
                country
            ),
            shipping_address:shipping_addresses(
                id,
                contact_name,
                address,
                city,
                state,
                zip_code,
                contact_number,
                country
            )
        `,
    )
    .eq("id", id)
    .single();

  if (orderResult.error) {
    return res.status(404).json({ error: "Order not found" });
  }

  const order = orderResult.data;
  const oldStatus = order.order_statuses?.[0]?.status || "pending";

  if (!order.tracking_link) {
    await supabaseAdminClient
      .from("orders")
      .update({
        tracking_link: trackingUrl,
      })
      .eq("id", id);
  }

  // Update status
  const statusResult = await supabaseAdminClient
    .from("order_statuses")
    .insert({
      order_id: id,
      customer_id: order.customer_id,
      status,
    })
    .select("id")
    .single();

  if (statusResult.error) {
    return res.status(400).json({ error: statusResult.error.message });
  }

  const now = new Date();
  const updatedAt = now.toISOString();

  await supabaseAdminClient
    .from("orders")
    .update({
      updated_at: updatedAt,
    })
    .eq("id", id);

  // Send email to customer
  try {
    const email = order.customer?.users?.email;
    const firstName = order.customer?.users?.first_name || undefined;
    const lastName = order.customer?.users?.last_name || undefined;
    const companyName = order.customer?.company_name || undefined;
    const phoneNumber = order.customer?.users?.phone || undefined;
    const accountNumber = order.customer?.customer_number || undefined;
    const maxtonAccountNum = order.customer?.users?.business_details?.[0]
      ?.maxton_account
      ? parseInt(order.customer?.users?.business_details[0].maxton_account)
      : undefined;

    // Only try to fetch user details if we have a valid user ID and status is 'completed'
    if (email && status === "completed") {
      // Process shipping and billing addresses
      const billingAddress = order.billing_address
        ? {
          contactName:
            `${firstName || ""} ${lastName || ""}`.trim() || undefined,
          address: order.billing_address.address || undefined,
          city: order.billing_address.city || undefined,
          state: order.billing_address.state || undefined,
          zipCode: order.billing_address.zip_code || undefined,
          phone: phoneNumber || undefined,
          country: order.billing_address.country || undefined,
        }
        : undefined;

      const shippingAddress = order.shipping_address
        ? {
          contactName: order.shipping_address.contact_name || undefined,
          address: order.shipping_address.address || undefined,
          city: order.shipping_address.city || undefined,
          state: order.shipping_address.state || undefined,
          zipCode: order.shipping_address.zip_code || undefined,
          phone: order.shipping_address.contact_number || undefined,
          country: order.shipping_address.country || undefined,
        }
        : undefined;

      const emailTemplate = createOrderUpdateTemplate({
        to: email,
        orderId: order.id,
        invoice: order.invoice || "",
        oldStatus,
        newStatus: status,
        statusDate: new Date().toISOString(),
        firstName,
        lastName,
        companyName,
        phoneNumber,
        accountNumber,
        maxtonAccountNum,
        // Add order details
        datePurchased: order.created_at || undefined,
        paymentMethod: order.payment_type || undefined,
        poNumber: order.purchase_order || undefined,
        deliveryMethod: order.delivery_method || undefined,
        shipCollect:
          typeof order.ship_collect === "boolean"
            ? order.ship_collect
            : undefined,
        upsAccountNumber: order.ups_account_number || undefined,
        items: order.order_items.map((item) => {
          const options = item.options as OrderOption[] | null;
          const calculatorData =
            item.calculator_data as unknown as CalculatorData;
          return {
            name: item.products.name,
            quantity: item.quantity,
            price: item.products.price || undefined,
            item_price: item.item_price || undefined,
            calculator_data: calculatorData,
            options:
              options?.map((opt) => ({
                name: opt.name || "",
                value: opt.value || "",
                price: typeof opt.price === "number" ? opt.price : undefined,
              })) || undefined,
          };
        }),
        trackingNumber: trackingNumber || undefined,
        trackingUrl: trackingUrl || undefined,
        estimatedDeliveryDate: estimatedDeliveryDate || undefined,
        additionalInfo: additionalInfo || undefined,
        billingAddress,
        shippingAddress,
        notes: order.notes || undefined,
      });

      const mailerOptions = createMailerOptions();
      const mailer = createMailer(mailerOptions);
      await sendEmail(mailer, emailTemplate);
    }
  } catch (emailError: any) {
    console.error("Failed to send order status update email:", emailError);
    // Don't return error response as the status update was successful
  }

  return res.status(200).json({ success: true });
}
