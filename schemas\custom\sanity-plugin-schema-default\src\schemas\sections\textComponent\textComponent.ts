import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdLineWeight } from "react-icons/md";

// Images
import variantAImage from "./images/variant_a.jpg";
import variantBImage from "./images/variant_b.jpg";
import variantCImage from "./images/variant_c.jpg";
import variantDImage from "./images/variant_a.jpg";
import variantEImage from "./images/variant_e.png";
import variantFImage from "./images/variant_f.png";

import initialValue from "./initialValue";
import { textComponentSchema } from "./schema";

export const variantsList = [
  {
    title: "Variant A",
    description: "Add a single-column text component.",
    value: "variant_a",
    image: variantAImage.src,
  },
  {
    title: "Variant B",
    value: "variant_b",
    description: "Add a text component with 2 columns.",
    image: variantBImage.src,
  },
  {
    title: "Variant C",
    value: "variant_c",
    description: "Add a text component with 3 columns.",
    image: variantCImage.src,
  },
  {
    title: "Variant D",
    value: "variant_d",
    description: "Add a text component with 2 columns.",
    image: variantDImage.src,
  },
  {
    title: "Variant E",
    value: "variant_e",
    description: "Add a text component with 2 columns.",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    value: "variant_f",
    description: "Add a text component with 2 columns.",
    image: variantFImage.src,
  },
];

export default rootSchema(
  "textComponent",
  "Text Component",
  MdLineWeight,
  variantsList,
  textComponentSchema,
  initialValue
);
