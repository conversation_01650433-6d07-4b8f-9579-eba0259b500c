import AdminLayout from "@/components/features/admin/layout";
import { ProductArrangementManager } from "@/components/features/admin/products/product-arrangement-manager";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { useGetAllCategoriesQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { ArrowLeft, ChevronDown, ChevronRight, Settings } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";

export default function ProductArrangementsPage() {
  const userData = useAuthStore((state) => state.data);
  const token = useAuthStore((state) => state.token);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  // Check if user has admin role (simplified check)
  const isAdmin = useAuthStore((state) => state.isAdmin)();
  const isStaff = useAuthStore((state) => state.isStaff)();

  const hasPermission = isAdmin || isStaff;

  console.log({ hasPermission, role: userData?.role });

  const { data: categoriesData, isLoading: isCategoriesLoading } = useGetAllCategoriesQuery(
    1,
    100,
    token
  );

  const categories = categoriesData?.categories || [];

  // Define the custom ordering for main categories
  const categoryOrder = [
    "Coil Hardware",
    "Elevator Shut off Valves",
    "Flanges",
    "Isolation Couplings",
    "Solenoid Coils",
    "Support Products",
    "Valve Kits",
    "Valves"
  ];

  // Separate main categories and subcategories
  const mainCategories = categories.filter(cat => !cat.parent_category_id);
  const subcategories = categories.filter(cat => cat.parent_category_id);

  // Sort main categories according to the custom order
  const sortedMainCategories = mainCategories.sort((a, b) => {
    const aIndex = categoryOrder.indexOf(a.name ?? "");
    const bIndex = categoryOrder.indexOf(b.name ?? "");

    // If category is not in the order list, put it at the end
    if (aIndex === -1 && bIndex === -1) return a.name?.localeCompare(b.name ?? "") ?? -1;
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;

    return aIndex - bIndex;
  });

  // Get subcategories for a specific parent category
  const getSubcategories = (parentId: string) => {
    return subcategories.filter(sub => sub.parent_category_id === parentId);
  };

  // Toggle category expansion
  const toggleCategoryExpansion = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  // Handle category selection
  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    // If it's Valves category, don't select it directly, just toggle expansion
    if (categoryName === "Valves") {
      toggleCategoryExpansion(categoryId);
      return;
    }
    setSelectedCategoryId(categoryId);
  };

  if (!hasPermission) {
    return (
      <AdminLayout>
        <Head>
          <title>Product Arrangements - Access Denied</title>
        </Head>
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
            <div className="mb-4">
              <Settings className="h-16 w-16 text-muted-foreground" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
            <p className="text-muted-foreground mb-4">
              You don't have permission to manage product arrangements.
            </p>
            <p className="text-sm text-muted-foreground mb-6">
              Please contact your administrator to request access.
            </p>
            <Link href="/admin/products">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Product Arrangements | Admin</title>
      </Head>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Product Arrangements</h1>
              <p className="text-muted-foreground mt-2">
                Manage the display order of products within categories using drag and drop.
              </p>
            </div>
            <Link href="/admin/products">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>

        {isCategoriesLoading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading categories...</p>
            </div>
          </div>
        ) : categories.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center min-h-[400px] text-center">
              <Settings className="h-16 w-16 text-muted-foreground mb-4" />
              <h2 className="text-xl font-semibold mb-2">No Categories Found</h2>
              <p className="text-muted-foreground mb-4">
                You need to create categories before you can arrange products.
              </p>
              <Link href="/admin/categories">
                <Button>Manage Categories</Button>
              </Link>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Category Selection Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Categories</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="space-y-1">
                    {sortedMainCategories.map((category) => {
                      const categorySubcategories = getSubcategories(category.id);
                      const isValves = category.name === "Valves";
                      const isExpanded = expandedCategories.has(category.id);

                      return (
                        <div key={category.id}>
                          <button
                            onClick={() => handleCategorySelect(category.id, category.name ?? "")}
                            className={`w-full text-left px-4 py-3 hover:bg-muted transition-colors flex items-center justify-between ${selectedCategoryId === category.id && !isValves
                              ? "bg-primary/10 border-r-2 border-primary font-medium"
                              : ""
                              } ${isValves ? "cursor-pointer" : ""}`}
                          >
                            <div className="truncate">{category.name}</div>
                            {isValves && categorySubcategories.length > 0 && (
                              <div className="ml-2">
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </div>
                            )}
                          </button>

                          {/* Show subcategories for Valves when expanded */}
                          {isValves && isExpanded && categorySubcategories.length > 0 && (
                            <div className="ml-4 border-l border-muted">
                              {categorySubcategories.map((subcategory) => (
                                <button
                                  key={subcategory.id}
                                  onClick={() => setSelectedCategoryId(subcategory.id)}
                                  className={`w-full text-left px-4 py-2 hover:bg-muted transition-colors text-sm ${selectedCategoryId === subcategory.id
                                    ? "bg-primary/10 border-r-2 border-primary font-medium"
                                    : ""
                                    }`}
                                >
                                  <div className="truncate">{subcategory.name}</div>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Product Arrangement Manager */}
            <div className="lg:col-span-3">
              {selectedCategoryId ? (
                (() => {
                  const selectedCategory = categories.find((c) => c.id === selectedCategoryId);
                  const isValvesCategory = selectedCategory?.name === "Valves" ||
                    (selectedCategory?.parent_category_id &&
                      categories.find(c => c.id === selectedCategory.parent_category_id)?.name === "Valves");

                  // If Valves main category is selected, show information instead
                  if (selectedCategory?.name === "Valves") {
                    const valvesSubcategories = getSubcategories(selectedCategoryId);
                    return (
                      <Card>
                        <CardContent className="flex flex-col items-center justify-center min-h-[400px] text-center">
                          <Settings className="h-16 w-16 text-muted-foreground mb-4" />
                          <h2 className="text-xl font-semibold mb-2">Valves Category</h2>
                          <p className="text-muted-foreground mb-4">
                            The Valves category contains subcategories. Please select a specific valve type to arrange products.
                          </p>
                          {valvesSubcategories.length > 0 && (
                            <div className="mt-4">
                              <h3 className="font-medium mb-2">Available Valve Subcategories:</h3>
                              <ul className="text-sm text-muted-foreground space-y-1">
                                {valvesSubcategories.map((sub) => (
                                  <li key={sub.id} className="flex items-center">
                                    <button
                                      onClick={() => setSelectedCategoryId(sub.id)}
                                      className="text-primary hover:underline"
                                    >
                                      {sub.name}
                                    </button>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    );
                  }

                  return (
                    <ProductArrangementManager
                      categoryId={selectedCategoryId}
                      categoryName={selectedCategory?.name || ""}
                    />
                  );
                })()
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center min-h-[400px] text-center">
                    <Settings className="h-16 w-16 text-muted-foreground mb-4" />
                    <h2 className="text-xl font-semibold mb-2">Select a Category</h2>
                    <p className="text-muted-foreground">
                      Choose a category from the left to manage product arrangements.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};