/**
 * CSV Export Utilities
 * Provides functions for converting data to CSV format with proper escaping
 */

export interface CSVExportOptions {
  includeHeaders?: boolean;
  delimiter?: string;
  quoteChar?: string;
  escapeQuotes?: boolean;
}

/**
 * Escapes a CSV field value by wrapping in quotes if necessary
 * and escaping any quotes within the value
 */
export function escapeCsvField(
  value: string | number | null | undefined,
  options: CSVExportOptions = {}
): string {
  const { delimiter = ",", quoteChar = '"', escapeQuotes = true } = options;

  if (value === null || value === undefined) {
    return "";
  }

  const stringValue = String(value);

  // Check if the field needs to be quoted
  const needsQuoting =
    stringValue.includes(delimiter) ||
    stringValue.includes(quoteChar) ||
    stringValue.includes("\n") ||
    stringValue.includes("\r") ||
    stringValue.trim() !== stringValue;

  if (!needsQuoting) {
    return stringValue;
  }

  // Escape quotes by doubling them
  const escapedValue = escapeQuotes
    ? stringValue.replace(new RegExp(quoteChar, "g"), quoteChar + quoteChar)
    : stringValue;

  return `${quoteChar}${escapedValue}${quoteChar}`;
}

/**
 * Converts an array of objects to CSV format
 */
export function arrayToCsv<T extends Record<string, any>>(
  data: T[],
  options: CSVExportOptions = {}
): string {
  const { includeHeaders = true, delimiter = "," } = options;

  if (data.length === 0) {
    return "";
  }

  const headers = Object.keys(data[0]);
  const rows: string[] = [];

  // Add headers if requested
  if (includeHeaders) {
    const headerRow = headers
      .map((header) => escapeCsvField(header, options))
      .join(delimiter);
    rows.push(headerRow);
  }

  // Add data rows
  data.forEach((item) => {
    const row = headers
      .map((header) => escapeCsvField(item[header], options))
      .join(delimiter);
    rows.push(row);
  });

  return rows.join("\n");
}

/**
 * Converts product group price data to CSV format
 * with product_code as first column and group names as headers
 */
export interface ProductGroupPriceData {
  product_code: string;
  product_name: string;
  group_prices: Record<string, number | null>;
}

export function productGroupPricesToCsv(
  data: ProductGroupPriceData[],
  groupNames: string[],
  options: CSVExportOptions = {}
): string {
  const { includeHeaders = true, delimiter = "," } = options;

  if (data.length === 0) {
    return "";
  }

  const rows: string[] = [];

  // Create headers: product_code, then all group names
  if (includeHeaders) {
    const headers = ["product_code", ...groupNames];
    const headerRow = headers
      .map((header) => escapeCsvField(header, options))
      .join(delimiter);
    rows.push(headerRow);
  }

  // Add data rows
  data.forEach((product) => {
    const rowData = [
      product.product_code,
      ...groupNames.map((groupName) => product.group_prices[groupName] ?? "")
    ];

    const row = rowData
      .map((value) => escapeCsvField(value, options))
      .join(delimiter);
    rows.push(row);
  });

  return rows.join("\n");
}

/**
 * Converts product group price data to CSV format in long format
 * with Part_Nbr,PC,Qty,Price,Weight headers (multiple rows per product)
 */
export function productGroupPricesToCsvLongFormat(
  data: ProductGroupPriceData[],
  groupNames: string[],
  options: CSVExportOptions = {}
): string {
  const { includeHeaders = true, delimiter = "," } = options;

  if (data.length === 0) {
    return "";
  }

  const rows: string[] = [];

  // Create headers for long format: Part_Nbr,PC,Qty,Price,Weight
  if (includeHeaders) {
    const headers = ["Part_Nbr", "PC", "Qty", "Price", "Weight"];
    const headerRow = headers
      .map((header) => escapeCsvField(header, options))
      .join(delimiter);
    rows.push(headerRow);
  }

  // Add data rows - one row per product per price group
  data.forEach((product) => {
    groupNames.forEach((groupName) => {
      const price = product.group_prices[groupName];
      // Only include rows with actual prices (not null/empty)
      if (price !== null && price !== undefined) {
        const rowData = [
          product.product_code, // Part_Nbr
          groupName,           // PC
          "",                  // Qty (empty)
          price,               // Price
          ""                   // Weight (empty)
        ];

        const row = rowData
          .map((value) => escapeCsvField(value, options))
          .join(delimiter);
        rows.push(row);
      }
    });
  });

  return rows.join("\n");
}

/**
 * Triggers a file download in the browser
 */
export function downloadCsv(
  csvContent: string,
  filename: string = "export.csv"
): void {
  // Create a Blob with the CSV content
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

  // Create a download link
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);

  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";

  // Trigger the download
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up the URL object
  URL.revokeObjectURL(url);
}

/**
 * Generates a filename with timestamp
 */
export function generateExportFilename(
  prefix: string = "export",
  extension: string = "csv"
): string {
  const now = new Date();
  const timestamp = now.toISOString().slice(0, 19).replace(/[:.]/g, "-");
  return `${prefix}_${timestamp}.${extension}`;
}

/**
 * Validates CSV data before export
 */
export function validateCsvData(data: any[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push("Data must be an array");
    return { isValid: false, errors };
  }

  if (data.length === 0) {
    errors.push("Data array is empty");
    return { isValid: false, errors };
  }

  // Check if all items have the same structure
  const firstItemKeys = Object.keys(data[0]);
  const inconsistentItems = data.filter(
    (item, index) => {
      const itemKeys = Object.keys(item);
      return (
        itemKeys.length !== firstItemKeys.length ||
        !itemKeys.every((key) => firstItemKeys.includes(key))
      );
    }
  );

  if (inconsistentItems.length > 0) {
    errors.push("All data items must have the same structure");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}