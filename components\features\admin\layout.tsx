import { SidebarProvider } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useGlobalSettings } from "@/stores/global-settings";
import { AdminHeader } from "./header";
import { AdminSidebar } from "./sidebar";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const collapseSidebar = useGlobalSettings((state) => state.collapseSidebar);
    const setCollapseSidebar = useGlobalSettings((state) => state.setCollapseSidebar);

    const toggleSidebar = () => setCollapseSidebar(collapseSidebar);

    return (
        <SidebarProvider open={!collapseSidebar}>
            <div className="relative flex h-screen w-screen overflow-hidden bg-white">
                {/* Sidebar */}
                <AdminSidebar
                    open={!collapseSidebar}
                    onClose={() => setCollapseSidebar(false)}
                    onToggle={toggleSidebar}
                />

                {/* Main content */}
                <div className={cn(
                    "grid grid-rows-[96px,1fr] transition-all duration-200 h-screen w-full",
                    collapseSidebar ? "ml-10" : "ml-[2.5rem]"
                )}>
                    {/* Top navigation */}
                    <AdminHeader
                        onMenuClick={toggleSidebar}
                    />

                    <main className="min-h-screen overflow-y-auto px-5 pt-2 pb-24">
                        {children}
                    </main>
                </div>
            </div>
        </SidebarProvider>
    );
}
