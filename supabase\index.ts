import { createClient } from "@supabase/supabase-js";
import { Database } from "./database.types";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL ?? "";
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ?? "";

export const supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey);
export function createSupabaseAdminClient() {
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY ?? "";
    return createClient<Database>(
        supabaseUrl,
        supabaseServiceRoleKey
    );
}

