/* Inline Editing split pane */
.nav-panesearch div[data-search-open] {
  display: none !important;
}

/* HEADER PANE */
.nav-paneheader div[data-testid="pane-header"] > div > div:nth-child(2) {
  /* Version history */
  display: none !important;
}

.nav-paneheader div[data-testid="pane-header"] > div > div > div:nth-child(1) {
  /* Go back to studio link */
  display: none !important;
}

.nav-paneheader div[data-testid="pane-header"] > div > div > div:nth-child(2) {
  /* Prevent button clicks that would hide the pane */
  pointer-events: none !important;
  cursor: default !important;
}

.nav-paneheader div[data-testid="pane-header"] > div > div:nth-child(2) {
  display: none !important;
}

/* end of HEADER PANE */

.desk-listpane div[data-testid="desk-tool-list-pane"] {
  display: none !important;
}

.document-pane div[data-testid="document-pane"] {
  position: relative !important;
}

.field-label div[data-testid="field-label"] {
  display: none !important;
}

.field-variant div[data-testid="field-variant"] {
  display: none !important;
}

.panel div[data-testid="document-panel-scroller"]>div {
  padding-top: 1rem;
  padding-bottom: 2rem;
}

/* FOOTER pane */
.pane-footer div[data-testid="pane-footer"] {
  position: sticky !important;
  bottom: 0;
}

.pane-footer div[data-testid="pane-footer"] > div > div > div > div > div:nth-child(2) > div > div:nth-child(2) {
  /* Hide menu button with options such as Duplicate, Delete, Discard page to only show the SAVE button */
  display: none !important;
}
/* end of FOOTER pane */

.fieldgroup-tabs div[data-ui="TabList"][data-testid="field-group-tabs"] {
  display: none;
}

.fieldgroup-select select[data-testid="field-group-select"]>option:first-child {
  display: none;
}

/* fieldgroup style for main product document */
.product-fieldgroup-tabs div[data-ui="TabList"][data-testid="field-group-tabs"]>div:not(:nth-child(2)):not(:nth-child(3)) {
  display: none;
}

.product-fieldgroup-select select[data-testid="field-group-select"]>option:not(:nth-child(2)):not(:nth-child(3)) {
  display: none;
}

/* Inline Editing button and container */
.hide {
  display: none;
}

.inline-editor:hover {
  transition: all 300ms ease-in-out;
  box-shadow: 0px 0px 25px #0045d8;
  z-index: 9;
  width: 99%;
  margin: auto;
}

.show-button:hover .hide {
  display: inline-flex;
  position: absolute;
  top: 0;
  right: 10px;
  z-index: 40;
}