import { matchRoute } from "@/middlewares/match-route";

import { checkAuth } from "@/middlewares/auth-middleware";
import { createSupabaseAdminClient } from "@/supabase";
import { Product } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";

export default checkAuth(
  matchRoute({
    GET: getProductsHandler,
  })
);

export interface GetCustomerAssignedProductsResponse {
  error?: string;
  products?: Product[];
  total?: number;
}

// Type for category data from supabase
interface CategoryData {
  id: string;
  name: string | null;
  parent_category_id: { id: string } | null;
}

async function getProductsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetCustomerAssignedProductsResponse>
) {
  try {
    const { id } = req.query;
    const { page = "1", limit = "10", category, search } = req.query;
    const userId = id as string;
    const activePage = parseInt(page as string) || 1;
    const limitNumber = parseInt(limit as string) || 10;
    const offset = (activePage - 1) * limitNumber;

    const supabaseClient = createSupabaseAdminClient();

    // Get customer and their assigned categories
    const { data: customer, error: customerError } = await supabaseClient
      .from("customers")
      .select(
        `id,
        user_id,
        categories:customer_categories(category_id:categories(id, name, parent_category_id)),
        subCategories:customer_categories(category_id:categories(id, name, parent_category_id))
        `
      )
      .eq("user_id", userId)
      .single();

    if (customerError) {
      return res.status(400).json({ error: customerError.message });
    }

    if (!customer) {
      return res.status(404).json({ error: "Customer not found" });
    }

    // Extract category IDs the customer has access to
    const categoryIds = [
      ...customer.categories
        .map((c) => c.category_id?.id || "")
        .filter(Boolean),
      ...customer.subCategories
        .map((c) => c.category_id?.id || "")
        .filter(Boolean),
    ];

    // if (categoryIds.length === 0) {
    //   return res.status(200).json({ products: [], total: 0 });
    // }

    // Get all categories to determine parent-child relationships
    const { data: allCategories, error: categoriesError } = await supabaseClient
      .from("categories")
      .select("id, name, parent_category_id");

    if (categoriesError) {
      return res.status(400).json({ error: categoriesError.message });
    }

    // Create a map of category IDs to category names for quick lookups
    const categoryMap = new Map();
    allCategories?.forEach((cat) => {
      if (cat.id) categoryMap.set(cat.id, cat);
    });

    // Build query - IMPORTANT: Don't use count for the initial query
    let query = supabaseClient
      .from("products")
      .select(
        `
                *,
                product_categories(category_id(id, name, value, parent_category_id(id))),
                options,
                product_group_prices(id, group_id, custom_price),
                product_arrangements(id, product_id, category_id, position)
            `
      )
      .eq("draft", false)
      .eq("available", true);

    // First just get all products without pagination to see if we have any matches
    const { data: allProducts, error: productsError } = await query;

    if (productsError) {
      return res.status(400).json({ error: productsError.message });
    }

    // Get all products and mark whether to show price based on customer's categories
    // Get all products and mark prices based on customer's categories
    let filteredProducts = allProducts || [];

    // Instead of filtering products, set price to -1 for products that don't match customer's categories
    const productsWithPriceVisibility = filteredProducts.map((product) => {
      // Check if product has categories
      if (
        !product.product_categories ||
        product.product_categories.length === 0
      ) {
        console.log({
          product: product.name,
          priceHidden: true,
          reason: "no categories",
        });
        // Set price to -1 for products without categories
        return { ...product, price: -1 };
      }

      // Check if any product category matches customer's assigned categories
      const matches = product.product_categories.some((pc) => {
        if (!pc.category_id) {
          return false;
        }

        // Cast the category_id to include the expected fields
        const categoryData = pc.category_id as unknown as CategoryData;

        const categoryId = categoryData?.id || "";
        const parentCategoryId = categoryData?.parent_category_id?.id || "";

        const isMatch =
          categoryIds.includes(categoryId) ||
          (parentCategoryId && categoryIds.includes(parentCategoryId));

        return Boolean(isMatch);
      });

      // If no matches, set price to -1
      if (!matches) {
        return { ...product, price: -1 };
      }

      // Otherwise, return the product with original price
      return product;
    });

    // Apply category filter if provided - still filter products for category search
    let finalFilteredProducts = productsWithPriceVisibility;
    if (category && typeof category === "string") {
      const categoryName = category.trim();
      finalFilteredProducts = finalFilteredProducts.filter((product) => {
        // Look through product_categories
        if (
          !product.product_categories ||
          product.product_categories.length === 0
        ) {
          console.log({
            product: product.name,
            isMatch: false,
            reason: "no categories for filter",
          });
          return false;
        }

        const matches = product.product_categories.some((pc) => {
          if (!pc.category_id) {
            return false;
          }

          // Get category from category_id
          const catData = pc.category_id as any as CategoryData;

          // Check direct category name match
          if (catData?.name?.replaceAll("\n", "") === categoryName) {
            return true;
          }

          // Check parent category match
          if (catData?.parent_category_id?.id) {
            return categoryIds.some((id) => {
              return (
                id === catData.parent_category_id?.id &&
                categoryName === categoryMap.get(id)?.name
              );
            });
          }

          return false;
        });

        return Boolean(matches);
      });
    }

    // Apply search filter if provided
    if (search && typeof search === "string") {
      const searchLower = search.toLowerCase().trim();
      finalFilteredProducts = finalFilteredProducts.filter((product) => {
        // Handle potentially undefined product name
        const productName = product.name || "";
        const productSku = product.sku || "";

        // Search in product name
        if (productName.toLowerCase().includes(searchLower)) {
          return true;
        }

        // Search in product SKU
        if (productSku.toLowerCase().includes(searchLower)) {
          return true;
        }

        // Search functionality limited to only name and SKU for more relevant results
        // Description, tags and categories searches have been removed
        return false;
      });
    }

    const totalCount = finalFilteredProducts.length;
    const paginatedProducts = finalFilteredProducts.slice(
      offset,
      offset + limitNumber
    );

    return res.status(200).json({
      products: paginatedProducts,
      total: totalCount,
    });
  } catch (error) {
    console.error("Error fetching products:", error);
    return res.status(500).json({ error: "Failed to fetch products" });
  }
}
