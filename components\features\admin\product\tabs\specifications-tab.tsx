import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/shadcn-button"
import { Textarea } from "@/components/ui/textarea"
import { PlusCircle, Trash2 } from "lucide-react"
import { UseFormReturn } from "react-hook-form"

interface Specification {
    key: string;
    value: string;
}

interface SpecificationsTabProps {
    form: UseFormReturn<any>
    specs: Specification[]
    addSpecification: () => void
    removeSpecification: (index: number) => void
    updateSpecification: (index: number, field: keyof Specification, value: string) => void
    features: string[]
    addFeature: () => void
    removeFeature: (index: number) => void
    updateFeature: (index: number, value: string) => void
}

export default function SpecificationsTab({
    form,
    specs,
    addSpecification,
    removeSpecification,
    updateSpecification,
    features,
    addFeature,
    removeFeature,
    updateFeature
}: SpecificationsTabProps) {
    return (
        <div className="space-y-6">
            {/* Features section */}
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <FormLabel>Features</FormLabel>
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addFeature}
                        className=""
                    >
                        <PlusCircle className="h-4 w-4 mr-1" /> Add Feature
                    </Button>
                </div>

                <div className="space-y-2">
                    {features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <Input
                                placeholder="Feature description"
                                value={feature}
                                onChange={(e) => updateFeature(index, e.target.value)}
                                className=" flex-1"
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removeFeature(index)}
                                disabled={features.length === 1 && index === 0}
                            >
                                <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                        </div>
                    ))}
                </div>

                <p className="text-sm text-muted-foreground mt-1">
                    Add key product features. Example: "This collection is a "go-to" for most spaces"
                </p>
            </div>

            {/* Specifications section */}
            <div className="space-y-4">
                <div className="flex justify-between items-center">
                    <FormLabel>Technical Specifications</FormLabel>
                    <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addSpecification}
                        className=""
                    >
                        <PlusCircle className="h-4 w-4 mr-1" /> Add Specification
                    </Button>
                </div>

                <div className="space-y-2">
                    {specs.map((spec, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <Input
                                placeholder="Name (e.g. Length, Weight)"
                                value={spec.key}
                                onChange={(e) => updateSpecification(index, "key", e.target.value)}
                                className=" flex-1"
                            />
                            <Input
                                placeholder="Value (e.g. 9-1/4 Inch, 0.46 lbs)"
                                value={spec.value}
                                onChange={(e) => updateSpecification(index, "value", e.target.value)}
                                className=" flex-1"
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removeSpecification(index)}
                                disabled={specs.length === 1 && index === 0}
                                className=""
                            >
                                <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                        </div>
                    ))}
                </div>

                <p className="text-sm text-muted-foreground mt-1">
                    Add specifications as key-value pairs. For example: Length → 9-1/4 Inch
                </p>
            </div>

            {/* Finish and Material section */}
            <FormField
                control={form.control}
                name="finish_and_material"
                render={({ field }) => (
                    <FormItem>
                        <FormLabel>Finish and Material</FormLabel>
                        <FormControl>
                            <Textarea
                                rows={2}
                                placeholder="Description of the product's finish and material"
                                value={field.value || ""}
                                onChange={field.onChange}
                            />
                        </FormControl>
                        <FormMessage />
                    </FormItem>
                )}
            />
        </div>
    )
} 