import React, { useState, useEffect, useRef, useMemo } from "react";
import dynamic from "next/dynamic";
import { InlineEditorContext } from "context/InlineEditorContext";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Text } from "@stackshift-ui/text";
import { PortableText, PortableTextComponentProps } from "@portabletext/react";
import type { PortableTextBlock } from "@portabletext/types";
import Image from "next/image";
import {
  BlogsData,
  Categories,
  ArchiveYearAndMonth,
  Sections,
  SanityBody,
  PostTag,
} from "types";
import Link from "next/link";
import { format } from "date-fns";
import { urlFor } from "lib/sanity";
import InlineEditor from "components/InlineEditor";
import { FaReadme } from "react-icons/fa";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";

const Navigation = dynamic(() =>
  import("components/sections/navigation").then((mod) => mod.Navigation)
);
const Footer = dynamic(() =>
  import("components/sections/footer").then((mod) => mod.Footer)
);

interface TagSectionProps {
  tag: PostTag;
  posts: BlogsData[];
  categories: Categories[];
  allArchives?: {
    years: any[];
    months: any[];
    validCombinations: Array<{
      year: {
        _id: string;
        title: string;
        slug: string;
      };
      month: {
        _id: string;
        title: string;
        slug: string;
      };
    }>;
  };
  archive: ArchiveYearAndMonth[];
}

function TagSection({
  tag,
  posts,
  categories,
  allArchives,
  archive,
}: TagSectionProps) {
  const showInlineEditor = React.useContext(InlineEditorContext);

  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 10;

  // State for collapsible sidebar sections
  const [isCategoryExpanded, setIsCategoryExpanded] = useState(false);
  const [isArchiveExpanded, setIsArchiveExpanded] = useState(false);
  const [isTagExpanded, setIsTagExpanded] = useState(false);

  // Check if current page is a tag page and set initial expansion state
  useEffect(() => {
    if (tag?.slug) {
      setIsTagExpanded(true);
    }
    if (archive && archive.length > 0) {
      setIsArchiveExpanded(true);
    }
  }, [tag, archive]);

  // Add state for share popup, comments, and likes
  const [postStates, setPostStates] = useState<
    Record<
      string,
      {
        showSharePopup: boolean;
        showComments: boolean;
        isLiked: boolean;
        likesCount: number;
        comments: Comment[];
      }
    >
  >({});

  // Refs for share popup click outside handling
  const sharePopupRefs = useRef<Record<string, HTMLDivElement>>({});
  const shareButtonRefs = useRef<Record<string, HTMLButtonElement>>({});

  // Custom scrollbar styles
  const scrollbarStyles = `
    /* Custom Scrollbar for Webkit browsers (Chrome, Safari, etc.) */
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f3f4f6;
      border-radius: 8px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(0, 87, 168, 0.4);
      border-radius: 8px;
    }
    
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 87, 168, 0.6);
    }
  `;

  useEffect(() => {
    const initialStates: Record<string, any> = {};
    if (posts) {
      posts.forEach((post) => {
        const postId = post._id;
        if (!postId) return;

        // Get total likes from localStorage
        const totalLikes = parseInt(
          localStorage.getItem(`post-${postId}-total-likes`) || "0"
        );
        // Get user-specific like status
        const userLiked =
          localStorage.getItem(`post-${postId}-user-liked`) === "true";
        const comments = JSON.parse(
          localStorage.getItem(`post-${postId}-comments`) || "[]"
        );

        initialStates[postId] = {
          showSharePopup: false,
          showComments: false,
          isLiked: userLiked,
          likesCount: totalLikes,
          comments: comments,
        };
      });
    }
    setPostStates(initialStates);
  }, [posts]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      Object.entries(postStates).forEach(([postId, state]) => {
        if (state.showSharePopup) {
          const popupRef = sharePopupRefs.current[postId];
          const buttonRef = shareButtonRefs.current[postId];

          if (
            popupRef &&
            !popupRef.contains(event.target as Node) &&
            buttonRef &&
            !buttonRef.contains(event.target as Node)
          ) {
            setPostStates((prev) => ({
              ...prev,
              [postId]: { ...prev[postId], showSharePopup: false },
            }));
          }
        }
      });
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [postStates]);

  // Sort posts by publishedAt date (latest to oldest)
  const sortedPosts = useMemo(() => {
    if (!posts) return [];
    return [...posts].sort((a, b) => {
      const dateA = a.publishedAt ? new Date(a.publishedAt).getTime() : 0;
      const dateB = b.publishedAt ? new Date(b.publishedAt).getTime() : 0;
      return dateB - dateA;
    });
  }, [posts]);

  // Calculate pagination
  const indexOfLastPost = currentPage * postsPerPage;
  const indexOfFirstPost = indexOfLastPost - postsPerPage;
  const currentPosts = sortedPosts.slice(indexOfFirstPost, indexOfLastPost);
  const totalPages = Math.ceil(sortedPosts.length / postsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0);
  };

  const toggleSharePopup = (postId: string) => {
    if (!postId) return;
    setPostStates((prev) => ({
      ...prev,
      [postId]: {
        ...prev[postId],
        showSharePopup: !prev[postId]?.showSharePopup,
      },
    }));
  };

  const toggleComments = (postId: string) => {
    if (!postId) return;
    setPostStates((prev) => ({
      ...prev,
      [postId]: {
        ...prev[postId],
        showComments: !prev[postId]?.showComments,
      },
    }));
  };

  const handleLike = (postId: string) => {
    if (!postId) return;
    setPostStates((prev) => {
      const currentState = prev[postId] || {
        showSharePopup: false,
        showComments: false,
        isLiked: false,
        likesCount: 0,
        comments: [],
      };

      const newLikeState = !currentState.isLiked;
      const newLikeCount = currentState.likesCount + (newLikeState ? 1 : -1);

      // Update localStorage for user-specific like status
      localStorage.setItem(`post-${postId}-user-liked`, String(newLikeState));
      // Update localStorage for total likes
      localStorage.setItem(`post-${postId}-total-likes`, String(newLikeCount));

      return {
        ...prev,
        [postId]: {
          ...currentState,
          isLiked: newLikeState,
          likesCount: newLikeCount,
        },
      };
    });
  };

  if (!tag) {
    return null;
  }

  return (
    <InlineEditor
      document={{
        id: tag._id || "",
        type: "tag",
      }}
      showInlineEditor={showInlineEditor}
      key={tag._id}
    >
      <style dangerouslySetInnerHTML={{ __html: scrollbarStyles }} />
      {tag?.navigation && (
        <Navigation
          data={tag?.navigation}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}
      <section className="py-10 md:pt-12 md:pb-20">
        <Container className="!px-0">
          {/* Title Section */}
          <div className="mx-auto mb-8">
            <Container maxWidth={1380}>
              {tag?.title && (
                <Heading
                  type="h1"
                  className="!text-2xl md:text-3xl !font-bold text-left !text-gray-800 mb-2"
                >
                  Tag: {tag?.title}
                </Heading>
              )}
              {tag?.title && (
                <Text className="text-gray-600 leading-relaxed">
                  Browsing all posts tagged with &quot;{tag?.title}&quot;
                </Text>
              )}
            </Container>
          </div>

          <Container maxWidth={1380}>
            <Flex className="flex-col lg:flex-row gap-8 mx-auto">
              {/* Main Content */}
              <div className="w-full lg:w-3/5">
                {/* Posts Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {currentPosts && currentPosts.length > 0 ? (
                    currentPosts.map((post) => {
                      // Ensure we have required fields
                      const postId = post._id;
                      if (!postId || !post.slug?.current) return null;

                      return (
                        <div
                          key={postId}
                          className="bg-white shadow-md overflow-hidden border border-gray-100 transition-all duration-300 hover:shadow-xl  flex flex-col h-full"
                        >
                          {/* Featured Image */}
                          {post?.mainImage && (
                            <div className="relative h-44 w-full overflow-hidden">
                              <Link href={`/${post.slug.current}`}>
                                <div className="relative h-full w-full group">
                                  <Image
                                    src={urlFor(post.mainImage)}
                                    alt={post.title || "Blog post image"}
                                    fill
                                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                                  />
                                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-25 transition-opacity duration-300" />
                                </div>
                              </Link>
                            </div>
                          )}

                          {/* Content */}
                          <div className="p-5 flex-grow flex flex-col">
                            {/* Title */}
                            <Link href={`/${post.slug.current}`}>
                              <h3 className="text-lg font-semibold text-gray-800 hover:text-primary transition-colors duration-300 mb-2 line-clamp-2">
                                {post.title}
                              </h3>
                            </Link>

                            {/* Meta - Author, Date, Category */}
                            <div className="flex flex-wrap items-center text-xs text-gray-500 mb-3">
                              {/* Author */}
                              {post.authors && post.authors[0] && (
                                <Link
                                  href={`/author/${post.authors[0].slug?.current}`}
                                  className="flex items-center mr-4 hover:text-primary"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-3 w-3 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                    />
                                  </svg>
                                  {post.authors[0].name}
                                </Link>
                              )}

                              {/* Date */}
                              {post.publishedAt && (
                                <Link
                                  href={`/archive/${format(
                                    new Date(post.publishedAt),
                                    "yyyy"
                                  )}/${format(
                                    new Date(post.publishedAt),
                                    "MM"
                                  )}`}
                                  className="flex items-center mr-4 hover:text-primary"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-3 w-3 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                    />
                                  </svg>
                                  {format(
                                    new Date(post.publishedAt),
                                    "MMM dd, yyyy"
                                  )}
                                </Link>
                              )}

                              {/* Category */}
                              {post.categories &&
                                post.categories[0] &&
                                post.categories[0].title && (
                                  <Link
                                    href={`/category/${post.categories[0].slug?.current}`}
                                    className="flex items-center hover:text-primary"
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      className="h-3 w-3 mr-1"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                                      />
                                    </svg>
                                    {post.categories[0].title}
                                  </Link>
                                )}
                            </div>

                            {/* Excerpt */}
                            {post.excerpt && (
                              <p
                                className={`text-gray-600 text-sm mb-4 ${
                                  post?.mainImage ? "line-clamp-3" : ""
                                }`}
                              >
                                {post.excerpt}
                              </p>
                            )}

                            {/* Tags */}
                            {post.tags && (
                              <div className="flex flex-wrap gap-1 mt-auto mb-3">
                                {Array.isArray(post.tags) ? (
                                  post.tags.map((tagItem) => (
                                    <Link
                                      key={tagItem._id}
                                      href={`/tag/${tagItem.slug?.current}`}
                                      className={`px-2 py-1 text-xs bg-gray-100 capitalize hover:bg-primary hover:text-white transition-colors duration-300 ${
                                        tagItem.slug?.current ===
                                        tag.slug?.current
                                          ? "bg-primary text-white"
                                          : "text-gray-600"
                                      }`}
                                    >
                                      {tagItem.title}
                                    </Link>
                                  ))
                                ) : (
                                  <Link
                                    href={`/tag/${post.tags.slug?.current}`}
                                    className={`px-2 py-1 text-xs bg-gray-100 rounded-md hover:bg-primary hover:text-white transition-colors duration-300 ${
                                      post.tags.slug?.current ===
                                      tag.slug?.current
                                        ? "bg-primary text-white"
                                        : "text-gray-600"
                                    }`}
                                  >
                                    {post.tags.title}
                                  </Link>
                                )}
                              </div>
                            )}

                            {/* Read More */}
                            <Link
                              href={`/${post.slug.current}`}
                              className="self-start mt-auto relative text-sm font-medium text-primary hover:text-primary-dark transition-colors duration-300 inline-flex items-center group"
                            >
                              <div className="flex items-center gap-2">
                                Read More
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                                  />
                                </svg>
                              </div>
                              <span className="absolute bottom-[-4px] left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                            </Link>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="col-span-2 py-8 text-center">
                      <Text className="text-gray-500 italic">
                        No posts found in this tag.
                      </Text>
                    </div>
                  )}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-8 flex justify-center">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </div>

              {/* Sidebar - Categories */}
              <Flex className="flex-col md:flex-row lg:flex-col w-full lg:w-2/5 gap-8">
                {/* Categories Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsCategoryExpanded(!isCategoryExpanded)}
                    aria-expanded={isCategoryExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Categories
                    </Text>
                    {isCategoryExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isCategoryExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isCategoryExpanded ? "thin" : "none",
                      scrollbarColor: isCategoryExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {categories
                        ?.sort((a, b) =>
                          (a.title || "").localeCompare(b.title || "")
                        )
                        ?.map((cat) => {
                          return (
                            <li
                              key={cat._id}
                              className="group py-2 border-b border-gray-100 last:border-b-0"
                            >
                              <Link
                                href={`/category/${cat.slug?.current}`}
                                className="flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform"
                              >
                                <svg
                                  className="w-3 h-3 mr-2 text-primary/60"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    fillRule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clipRule="evenodd"
                                  />
                                </svg>
                                {cat.title}
                              </Link>
                            </li>
                          );
                        })}
                    </ul>
                  </div>
                </div>

                {/* Tags Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsTagExpanded(!isTagExpanded)}
                    aria-expanded={isTagExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Tags
                    </Text>
                    {isTagExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isTagExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isTagExpanded ? "thin" : "none",
                      scrollbarColor: isTagExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {(() => {
                        // Extract all unique tags from posts
                        const allTags = new Map();

                        posts.forEach((post) => {
                          if (post.tags) {
                            if (Array.isArray(post.tags)) {
                              // Handle array of tags
                              post.tags.forEach((tagItem) => {
                                if (
                                  tagItem &&
                                  tagItem._id &&
                                  !allTags.has(tagItem._id)
                                ) {
                                  allTags.set(tagItem._id, tagItem);
                                }
                              });
                            } else {
                              // Handle single PostTag object
                              if (
                                post.tags._id &&
                                !allTags.has(post.tags._id)
                              ) {
                                allTags.set(post.tags._id, post.tags);
                              }
                            }
                          }
                        });

                        // Convert to array and sort
                        const uniqueTags = Array.from(allTags.values()).sort(
                          (a, b) => (a.title || "").localeCompare(b.title || "")
                        );

                        return uniqueTags.length > 0 ? (
                          uniqueTags.map((tagItem) => {
                            // Check if this is the current tag
                            const isCurrentTag =
                              tagItem.slug?.current === tag.slug?.current;

                            return (
                              <li
                                key={tagItem._id}
                                className={`group py-2 border-b border-gray-100 last:border-b-0 ${
                                  isCurrentTag
                                    ? "bg-primary/5 -mx-3 px-3 rounded"
                                    : ""
                                }`}
                              >
                                <Link
                                  href={`/tag/${tagItem.slug?.current}`}
                                  className={`flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform ${
                                    isCurrentTag
                                      ? "!text-primary font-semibold"
                                      : ""
                                  }`}
                                >
                                  <svg
                                    className={`w-3 h-3 mr-2 ${
                                      isCurrentTag
                                        ? "text-primary"
                                        : "text-primary/60"
                                    }`}
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                  {tagItem.title}
                                  {isCurrentTag && (
                                    <span className="ml-auto bg-primary text-white text-xs py-0.5 px-2 rounded-full">
                                      Current
                                    </span>
                                  )}
                                </Link>
                              </li>
                            );
                          })
                        ) : (
                          <li>
                            <Text className="text-gray-500 !text-sm italic">
                              No tags available
                            </Text>
                          </li>
                        );
                      })()}
                    </ul>
                  </div>
                </div>

                {/* Archives Section */}
                <div className="md:w-1/2 lg:w-full bg-white shadow-lg border border-gray-100">
                  <button
                    className="w-full py-5 px-6 flex items-center justify-between text-left"
                    onClick={() => setIsArchiveExpanded(!isArchiveExpanded)}
                    aria-expanded={isArchiveExpanded}
                  >
                    <Text className="!text-base !font-bold uppercase text-primary">
                      Archives
                    </Text>
                    {isArchiveExpanded ? (
                      <FaChevronDown className="text-primary" />
                    ) : (
                      <FaChevronRight className="text-primary" />
                    )}
                  </button>

                  <div
                    className={`transition-all duration-300 overflow-hidden ${
                      isArchiveExpanded
                        ? "border-t border-primary/20 max-h-[300px] overflow-y-auto p-6 pt-3 custom-scrollbar"
                        : "max-h-0"
                    }`}
                    style={{
                      scrollbarWidth: isArchiveExpanded ? "thin" : "none",
                      scrollbarColor: isArchiveExpanded
                        ? "rgba(0, 87, 168, 0.4) #f3f4f6"
                        : "transparent transparent",
                    }}
                  >
                    <ul className="space-y-1">
                      {(() => {
                        // Deduplicate combinations
                        const seen = new Set();
                        const uniqueCombinations =
                          allArchives?.validCombinations?.filter((combo) => {
                            const key = `${combo.year._id}-${combo.month._id}`;
                            if (seen.has(key)) return false;
                            seen.add(key);
                            return true;
                          });

                        return (
                          uniqueCombinations?.map((combo) => {
                            // Check if this is the current archive
                            const isActive = archive?.some(
                              (item) =>
                                item.title === combo.year.title ||
                                item.title === combo.month.title
                            );

                            return (
                              <li
                                key={`${combo.year._id}-${combo.month._id}`}
                                className={`group py-2 border-b border-gray-100 last:border-b-0 ${
                                  isActive
                                    ? "bg-primary/5 -mx-3 px-3 rounded"
                                    : ""
                                }`}
                              >
                                <Link
                                  href={`/archive/${combo.year.slug}/${combo.month.slug}`}
                                  className={`flex items-center text-gray-600 !text-sm leading-snug hover:text-primary transition-all duration-300 group-hover:translate-x-1 transform ${
                                    isActive
                                      ? "!text-primary font-semibold"
                                      : ""
                                  }`}
                                >
                                  <svg
                                    className={`w-3 h-3 mr-2 ${
                                      isActive
                                        ? "text-primary"
                                        : "text-primary/60"
                                    }`}
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                  {combo.month.title} {combo.year.title}
                                  {isActive && (
                                    <span className="ml-auto bg-primary text-white text-xs py-0.5 px-2 rounded-full">
                                      Current
                                    </span>
                                  )}
                                </Link>
                              </li>
                            );
                          }) || (
                            <li>
                              <Text className="text-gray-500 !text-sm italic">
                                No archives available
                              </Text>
                            </li>
                          )
                        );
                      })()}
                    </ul>
                  </div>
                </div>
              </Flex>
            </Flex>
          </Container>
        </Container>
      </section>

      {tag.footer && (
        <Footer
          data={tag.footer}
          template={{
            bg: "gray",
            color: "webriq",
          }}
        />
      )}
    </InlineEditor>
  );
}

function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}) {
  const [maxVisiblePages, setMaxVisiblePages] = useState(5);
  type PageItem = number | string;

  useEffect(() => {
    const updateMaxPages = () => {
      if (window.innerWidth < 640) {
        setMaxVisiblePages(3);
      } else if (window.innerWidth < 768) {
        setMaxVisiblePages(5);
      } else {
        setMaxVisiblePages(7);
      }
    };

    updateMaxPages();
    window.addEventListener("resize", updateMaxPages);
    return () => window.removeEventListener("resize", updateMaxPages);
  }, []);

  const renderPageNumbers = () => {
    const pageItems: PageItem[] = [];

    // Calculate range of pages to display
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    // Adjust if at the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // First page and ellipsis
    if (startPage > 1) {
      pageItems.push(1);
      if (startPage > 2) pageItems.push("...");
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pageItems.push(i);
    }

    // Last page and ellipsis
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) pageItems.push("...");
      pageItems.push(totalPages);
    }

    return pageItems;
  };

  const handlePageClick = (page: PageItem) => {
    if (typeof page === "number") {
      onPageChange(page);
    } else if (
      page === "..." &&
      currentPage > Math.floor(maxVisiblePages / 2) + 1
    ) {
      // If clicking ellipsis after current page, jump forward
      onPageChange(
        Math.min(currentPage + Math.floor(maxVisiblePages / 2), totalPages)
      );
    } else if (page === "...") {
      // If clicking ellipsis before current page, jump backward
      onPageChange(Math.max(1, currentPage - Math.floor(maxVisiblePages / 2)));
    }
  };

  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  return (
    <div className="flex items-center justify-center">
      {/* Previous Button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`mr-2 px-3 py-1 rounded border ${
          currentPage === 1
            ? "border-gray-300 text-gray-400 cursor-not-allowed"
            : "border-primary text-primary hover:bg-primary hover:text-white transition-colors"
        }`}
        aria-label="Go to previous page"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* Page Numbers */}
      <div className="flex items-center">
        {renderPageNumbers().map((page, index) => (
          <button
            key={index}
            onClick={() => handlePageClick(page)}
            disabled={page === currentPage}
            className={`mx-1 min-w-[32px] h-8 px-2 rounded flex items-center justify-center ${
              page === currentPage
                ? "bg-primary text-white"
                : page === "..."
                ? "text-gray-600 hover:text-primary"
                : "text-gray-700 hover:bg-gray-100 hover:text-primary"
            }`}
            aria-label={
              typeof page === "number" ? `Go to page ${page}` : "Jump pages"
            }
          >
            {page}
          </button>
        ))}
      </div>

      {/* Next Button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`ml-2 px-3 py-1 rounded border ${
          currentPage === totalPages
            ? "border-gray-300 text-gray-400 cursor-not-allowed"
            : "border-primary text-primary hover:bg-primary hover:text-white transition-colors"
        }`}
        aria-label="Go to next page"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  );
}

export default TagSection;
