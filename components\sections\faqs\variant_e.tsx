import { <PERSON><PERSON> } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState } from "react";
import { FAQProps } from ".";
import { AskedQuestion, FaqsWithCategory } from "../../../types";
import { Input } from "@stackshift-ui/input";
import { CiSearch } from "react-icons/ci";
import { MyPortableTextComponents } from "../../../types";
import { urlFor } from "lib/sanity";
import { PortableText, PortableTextComponents } from "@portabletext/react";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-xl  text-gray-800">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-xs md:text-lg text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className=" pl-10 mb-6 text-xs md:text-sm text-gray-800 leading-loose list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Faqs_E({
  subtitle,
  title,
  faqsWithCategories,
}: FAQProps) {
  const sectionRef = React.useRef<HTMLDivElement>(null);
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState<number>(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  // Create a unique ID for this FAQ section
  const faqSectionId = React.useMemo(
    () => `faq-section-${Math.random().toString(36).substr(2, 9)}`,
    []
  );

  // Prepare FAQ array with all questions
  const allQuestions = faqsWithCategories
    ?.reduce((acc: AskedQuestion[], curr) => {
      return [...acc, ...(curr.askedQuestions || [])];
    }, [])
    ?.sort((a, b) =>
      a.question && b.question ? a.question.localeCompare(b.question) : 0
    );

  // Filter questions based on search and category
  const getFilteredQuestions = () => {
    let questions =
      activeCategory === "all"
        ? allQuestions || []
        : faqsWithCategories?.find((cat) => cat.category === activeCategory)
            ?.askedQuestions || [];

    if (searchQuery) {
      questions = questions.filter(
        (q) => q.question?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return questions;
  };

  const filteredQuestions = getFilteredQuestions();

  const handleQuestionClick = (index: number) => {
    setSelectedQuestionIndex(index);

    // Use the ID to scroll to the answer section with additional offset
    const answerSection = document.getElementById("faq-answer-section");
    if (answerSection) {
      // Add a small delay to ensure state updates before scrolling
      setTimeout(() => {
        // Get the element's position with offset
        const yOffset = -280; // 200px offset from the top
        const y =
          answerSection.getBoundingClientRect().top +
          window.pageYOffset +
          yOffset;

        // Scroll to the element with offset
        window.scrollTo({
          top: y,
          behavior: "smooth",
        });
      }, 10);
    }
  };

  // Handle next question
  const handleNext = () => {
    if (filteredQuestions && filteredQuestions.length > 0) {
      const nextIndex = (selectedQuestionIndex + 1) % filteredQuestions.length;
      setSelectedQuestionIndex(nextIndex);
    }
  };

  // Handle previous question
  const handlePrevious = () => {
    if (filteredQuestions && filteredQuestions.length > 0) {
      const prevIndex =
        (selectedQuestionIndex - 1 + filteredQuestions.length) %
        filteredQuestions.length;
      setSelectedQuestionIndex(prevIndex);
    }
  };

  // Handle touch events for swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      handleNext();
    } else if (isRightSwipe) {
      handlePrevious();
    }
  };

  return (
    <Section id={faqSectionId} className="py-20 bg-background" ref={sectionRef}>
      <Container maxWidth={1280}>
        <div className="mb-8 text-center">
          {title && (
            <Heading type="h2" className="text-4xl">
              {title}
            </Heading>
          )}
          {subtitle && (
            <Text muted className="mt-3">
              {subtitle}
            </Text>
          )}
        </div>

        {/* Categories tabs */}
        <div className=" mx-auto mb-8 border-b border-gray-200">
          <Flex className="gap-2 justify-start">
            <Button
              as="button"
              variant="unstyled"
              onClick={() => {
                setActiveCategory("all");
                setSelectedQuestionIndex(0);
              }}
              className={`px-4 py-2 font-medium ${
                activeCategory === "all"
                  ? "border-b-2 border-primary bg-primary text-white"
                  : "text-gray-500 hover:text-primary"
              }`}
            >
              All
            </Button>
            {faqsWithCategories?.map((tab, index) => (
              <Button
                as="button"
                key={index}
                variant="unstyled"
                onClick={() => {
                  setActiveCategory(tab?.category ?? "");
                  setSelectedQuestionIndex(0);
                }}
                className={`px-4 py-2 font-medium ${
                  activeCategory === tab?.category
                    ? "border-b-2 border-primary bg-primary text-white"
                    : "text-gray-500 hover:text-primary"
                }`}
              >
                {tab?.category}
              </Button>
            ))}
          </Flex>
        </div>

        {/* Centered search field */}
        <div className="flex justify-between items-center mb-8 max-w-5xl gap-4">
          <div className="relative w-full max-w-md">
            <Input
              type="text"
              placeholder="Search questions..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setSelectedQuestionIndex(0);
              }}
              className="w-full border border-primary/80 !rounded-none pr-10 focus:border-primary focus:!ring-1 focus:!ring-primary focus:!outline-1 focus:!outline-primary"
            />
            <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        {filteredQuestions && filteredQuestions.length === 0 ? (
          <Text className="text-gray-500 text-center">
            No questions found in{" "}
            {activeCategory === "all" ? "any category" : activeCategory}
          </Text>
        ) : (
          <div className="flex flex-col-reverse md:flex-row mt-8 h-full max-w-5xl">
            {/* Left column - Question titles */}
            <div className="hidden md:block md:w-2/5 pr-0 md:pr-10 mb-8 md:mb-0 divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
              {filteredQuestions?.map((question, index) => (
                <div key={index} className="cursor-pointer w-full py-3">
                  <Heading
                    type="h3"
                    className={`text-lg font-semibold border-l-4 pl-3 py-2 transition-all ${
                      index === selectedQuestionIndex
                        ? "border-primary !text-primary"
                        : "border-gray-300 !text-black hover:!border-primary/50 hover:!text-primary/70"
                    }`}
                    onClick={() => handleQuestionClick(index)}
                  >
                    {question.question}
                  </Heading>
                </div>
              ))}
            </div>

            {/* Right column - Selected question content */}
            <div className="md:w-3/5 md:pl-6" id="faq-answer-section">
              {filteredQuestions?.length > 0 && (
                <div
                  className="w-full md:w-full transition-all duration-300 opacity-100 p-6 bg-white rounded-lg shadow-sm border border-gray-100"
                  onTouchStart={onTouchStart}
                  onTouchMove={onTouchMove}
                  onTouchEnd={onTouchEnd}
                >
                  {/* Question counter */}
                  <div className="flex justify-between items-center mb-4">
                    <p className="text-base font-bold font-mono text-primary">
                      {`${(selectedQuestionIndex + 1)
                        .toString()
                        .padStart(2, "0")}/${filteredQuestions?.length
                        .toString()
                        .padStart(2, "0")}`}
                    </p>

                    {/* Optional: Add navigation buttons */}
                    <div className="flex gap-2">
                      <Button
                        as="button"
                        variant="unstyled"
                        onClick={handlePrevious}
                        className="text-primary hover:text-primary/70"
                      >
                        Previous
                      </Button>
                      <Button
                        as="button"
                        variant="unstyled"
                        onClick={handleNext}
                        className="text-primary hover:text-primary/70"
                      >
                        Next
                      </Button>
                    </div>
                  </div>

                  {/* Question title */}
                  <Heading
                    type="h3"
                    className="text-2xl font-bold mb-4 text-primary"
                  >
                    {filteredQuestions?.[selectedQuestionIndex]?.question}
                  </Heading>

                  {/* Question answer */}
                  {filteredQuestions?.[selectedQuestionIndex]?.firstColumn && (
                    <div className="prose prose-sm md:prose-base w-full max-w-none">
                      <PortableText
                        value={
                          filteredQuestions?.[selectedQuestionIndex]
                            ?.firstColumn
                        }
                        components={textComponentBlockStyling}
                        onMissingComponent={false}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </Container>
    </Section>
  );
}

export { Faqs_E };
