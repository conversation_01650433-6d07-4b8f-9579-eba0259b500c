import { Checkbox } from "@/components/ui/checkbox";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import { CheckoutFormValues } from "@/pages/store/checkout";
import { useAddShippingAddressMutation, useGetBillingAddressesQuery, useGetCustomerQuery, useGetShippingAddressesQuery } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { Truck } from "lucide-react";
import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { ShippingAddresses } from "../customers/shipping-address";

export function CheckoutShippingInformation({
    checkoutForm,
}: {
    checkoutForm: UseFormReturn<CheckoutFormValues>;
}) {
    const [useBillingAddress, setUseBillingAddress] = useState(false);
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;
    const customerData = useGetCustomerQuery(userId);
    const billingAddressId = checkoutForm.watch("billingAddressId");
    const billingAddresses = useGetBillingAddressesQuery(userId);
    const shippingAddresses = useGetShippingAddressesQuery(userId);
    const addShippingAddressMutation = useAddShippingAddressMutation(userId);
    const [selectedBillingAddress, setSelectedBillingAddress] = useState<{
        id: string;
        address: string;
        address_2: string | null;
        address_type: "residential" | "commercial" | null;
        city: string;
        country: string;
        state: string;
        zip_code: string;
        company_name: string | null;
        contact_name: string | null;
        contact_email: string | null;
        contact_number: string | null;
    } | null>(null);

    // Effect to update shipping address when billing address is selected
    useEffect(() => {
        if (useBillingAddress && billingAddressId && billingAddresses.data) {
            console.log(
                "Updating shipping address with billing address:",
                billingAddressId
            );
            const billingAddress = billingAddresses.data.find(
                (addr) => addr.id === billingAddressId
            );

            console.log("Found billing address:", billingAddress);
            console.log("Customer data:", customerData.data);

            if (billingAddress) {
                // Convert billing address to display format with proper types
                const addressData = {
                    id: billingAddress.id,
                    address: billingAddress.address || "",
                    address_2: billingAddress.address_2,
                    address_type: (billingAddress.address_type || "commercial") as
                        | "residential"
                        | "commercial",
                    city: billingAddress.city || "",
                    country: billingAddress.country || "",
                    state: billingAddress.state || "",
                    zip_code: billingAddress.zip_code || "",
                    company_name: billingAddress.company_name || null,
                    contact_name:
                        customerData.data?.customer?.primary_contact_name || null,
                    contact_email: userData.email || null,
                    contact_number: customerData.data?.customer?.phone || null,
                };

                console.log("Created address data for display:", addressData);
                setSelectedBillingAddress(addressData);

                // Check if a shipping address with the same details already exists
                const existingShippingAddress = shippingAddresses.data?.find(
                    (addr) =>
                        addr.address === billingAddress.address &&
                        addr.address_2 === billingAddress.address_2 &&
                        addr.city === billingAddress.city &&
                        addr.state === billingAddress.state &&
                        addr.zip_code === billingAddress.zip_code &&
                        addr.country === billingAddress.country &&
                        addr.option_name === "Created from billing address"
                );

                if (existingShippingAddress) {
                    console.log(
                        "Using existing shipping address:",
                        existingShippingAddress
                    );
                    checkoutForm.setValue(
                        "shippingAddressId",
                        existingShippingAddress.id,
                        {
                            shouldValidate: false,
                            shouldDirty: true,
                            shouldTouch: true,
                        }
                    );

                    handleAddressUpdate(billingAddress, existingShippingAddress.id);
                } else {
                    // Create a new shipping address from billing address with proper type casting
                    const shippingAddressData = {
                        address: billingAddress.address || "",
                        address_2: billingAddress.address_2 || "",
                        address_type: (billingAddress.address_type || "commercial") as
                            | "residential"
                            | "commercial",
                        city: billingAddress.city || "",
                        country: billingAddress.country || "",
                        state: billingAddress.state || "",
                        zip_code: billingAddress.zip_code || "",
                        contact_name:
                            customerData.data?.customer?.primary_contact_name || "",
                        contact_email: userData.email || "",
                        contact_number: customerData.data?.customer?.phone || "",
                        company_name: billingAddress.company_name || "",
                        option_name: "Created from billing address",
                        default: false,
                    };

                    console.log(
                        "Creating new shipping address with data:",
                        shippingAddressData
                    );
                    console.log(
                        "Customer primary contact name:",
                        customerData.data?.customer?.primary_contact_name
                    );
                    console.log(
                        "Billing address company name:",
                        billingAddress.company_name
                    );

                    // Create new shipping address
                    addShippingAddressMutation
                        .mutateAsync(shippingAddressData)
                        .then((newShippingAddress) => {
                            console.log("New shipping address created:", newShippingAddress);
                            // Set the new shipping address ID in the form
                            checkoutForm.setValue(
                                "shippingAddressId",
                                newShippingAddress.id,
                                {
                                    shouldValidate: false,
                                    shouldDirty: true,
                                    shouldTouch: true,
                                }
                            );

                            handleAddressUpdate(billingAddress, newShippingAddress.id);
                        })
                        .catch((error) => {
                            console.error("Error creating shipping address:", error);
                            toast({
                                title: "Error",
                                description:
                                    "Failed to create shipping address from billing address",
                                variant: "destructive",
                            });
                        });
                }
            }
        } else {
            if (!useBillingAddress) {
                console.log("Clearing shipping address");
                checkoutForm.setValue("shippingAddressId", "", {
                    shouldValidate: false,
                });
                setSelectedBillingAddress(null);
            }
        }
    }, [useBillingAddress, billingAddressId, billingAddresses.data]);

    // Helper function to handle address updates
    const handleAddressUpdate = (
        billingAddress: any,
        shippingAddressId: string
    ) => {
        // Check if billing address is in a special country/state
        const billingCountry = billingAddress.country?.toLowerCase();
        const billingState = billingAddress.state?.toLowerCase();
        const isSpecialCountry =
            billingCountry === "mexico" ||
            billingCountry === "puerto rico" ||
            billingState === "puerto rico" ||
            billingState === "mexico";

        // For special countries, ship_collect must be false
        if (isSpecialCountry) {
            checkoutForm.setValue("ship_collect", false, {
                shouldValidate: false,
            });
            // Clear UPS account number if it was set
            checkoutForm.setValue("ups_account_number", "", {
                shouldValidate: false,
            });
        } else {
            // For non-special countries, reset ship_collect to undefined to force selection
            checkoutForm.setValue("ship_collect", undefined, {
                shouldValidate: false,
            });
            checkoutForm.clearErrors("ship_collect");
        }

        // Always reset delivery_method when changing shipping address
        checkoutForm.setValue("delivery_method", "", {
            shouldValidate: false,
        });
        checkoutForm.clearErrors("delivery_method");
    };

    return (
        <Card className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center gap-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Truck className="h-5 w-5 text-primary" />
                </div>
                <div>
                    <CardTitle>Shipping Information</CardTitle>
                    <CardDescription>Select your shipping address</CardDescription>
                </div>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="flex items-center space-x-2 pb-4">
                    <Checkbox
                        id="use-billing-address"
                        checked={useBillingAddress}
                        onCheckedChange={(checked) => {
                            setUseBillingAddress(checked === true);
                            if (!checked) {
                                // Clear shipping address when unchecking
                                checkoutForm.setValue("shippingAddressId", "", {
                                    shouldValidate: false,
                                });
                                setSelectedBillingAddress(null);
                            }
                        }}
                    />
                    <label
                        htmlFor="use-billing-address"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                        Use billing address as shipping address
                    </label>
                </div>

                {useBillingAddress && selectedBillingAddress ? (
                    <div className="rounded-lg border p-4 space-y-2">
                        <p className="font-medium">Selected Billing Address:</p>
                        <div className="space-y-1 text-sm">
                            {selectedBillingAddress.contact_name && (
                                <p>{selectedBillingAddress.contact_name}</p>
                            )}
                            {selectedBillingAddress.company_name && (
                                <p>{selectedBillingAddress.company_name}</p>
                            )}
                            <p>{selectedBillingAddress.address}</p>
                            {selectedBillingAddress.address_2 && (
                                <p>{selectedBillingAddress.address_2}</p>
                            )}
                            <p>
                                {selectedBillingAddress.city}, {selectedBillingAddress.state}{" "}
                                {selectedBillingAddress.zip_code}
                            </p>
                            <p>{selectedBillingAddress.country}</p>
                            {selectedBillingAddress.contact_number && (
                                <p>Phone: {selectedBillingAddress.contact_number}</p>
                            )}
                            {selectedBillingAddress.contact_email && (
                                <p>Email: {selectedBillingAddress.contact_email}</p>
                            )}
                        </div>
                    </div>
                ) : !useBillingAddress ? (
                    <ShippingAddresses checkoutForm={checkoutForm} />
                ) : null}
            </CardContent>
        </Card>
    );
}
