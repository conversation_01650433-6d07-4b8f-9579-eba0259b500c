import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { createSupabaseAdminClient } from "@/supabase";
import { UserRole, UserStatus } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAdmin(
  matchRoute({
    GET: searchUsersHandler,
  })
);

export interface SearchUsersResponse {
  data?: PublicUserWithCustomer[];
  error?: string;
  total?: number;
}

async function searchUsersHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<SearchUsersResponse>
) {
  try {
    const {
      query,
      page = 1,
      limit = 10,
      roles,
      status,
      group_id,
      sortBy,
      sortOrder,
    } = req.query;

    if (!query) {
      return res.status(400).json({ error: "Search query is required" });
    }

    const searchTerm = decodeURIComponent(query.toString().toLowerCase());
    const pageNumber = Number(page);
    const limitNumber = Number(limit);
    const offset = (pageNumber - 1) * limitNumber;
    const sortByParam = sortBy ? String(sortBy) : "created_at";
    const sortOrderParam = sortOrder ? String(sortOrder) : "desc";

    const fromIndex = (pageNumber - 1) * limitNumber;
    const toIndex = fromIndex + limitNumber - 1;

    // Apply sorting - handle company name sorting with proper join
    const ascending = sortOrderParam === "asc";
    const shouldSortByCompanyName = sortByParam === "companyName";

    let customersCount = 0;

    const rolesParam = roles as string;
    const validUserRoles: UserRole[] = ["admin", "staff", "manager"];
    const rolesList = rolesParam
      ? (rolesParam
          .split(",")
          .map((r) => r.trim().toLowerCase())
          .filter((r) => validUserRoles.includes(r as UserRole)) as UserRole[])
      : null;

    const statusFilter = status as string | undefined;
    const groupIdFilter = group_id as string | undefined;

    const supabaseAdminClient = createSupabaseAdminClient();

    let userIdsFromGroupFilter: string[] | null = null;

    if (groupIdFilter && groupIdFilter !== "all") {
      const { data: customerGroupEntries, error: cgError } =
        await supabaseAdminClient
          .from("customer_groups")
          .select("customer_id")
          .eq("group_id", groupIdFilter);

      if (cgError) {
        return res.status(500).json({
          error: `Failed to fetch customer group data: ${cgError.message}`,
        });
      }

      if (!customerGroupEntries || customerGroupEntries.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }
      const customerIdsFromGroup = customerGroupEntries.map(
        (cg) => cg.customer_id
      );

      customersCount = customerIdsFromGroup.length;
      const customerIds = customerIdsFromGroup.slice(fromIndex, toIndex + 1);

      if (customerIdsFromGroup.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }

      const { data: customersInGroup, error: custError } =
        await supabaseAdminClient
          .from("customers")
          .select("user_id")
          .in("id", customerIds);

      if (custError) {
        return res.status(500).json({
          error: `Failed to fetch customers in group: ${custError.message}`,
        });
      }

      if (!customersInGroup || customersInGroup.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }
      userIdsFromGroupFilter = customersInGroup
        .map((c) => c.user_id)
        .filter((id) => id !== null) as string[];
      if (userIdsFromGroupFilter.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }
    }

    let baseQuery = supabaseAdminClient
      .from("users")
      .select("*", {
        count: "exact",
      })
      .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`);

    // Apply status filtering
    if (statusFilter && statusFilter !== "all") {
      const validStatuses = ["approved", "pending", "rejected"];
      if (validStatuses.includes(statusFilter)) {
        baseQuery = baseQuery.eq("status", statusFilter as UserStatus);
      }
      // If statusFilter is provided but not 'all' and not a recognized specific status,
      // the query proceeds without this status constraint, potentially returning 0 results for this part if other conditions match.
    } else if (statusFilter === "all") {
      // Explicitly do nothing for status filtering if "all" is selected for a search,
      // meaning all users matching search term, regardless of status (including rejected), will be considered.
    } else {
      // If statusFilter is not provided at all (undefined), also do nothing for status filtering by default during search.
    }

    if (rolesList && rolesList.length > 0) {
      baseQuery = baseQuery.in("role", rolesList);
    }

    if (userIdsFromGroupFilter) {
      baseQuery = baseQuery.in("id", userIdsFromGroupFilter);
    }

    let users;
    let usersError;
    let countResult;

    if (shouldSortByCompanyName) {
      // For company name sorting, we need to sort the entire dataset before pagination
      // Step 1: Get all users with their company names matching search criteria
      let allUsersQuery = supabaseAdminClient
        .from("users")
        .select(
          `
          id,
          created_at,
          updated_at,
          status,
          role,
          first_name,
          last_name,
          email,
          notes,
          phone,
          customers(company_name)
          `,
          { count: "exact" }
        )
        .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`);

      // Apply status filtering
      if (statusFilter && statusFilter !== "all") {
        const validStatuses = ["approved", "pending", "rejected"];
        if (validStatuses.includes(statusFilter)) {
          allUsersQuery = allUsersQuery.eq(
            "status",
            statusFilter as UserStatus
          );
        }
      }

      if (rolesList && rolesList.length > 0) {
        allUsersQuery = allUsersQuery.in("role", rolesList);
      }

      if (userIdsFromGroupFilter) {
        allUsersQuery = allUsersQuery.in("id", userIdsFromGroupFilter);
      }

      const {
        data: allUsersWithCompany,
        error: allUsersError,
        count,
      } = await allUsersQuery;

      if (allUsersError) {
        return res.status(500).json({ error: allUsersError.message });
      }

      if (!allUsersWithCompany || allUsersWithCompany.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }

      // Step 2: Sort by company name (handling nulls and empty strings)
      const sortedUsers = allUsersWithCompany.sort((a, b) => {
        const aCompanyName = a.customers?.[0]?.company_name?.trim() || "";
        const bCompanyName = b.customers?.[0]?.company_name?.trim() || "";

        // Handle empty company names - treat them as "N/A" and put at end for asc, beginning for desc
        if (!aCompanyName && bCompanyName) {
          return ascending ? 1 : -1;
        }
        if (!bCompanyName && aCompanyName) {
          return ascending ? -1 : 1;
        }

        // Both empty or both have values
        const comparison = aCompanyName.localeCompare(bCompanyName, "en", {
          sensitivity: "base",
        });
        return ascending ? comparison : -comparison;
      });

      // Step 3: Apply pagination to the sorted results
      const startIndex = offset;
      const endIndex = startIndex + limitNumber;
      const paginatedUserIds = sortedUsers
        .slice(startIndex, endIndex)
        .map((user) => user.id);

      // Step 4: Fetch the detailed user data for the paginated results
      const paginatedUsersQuery = supabaseAdminClient
        .from("users")
        .select("*")
        .in("id", paginatedUserIds);

      const { data: paginatedUsers, error: paginatedError } =
        await paginatedUsersQuery;

      if (paginatedError) {
        return res.status(500).json({ error: paginatedError.message });
      }

      // Maintain the sorted order by mapping the IDs back to users
      const orderedUsers = paginatedUserIds
        .map((id) => paginatedUsers?.find((user) => user.id === id))
        .filter(Boolean);

      users = orderedUsers;
      usersError = null;
      countResult = count;
    } else {
      // Apply database sorting for non-company name columns
      const sortColumnMap: Record<string, string> = {
        name: "first_name", // Sort by first name for name column
        status: "status",
        email: "email",
        created_at: "created_at",
        updated_at: "updated_at",
      };

      const dbSortColumn = sortColumnMap[sortByParam] || "created_at";
      const finalQuery = baseQuery.order(dbSortColumn, { ascending });

      const result = await finalQuery.range(offset, offset + limitNumber - 1);
      users = result.data;
      usersError = result.error;
      countResult = result.count;
    }

    const count = group_id ? customersCount : countResult;

    if (usersError) {
      return res.status(500).json({ error: usersError.message });
    }

    if (!users || users.length === 0) {
      return res.status(200).json({ data: [], total: 0 });
    }

    const userIds = users.map((user) => user.id);

    // Get business details for these users
    const { data: businessDetails, error: businessError } =
      await supabaseAdminClient
        .from("business_details")
        .select("*")
        .in("user_id", userIds);

    if (businessError) {
      return res.status(500).json({ error: businessError.message });
    }

    // Get customer data for these users
    const { data: customers, error: customersError } = await supabaseAdminClient
      .from("customers")
      .select(
        `
        id,
        user_id,
        company_name,
        primary_contact_name,
        phone,
        customer_number,
        credit_limit,
        company_website,
        role,
        status,
        shipping_notes,
        billing_addresses(*),
        categories:customer_categories(*, category_data:categories(*)),
        group_data:customer_groups(*, data:groups(*)),
        updated_at
      `
      )
      .in("user_id", userIds);

    if (customersError) {
      return res.status(500).json({ error: customersError.message });
    }

    // If no customers found, return just users with business details
    if (!customers?.length) {
      return res.status(200).json({
        data: users.map((user) => ({
          ...user,
          business_details:
            businessDetails?.find((bd) => bd.user_id === user.id) || null,
          customer_data: [],
        })) as unknown as PublicUserWithCustomer[],
        total: count || 0,
      });
    }

    const customerIds = customers.map((c) => c.id);

    // Get all related data for customers concurrently
    const [
      customerGroupsResult,
      billingAddressesResult,
      customerCategoriesResult,
    ] = await Promise.all([
      supabaseAdminClient
        .from("customer_groups")
        .select(
          `
          customer_id,
          groups (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("billing_addresses")
        .select(
          `
          id,
          city,
          state,
          address,
          country,
          default,
          approved,
          zip_code,
          created_at,
          customer_id
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("customer_categories")
        .select(
          `
          customer_id,
          categories (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
    ]);

    const { data: customerGroupsData } = customerGroupsResult;
    const { data: billingAddressesData } = billingAddressesResult;
    const { data: customerCategoriesData } = customerCategoriesResult;

    // Rebuild the nested structure to match the original response format
    const customersWithDetails = users.map((user) => {
      // Find business details for this user
      const userBusinessDetails = businessDetails?.find(
        (bd) => bd.user_id === user.id
      );

      // Find customers for this user
      const userCustomersData = customers
        ?.filter((c) => c.user_id === user.id)
        .map((customer) => {
          const customerGroupInfo = customerGroupsData?.find(
            (cg) => cg.customer_id === customer.id
          );

          const customerBillingAddresses = billingAddressesData?.filter(
            (ba) => ba.customer_id === customer.id
          );
          const customerCategories = customerCategoriesData?.filter(
            (cc) => cc.customer_id === customer.id
          );

          return {
            ...customer,
            group_data:
              customerGroupInfo && customerGroupInfo.groups
                ? { data: customerGroupInfo.groups }
                : null,
            billing_addresses: customerBillingAddresses || [],
            categories:
              customerCategories?.map((cc) => ({
                category_data: cc.categories,
              })) || [],
          };
        });

      return {
        ...user,
        business_details: userBusinessDetails || null,
        customer_data: userCustomersData || [],
      };
    }) as unknown as PublicUserWithCustomer[];

    return res.status(200).json({
      data: customersWithDetails,
      total: count || customersWithDetails.length,
    });
  } catch (error: any) {
    console.error("Search users error:", error);
    return res.status(400).json({
      error: error.message || "An error occurred while searching users data",
    });
  }
}
