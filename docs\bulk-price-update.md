## Bulk Price Update System

### Overview
The bulk price update system allows administrators to upload CSV files containing pricing information for multiple products across different customer groups. The system validates data, handles group management, and provides comprehensive error handling.

### CSV Format
The system expects CSV files in the following long format:
```
Part_Nbr,PC,Qty,Price,Weight
PRODUCT001,GROUP1,1,100.00,5.5
PRODUCT001,GROUP2,1,105.00,5.5
PRODUCT002,GROUP1,1,200.00,10.0
```

**Required Columns:**
- `Part_Nbr`: Product part number
- `PC`: Price code/customer group (case-insensitive, stored as uppercase)
- `Qty`: Quantity (typically 1)
- `Price`: Unit price
- `Weight`: Product weight

### Admin Workflow

#### 1. File Upload and Validation
- Upload CSV file using the file picker
- System validates file format and required columns
- Displays preview of parsed data

#### 2. Group Validation
The system performs case-insensitive validation of customer groups:
- Existing groups are identified and validated
- Non-existent groups are flagged for admin attention
- Admin has two options for handling invalid groups:

**Option A: Create Missing Groups**
- Click "Create Groups" button
- Review list of groups to be created
- Confirm creation to add new groups to the system
- Proceed with price update

**Option B: Skip Invalid Data**
- Click "Skip Invalid" button
- System removes rows with non-existent groups
- Proceed with remaining valid data
- Skipped data is logged for reference

#### 3. Price Update Processing
- Valid data is processed through the bulk price update API
- Progress indicator shows update status
- Success/error messages provide feedback
- Updated data is reflected in the system

### Export Functionality
The system provides export capabilities in the same long format:
- Export current pricing data for selected groups
- Maintains consistency with import format
- Useful for backup and data management

### Error Handling
- Comprehensive validation of CSV structure
- Clear error messages for invalid data
- Graceful handling of API errors
- Detailed logging of skipped records

### Technical Implementation
- React/TypeScript with Next.js
- CSV parsing with validation utilities
- Integration with existing group and pricing APIs
- Responsive UI with loading states and progress indicators
