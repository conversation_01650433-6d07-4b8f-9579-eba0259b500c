import {
    Card,
    CardContent,
    CardDescription,
    Card<PERSON><PERSON>er,
    CardTitle,
} from "@/components/ui/card";
import {
    FormControl,
    FormField,
    FormItem,
    FormMessage
} from "@/components/ui/form";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
    FileText
} from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import {
    getCountyForCity
} from "@/utils/nevada-tax-rates";
import { CheckoutFormValues } from "@/pages/store/checkout";
import { useTaxRateCalculation } from "@/queries/tax-rate-queries";

interface CheckoutTaxExemptCardProps {
    form: UseFormReturn<CheckoutFormValues>;
    shippingAddresses: any;
}

export function CheckoutTaxExemptCard({ form, shippingAddresses }: CheckoutTaxExemptCardProps) {
    // Get the current shipping address
    const shippingAddressId = form.watch("shippingAddressId");
    const shippingAddress = shippingAddresses.data?.find(
        (addr: any) => addr.id === shippingAddressId
    );

    // Only show Tax Exempt card if shipping state is Nevada
    if (shippingAddress?.state !== "Nevada" && shippingAddress?.state !== "NV") {
        return null;
    }

    // Calculate current tax rate for the shipping city
    const currentTaxRate = useTaxRateCalculation(
        shippingAddress?.city || "",
        shippingAddress?.state || ""
    );
    const county = getCountyForCity(shippingAddress?.city || "");

    return (
        <Card className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center gap-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <FileText className="h-5 w-5 text-primary" />
                </div>
                <div>
                    <CardTitle>Tax Exempt</CardTitle>
                    <CardDescription>
                        Indicate if this order is tax exempt
                    </CardDescription>
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div className="rounded-lg border bg-muted/50 p-4">
                        <div className="mb-3">
                            <p className="text-sm font-medium text-foreground mb-1">
                                Tax Information for {shippingAddress?.city || "your location"}
                            </p>
                            <p className="text-xs text-muted-foreground">
                                {county ? `${county} County` : "Nevada"} • Tax Rate:{" "}
                                {currentTaxRate.toFixed(3)}%
                            </p>
                        </div>
                        <p className="text-sm text-muted-foreground">
                            Maxton only collects sales tax for the state of Nevada. If you are
                            a non-taxable entity, please email tax exempt certificate to{" "}
                            <a
                                className="text-primary underline"
                                href="mailto:<EMAIL>"
                            >
                                <EMAIL>
                            </a>{" "}
                            or fax it to{" "}
                            <a className="text-primary underline" href="tel:+17757821701">
                                (*************
                            </a>
                            . All tax exempt claims must be backed by a tax exempt certificate
                            as proof prior to shipment of product.
                        </p>
                    </div>
                    <FormField
                        control={form.control}
                        name="tax_exempt"
                        render={({ field }) => (
                            <FormItem className="space-y-3">
                                <FormControl>
                                    <RadioGroup
                                        onValueChange={(value) => {
                                            field.onChange(value === "true");
                                        }}
                                        defaultValue={field.value ? "true" : "false"}
                                        className="flex flex-col space-y-2"
                                    >
                                        <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                                            <RadioGroupItem value="true" id="tax-exempt-true" />
                                            <span>
                                                This is a tax exempt order and I will provide the
                                                required documentation
                                            </span>
                                        </Label>
                                        <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                                            <RadioGroupItem value="false" id="tax-exempt-false" />
                                            <span>This order is not tax exempt</span>
                                        </Label>
                                    </RadioGroup>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
            </CardContent>
        </Card>
    );
}