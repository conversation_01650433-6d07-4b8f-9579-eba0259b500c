import { PortableText, PortableTextComponents } from "@portabletext/react";
import { But<PERSON> } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { FormField } from "@stackshift-ui/form-field";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { SocialIcons } from "@stackshift-ui/social-icons";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { IoLocationSharp } from "react-icons/io5";
import { FaPhoneAlt, FaFax, FaBusinessTime } from "react-icons/fa";
import { MdEmail } from "react-icons/md";

import { ContactProps } from ".";
import { thankYouPageLink } from "../../../helper";
import { SocialLink, Form as iForm } from "../../../types";
import { usePathname } from "next/navigation";

export default function Contact_C({
  contactDescription,
  officeInformation,
  contactEmail,
  contactNumber,
  officeHours,
  contactFaxNumber,
  socialLinks,
  form,
  block,
  title,
}: ContactProps) {
  return (
    <Section className="pt-10 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex
          direction="col-reverse"
          gap={8}
          justify="between"
          className="lg:flex-row-reverse"
        >
          {/* <FormFields
            form={form}
            block={block}
            blockCustomization={blockCustomization}
          /> */}

          <div className="relative lg:-mt-48 w-full lg:w-2/3 lg:pl-10">
            <FormFields
              form={form}
              block={block}
              blockCustomization={blockCustomization}
            />
          </div>
          <Flex direction="col" gap={8} className=" w-full basis-1/2">
            <ContactTitleAndDescription
              title={title}
              contactDescription={contactDescription}
            />
            <OfficeInformation
              officeInformation={officeInformation}
              contactEmail={contactEmail}
              contactNumber={contactNumber}
              officeHours={officeHours}
              contactFaxNumber={contactFaxNumber}
            />
            <SocialLinksCard socialLinks={socialLinks} />
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function ContactTitleAndDescription({
  title,
  contactDescription,
}: {
  title?: string;
  contactDescription?: string;
}) {
  return (
    <div>
      {title && <Heading type="h2">{title}</Heading>}
      {contactDescription && (
        <Text muted className="mt-5 leading-loose">
          {contactDescription}
        </Text>
      )}
    </div>
  );
}

function OfficeInformation({
  officeInformation,
  officeHours,
  contactEmail,
  contactNumber,
  contactFaxNumber,
}: {
  officeInformation?: string;
  officeHours?: string;
  contactEmail?: string;
  contactNumber?: string;
  contactFaxNumber?: string;
}) {
  if (!officeInformation) return null;

  return (
    <Flex direction="col" gap={6} className="w-full">
      <div className="pb-4 border-b border-gray-300 w-full">
        <Flex gap={3} items="center">
          <IoLocationSharp className="w-5 h-5 text-primary" />
          <Heading className="text-lg text-primary" type="h2">
            Office
          </Heading>
        </Flex>
        {officeInformation && (
          <Text className="mt-2 ml-7 text-secondary">{officeInformation}</Text>
        )}
      </div>

      {contactNumber && (
        <div className="pb-4 border-b border-gray-300 w-full">
          <Flex gap={3} items="center">
            <FaPhoneAlt className="w-5 h-5 text-primary" />
            <Heading className="text-lg text-primary" type="h2">
              Phone
            </Heading>
          </Flex>
          <Text className="mt-2 ml-7 text-secondary">
            <Link href={`tel:${contactNumber}`}>{contactNumber}</Link>
          </Text>
        </div>
      )}

      {contactFaxNumber && (
        <div className="pb-4 border-b border-gray-300 w-full">
          <Flex gap={3} items="center">
            <FaFax className="w-5 h-5 text-primary" />
            <Heading className="text-lg text-primary" type="h2">
              Fax Number
            </Heading>
          </Flex>
          <Text className="mt-2 ml-7 text-secondary">{contactFaxNumber}</Text>
        </div>
      )}

      {contactEmail && (
        <div className="pb-4 border-b border-gray-300 w-full">
          <Flex gap={3} items="center">
            <MdEmail className="w-5 h-5 text-primary" />
            <Heading className="text-lg text-primary" type="h2">
              Email
            </Heading>
          </Flex>
          <Text className="mt-2 ml-7 text-secondary">
            <Link href={`mailto:${contactEmail}`}>{contactEmail}</Link>
          </Text>
        </div>
      )}

      {officeHours && (
        <div className="pb-4 border-b border-gray-300 w-full">
          <Flex gap={3} items="center">
            <FaBusinessTime className="w-5 h-5 text-primary" />
            <Heading className="text-lg text-primary" type="h2">
              Office Hours
            </Heading>
          </Flex>
          <Text className="mt-2 ml-7 text-secondary">{officeHours}</Text>
        </div>
      )}
    </Flex>
  );
}

function SocialLinksCard({ socialLinks }: { socialLinks?: SocialLink[] }) {
  if (!socialLinks) return null;
  return (
    <div className="w-full md:w-1/3 lg:w-full">
      <Heading type="h2" fontSize="2xl" className="mb-2">
        Socials
      </Heading>
      <Flex gap={4}>
        <SocialLinks socialLinks={socialLinks} />
      </Flex>
    </div>
  );
}

function SocialLinks({ socialLinks }: { socialLinks?: SocialLink[] }) {
  if (!socialLinks) return null;

  return (
    <React.Fragment>
      {socialLinks?.map((social) => (
        <Link
          aria-label={social?.socialMedia || social?.socialMediaPlatform || ""}
          className="inline-block mr-4 rounded"
          target="_blank"
          rel="noopener noreferrer"
          href={social?.socialMediaLink ?? "/page-not-found"}
          key={social?._key}
        >
          {social?.socialMediaIcon?.image ? (
            <Image
              src={social?.socialMediaIcon?.image}
              width={24}
              height={24}
              alt={social?.socialMediaIcon?.alt ?? "contact-socialMedia-icon"}
            />
          ) : (
            <SocialIcons social={social.socialMedia as any} />
          )}
        </Link>
      ))}
    </React.Fragment>
  );
}

function SubtitleAndHeadingText({ form }: { form?: iForm }) {
  return (
    <div className="mb-6 text-left  lg:mb-10">
      {form?.name ? (
        <Heading type="h2" className="text-2xl lg:text-2xl">
          {form?.name}
        </Heading>
      ) : null}
      {form?.subtitle && (
        <Text muted className="mt-3 !text-sm leading-loose">
          {form?.subtitle}
        </Text>
      )}
    </div>
  );
}

function FormFields({
  form,
  block,
  blockCustomization,
}: {
  form?: iForm;
  block: any;
  blockCustomization: PortableTextComponents;
}) {
  if (!form) return null;

  const pathname = usePathname();

  const isReq = pathname.includes("/returned-goods") ? "*" : "";
  return (
    <div className="w-full bg-white rounded-lg shadow-xl p-5 d:p-8">
      <SubtitleAndHeadingText form={form} />
      {form?.fields && (
        <Form
          id={form?.id ?? undefined}
          name="Contact-VariantA-Form"
          className="lg:mx-auto text-xs space-y-3 font-semibold"
          thankyouPage={thankYouPageLink(form?.thankYouPage)}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {form?.fields?.map((formField, index) => {
              // If field has no type, treat it as a section header
              if (!formField.type) {
                return (
                  <Heading
                    key={index}
                    type="h3"
                    fontSize="base"
                    weight="bold"
                    className="mt-4 mb-2 sm:col-span-2 "
                  >
                    {formField.name}
                  </Heading>
                );
              }

              // Find the previous textarea or header
              let previousBreakIndex = index - 1;
              while (
                previousBreakIndex >= 0 &&
                form.fields[previousBreakIndex].type &&
                form.fields[previousBreakIndex].type !== "textarea"
              ) {
                previousBreakIndex--;
              }

              // Find the next textarea or header
              let nextBreakIndex = index + 1;
              while (
                nextBreakIndex < form.fields.length &&
                form.fields[nextBreakIndex].type &&
                form.fields[nextBreakIndex].type !== "textarea"
              ) {
                nextBreakIndex++;
              }

              // Count fields in current sequence
              const fieldsInSequence =
                nextBreakIndex - (previousBreakIndex + 1);
              const positionInSequence = index - (previousBreakIndex + 1);

              const isLastInSequence =
                positionInSequence === fieldsInSequence - 1;
              const isOddSequence = fieldsInSequence % 2 !== 0;

              const isFullWidth =
                formField.type === "textarea" ||
                (isLastInSequence && isOddSequence);

              return (
                <div key={index} className={isFullWidth ? "sm:col-span-2" : ""}>
                  {formField.type === "inputCheckbox" ? (
                    <FormField
                      noLabel
                      name={formField.name ?? ""}
                      placeholder={formField?.placeholder}
                      required={formField.isRequired}
                      className="!bg-gray-100 !text-xs !text-black"
                      {...formField}
                    />
                  ) : (
                    <FormField
                      noLabel
                      variant="primary"
                      name={formField.name ?? ""}
                      placeholder={
                        formField?.isRequired
                          ? isReq + formField?.placeholder
                          : formField?.placeholder
                      }
                      required={formField.isRequired}
                      className="!bg-gray-100 !text-xs !text-black"
                      // {...formField}
                    />
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex flex-col items-center">
            <div>
              <div className="webriq-recaptcha" />
            </div>
            {form?.buttonLabel && (
              <div className="w-full flex justify-center mt-5">
                <Button
                  as="button"
                  ariaLabel={form?.buttonLabel ?? "Contact form submit button"}
                  className="w-full px-6 py-2 font-bold leading-loose text-white transition duration-200 rounded-global bg-primary hover:bg-primary-foreground"
                  type="submit"
                >
                  {form?.buttonLabel}
                </Button>
              </div>
            )}
          </div>
        </Form>
      )}
    </div>
  );
}

// block styling as props to `components` of the PortableText component
const blockCustomization: PortableTextComponents = {
  marks: {
    internalLink: ({ children, value }) => (
      <Link
        aria-label={value.href ?? "internal link"}
        style={{ color: "red" }}
        href={value.slug.current}
      >
        {children}
      </Link>
    ),
    link: ({ children, value }) =>
      value.blank ? (
        <Link
          aria-label={value.href ?? "external link"}
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      ) : (
        <Link
          aria-label={value.href ?? "external link"}
          style={{ color: "blue" }}
          href={value.href}
        >
          {children}
        </Link>
      ),
  },
};

export { Contact_C };
