# Technical Documentation

## Table of Contents
- [State Management](#state-management)
  - [Auth Store](#auth-store-storesauth-storets)
  - [Cart Store](#cart-store-storescart-storets)
  - [Global Settings Store](#global-settings-store-storesglobal-settingsts)
- [Data Fetching](#data-fetching)
  - [React Query Implementation](#react-query-implementation)
  - [Product Queries](#product-queries-product-queriests)
  - [Customer Queries](#customer-queries-customer-queriests)
  - [Admin Queries](#admin-queries-admin-queriests)
  - [User Queries](#user-queries-user-queriests)
- [Authentication Implementation](#authentication-implementation)
  - [Sign-Up Flow](#sign-up-flow)
  - [Sign-In Flow](#sign-in-flow)

## State Management

### Auth Store (`stores/auth-store.ts`)
Manages authentication state using Zustand with persistence.

```typescript
interface AuthStore {
    token: string;
    expiresAt: number;
    expiresIn: number;
    data: User;
    role: UserRole;
    // Methods
    isAuthenticated(): boolean;
    isAdmin(): boolean;
    getRole(): UserRole;
    setRole(role: UserRole): void;
    setToken(value: string): void;
    setData(data: User): void;
    refreshUserData(): Promise<void>;
    refreshSession(): Promise<void>;
    logout(): void;
}
```

### Cart Store (`stores/cart-store.ts`)
Manages shopping cart state using Zustand with persistence.

```typescript
interface CartState {
    items: CartItem[];
    addItem: (item: CartItem) => void;
    removeItem: (itemId: string, selectedOptions?: SelectedOption[]) => void;
    updateQuantity: (itemId: string, quantity: number, selectedOptions?: SelectedOption[]) => void;
    clearCart: () => void;
    getSubtotal: () => number;
    getAdditionalAmount: () => number;
}

interface CartItem {
    id: string;
    name: string;
    price: number;
    quantity: number;
    image?: string;
    sku: string;
    selected: boolean;
    discount?: number;
    selectedOptions?: SelectedOption[];
}
```

### Global Settings Store (`stores/global-settings.ts`)
Manages application-wide settings using Zustand with persistence.

```typescript
interface GlobalSettings {
    collapseSidebar: boolean;
    setCollapseSidebar: (collapseSidebar: boolean) => void;
}
```

## Data Fetching

### React Query Implementation
Located in `@queries/` directory with domain-specific query files:

#### Product Queries (`product-queries.ts`)
- `useGetPaginatedProductsQuery`: Fetch paginated products for authenticated users
  - Parameters: page, limit, token, category (optional)
  - Returns: products, categories, total, totalPages
- `useGetPublicProductsQuery`: Fetch public products
  - Parameters: page, limit, category (optional)
  - Returns: products, categories, total, totalPages
- `useGetAllProductsQuery`: Fetch all products with category filtering
  - Parameters: page, category_ids
  - Returns: products array
- `useGetProductByIdQuery`: Fetch single product
  - Parameters: id
  - Returns: product
- `useInsertProductMutation`: Add new product
  - Parameters: product
  - Returns: inserted product
- `useUpdateProductMutation`: Update product
  - Parameters: id, updates
  - Returns: updated product
- `useDeleteProductMutation`: Delete product
  - Parameters: id
  - Returns: deleted product id

#### Customer Queries (`customer-queries.ts`)
- `useGetCustomerQuery`: Fetch customer details
- `useGetBillingAddressesQuery`: Fetch billing addresses
- `useAddBillingAddressMutation`: Add billing address
- `useUpdateBillingAddressMutation`: Update billing address
- `useDeleteBillingAddressMutation`: Delete billing address
- `useGetShippingAddressesQuery`: Fetch shipping addresses
- `useAddShippingAddressMutation`: Add shipping address
- `useUpdateShippingAddressMutation`: Update shipping address
- `useDeleteShippingAddressMutation`: Delete shipping address
- `useGetOrdersByUserIdQuery`: Fetch user orders
- `useGetOrderByIdQuery`: Fetch single order
- `useCancelOrderMutation`: Cancel order
- `useSubmitCancellationRequestMutation`: Submit cancellation request
- `useGetCustomerDashboardDataQuery`: Fetch dashboard data

#### Admin Queries (`admin-queries.ts`)
- `useGetCustomersQuery`: Fetch all customers
- `useGetAllProductsQuery`: Fetch all products (admin view)
- `createProductMutation`: Create new product
- `useGetDiscountsQuery`: Fetch all discounts
- `createDiscountMutation`: Create new discount
- `useGetGroupsQuery`: Fetch all groups
- `useCreateGroupMutation`: Create new group
- `useGetCustomerGroupsQuery`: Fetch customer groups
- `useGetBillingAddressesQuery`: Fetch all billing addresses
- `useApproveBillingAddressMutation`: Approve billing address
- `useGetDashboardDataQuery`: Fetch admin dashboard data
- `useGetAllUsersQuery`: Fetch all users
- `useUpdateUserStatusMutation`: Update user status
- `useGetOrdersQuery`: Fetch all orders
- `useUpdateOrderStatusMutation`: Update order status
- `useGetAllCategoriesQuery`: Fetch all categories
- `useGetPendingBillingAddressesQuery`: Fetch pending billing addresses
- `useGetCancellationRequestsQuery`: Fetch cancellation requests
- `useUpdateCancellationRequestMutation`: Update cancellation request
- `useCreateCategoryMutation`: Create new category
- `useDeleteCategoryMutation`: Delete category
- `useUpdateCategoryMutation`: Update category
- `useDeleteProductCategoryMutation`: Remove category from product
- `useCreateProductCategoryMutation`: Assign category to product
- `useDeleteCustomerGroupMutation`: Remove group from customer
- `useGetPendingUsersQuery`: Fetch pending users
- `useGetCustomerCategoriesQuery`: Fetch customer categories
- `useCreateCustomerCategoryMutation`: Assign category to customer
- `useDeleteCustomerCategoryMutation`: Remove category from customer
- `useGetSalesReportQuery`: Fetch sales report

#### User Queries (`user-queries.ts`)
- `useGetPublicUserQuery`: Fetch user profile
- `useSignupMutation`: Register new user
- `useSigninMutation`: User login
- `useSignUpCallbackMutation`: Handle signup callback
- `useResetPasswordMutation`: Reset password
- `useSignOutMutation`: User logout
- `useResetPasswordWithTokenMutation`: Reset password with token
- `useUpdateUserMutation`: Update user profile
- `useChangePasswordMutation`: Change password

## Authentication Implementation

### Sign-Up Flow
1. User submits registration form with:
   - Email, password
   - Personal information (first/last name)
   - Address information
2. Front-end calls `useSignupMutation`
3. API creates auth user in Supabase using `supabaseClient.auth.signUp`
4. API creates public user profile with personal information
5. Supabase sends verification email with callback URL
6. User clicks verification link, redirected to `/auth/callback`
7. Front-end calls `useSignUpCallbackMutation`
8. API validates token and completes registration
9. JWT token is stored in AuthStore
10. User is redirected to dashboard

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Supabase
    participant Email

    User->>Frontend: Fill registration form
    Frontend->>API: POST /api/users (createUser)
    API->>Supabase: auth.signUp()
    Supabase-->>API: User data + session
    API->>Supabase: Insert public user profile
    Supabase-->>API: Public user data
    Supabase->>Email: Send verification email
    Email->>User: Receive verification link
    User->>Frontend: Click verification link
    Frontend->>API: POST /api/auth/callback
    API->>Supabase: Verify token
    Supabase-->>API: Verified session
    API-->>Frontend: JWT token + user data
    Frontend->>Frontend: Store in AuthStore
    Frontend->>Frontend: Redirect to dashboard
```

### Sign-In Flow
1. User submits login form with email and password
2. Front-end calls `useSigninMutation`
3. API authenticates with Supabase
4. API returns JWT token and user data
5. Token and user data are stored in AuthStore
6. User is redirected to dashboard

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant Supabase

    User->>Frontend: Fill login form
    Frontend->>API: POST /api/auth/sign-in
    API->>Supabase: auth.signInWithPassword()
    Supabase-->>API: User data + session
    API-->>Frontend: JWT token + user data
    Frontend->>Frontend: Store in AuthStore
    Frontend->>Frontend: Redirect to dashboard
```

### Session Management
1. JWT-based authentication
   - Token stored in AuthStore
   - Automatic token refresh
   - Session expiration handling
2. Role-based access control (RBAC)
   - User roles (admin, manager, customer)
   - Permission checking
3. Secure token storage
   - LocalStorage with encryption
   - Token expiration
4. Session refresh mechanism
   - Automatic refresh before expiration
   - Graceful session expiration handling

### Password Reset Flow
1. User requests password reset
2. Front-end calls `useResetPasswordMutation`
3. API sends reset email via Supabase
4. User clicks reset link
5. Front-end displays reset form
6. User submits new password
7. Front-end calls `useResetPasswordWithTokenMutation`
8. Password is updated in Supabase
9. User can login with new password

## Libraries and Tools

### Core Libraries
- Next.js 14 (App Router)
  - Server Components
  - API Routes
  - Middleware
- React 18
  - Hooks
  - Context
- TypeScript
  - Strict type checking
  - Interface definitions
- Zustand
  - State management
  - Persistence middleware
- React Query (TanStack Query)
  - Data fetching
  - Cache management
  - Mutations
- Zod
  - Schema validation
  - Type inference
- Supabase
  - Authentication
  - Database
  - Storage

### UI Libraries
- Tailwind CSS
  - Utility-first CSS
  - Responsive design
  - Dark mode support
- Shadcn UI
  - Accessible components
  - Customizable themes
- Radix UI
  - Headless components
  - Accessibility

## Data Flow Architecture

```mermaid
graph TD
    A[Client] --> B[Next.js App Router]
    B --> C[React Components]
    C --> D[Zustand Stores]
    C --> E[React Query]
    E --> F[API Routes]
    F --> G[Supabase]
    D --> H[LocalStorage]
    
    subgraph State Management
        D --> I[Auth Store]
        D --> J[Cart Store]
        D --> K[Global Settings]
    end
    
    subgraph Data Fetching
        E --> L[Product Queries]
        E --> M[Customer Queries]
        E --> N[Admin Queries]
        E --> O[User Queries]
    end
    
    subgraph Backend Services
        G --> P[Authentication]
        G --> Q[Database]
        G --> R[Storage]
    end
```

## Error Handling

### API Error Handling
- Consistent error response format
- HTTP status codes
- Error messages and codes
- Client-side error handling

### Form Validation
- Zod schemas
- Client-side validation
- Server-side validation
- Error messages

### State Error Handling
- Query error states
- Mutation error handling
- Store error states
- Error boundaries 