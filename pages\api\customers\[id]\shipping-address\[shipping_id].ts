import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { ShippingAddress } from "@/supabase/types";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
  matchRoute({
    DELETE: deleteShippingAddressHandler,
    PATCH: updateShippingAddressHandler,
  }),
);

export interface DeleteShippingAddressResponse {
  error?: string;
  shipping_address?: ShippingAddress;
}

async function deleteShippingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<DeleteShippingAddressResponse>,
) {
  const user_id = req.query.id?.toString();
  const shipping_id = req.query.shipping_id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  if (!shipping_id) {
    return res.status(400).json({ error: "Missing shipping id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const customerId = customer.data.id;

  const data = await supabaseAdminClient
    .from("shipping_addresses")
    .delete()
    .eq("id", shipping_id)
    .eq("customer_id", customerId)
    .select("*")
    .single();

  if (data.error) {
    return res.status(400).json({ error: data.error.message });
  }

  return res.status(200).json({ shipping_address: data.data });
}

const updateShippingAddressSchema = z.object({
  address: z.string().min(1, "Address is required"),
  address_2: z.string().optional(),
  address_type: z.enum(["residential", "commercial"], {
    required_error: "Address type is required",
  }),
  city: z.string().min(1, "City is required"),
  country: z.string().min(1, "Country is required"),
  state: z.string().min(1, "State is required"),
  zip_code: z.string().min(1, "Zip code is required"),
  default: z.boolean().default(false),
  option_name: z.string().optional(),
  contact_name: z.string().optional(),
  contact_email: z
    .string()
    .email("Invalid email address")
    .optional()
    .or(z.literal("")),
  contact_number: z.string().optional().or(z.literal("")),
});

export type UpdateShippingAddress = z.infer<typeof updateShippingAddressSchema>;

export interface UpdateShippingAddressResponse {
  error?: string;
  shipping_address?: ShippingAddress;
}

async function updateShippingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<UpdateShippingAddressResponse>,
) {
  const user_id = req.query.id?.toString();
  const shipping_id = req.query.shipping_id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  if (!shipping_id) {
    return res.status(400).json({ error: "Missing shipping id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const data = updateShippingAddressSchema.safeParse(req.body);

  if (data.error) {
    return res.status(400).json({ error: data.error.issues[0].message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const shippingAddress = data.data as UpdateShippingAddress;

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;
  const now = new Date();
  const updatedAt = now.toISOString();

  if (shippingAddress.default) {
    await supabaseAdminClient
      .from("shipping_addresses")
      .update({ default: false, updated_at: updatedAt })
      .eq("customer_id", customerId);
  }

  const updatedShippingAddress = await supabaseAdminClient
    .from("shipping_addresses")
    .update({
      ...shippingAddress,
      updated_at: updatedAt,
    })
    .eq("id", shipping_id)
    .eq("customer_id", customerId)
    .select("*")
    .single();

  if (updatedShippingAddress.error) {
    return res
      .status(400)
      .json({ error: updatedShippingAddress.error.message });
  }

  return res
    .status(200)
    .json({ shipping_address: updatedShippingAddress.data });
}
