import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { PortableText } from "@portabletext/react";
import React, { useState } from "react";
import { CiSearch } from "react-icons/ci";
import { Input } from "@stackshift-ui/input";

import { PortfolioProps } from ".";
import {
  Content,
  LabeledRoute,
  MyPortableTextComponents,
  PortfoliosWithCategories,
} from "../../../types";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-xl  text-gray-800">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target={`${
          value?.href?.includes("wwww") || value?.href?.includes("https")
            ? "_blank"
            : ""
        }`}
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addTable: ({ value }) => {
      if (!value?.columns || !value?.rows) {
        console.error("Missing table data:", value);
        return null;
      }

      return (
        <div className="overflow-x-auto my-6">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {value.columns.map(
                  (column: { title: string }, index: number) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-semibold text-gray-800 uppercase tracking-wider"
                    >
                      {column.title}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {value.rows.map((row: { cells: string[] }, rowIndex: number) => (
                <tr key={rowIndex}>
                  {row.cells.map((cell: string, cellIndex: number) => (
                    <td
                      key={cellIndex}
                      className="px-6 py-4 text-xs md:text-sm text-gray-500"
                      dangerouslySetInnerHTML={{
                        __html: cell
                          .replace(
                            /\n\n/g,
                            "<hr class='my-2 border-gray-300' />"
                          )
                          .replace(/\n/g, "<br />"),
                      }}
                    />
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    },
  },
};

export default function Portfolio_E({
  caption,
  title,
  portfoliosWithCategory,
}: PortfolioProps): React.JSX.Element {
  const [activeTab, setActiveTab] = React.useState<string | null | undefined>(
    portfoliosWithCategory?.[0]?.category
  );
  const [selectedContentIndex, setSelectedContentIndex] = React.useState(0);
  const [selectedSubContentIndex, setSelectedSubContentIndex] = React.useState<
    number | null
  >(null);
  const [searchQuery, setSearchQuery] = React.useState("");

  const portfoliosPerCategory = portfoliosWithCategory?.find(
    (portfolio) => portfolio?.category === activeTab
  );

  // Update the search results type and state
  const [searchResults, setSearchResults] = React.useState<
    Array<{ index: number; subIndex: number | null }>
  >([]);

  // Replace the existing searchResults useMemo with this new implementation
  const filteredResults = React.useMemo(() => {
    if (!searchQuery) return [];

    const contents = portfoliosPerCategory?.content;
    if (!contents) return [];

    const results: Array<{ index: number; subIndex: number | null }> = [];

    contents.forEach((content, index) => {
      // Check main title
      if (content.title?.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push({ index, subIndex: null });
      }

      // Check sub-content titles
      content.arrayOfTitleAndDescription?.forEach((subContent, subIndex) => {
        if (
          subContent.title?.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          results.push({ index, subIndex });
        }
      });
    });

    return results;
  }, [portfoliosPerCategory?.content, searchQuery]);

  // Update the useEffect to handle multiple results
  React.useEffect(() => {
    setSearchResults(filteredResults);
    if (filteredResults.length > 0) {
      setSelectedContentIndex(filteredResults[0].index);
      setSelectedSubContentIndex(filteredResults[0].subIndex);
    }
  }, [filteredResults]);

  const selectedContent =
    portfoliosPerCategory?.content?.[selectedContentIndex];

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        <Flex className=" justify-center md:justify-start  ">
          <PortfolioCategories
            categories={portfoliosWithCategory}
            activeTab={activeTab}
            onClickFn={setActiveTab}
          />
        </Flex>

        {/* Search Bar */}
        <Flex className=" justify-center my-10">
          <div className="relative max-w-md w-full">
            <Input
              type="text"
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full border border-primary/80 rounded-lg pr-10 focus:border-primary focus:ring-1 focus:ring-primary"
            />
            <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </Flex>

        <Flex className="flex-col md:flex-row gap-8 md:gap-16">
          <div className="w-full md:w-1/4 lg:w-1/3 max-w-[300px]">
            <TableOfContents
              contents={portfoliosPerCategory?.content}
              selectedIndex={selectedContentIndex}
              selectedSubIndex={selectedSubContentIndex}
              onSelect={setSelectedContentIndex}
              onSelectSub={setSelectedSubContentIndex}
            />
          </div>
          <div className="w-full md:w-3/4 lg:w-2/3">
            {searchQuery && filteredResults.length === 0 ? (
              <div className="text-center py-8">
                <Text className="text-gray-500">
                  No results found for "{searchQuery}" in {activeTab}
                </Text>
              </div>
            ) : (
              <>
                {searchQuery && filteredResults.length > 0 && (
                  <div className="mb-6">
                    <Text className="text-gray-500">
                      Found {filteredResults.length} result
                      {filteredResults.length > 1 ? "s" : ""} for "{searchQuery}
                      "
                    </Text>
                  </div>
                )}
                {searchQuery ? (
                  // Show all matching results when searching
                  <div className="space-y-12">
                    {filteredResults.map((result, idx) => {
                      const content =
                        portfoliosPerCategory?.content?.[result.index];
                      return (
                        <ContentDisplay
                          key={`${content?._key}-${result.subIndex}-${idx}`}
                          content={content}
                          selectedSubIndex={result.subIndex}
                        />
                      );
                    })}
                  </div>
                ) : (
                  // Show normal single content when not searching
                  <ContentDisplay
                    content={selectedContent}
                    selectedSubIndex={selectedSubContentIndex}
                  />
                )}
              </>
            )}
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

function TableOfContents({
  contents,
  selectedIndex,
  selectedSubIndex,
  onSelect,
  onSelectSub,
}: {
  contents?: Content[] | null;
  selectedIndex: number;
  selectedSubIndex: number | null;
  onSelect: (index: number) => void;
  onSelectSub: (index: number | null) => void;
}) {
  const [openDropdowns, setOpenDropdowns] = React.useState<{
    [key: string]: boolean;
  }>({});

  if (!contents || !Array.isArray(contents)) return null;

  const toggleDropdown = (key: string) => {
    setOpenDropdowns((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className="sticky top-24 bg-gray-100 p-6 rounded-lg">
      <Heading
        fontSize="lg"
        type="h2"
        weight="bold"
        className="mb-6 !text-gray-900"
      >
        Table of Contents
      </Heading>
      <div className="space-y-2">
        {contents.map((content, index) => (
          <div key={content?._key} className="border-b border-gray-100">
            <Button
              variant="ghost"
              as="button"
              ariaLabel={
                content?.title ?? `Table of Contents button ${index + 1}`
              }
              className={`w-full text-left flex  justify-between items-center px-0 py-3 !font-semibold ${
                selectedIndex === index ? "text-primary " : "text-gray-700 "
              }`}
              onClick={() => {
                onSelect(index);
                onSelectSub(null);
                if (content?.arrayOfTitleAndDescription?.length) {
                  toggleDropdown(content?._key || "");
                }
              }}
            >
              <span>{content?.title}</span>
              {content?.arrayOfTitleAndDescription?.length > 0 && (
                <svg
                  className={`w-4 h-4 transition-transform ${
                    openDropdowns[content?._key || ""] ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              )}
            </Button>

            {openDropdowns[content?._key || ""] &&
              content?.arrayOfTitleAndDescription && (
                <div className="pl-4 py-2 space-y-2">
                  {content.arrayOfTitleAndDescription.map((item, subIndex) => (
                    <Button
                      key={item?._key}
                      variant="ghost"
                      as="button"
                      className={`w-full text-left text-sm pl-4 py-2 !font-semibold ${
                        selectedIndex === index && selectedSubIndex === subIndex
                          ? "text-primary/80"
                          : "text-gray-600"
                      }`}
                      onClick={() => {
                        onSelect(index);
                        onSelectSub(subIndex);
                      }}
                    >
                      {item?.title}
                    </Button>
                  ))}
                </div>
              )}
          </div>
        ))}
      </div>
    </div>
  );
}

function ContentDisplay({
  content,
  selectedSubIndex,
}: {
  content?: Content;
  selectedSubIndex: number | null;
}) {
  if (!content) return null;

  // If a sub-content is selected, only show that specific content
  if (selectedSubIndex !== null && content.arrayOfTitleAndDescription) {
    const subContent = content.arrayOfTitleAndDescription[selectedSubIndex];
    return (
      <div>
        <Heading fontSize="2xl" type="h3" className="mb-6 ">
          {subContent?.title}
        </Heading>
        {subContent?.firstColumn && (
          <div className="prose max-w-none">
            <PortableText
              value={subContent.firstColumn}
              components={textComponentBlockStyling}
              onMissingComponent={false}
            />
          </div>
        )}
        {subContent?.secondColumn && (
          <div className="prose max-w-none">
            <PortableText
              value={subContent.secondColumn}
              components={textComponentBlockStyling}
              onMissingComponent={false}
            />
          </div>
        )}
      </div>
    );
  }

  // Show main content
  return (
    <div>
      <Heading fontSize="2xl" type="h3" className="mb-6 ">
        {content?.title}
      </Heading>
      {content?.firstColumn && (
        <div className="prose max-w-none">
          <PortableText
            value={content.firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
      {content?.secondColumn && (
        <div className="prose max-w-none mt-10">
          <PortableText
            value={content.secondColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
      {!selectedSubIndex &&
        content?.arrayOfTitleAndDescription?.map((item, index) => (
          <div key={item?._key} className="mt-8">
            <Heading
              fontSize="lg"
              type="h4"
              weight="semibold"
              className="mb-4 "
            >
              {item?.title}
            </Heading>
            {item?.firstColumn && (
              <div className="prose max-w-none">
                <PortableText
                  value={item.firstColumn}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            )}
            {item?.secondColumn && (
              <div className="prose max-w-none">
                <PortableText
                  value={item.secondColumn}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            )}
          </div>
        ))}
    </div>
  );
}

function CaptionAndTitleText({
  caption,
  title,
}: {
  caption?: string | null;
  title?: string | null;
}) {
  if (!caption && !title) return null;

  return (
    <div className="mb-8 text-center">
      {caption && <Text weight="semibold">{caption}</Text>}
      {title && (
        <Heading className="mt-2" type="h2" fontSize="3xl">
          {title}
        </Heading>
      )}
    </div>
  );
}

function PortfolioCategories({
  categories,
  activeTab,
  onClickFn,
}: {
  categories?: PortfoliosWithCategories[] | null;
  activeTab?: string | null;
  onClickFn?: React.Dispatch<React.SetStateAction<string | null | undefined>>;
}) {
  if (!categories || categories?.length === 0) return null;

  return (
    <Flex className="flex flex-col md:flex-row py-1 text-lg  items-center md:items-start gap-2 md:gap-0">
      {categories?.map((content, index) => (
        <Button
          variant="unstyled"
          as="button"
          ariaLabel={content?.category ?? `Category button ${index + 1}`}
          key={content?._key}
          onClick={() => onClickFn?.(content?.category)}
          className={`px-4 py-2 font-medium  ${
            activeTab === content?.category
              ? "border-b-2 border-primary text-primary"
              : "text-gray-500 hover:text-primary"
          }`}
        >
          {content?.category}
        </Button>
      ))}
    </Flex>
  );
}

export { Portfolio_E };
