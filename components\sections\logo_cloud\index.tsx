import dynamic from "next/dynamic";
import { SectionsProps, Images, LabeledRoute } from "../../../types";

import * as LogoCloudVariant from "@stackshift-ui/logo-cloud";

const Variants = {
  variant_a: LogoCloudVariant.LogoCloud_A,
  variant_b: LogoCloudVariant.LogoCloud_B,
  variant_c: LogoCloudVariant.LogoCloud_C,
  variant_d: LogoCloudVariant.LogoCloud_D,
  variant_e: dynamic(() => import("./variant_e")),
};

export interface LogoCloudProps {
  title?: string;
  images?: Images[];
  text?: string;
  button?: LabeledRoute;
}

const displayName = "LogoCloud";

export const LogoCloud: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    title: data?.variants?.title ?? undefined,
    images: data?.variants?.images ?? undefined,
    text: data?.variants?.plainText ?? undefined,
    button: data?.variants?.primaryButton ?? undefined,
  };
  return Variant ? <Variant {...props} /> : null;
};

LogoCloud.displayName = displayName;
