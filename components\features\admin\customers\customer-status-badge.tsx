import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface CustomerStatusBadgeProps {
    status: string;
}

export function CustomerStatusBadge({ status }: CustomerStatusBadgeProps) {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "active":
                return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
            case "inactive":
                return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
            case "pending":
                return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
            default:
                return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
        }
    };

    return (
        <Badge className={cn(
            "font-medium uppercase",
            getStatusColor(status)
        )}>
            {status}
        </Badge>
    );
} 