import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase";
import { Product, ProductGroupPrice } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: getProductByIdHandler,
  PUT: checkAdmin(updateProductHandler),
});

export interface ProductWithGrouPricing extends Product {
  product_group_prices?: Pick<
    ProductGroupPrice,
    "id" | "group_id" | "custom_price"
  >[];
  product_categories?: {
    category_id: {
      id: string;
      name: string | null;
      value: string | null;
    } | null;
  }[];
}

export interface GetProductResponse {
  error?: string;
  product?: Partial<ProductWithGrouPricing>;
  related_products?: Partial<ProductWithGrouPricing>[];
}

const uuid = z.string().uuid({ message: "Invalid UUID" });

async function getProductByIdHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetProductResponse>
) {
  const { slug } = req.query;

  if (!slug || typeof slug !== "string") {
    return res.status(400).json({ error: "Product ID is required" });
  }

  const productUuid = uuid.safeParse(slug);

  let query = supabaseClient
    .schema("public")
    .from("products")
    .select(
      `
          *,
          product_categories(category_id(id,name,value)),
          product_group_prices(id, group_id, custom_price),
          options,
          specifications,
          installation_instructions,
          delivery_and_shipping,
          helpful_hints,
          warranty
      `
    );

  // fallback to sku if id is not provided
  if (productUuid.error) {
    query = query.eq("sku", slug);
  } else {
    query = query.eq("id", productUuid.data);
  }

  const { data, error } = await query.single();

  if (error) {
    console.log(error);
    return res.status(500).json({ error: error.message });
  }

  if (!data) {
    return res.status(404).json({ error: "Product not found" });
  }

  // Parse comma-separated SKUs from tags
  const relatedSkus = data.tags ? `{${data.tags.join(",")}}` : "{}";

  let relatedProductsQuery = supabaseClient
    .schema("public")
    .from("products")
    .select(
      `
      *,
      product_categories(category_id(id,name,value)),
      product_group_prices(id, group_id, custom_price)
    `
    )
    .not("tags", "is", null)
    .not("draft", "eq", true)
    .not("available", "eq", false);

  if (productUuid.error) {
    relatedProductsQuery = relatedProductsQuery.neq("sku", slug);
  } else {
    relatedProductsQuery = relatedProductsQuery.neq("id", productUuid.data);
  }

  // Filter products by SKUs from tags
  const { data: relatedProducts, error: _ } = await relatedProductsQuery
    .in("sku", relatedSkus)
    .limit(5);

  return res
    .status(200)
    .json({ product: data, related_products: relatedProducts ?? [] });
}

export interface UpdateProductResponse {
  error?: string;
  product?: Product;
  message?: string;
}

// Validation schema for update product request
const updateProductSchema = z.object({
  name: z.string().min(1, "Product name is required").optional(),
  brand: z.string().optional(),
  description: z.string().optional(),
  price: z.number().min(0, "Price must be a positive number").optional(),
  sku: z.string().optional(),
  available: z.boolean().optional(),
  is_quote: z.boolean().optional(),
  variant: z.string().optional(),
  options: z.array(z.record(z.any())).optional(),
  features: z.array(z.string()).optional(),
  finish_and_material: z.string().optional(),
  image: z.string().optional().nullable(),
  additional_images: z.array(z.string()).optional().nullable(),
  category_id: z.string().optional(),
  draft: z.boolean().optional(),
  helpful_hints: z
    .array(
      z.object({
        url: z.string(),
        label: z.string(),
      })
    )
    .optional()
    .nullable(),
  installation_instructions: z.string().optional().nullable(),
  delivery_and_shipping: z
    .object({
      shipping: z.object({
        url: z.string(),
        label: z.string(),
      }),
    })
    .optional()
    .nullable(),
  warranty: z
    .object({
      url: z.string(),
      label: z.string(),
    })
    .optional()
    .nullable(),
  specifications: z.record(z.any()).optional(),
  group_prices: z
    .array(
      z.object({
        id: z.string().optional(),
        group_id: z.string(),
        custom_price: z.number(),
        hash: z.string().optional(),
      })
    )
    .optional()
    .nullable(),
  user_id: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

async function updateProductHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateProductResponse>
) {
  const { slug } = req.query;

  if (!slug || typeof slug !== "string") {
    return res.status(400).json({ error: "Product ID is required" });
  }

  try {
    // Validate request body
    const updateData = updateProductSchema.parse(req.body);

    // Create admin client for operations
    const supabaseAdmin = createSupabaseAdminClient();

    // First, check if product exists
    const { data: existingProduct, error: fetchError } = await supabaseAdmin
      .from("products")
      .select("*")
      .eq("id", slug)
      .single();

    if (fetchError) {
      return res.status(404).json({ error: "Product not found" });
    }

    // Extract category_id for product_categories update later
    const categoryId = updateData.category_id;

    // Remove category_id from updateData as it's not a direct column
    const { category_id, ...productUpdateData } = updateData;

    console.log("productUpdateData", productUpdateData);

    const now = new Date();
    const updateAt = now.toISOString();

    // Update the product
    const { data: updatedProduct, error: updateError } = await supabaseAdmin
      .from("products")
      .update({
        name: productUpdateData.name,
        brand: productUpdateData.brand,
        description: productUpdateData.description,
        price: productUpdateData.price,
        sku: productUpdateData.sku,
        available: productUpdateData.available,
        variant: productUpdateData.variant,
        options: productUpdateData.options ?? [],
        specifications: productUpdateData.specifications,
        helpful_hints: productUpdateData.helpful_hints,
        installation_instructions: productUpdateData.installation_instructions,
        delivery_and_shipping: productUpdateData.delivery_and_shipping,
        warranty: productUpdateData.warranty,
        image: productUpdateData.image,
        draft: productUpdateData.draft,
        tags: productUpdateData.tags,
        additional_images: productUpdateData.additional_images,
        is_quote: productUpdateData.is_quote,
        updated_at: updateAt,
      })
      .eq("id", slug)
      .select()
      .single();

    if (updateError) {
      return res.status(500).json({ error: updateError.message });
    }

    // Handle category updates if provided
    if (categoryId) {
      // First delete existing categories
      await supabaseAdmin
        .from("product_categories")
        .delete()
        .eq("product_id", slug);

      // Insert new category
      const { error: categoryError } = await supabaseAdmin
        .from("product_categories")
        .insert({
          product_id: slug,
          category_id: categoryId,
        });

      if (categoryError) {
        return res.status(500).json({ error: categoryError.message });
      }
    }

    // Handle group pricing updates if provided
    if (
      !productUpdateData.group_prices ||
      productUpdateData.group_prices.length === 0
    ) {
      await supabaseAdmin
        .from("product_group_prices")
        .delete()
        .eq("product_id", existingProduct.id);
    }

    if (
      productUpdateData.group_prices &&
      productUpdateData.group_prices.length > 0
    ) {
      // First delete existing group prices
      await supabaseAdmin
        .from("product_group_prices")
        .delete()
        .eq("product_id", slug);

      // Insert new group prices
      const groupPrices = productUpdateData.group_prices.map((price) => ({
        product_id: slug,
        group_id: price.group_id,
        custom_price: price.custom_price,
        hash:
          price.hash ||
          `${productUpdateData.name || existingProduct.name}-${price.group_id}`
            .replace(/\s+/g, "-")
            .toLowerCase(),
      }));

      const { error: groupPriceError } = await supabaseAdmin
        .from("product_group_prices")
        .insert(groupPrices);

      if (groupPriceError) {
        return res.status(500).json({ error: groupPriceError.message });
      }
    }

    return res.status(200).json({
      product: updatedProduct as Product,
      message: "Product updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res
        .status(400)
        .json({ error: error.errors.map((e) => e.message).join(", ") });
    }
    return res.status(500).json({ error: "Failed to update product" });
  }
}
