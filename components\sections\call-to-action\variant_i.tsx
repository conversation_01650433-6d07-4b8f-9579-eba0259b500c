import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Input } from "@stackshift-ui/input";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { CTAProps } from ".";
import { logoLink, thankYouPageLink } from "../../../helper";
import { Logo, Form as iForm } from "../../../types";
import { useRouter } from "next/router";
import { FaCheck } from "react-icons/fa";

// Helper function to clean field names by removing parenthetical suffixes
const cleanFieldName = (fieldName: string | undefined): string => {
  if (!fieldName) return "";
  return fieldName
    .replace(/\s*\(Reference 1\)\s*/g, "")
    .replace(/\s*\(Reference 2\)\s*/g, "")
    .replace(/\s*\(Reference 3\)\s*/g, "")
    .replace(/\s*\(Contact Details\)\s*/g, "")
    .replace(/\s*\(Bank Reference\)\s*/g, "")
    .trim();
};

export default function CallToAction_I({
  logo,
  title,
  plainText,
  form,
}: CTAProps) {
  return (
    <Section className="py-20 bg-background">
      <Container className="text-center" maxWidth={1280}>
        <HeadingAndText title={title} text={plainText} />
        <CTAForm form={form} />
      </Container>
    </Section>
  );
}

function HeadingAndText({ title, text }: { title?: string; text?: string }) {
  return (
    <React.Fragment>
      {title ? (
        <Heading fontSize="xl" className="mb-4 text-center">
          {title}
        </Heading>
      ) : null}
      {text ? <Text className="mb-8 text-center">{text}</Text> : null}
    </React.Fragment>
  );
}

function CTAForm({ form }: { form?: iForm }) {
  if (!form) return null;

  const [step, setStep] = useState(1);
  const totalSteps = 5;
  const [formData, setFormData] = useState<Record<string, string>>({});

  const goToNext = () => {
    if (step < totalSteps) {
      setStep(step + 1);
    }
  };

  const goToPrevious = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // This useEffect runs once on component mount to set default values for radio buttons
  useEffect(() => {
    // For any radio groups that don't have a selection, set a default selection
    if (form.fields) {
      const newFormData = { ...formData };
      let dataChanged = false;

      // Set defaults for radio buttons in business makeup section
      form.fields.slice(11, 15).forEach((field) => {
        if (
          field.name &&
          field.type === "inputRadio" &&
          !formData[field.name] &&
          field.items &&
          field.items.length > 0
        ) {
          newFormData[field.name] = field.items[0];
          dataChanged = true;
        }
      });

      // Set defaults for address type radio
      form.fields.slice(5, 6).forEach((field) => {
        if (
          field.name &&
          field.type === "inputRadio" &&
          !formData[field.name] &&
          field.items &&
          field.items.length > 0
        ) {
          newFormData[field.name] = field.items[0];
          dataChanged = true;
        }
      });

      if (dataChanged) {
        setFormData(newFormData);
      }
    }
  }, [form.fields]);

  return (
    <Form
      id={form?.id || ""}
      name="Credit-Application-Form"
      thankyouPage={thankYouPageLink(form?.thankYouPage)}
    >
      <div className="flex flex-col md:flex-row bg-transparent rounded-xl overflow-hidden shadow-lg mx-auto">
        {/* Left sidebar */}
        <div className="w-full md:w-80 bg-primary p-8 flex flex-col rounded-l-xl">
          <div className="mb-10">
            <h1 className="text-2xl font-medium text-white">
              Credit Application
            </h1>
          </div>

          <h2 className="text-xl font-medium text-white mb-8">
            Application Steps
          </h2>

          {/* Progress steps */}
          <div className="space-y-8 relative">
            <div className="absolute left-[24px] top-[40px] w-[2px] h-[calc(100%-60px)]">
              <div
                className="h-full bg-gray-200"
                style={{
                  background: `linear-gradient(to bottom, 
                    ${step >= 1 ? "white" : "rgba(255,255,255,0.3)"} 0%, 
                    ${step >= 2 ? "white" : "rgba(255,255,255,0.3)"} 25%, 
                    ${step >= 3 ? "white" : "rgba(255,255,255,0.3)"} 50%, 
                    ${step >= 4 ? "white" : "rgba(255,255,255,0.3)"} 75%, 
                    ${step >= 5 ? "white" : "rgba(255,255,255,0.3)"} 100%)`,
                }}
              />
            </div>

            {/* Step 1: Company Details */}
            <div className="flex items-center relative z-10 mb-6">
              <div
                className={`w-12 h-12 rounded-full ${
                  step >= 1 ? "bg-white" : "bg-white"
                } flex items-center justify-center ${
                  step >= 1 ? "text-primary" : "text-primary/50"
                }`}
              >
                {step > 1 ? <FaCheck className="w-4 h-4" /> : 1}
              </div>
              <span
                className={`ml-4 ${
                  step >= 1 ? "font-medium text-white" : "text-white/70"
                }`}
              >
                Company Details
              </span>
            </div>

            {/* Step 2: Business Make Up */}
            <div className="flex items-center relative z-10 mb-6">
              <div
                className={`w-12 h-12 rounded-full ${
                  step >= 2 ? "bg-white" : "bg-white"
                } flex items-center justify-center ${
                  step >= 2 ? "text-primary" : "text-primary/50"
                }`}
              >
                {step > 2 ? <FaCheck className="w-4 h-4" /> : 2}
              </div>
              <span
                className={`ml-4 ${
                  step >= 2 ? "font-medium text-white" : "text-white/70"
                }`}
              >
                Business Make Up
              </span>
            </div>

            {/* Step 3: Partners/Officers */}
            <div className="flex items-center relative z-10 mb-6">
              <div
                className={`w-12 h-12 rounded-full ${
                  step >= 3 ? "bg-white" : "bg-white"
                } flex items-center justify-center ${
                  step >= 3 ? "text-primary" : "text-primary/50"
                }`}
              >
                {step > 3 ? <FaCheck className="w-4 h-4" /> : 3}
              </div>
              <span
                className={`ml-4 ${
                  step >= 3 ? "font-medium text-white" : "text-white/70"
                }`}
              >
                Partners/Officers
              </span>
            </div>

            {/* Step 4: Bank Reference */}
            <div className="flex items-center relative z-10 mb-6">
              <div
                className={`w-12 h-12 rounded-full ${
                  step >= 4 ? "bg-white" : "bg-white"
                } flex items-center justify-center ${
                  step >= 4 ? "text-primary" : "text-primary/50"
                }`}
              >
                {step > 4 ? <FaCheck className="w-4 h-4" /> : 4}
              </div>
              <span
                className={`ml-4 ${
                  step >= 4 ? "font-medium text-white" : "text-white/70"
                }`}
              >
                Bank Reference
              </span>
            </div>

            {/* Step 5: Trade References */}
            <div className="flex items-center relative z-10 mb-6">
              <div
                className={`w-12 h-12 rounded-full ${
                  step >= 5 ? "bg-white" : "bg-white"
                } flex items-center justify-center ${
                  step >= 5 ? "text-primary" : "text-primary/50"
                }`}
              >
                {step > 5 ? <FaCheck className="w-4 h-4" /> : 5}
              </div>
              <span
                className={`ml-4 ${
                  step >= 5 ? "font-medium text-white" : "text-white/70"
                }`}
              >
                Trade References
              </span>
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 p-8 md:p-12 bg-white/5 backdrop-blur-sm rounded-r-xl">
          <div className="max-w-2xl">
            {/* Step 1: Company Details */}
            {step === 1 && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  COMPANY DETAILS
                </h3>

                <div className="mb-10">
                  <div className="flex items-start justify-start flex-col mb-6 gap-y-4">
                    {form.fields?.slice(0, 5).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `field-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={formField.type}
                          id={formField.name || `field-${index}`}
                          name={formField.name || `field-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>

                  {/* Address Type Radio */}
                  <div className="mb-6">
                    {form.fields?.slice(5, 6).map((formField, index) => (
                      <div key={index}>
                        <label className="block text-md mb-2 text-left">
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.label)}
                        </label>
                        <div className="flex flex-row gap-x-4 p-2 ">
                          {formField?.items?.map((item, i) => (
                            <label key={i} className="inline-flex items-center">
                              <input
                                type="radio"
                                className="form-radio"
                                name={formField.name || `radio-field-${index}`}
                                value={item}
                                checked={
                                  formField.name
                                    ? formData[formField.name] === item
                                    : false
                                }
                                onChange={handleInputChange}
                                required={formField.isRequired}
                              />
                              <span className="ml-2">{item}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Contact Information */}
                  <div className="mb-10">
                    <h3 className="text-sm font-semibold text-gray-700 mb-4">
                      CONTACT DETAILS
                    </h3>
                    <div className="flex flex-col gap-y-4">
                      {form.fields?.slice(6, 11).map((formField, index) => (
                        <div key={index} className="w-full">
                          <label
                            htmlFor={formField.name || `contact-${index}`}
                            className="block text-left text-md mb-2"
                          >
                            {formField?.isRequired && (
                              <span className="text-red-600">*</span>
                            )}{" "}
                            {cleanFieldName(formField.name)}
                          </label>
                          <input
                            type={
                              formField.type === "inputEmail" ? "email" : "text"
                            }
                            id={formField.name || `contact-${index}`}
                            name={formField.name || `contact-${index}`}
                            placeholder={formField.placeholder}
                            required={formField?.isRequired}
                            value={
                              formField.name
                                ? formData[formField.name] || ""
                                : ""
                            }
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Business Make Up */}
            {step === 2 && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  BUSINESS MAKE UP
                </h3>
                <div className="mb-10">
                  <p className="text-md font-medium mb-2 text-left">
                    <span className="text-red-600">*</span> Check one box in
                    each row
                  </p>

                  {form.fields?.slice(11, 15).map((formField, index) => (
                    <div key={index} className="mb-6">
                      <div className="flex flex-row items-start just gap-x-4">
                        <span className="">{index + 1}.</span>
                        <div className="flex-1">
                          {formField && (
                            <label className="block text-md mb-2 text-left">
                              {cleanFieldName(formField.label)}
                            </label>
                          )}

                          <div className="flex flex-row flex-wrap gap-x-6 p-2 ">
                            {formField?.items?.map((item, i) => (
                              <label
                                key={i}
                                className="inline-flex items-center mb-2"
                              >
                                <input
                                  type="radio"
                                  className="form-radio "
                                  name={formField.name || `business-${index}`}
                                  value={item}
                                  checked={
                                    formField.name
                                      ? formData[formField.name] === item
                                      : false
                                  }
                                  onChange={handleInputChange}
                                  required={formField.isRequired}
                                />
                                <span className="ml-2">{item}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Step 3: Partners or Corporate Officers */}
            {step === 3 && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  PARTNERS OR CORPORATE OFFICERS
                </h3>
                <div className="mb-10">
                  <div className="flex flex-col gap-y-4">
                    {form.fields?.slice(15, 19).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `officer-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={formField.type}
                          id={formField.name || `officer-${index}`}
                          name={formField.name || `officer-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Bank Reference */}
            {step === 4 && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  BANK REFERENCE
                </h3>
                <div className="mb-10">
                  <div className="flex flex-col gap-y-4">
                    {form.fields?.slice(19, 23).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `bank-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={formField.type}
                          id={formField.name || `bank-${index}`}
                          name={formField.name || `bank-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Trade References */}
            {step === 5 && (
              <div>
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  TRADE REFERENCES
                </h3>

                {/* Reference 1 */}
                <div className="mb-8">
                  <h4 className="text-md font-semibold text-left mb-4">
                    Reference 1
                  </h4>
                  <div className="flex flex-col gap-y-4">
                    {form.fields?.slice(23, 28).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `ref1-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={
                            formField.type === "inputEmail" ? "email" : "text"
                          }
                          id={formField.name || `ref1-${index}`}
                          name={formField.name || `ref1-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Reference 2 */}
                <div className="mb-8">
                  <h4 className="text-md font-semibold text-left mb-4">
                    Reference 2
                  </h4>
                  <div className="flex flex-col gap-y-4">
                    {form.fields?.slice(28, 33).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `ref2-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={
                            formField.type === "inputEmail" ? "email" : "text"
                          }
                          id={formField.name || `ref2-${index}`}
                          name={formField.name || `ref2-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Reference 3 */}
                <div className="mb-8">
                  <h4 className="text-md font-semibold text-left mb-4">
                    Reference 3
                  </h4>
                  <div className="flex flex-col gap-y-4">
                    {form.fields?.slice(33, 38).map((formField, index) => (
                      <div key={index} className="w-full">
                        <label
                          htmlFor={formField.name || `ref3-${index}`}
                          className="block text-left text-md mb-2"
                        >
                          {formField?.isRequired && (
                            <span className="text-red-600">*</span>
                          )}{" "}
                          {cleanFieldName(formField.name)}
                        </label>
                        <input
                          type={
                            formField.type === "inputEmail" ? "email" : "text"
                          }
                          id={formField.name || `ref3-${index}`}
                          name={formField.name || `ref3-${index}`}
                          placeholder={formField.placeholder}
                          required={formField?.isRequired}
                          value={
                            formField.name ? formData[formField.name] || "" : ""
                          }
                          onChange={handleInputChange}
                          className="w-full p-2 border rounded bg-white placeholder-transparent focus:outline-2 focus:ring-1  focus:outline-primary"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Add hidden inputs for all form fields to ensure they are submitted */}
            {form.fields &&
              form.fields.map((field, index) => {
                // Skip fields without a name
                if (!field.name) return null;

                // Skip fields that are visible in the current step
                if (step === 1 && index < 11) return null;
                if (step === 2 && index >= 11 && index < 15) return null;
                if (step === 3 && index >= 15 && index < 19) return null;
                if (step === 4 && index >= 19 && index < 23) return null;
                if (step === 5 && index >= 23 && index < 38) return null;

                // For radio buttons, only add if there's a selected value
                if (field.type === "inputRadio" && !formData[field.name])
                  return null;

                return (
                  <input
                    key={`hidden-${index}`}
                    type="hidden"
                    name={field.name}
                    value={formData[field.name] || ""}
                  />
                );
              })}

            {/* Navigation buttons */}
            <div className="border-t border-gray-200 pt-8 flex justify-between">
              {step > 1 && (
                <Button
                  variant="maxtonPrimary"
                  as="button"
                  type="button"
                  onClick={goToPrevious}
                  ariaLabel="Previous"
                >
                  Previous
                </Button>
              )}

              <div className="ml-auto">
                {step < totalSteps ? (
                  <Button
                    variant="maxtonPrimary"
                    as="button"
                    type="button"
                    onClick={goToNext}
                    ariaLabel="Next"
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    variant="maxtonSecondary"
                    as="button"
                    type="submit"
                    ariaLabel={form?.buttonLabel || "Submit"}
                  >
                    {form?.buttonLabel || "Submit"}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add reCAPTCHA div directly under Form (not nested in Steps) */}
      <div className="mt-4">
        <div className="webriq-recaptcha" />
      </div>
    </Form>
  );
}

function handleInputFileChange(
  e: React.ChangeEvent<HTMLInputElement>,
  setFilename: React.Dispatch<React.SetStateAction<string>>
) {
  if (e.target && e.target.files && e.target.files.length > 0) {
    setFilename(e.target.files[0].name);
  } else {
    setFilename("");
  }
}

export { CallToAction_I };
