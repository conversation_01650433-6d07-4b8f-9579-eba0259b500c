import { SvgSpinners90Ring } from "@/components/common/icons";
import AdminLayout from "@/components/features/admin/layout";
import { SettingsLayout } from "@/components/features/admin/settings/layout";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CitiesList } from "@/components/ui/cities-list";
import { toast } from "@/hooks/use-toast";
import {
  useGetPaginatedTaxRatesQuery,
  useUpdateTaxRateMutation,
} from "@/queries/tax-rate-queries";
import {
  getCountyForCity,
  getCitiesInSameCounty,
  getCitiesForCounty
} from "@/lib/utils/nevada-tax-rates";
import { NevadaTaxRate } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ColumnDef,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  useReactTable,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import { Info, Pencil, Settings } from "lucide-react";
import Head from "next/head";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const updateTaxRateSchema = z.object({
  tax_rate: z
    .number()
    .min(0, "Tax rate cannot be negative")
    .max(100, "Tax rate cannot exceed 100%"),
  city: z.string().min(1, "City is required"),
});

type UpdateTaxRateFormValues = z.infer<typeof updateTaxRateSchema>;

// Custom Tax Rates Table Skeleton
function TaxRatesTableSkeleton() {
  return (
    <div className="space-y-4">
      {/* Filter skeleton */}
      <div className="flex items-center space-x-2">
        <Skeleton className="h-10 w-[250px]" />
        <Skeleton className="h-10 w-[120px]" />
      </div>

      {/* Table skeleton */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">
                <Skeleton className="h-4 w-[60px]" />
              </TableHead>
              <TableHead className="w-[150px]">
                <Skeleton className="h-4 w-[80px]" />
              </TableHead>
              <TableHead className="w-[150px]">
                <Skeleton className="h-4 w-[100px]" />
              </TableHead>
              <TableHead className="w-[100px] text-right">
                <Skeleton className="h-4 w-[60px] ml-auto" />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className="h-4 w-[120px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[60px]" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-[80px]" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-8 w-8 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-[100px]" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-4 w-[100px]" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  );
}

// Table Actions Component
function TaxRateTableActions({
  taxRate,
  onEdit
}: {
  taxRate: NevadaTaxRate;
  onEdit: (taxRate: NevadaTaxRate) => void;
}) {
  return (
    <div className="flex justify-end">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onEdit(taxRate)}
        className="h-8 w-8 p-0"
      >
        <Pencil className="h-4 w-4" />
        <span className="sr-only">Edit tax rate</span>
      </Button>
    </div>
  );
}

// Create column definitions function that takes the onEdit handler
const createColumns = (onEdit: (taxRate: NevadaTaxRate) => void): ColumnDef<NevadaTaxRate>[] => [
  {
    accessorKey: "city",
    header: "City / County",
    cell: ({ row }) => {
      const city = row.getValue("city") as string;
      const county = city ? getCountyForCity(city) : null;

      return (
        <div className="space-y-1">
          <div className="font-medium">{city || "Unknown"}</div>
        </div>
      );
    },
  },
  {
    id: "related_cities",
    header: "Related Cities",
    cell: ({ row }) => {
      const city = row.getValue("city") as string;
      if (!city) return null;

      const relatedCities = getCitiesForCounty(city);

      if (relatedCities.length === 0) {
        return (
          <div className="text-sm text-muted-foreground">
            No other cities
          </div>
        );
      }

      return (
        <CitiesList
          cities={relatedCities}
          maxVisible={2}
          variant="outline"
        />
      );
    },
  },
  {
    accessorKey: "tax_rate",
    header: "Tax Rate",
    cell: ({ row }) => {
      const taxRate = row.getValue("tax_rate") as number;
      return <div>{taxRate ? `${taxRate.toFixed(3)}%` : "0.000%"}</div>;
    },
  },
  {
    accessorKey: "created_at",
    header: "Created At",
    cell: ({ row }) => {
      const createdAt = row.getValue("created_at") as string;
      return (
        <div className="text-muted-foreground">
          {createdAt ? new Date(createdAt).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          }) : "Unknown"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: () => <div className="text-right">Actions</div>,
    cell: ({ row }) => <TaxRateTableActions taxRate={row.original} onEdit={onEdit} />,
    meta: {
      className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
    },
  },
];

export default function SettingsTaxRatesPage() {
  const [editingTaxRate, setEditingTaxRate] = useState<NevadaTaxRate | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // Use paginated query for the table
  const { data: paginatedData, isLoading, error } = useGetPaginatedTaxRatesQuery({
    page: currentPage,
    limit: pageSize,
  });

  const updateTaxRateMutation = useUpdateTaxRateMutation();

  // Extract data from paginated response
  const taxRates = paginatedData?.taxRates || [];
  const totalItems = paginatedData?.total || 0;
  const totalPages = paginatedData?.totalPages || 0;

  const form = useForm<UpdateTaxRateFormValues>({
    resolver: zodResolver(updateTaxRateSchema),
    defaultValues: {
      tax_rate: 0,
      city: "",
    },
  });

  const handleEditTaxRate = (taxRate: NevadaTaxRate) => {
    setEditingTaxRate(taxRate);
    form.reset({
      tax_rate: taxRate.tax_rate || 0,
      city: taxRate.city || "",
    });
    setIsEditDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsEditDialogOpen(false);
    setEditingTaxRate(null);
    form.reset();
  };

  const onSubmit = async (values: UpdateTaxRateFormValues) => {
    if (!editingTaxRate?.id) return;

    try {
      await updateTaxRateMutation.mutateAsync({
        city: editingTaxRate.city || "",
        data: values,
      });

      toast({
        title: "Tax rate updated",
        description: `Tax rate for ${values.city} has been updated successfully.`,
      });

      handleCloseDialog();
    } catch (error) {
      toast({
        title: "Error updating tax rate",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  // Create table instance with server-side pagination
  const table = useReactTable({
    data: taxRates,
    columns: createColumns(handleEditTaxRate),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true, // Enable server-side pagination
    pageCount: totalPages,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: currentPage - 1,
          pageSize,
        });
        setCurrentPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setCurrentPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
      pagination: {
        pageIndex: currentPage - 1, // Convert to 0-indexed for the table
        pageSize,
      },
    },
  });

  if (isLoading) {
    return (
      <AdminLayout>
        <Head>
          <title>Tax Rates - Settings</title>
        </Head>
        <SettingsLayout
          title="Tax Rates Management"
          description="Manage Nevada tax rates by city"
        >
          <div className="space-y-6">
            <div className="flex items-center justify-end">
              <Badge variant="outline" className="px-6 py-2 text-lg">
                <Settings className="mr-2 h-4 w-4" />
                <span>
                  <Skeleton className="h-4 w-[60px] inline-block" />
                </span>
              </Badge>
            </div>
            <DataTableSkeleton />
          </div>
        </SettingsLayout>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Head>
          <title>Tax Rates - Settings</title>
        </Head>
        <SettingsLayout
          title="Tax Rates Management"
          description="Manage Nevada tax rates by city"
        >
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Nevada Tax Rates</h2>
                <p className="text-muted-foreground">
                  Manage Nevada tax rates by city
                </p>
              </div>
            </div>
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  Error loading tax rates. Please try again later.
                </p>
              </CardContent>
            </Card>
          </div>
        </SettingsLayout>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Tax Rates - Settings</title>
      </Head>
      <SettingsLayout
        title="Tax Rates Management"
        description="Manage Nevada tax rates for checkout calculations"
      >
        <div className="space-y-6">
          <div className="flex items-center justify-end">
            <Badge variant="outline" className="px-6 py-2 text-lg">
              <Settings className="mr-2 h-4 w-4" />
              <span>
                {totalItems} {totalItems === 1 ? "city" : "cities"}
              </span>
            </Badge>
          </div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              columns={createColumns(handleEditTaxRate)}
              data={taxRates}
              table={table}
              filterColumn="city"
              filterPlaceholder="Filter cities..."
            />
          )}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Edit Tax Rate</DialogTitle>
                <DialogDescription>
                  Update the tax rate for {editingTaxRate?.city}
                  {editingTaxRate?.city && getCountyForCity(editingTaxRate.city) && (
                    <span className="block mt-1 text-sm">
                      Located in {getCountyForCity(editingTaxRate.city)} County
                    </span>
                  )}
                </DialogDescription>
              </DialogHeader>

              {/* County Context Section */}
              {editingTaxRate?.city && (
                <div className="bg-muted/50 py-4 rounded-lg space-y-3">
                  <div className="space-y-2">
                    {getCitiesForCounty(editingTaxRate.city).length > 0 && (
                      <div className="space-y-1">
                        <div className="text-sm font-medium">Related Cities:</div>
                        <CitiesList
                          cities={getCitiesForCounty(editingTaxRate.city)}
                          maxVisible={4}
                          variant="secondary"
                        />
                        <div className="text-xs text-muted-foreground flex items-center gap-1 pt-1">
                          <Info className="h-3 w-3" />
                          Changes to this tax rate will only affect {editingTaxRate.city} and its related cities
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input disabled placeholder="Enter city name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tax_rate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Rate (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.001"
                            min="0"
                            max="100"
                            placeholder="0.000"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value);
                              field.onChange(isNaN(value) ? 0 : value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCloseDialog}
                      disabled={updateTaxRateMutation.isPending}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={updateTaxRateMutation.isPending}
                      className="min-w-[100px]"
                    >
                      {updateTaxRateMutation.isPending ? (
                        <SvgSpinners90Ring className="h-4 w-4" />
                      ) : (
                        "Update"
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </SettingsLayout>
    </AdminLayout>
  );
}
