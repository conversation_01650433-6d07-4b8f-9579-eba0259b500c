import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";

export default checkAdmin(
  matchRoute({
    DELETE: checkPermission("delete:groups", deleteGroupHandler),
  })
);

export interface DeleteGroupResponse {
  error?: string;
  message?: string;
}

async function deleteGroupHandler(
  req: NextApiRequest,
  res: NextApiResponse<DeleteGroupResponse>
) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Group ID is required" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const groupHasReferenceToCustomers = await supabaseAdminClient
    .from("customer_groups")
    .select("id", { count: "exact" })
    .eq("group_id", id as string);
  const totalCustomerReference =
    groupHasReferenceToCustomers.data?.length ||
    groupHasReferenceToCustomers.count ||
    0;

  console.log("groupHasReferenceToCustomers: ", totalCustomerReference);

  if (totalCustomerReference > 0) {
    return res.status(400).json({
      error: `Group is referenced by ${totalCustomerReference} customer(s), cannot delete.`,
    });
  }

  const groupHasReferenceToProducts = await supabaseAdminClient
    .from("product_group_prices")
    .select("id")
    .eq("group_id", id as string);

  const totalProductReference =
    groupHasReferenceToProducts.data?.length ||
    groupHasReferenceToProducts.count ||
    0;

  console.log("groupHasReferenceToProducts: ", totalProductReference);

  if (totalProductReference > 0) {
    return res.status(400).json({
      error: "Group is referenced by one or more products, cannot delete.",
    });
  }

  const { data: _, error } = await supabaseAdminClient
    .schema("public")
    .from("groups")
    .delete()
    .eq("id", id as string);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.status(200).json({ message: "Group deleted successfully." });
}
