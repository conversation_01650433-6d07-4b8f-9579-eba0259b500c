import { <PERSON><PERSON>, DialogContent, <PERSON>alogDes<PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog-shadcn";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { useCreateProductCategoryMutation, useGetAllCategoriesQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Loader2, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";

interface AddProductCategoryButtonProps {
    productId: string;
    product: any;
}

export function AddProductCategoryButton({ productId, product }: AddProductCategoryButtonProps) {
    const [open, setOpen] = useState(false);
    const token = useAuthStore((state) => state.token);
    const createProductCategory = useCreateProductCategoryMutation(productId, token);
    const { toast } = useToast();
    const { data: categoriesData } = useGetAllCategoriesQuery(1, 100, token);

    const form = useForm({
        defaultValues: {
            category_ids: [] as string[],
        },
    });

    // Get the list of already selected category IDs from the product
    const selectedCategoryIds = product.product_categories?.map((cat: any) => cat.category_data.id) || [];

    const onSubmit = async (data: { category_ids: string[] }) => {
        const categoryIds = data.category_ids;

        try {
            await createProductCategory.mutateAsync(categoryIds);

            toast({
                title: "Success",
                description: "Categories added to product successfully",
            });
            setOpen(false);
            form.reset();
        } catch (error) {
            toast({
                title: "Error",
                description: "Failed to add categories to product",
                variant: "destructive",
            });
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="sm">Add Categories</Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Add Categories to Product</DialogTitle>
                    <DialogDescription>
                        Select one or more categories to add to this product.
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="category_ids"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Categories</FormLabel>
                                    <Select
                                        onValueChange={(value) => {
                                            const currentValue = field.value || [];
                                            if (currentValue.includes(value)) {
                                                field.onChange(currentValue.filter((id) => id !== value));
                                            } else {
                                                field.onChange([...currentValue, value]);
                                            }
                                        }}
                                        value={field.value?.[0] || ""}
                                    >
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select categories" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {categoriesData?.categories
                                                ?.filter(category => !selectedCategoryIds.includes(category.id))
                                                .map((category) => (
                                                    <SelectItem
                                                        key={category.id}
                                                        value={category.id}
                                                        className="flex items-center gap-2 py-2"
                                                    >
                                                        <input
                                                            type="checkbox"
                                                            checked={field.value?.includes(category.id)}
                                                            onChange={() => {
                                                                const currentValue = field.value || [];
                                                                if (currentValue.includes(category.id)) {
                                                                    field.onChange(currentValue.filter((id) => id !== category.id));
                                                                } else {
                                                                    field.onChange([...currentValue, category.id]);
                                                                }
                                                            }}
                                                            className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                                                        />
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                    {field.value?.length > 0 && (
                                        <div className="mt-2 flex flex-wrap gap-2">
                                            {field.value.map((id) => {
                                                const category = categoriesData?.categories?.find(c => c.id === id);
                                                return (
                                                    <div key={id} className="flex items-center gap-1 bg-accent px-2 py-1 rounded-md">
                                                        <span className="text-sm">{category?.name}</span>
                                                        <Button
                                                            type="button"
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-4 w-4 p-0"
                                                            onClick={() => {
                                                                field.onChange(field.value.filter((v) => v !== id));
                                                            }}
                                                        >
                                                            <X className="h-3 w-3 text-red-500" />
                                                        </Button>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button
                                type="submit"
                                disabled={createProductCategory.isPending
                                    || !form.watch("category_ids")?.length
                                }
                                variant="default"
                            >
                                {createProductCategory.isPending ? "Adding..." : "Add Categories"}
                                {createProductCategory.isPending && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 