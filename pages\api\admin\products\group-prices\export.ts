import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";

export default matchRoute({
  GET: checkAdmin(exportProductGroupPricesHandler),
});

export interface ProductGroupPriceExportData {
  product_code: string;
  product_name: string;
  group_prices: Record<string, number | null>;
}

export interface ExportProductGroupPricesResponse {
  error?: string;
  data?: ProductGroupPriceExportData[];
  groups?: string[];
}

async function exportProductGroupPricesHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<ExportProductGroupPricesResponse>
) {
  try {
    const supabaseAdminClient = createSupabaseAdminClient();

    // First, get all groups to determine the columns
    const { data: groups, error: groupsError } = await supabaseAdminClient
      .from("groups")
      .select("id, name")
      .order("name");

    if (groupsError) {
      return res.status(500).json({ error: groupsError.message });
    }

    if (!groups || groups.length === 0) {
      return res.status(200).json({ 
        data: [], 
        groups: [],
        error: "No groups found" 
      });
    }

    // Get all products with their SKUs and names
    const { data: products, error: productsError } = await supabaseAdminClient
      .from("products")
      .select("id, sku, name")
      .order("sku");

    if (productsError) {
      return res.status(500).json({ error: productsError.message });
    }

    if (!products || products.length === 0) {
      return res.status(200).json({ 
        data: [], 
        groups: groups.map(g => g.name),
        error: "No products found" 
      });
    }

    // Get all product group prices
    const { data: productGroupPrices, error: pricesError } = await supabaseAdminClient
      .from("product_group_prices")
      .select(`
        product_id,
        group_id,
        custom_price,
        groups!inner(name),
        products!inner(id, sku, name)
      `);

    if (pricesError) {
      return res.status(500).json({ error: pricesError.message });
    }

    // Create a map for quick lookup of prices
    const priceMap = new Map<string, number>();
    if (productGroupPrices) {
      productGroupPrices.forEach((price: any) => {
        const key = `${price.product_id}-${price.group_id}`;
        priceMap.set(key, price.custom_price);
      });
    }

    // Create a map for group ID to name lookup
    const groupIdToName = new Map(groups.map(g => [g.id, g.name]));

    // Build the export data
    const exportData: ProductGroupPriceExportData[] = products.map(product => {
      const product_code = product.sku || product.id;
      const group_prices: Record<string, number | null> = {};

      // Initialize all groups with null prices
      groups.forEach(group => {
        group_prices[group.name] = null;
      });

      // Fill in actual prices where they exist
      groups.forEach(group => {
        const key = `${product.id}-${group.id}`;
        const price = priceMap.get(key);
        if (price !== undefined) {
          group_prices[group.name] = price;
        }
      });

      return {
        product_code,
        product_name: product.name,
        group_prices
      };
    });

    // Return all products without filtering
    return res.status(200).json({
      data: exportData,
      groups: groups.map(g => g.name)
    });

  } catch (error) {
    console.error("Export error:", error);
    return res.status(500).json({ 
      error: "Internal server error while exporting product group prices" 
    });
  }
}