import dynamic from "next/dynamic";
import { SectionsProps, ArrayOfTitleAndText } from "../../../types";
import * as HowItWorksVariant from "@stackshift-ui/how-it-works";

const Variants = {
  variant_a: HowItWorksVariant.HowItWorks_A,
  variant_b: HowItWorksVariant.HowItWorks_B,
  variant_c: HowItWorksVariant.HowItWorks_C,
  variant_d: HowItWorksVariant.HowItWorks_D,
  variant_e: HowItWorksVariant.HowItWorks_E,
  variant_f: dynamic(() => import("./variant_f")),
};

export interface HowItWorksProps {
  subtitle?: string;
  title?: string;
  text?: string;
  video?: string;
  steps?: ArrayOfTitleAndText[];
}

const displayName = "HowItWorks";

export const HowItWorks: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    subtitle: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    text: data?.variants?.plainText ?? undefined,
    video: data?.variants?.youtubeLink ?? undefined,
    steps: data?.variants?.arrayOfTitleAndText ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

HowItWorks.displayName = displayName;
