import { NextApiRequest, NextApiResponse } from "next";
import { NextApiFunction } from ".";

interface MatchRouteOptions {
  GET?: NextApiFunction;
  POST?: NextApiFunction;
  PUT?: NextApiFunction;
  PATCH?: NextApiFunction;
  DELETE?: NextApiFunction;
  OPTIONS?: NextApiFunction;
  HEAD?: NextApiFunction;
}

/**
 * #### Match incoming request to a specific handler
 * ##### Example:
 * ```js
 * - matchRoute({ GET: handler });
 * # With middleware to some handlers
 * - matchRoute({ GET: checkAuth(handler) });
 * - matchRoute({ GET: handler, POST: checkAuth(handler) });
 * With middleware:
 * # The middleware will be applied to all handlers
 * - checkAuth(matchRoute({ GET: handler }));
 * - checkAdmin(matchRoute({ GET: handler }));
 * ```
 */
export function matchRoute(options: Partial<MatchRouteOptions>) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const { method } = req;
    const handler = options[method as keyof MatchRouteOptions];

    if (handler) {
      await handler(req, res);
    } else {
      res.status(405).json({ error: "Method not allowed" });
    }
  };
}
