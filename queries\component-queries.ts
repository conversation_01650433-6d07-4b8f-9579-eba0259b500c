import { FooterProps } from "@/components/sections/footer";
import { NavigationProps } from "@/components/sections/navigation";
import { client } from "@/lib/sanity.client";
import { variantEFooterQuery, variantFNavigationQuery } from "@/pages/api/query";
import { SectionsProps } from "@/types";
import { useQuery } from "@tanstack/react-query";

interface GlobalNavigationData extends SectionsProps {
    variant?: string;
}

interface GlobalFooterData extends SectionsProps {
    variant?: string;
}

export function useNavigationQuery() {
    const navigationData = useQuery<GlobalNavigationData>({
        queryKey: ["navigation"],
        queryFn: async () => {
            const navigation = await client.fetch(variantFNavigationQuery);

            const data = await navigation;

            return { data };
        },
    });

    return navigationData;
}

export function useFooterQuery() {
    const footerData = useQuery<GlobalFooterData>({
        queryKey: ["footer"],
        queryFn: async () => {
            const footer = await client.fetch(variantEFooterQuery);

            const data = await footer;

            return { data };
        }
    });

    return footerData;
}
