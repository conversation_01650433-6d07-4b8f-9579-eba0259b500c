import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { groq } from "next-sanity";
import { PreviewSuspense } from "next-sanity/preview";
import { sanityClient, getClient } from "lib/sanity.client";
import {
  postsByArchive,
  archiveQuery,
  globalSEOQuery,
  allCategories,
  allArchives,
} from "pages/api/query";
import { usePreviewSubscription } from "lib/sanity";
import ArchiveSection from "components/archive";
import { PreviewBanner } from "components/PreviewBanner";
import { PreviewNoContent } from "components/PreviewNoContent";
import { filterDataToSingleItem } from "components/list";
import PageNotFound from "pages/404";
import InlineEditorContextProvider from "context/InlineEditorContext";
import { GetStaticPaths, GetStaticProps } from "next";
import {
  BlogsData,
  DefaultSeoData,
  SanityImage,
  Sections,
  Seo,
  SanitySlug,
  PostCategory,
  Categories,
  ArchiveYearAndMonth,
} from "types";

interface ArchiveCombination {
  year: {
    _id: string;
    title: string;
    slug: string;
  };
  month: {
    _id: string;
    title: string;
    slug: string;
  };
}

interface PageBySlugProps {
  data: ArchivePageData | null;
  preview: boolean;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

interface DocumentWithPreviewProps {
  data: ArchivePageData | null;
  year: string | string[] | undefined;
  month: string | string[] | undefined;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

export interface ArchivePageData {
  archive: {
    year: { title: string; _id: string; seo?: any };
    month: { title: string; _id: string; seo?: any };
    footer?: Sections | null;
    navigation?: Sections | null;
    slug?: SanitySlug | null;
    name?: string;
    seo?: Seo | null;
    bio?: string | null;
    image?: SanityImage | null;
  } | null;
  posts: BlogsData[] | null;
  categories: Categories[] | null;
  allArchives?: {
    years: ArchiveYearAndMonth[] | null;
    months: ArchiveYearAndMonth[] | null;
    validCombinations: ArchiveCombination[] | null;
  } | null;
}

export function ArchivePage({
  data,
  preview,
  token,
  source,
  defaultSeo,
}: PageBySlugProps) {
  const router = useRouter();
  const { year, month } = router.query;
  const showInlineEditor = source === "studio";

  if (preview) {
    return (
      <>
        <PreviewBanner />
        <PreviewSuspense fallback="Loading...">
          <InlineEditorContextProvider showInlineEditor={showInlineEditor}>
            <DocumentWithPreview
              {...{ data, token, year, month, source, defaultSeo }}
            />
          </InlineEditorContextProvider>
        </PreviewSuspense>
      </>
    );
  }

  return <Document {...{ data, defaultSeo }} />;
}

function Document({
  data,
  defaultSeo,
}: {
  data: ArchivePageData | null;
  defaultSeo: DefaultSeoData;
}) {
  const publishedData = data?.archive;

  if (!publishedData) {
    return null;
  }

  const title = `${publishedData.month.title} ${publishedData.year.title}`;
  const seoTitle =
    publishedData.month.seo?.seoTitle || publishedData.year.seo?.seoTitle;

  return (
    <>
      <Head>
        <title>
          {seoTitle ?? title ?? "WebriQ Studio"} - Maxton Manufacturing Company
        </title>
      </Head>

      {data?.archive && (
        <ArchiveSection
          posts={data?.posts}
          archive={data.archive}
          categories={data?.categories}
          allArchives={{
            years: data?.allArchives?.years || [],
            months: data?.allArchives?.months || [],
            validCombinations: data?.allArchives?.validCombinations || [],
          }}
        />
      )}
    </>
  );
}

function DocumentWithPreview({
  data,
  year,
  month,
  token,
  defaultSeo,
}: DocumentWithPreviewProps) {
  const archiveQueryString = archiveQuery;
  const { data: previewData } = usePreviewSubscription(archiveQueryString, {
    initialData: data?.archive,
    enabled: Boolean(token) && Boolean(year) && Boolean(month),
    params: {
      year: Array.isArray(year) ? year[0] : year,
      month: Array.isArray(month) ? month[0] : month,
    },
  } as any); // Type assertion needed due to complex types

  if (!previewData) {
    return null;
  }

  const title = `${previewData.month.title} ${previewData.year.title}`;
  const seoTitle =
    previewData.month.seo?.seoTitle || previewData.year.seo?.seoTitle;

  return (
    <>
      <Head>
        <title>Archive | {seoTitle ?? title ?? "WebriQ Studio"}</title>
      </Head>

      {data?.archive && (
        <ArchiveSection
          posts={data?.posts}
          archive={previewData}
          categories={data?.categories}
          allArchives={{
            years: data?.allArchives?.years || [],
            months: data?.allArchives?.months || [],
            validCombinations: data?.allArchives?.validCombinations || [],
          }}
        />
      )}
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData = {},
}: any) => {
  const client =
    preview && previewData?.token
      ? getClient(false).withConfig({ token: previewData.token })
      : getClient(preview);

  const { year, month } = params;

  // First get the archive documents to get their IDs
  const archiveData = await client.fetch(
    groq`{
      "year": *[_type == "archiveYear" && slug.current == $year][0],
      "month": *[_type == "archiveMonth" && slug.current == $month][0]
    }`,
    { year, month }
  );

  if (!archiveData.year || !archiveData.month) {
    return { notFound: true };
  }

  const [archive, posts, globalSEO, categories, archives] = await Promise.all([
    client.fetch(archiveQuery, { year, month }),
    client.fetch(postsByArchive, {
      yearId: archiveData.year._id,
      monthId: archiveData.month._id,
    }),
    client.fetch(globalSEOQuery),
    client.fetch(allCategories),
    client.fetch(allArchives),
  ]);

  const singleArchiveData = filterDataToSingleItem(archive, preview);

  if (!singleArchiveData) {
    return {
      props: {
        preview,
        token: (preview && previewData.token) || "",
        source: (preview && previewData?.source) || "",
        data: null,
        defaultSeo: globalSEO || null,
      },
    };
  }

  return {
    props: {
      preview,
      token: (preview && previewData.token) || "",
      source: (preview && previewData?.source) || "",
      data: {
        archive: singleArchiveData || null,
        posts: posts || null,
        categories: categories || null,
        allArchives: {
          years: archives.years || null,
          months: archives.months || null,
          validCombinations: archives.validCombinations || null,
        },
      },
      defaultSeo: globalSEO || null,
    },
    revalidate: process.env.SANITY_REVALIDATE_SECRET ? undefined : 60,
  };
};

export const getStaticPaths: GetStaticPaths = async () => {
  const archivePaths = await sanityClient.fetch(
    groq`*[_type == "post" && defined(archiveYear) && defined(archiveMonth)]{
      "years": archiveYear[]->slug.current,
      "months": archiveMonth[]->slug.current
    }`
  );

  const paths = archivePaths.flatMap(
    (post: { years: string[]; months: string[] }) =>
      post.years.flatMap((year) =>
        post.months.map((month) => ({
          params: { year, month },
        }))
      )
  );

  const uniquePaths = Array.from(
    new Set(paths.map((p) => JSON.stringify(p)))
  ).map((p) => JSON.parse(p)) as { params: { year: string; month: string } }[];

  return {
    paths: uniquePaths,
    fallback: "blocking",
  };
};

export default React.memo(ArchivePage);
