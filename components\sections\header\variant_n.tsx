import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { HeaderProps } from ".";
import ImageMaps from "./ImageMaps";
import { useRouter } from "next/router";

const noteBackgroundColors = {
  green: "bg-[#ACFFBC]",
  yellow: "bg-[#FFFDAD]",
  blue: "bg-[#CAE7FF]",
};

export default function Header_K({
  mainImage,
  title,
  description,
  repairParts,
}: HeaderProps) {
  // Helper function to get background color class
  const getBackgroundColor = (color?: string) => {
    if (!color) return "";
    return (
      noteBackgroundColors[color as keyof typeof noteBackgroundColors] || ""
    );
  };

  // Function to process description text and add background colors
  const processDescription = (text: string) => {
    const colorWords = ["yellow", "green", "blue"];
    let parts = text.split(/(yellow|green|blue)/i);

    return parts.map((part, index) => {
      const lowerPart = part.toLowerCase();
      if (colorWords.includes(lowerPart)) {
        return (
          <span
            key={index}
            className={`${
              noteBackgroundColors[
                lowerPart as keyof typeof noteBackgroundColors
              ]
            } px-1 rounded`}
          >
            {part}
          </span>
        );
      }
      return part;
    });
  };

  const router = useRouter();
  const currentPath = router.asPath;

  return (
    <Section className="!px-0 lg:pt-32 pt-20 pb-20 bg-white">
      <Container maxWidth={1536}>
        {/* Title Section */}
        <Flex
          direction="col"
          align="center"
          className="mb-14 border-b border-primary/50 pb-14"
        >
          <Heading weight="bold" fontSize="3xl" className="mb-2 text-center">
            {title}
          </Heading>
          <Text className="text-sm text-gray-900 max-w-2xl text-center !font-bold">
            {description ? processDescription(description) : ""}
          </Text>
        </Flex>

        {/* Main Content Section - Modified for better image display */}
        <div className="w-full flex justify-center">
          <div className="sm:max-w-[90%] w-auto">
            {mainImage && (
              <ImageMaps
                image={mainImage}
                currentPath={currentPath}
                title={title || ""}
              />
            )}
          </div>
        </div>
      </Container>
    </Section>
  );
}

export { Header_K };
