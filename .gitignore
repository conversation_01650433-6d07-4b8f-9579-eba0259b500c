# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
next-env.d.ts

# production
/build
/studio/dist
.yalc
yalc.lock

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.*

# vercel
.vercel

# vscode
.vscode

# typescript
*.tsbuildinfo

# sitemap
public/sitemap.xml

# chromatic
chromatic.log
build-storybook.log
storybook-static/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
playwright/.auth/

# Yarn 3
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

.env
plans/
forge.yaml