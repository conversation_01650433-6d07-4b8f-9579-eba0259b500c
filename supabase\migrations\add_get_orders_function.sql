create or replace function get_orders (
  search TEXT,
  limit_rows INT default 10,
  page_num INT default 1,
  sort_order TEXT default 'desc',
  order_by_col TEXT default 'created_at',
  status_filter TEXT default null
) RETURNS SETOF uuid as $$
DECLARE
  offset_rows INT := (page_num - 1) * limit_rows;
  order_clause TEXT;
BEGIN
  -- Validate sort_order
  IF lower(sort_order) NOT IN ('asc', 'desc') THEN
    sort_order := 'asc';
  END IF;

  -- Determine ORDER BY clause
  IF order_by_col = 'quantity' THEN
    -- order by sum of quantity from order_items
    order_clause := format('quantity_sum %s NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'company_name' THEN
    order_clause := format('c.company_name %s NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'maxton_account' THEN
    order_clause := format('b.maxton_account %s NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'email' THEN
    order_clause := format('u.email %s NULLS LAST', upper(sort_order));
  ELSIF order_by_col IN ('id', 'created_at', 'updated_at', 'total_amount', 'tax_exempt', 'tracking_link') THEN
    order_clause := format('o.%I %s NULLS LAST', order_by_col, upper(sort_order));
  ELSE
    -- Default order by created_at
    order_clause := format('o.created_at %s NULLS LAST', upper(sort_order));
  END IF;

  RETURN QUERY EXECUTE format($f$
    SELECT o.id
    FROM orders o
    LEFT JOIN customers c ON o.customer_id = c.id
    LEFT JOIN users u ON u.id = c.user_id
    LEFT JOIN business_details b ON b.user_id = u.id
    LEFT JOIN (
      SELECT order_id, SUM(quantity) AS quantity_sum
      FROM order_items
      GROUP BY order_id
    ) oi_sum ON oi_sum.order_id = o.id
    WHERE
      (
        $1 IS NULL OR $1 = ''
        OR to_tsvector('english', b.maxton_account) @@ plainto_tsquery('english', $1)
        OR to_tsvector('english', u.email) @@ plainto_tsquery('english', $1)
        OR (length($1) = 36 AND o.id::text = $1)
        OR (length($1) < 36 AND o.id::text ILIKE $1 || '%%')
        OR to_tsvector('english', c.company_name) @@ plainto_tsquery('english', $1)
      )
      AND (
        $2 IS NULL OR EXISTS (
          SELECT 1 FROM order_statuses os2
          WHERE os2.order_id = o.id AND os2.status::text = $2
        )
      )
    ORDER BY %s
    LIMIT $3 OFFSET $4;
  $f$, order_clause)
  USING search, status_filter, limit_rows, offset_rows;

END;
$$ LANGUAGE plpgsql STABLE;

create or replace function get_order_ids_with_count (
  search TEXT,
  limit_rows INT default 10,
  page_num INT default 1,
  sort_order TEXT default 'desc',
  order_by_col TEXT default 'created_at',
  status_filter TEXT default null
) RETURNS table (total_count bigint, ids uuid[]) as $$
DECLARE
  offset_rows INT := (page_num - 1) * limit_rows;
  order_clause TEXT;
BEGIN
  -- Validate sort_order
  IF lower(sort_order) NOT IN ('asc', 'desc') THEN
    sort_order := 'asc';
  END IF;

  -- Determine ORDER BY clause and make sure referenced columns are selected in filtered_orders
  IF order_by_col = 'quantity' THEN
    order_clause := format('quantity_sum %s NULLS LAST, created_at DESC NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'company_name' THEN
    order_clause := format('company_name %s NULLS LAST, created_at DESC NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'maxton_account' THEN
    order_clause := format('maxton_account %s NULLS LAST, created_at DESC NULLS LAST', upper(sort_order));
  ELSIF order_by_col = 'email' THEN
    order_clause := format('email %s NULLS LAST, created_at DESC NULLS LAST', upper(sort_order));
  ELSIF order_by_col IN ('id', 'created_at', 'updated_at', 'total_amount', 'tax_exempt', 'tracking_link') THEN
    order_clause := format('%I %s NULLS LAST', order_by_col, upper(sort_order));
  ELSE
    order_clause := format('created_at %s NULLS LAST', upper(sort_order));
  END IF;

  RETURN QUERY EXECUTE format($f$
    WITH filtered_orders AS (
      SELECT
        o.id,
        o.created_at,
        oi_sum.quantity_sum,
        c.company_name,
        b.maxton_account,
        u.email,
        o.updated_at,
        o.total_amount,
        o.tax_exempt,
        o.tracking_link
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN users u ON u.id = c.user_id
      LEFT JOIN business_details b ON b.user_id = u.id
      LEFT JOIN (
        SELECT order_id, SUM(quantity) AS quantity_sum
        FROM order_items
        GROUP BY order_id
      ) oi_sum ON oi_sum.order_id = o.id
      WHERE
        (
          $1 IS NULL OR $1 = ''
          OR to_tsvector('english', b.maxton_account) @@ plainto_tsquery('english', $1)
          OR to_tsvector('english', u.email) @@ plainto_tsquery('english', $1)
          OR (length($1) = 36 AND o.id::text = $1)
          OR (length($1) < 36 AND o.id::text ILIKE $1 || '%%')
          OR to_tsvector('english', c.company_name) @@ plainto_tsquery('english', $1)
        )
        AND (
          $2 IS NULL OR EXISTS (
            SELECT 1 FROM latest_order_statuses os2
            WHERE os2.order_id = o.id AND os2.status::text = $2
          )
        )
    )
    SELECT
      (SELECT COUNT(*) FROM filtered_orders) AS total_count,
      ARRAY(
        SELECT id
        FROM filtered_orders
        ORDER BY %s
        LIMIT $3 OFFSET $4
      ) AS ids;
  $f$, order_clause)
  USING search, status_filter, limit_rows, offset_rows;
END;
$$ LANGUAGE plpgsql STABLE;
