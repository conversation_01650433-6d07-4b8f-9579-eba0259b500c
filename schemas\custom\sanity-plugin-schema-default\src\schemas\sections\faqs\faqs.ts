import { contactSchema, rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { faqsVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";


import variantDImage from "./images/variant_d.png";
import variantEImage from "./images/variant_e.png";

import initialValue from "./initialValue"
import { faqsSchema } from "./schema";




export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows
  
  {
    title: "Variant D ",
    description: "A new variant for header component",
    value: "variant_d", 
    image: variantDImage.src, 
  },
  {
    title: "Variant E",
    description: "A new variant for header component",
    value: "variant_e",
    image: variantEImage.src,
  },


];

export default rootSchema(
  "faqs",
  "FAQs",
  MdVerticalAlignTop,
  variantsList,
  faqsSchema,
  initialValue

);


