import { matchRoute } from "@/middlewares/match-route";
import { signinFormSchema } from "@/pages/log-in";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase/index";
import { UserRole } from "@/supabase/types";
import { User } from "@supabase/supabase-js";
import { serialize } from "cookie";
import { NextApiRequest, NextApiResponse } from "next";

export default matchRoute({
  POST: handler,
});

export interface SignInResponse {
  access_token?: string;
  access_token_expires_in?: number;
  access_token_expires_at?: number;
  user?: User;
  role?: UserRole;
  permissions?: string[];
  error?: string;
}

async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SignInResponse>
) {
  const data = signinFormSchema.safeParse(req.body);

  if (data.error) {
    console.log(data.error);
    return res.status(400).json({
      error: data.error.issues[0].message,
    });
  }

  const { data: signinData } = data;

  // TODO: captcha token
  const signin = await supabaseClient.auth.signInWithPassword(signinData);

  if (signin.error && signin.error.code == "email_not_confirmed") {
    const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;
    const { error: _ } = await supabaseClient.auth.resend({
      type: "signup",
      email: signinData.email,
      options: {
        emailRedirectTo: `${SITE_URL}/auth/callback`,
      },
    });

    return res.status(400).json({
      error:
        "Email not confirmed, please check your email for a confirmation link.",
    });
  }

  if (signin.error) {
    return res.status(400).json({
      error: "Invalid credentials",
    });
  }

  const { session, user } = signin.data;

  const supabaseAdminClient = createSupabaseAdminClient();
  const isApproved = await supabaseAdminClient
    .schema("public")
    .from("users")
    .select("status, role")
    .eq("id", user.id)
    .single();

  if (isApproved.error) {
    return res.status(400).json({
      error: "Invalid credentials",
    });
  }

  const { status, role } = isApproved.data;

  if (status !== "approved") {
    return res.status(400).json({
      error: "User status is pending, please contact an admin.",
    });
  }

  const newSessionStr = JSON.stringify({
    ...session,
    access_token: undefined,
    user: undefined,
  });

  const authCookie = serialize("maxton-auth-session", newSessionStr, {
    httpOnly: true,
    secure: process.env.NODE_ENV !== "development",
    sameSite: "strict",
    maxAge: 60 * 60 * 24 * 365,
    path: "/",
  });

  res.setHeader("Set-Cookie", authCookie);

  const permissions = await supabaseAdminClient
    .schema("public")
    .from("permissions")
    .select("allowed_actions")
    .eq("role", role === "manager" ? "customer" : role) // Managers have customer permissions
    .single();

  return res.status(200).json({
    user,
    role,
    permissions: permissions.data?.allowed_actions || [],
    access_token: session?.access_token,
    access_token_expires_in: session?.expires_in,
    access_token_expires_at: session?.expires_at,
  });
}
