import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import {
  ProductDeliveryAndShipping,
  ProductHelpfulHint,
  ProductInstallation,
  ProductSpecification,
  ProductWarranty,
} from "@/supabase/types";
import { CheckCircle2, ExternalLink, FileText } from "lucide-react";
import Link from "next/link";

interface ProductDetailsProps {
  specifications?: ProductSpecification;
  installation?: ProductInstallation;
  helpfulHints?: ProductHelpfulHint[];
  deliveryAndShipping?: ProductDeliveryAndShipping;
  warranty?: ProductWarranty;
}

export function ProductDetails({
  specifications,
  installation,
  helpfulHints,
  deliveryAndShipping,
  warranty,
}: ProductDetailsProps) {
  return (
    <div className="space-y-6 mt-6 pt-10">
      {/* Specifications */}
      <Accordion
        type="single"
        collapsible
        className="w-full"
        defaultValue="specifications"
      >
        <AccordionItem
          value="specifications"
          className={cn(
            "border-none",
            (specifications?.specification &&
              Object.keys(specifications.specification).length > 0) ||
              specifications?.finish_and_material ||
              (specifications?.features && specifications.features.length > 0)
              ? "border-b"
              : "hidden"
          )}
        >
          <AccordionTrigger className="text-lg font-semibold py-3 hover:bg-primary hover:text-white px-3 transition-all data-[state=open]:bg-primary data-[state=open]:text-white">
            Specifications
          </AccordionTrigger>
          <AccordionContent className="px-1">
            {specifications?.specification &&
            Object.keys(specifications.specification).length > 0 ? (
              <div className="space-y-4 mt-2">
                <table className="w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm">
                  <tbody>
                    {Object.entries(specifications.specification).map(
                      ([key, value], index) => (
                        <tr
                          key={key}
                          className={`
                            border-b 
                            transition-colors 
                            hover:bg-neutral-50
                            ${index % 2 === 0 ? "bg-neutral-50/50" : "bg-white"}
                            duration-150 ease-in-out
                          `}
                        >
                          <td className="py-3 px-4 font-medium text-left text-base">
                            {key}
                          </td>
                          <td className="py-3 px-4 text-right text-base">
                            {value}
                          </td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>
            ) : null}

            {specifications?.finish_and_material && (
              <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
                <h4 className="font-semibold mb-2 text-base">
                  Finish & Material
                </h4>
                <p className="text-base">
                  {specifications.finish_and_material}
                </p>
              </div>
            )}

            {specifications?.features && specifications.features.length > 0 && (
              <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
                <h4 className="font-semibold mb-2 text-base">Features</h4>
                <ul className="space-y-2">
                  {specifications.features.map((feature, index) => (
                    <li
                      key={index}
                      className="text-base flex items-start gap-2"
                    >
                      <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Installation Instructions */}
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="installation"
          className={cn(
            "border-none",
            installation?.label ? "border-b" : "hidden"
          )}
        >
          <AccordionTrigger className="text-lg font-semibold py-3 hover:bg-primary hover:text-white px-3 transition-all data-[state=open]:bg-primary data-[state=open]:text-white">
            Installation Instructions
          </AccordionTrigger>
          <AccordionContent className="px-1">
            {installation && installation.label ? (
              <div className="bg-white p-4 rounded-lg shadow-sm mt-2">
                <p className="text-base">{installation.label}</p>
                {installation.fileUrl && (
                  <Link
                    href={installation.fileUrl}
                    target="_blank"
                    className="flex items-center gap-2 text-blue-600 hover:text-blue-800 mt-3 hover:underline transition-colors"
                  >
                    <FileText className="h-5 w-5" />
                    <span className="text-base font-medium">
                      Download Installation Guide
                    </span>
                  </Link>
                )}
              </div>
            ) : null}
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Helpful Hints */}
      {helpfulHints && helpfulHints.length > 0 && (
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem
            value="helpfulHints"
            className={cn(
              "border-none",
              helpfulHints.length > 0 ? "border-b" : "hidden"
            )}
          >
            <AccordionTrigger className="text-lg font-semibold py-3 hover:bg-primary hover:text-white px-3 transition-all data-[state=open]:bg-primary data-[state=open]:text-white">
              Helpful Hints
            </AccordionTrigger>
            <AccordionContent className="px-1">
              {helpfulHints.length > 0 ? (
                <div className="bg-white p-4 rounded-lg shadow-sm mt-2">
                  <ul className="space-y-3">
                    {helpfulHints.map((hint, index) => (
                      <li key={index}>
                        <Link
                          href={hint.url}
                          target="_blank"
                          className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                        >
                          <ExternalLink className="h-5 w-5 flex-shrink-0" />
                          <span className="text-base">{hint.label}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="bg-white p-4 rounded-lg shadow-sm mt-2">
                  <p className="text-base text-neutral-500 text-center italic">
                    N/A
                  </p>
                </div>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}

      {/* Delivery and Shipping */}
      {deliveryAndShipping && (
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem
            value="deliveryAndShipping"
            className={cn(
              "border-none",
              deliveryAndShipping.shipping || deliveryAndShipping.delivery
                ? "border-b"
                : "hidden"
            )}
          >
            <AccordionTrigger className="text-lg font-semibold py-3 hover:bg-primary hover:text-white px-3 transition-all data-[state=open]:bg-primary data-[state=open]:text-white">
              Delivery and Shipping
            </AccordionTrigger>
            <AccordionContent className="px-1">
              <div className="bg-white p-4 rounded-lg shadow-sm mt-2 space-y-4">
                <h4 className="font-semibold mb-2 text-base">Shipping</h4>
                <p>
                  <span className="text-red-500">*</span> Total does not include
                  applicable shipping charges.
                </p>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )}

      {/* Warranty */}
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="warranty"
          className={cn("border-none", warranty?.label ? "border-b" : "hidden")}
        >
          <AccordionTrigger className="text-lg font-semibold py-3 hover:bg-primary hover:text-white px-3 transition-all data-[state=open]:bg-primary data-[state=open]:text-white">
            Warranty
          </AccordionTrigger>
          <AccordionContent className="px-1">
            {warranty?.label ? (
              <div className="bg-white p-4 rounded-lg shadow-sm mt-2">
                {warranty?.url ? (
                  <Link
                    href={warranty.url}
                    target="_blank"
                    className="flex items-center gap-2 text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                  >
                    <ExternalLink className="h-5 w-5 flex-shrink-0" />
                    <span className="text-base">{warranty.label}</span>
                  </Link>
                ) : (
                  <p className="text-base">{warranty.label}</p>
                )}
              </div>
            ) : null}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
