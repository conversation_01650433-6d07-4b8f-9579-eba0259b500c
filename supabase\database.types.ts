export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)";
  };
  public: {
    Tables: {
      billing_addresses: {
        Row: {
          address: string;
          address_2: string | null;
          address_type: Database["public"]["Enums"]["address_type"] | null;
          approved: boolean;
          city: string;
          company_name: string | null;
          country: string;
          created_at: string;
          customer_id: string | null;
          default: boolean;
          effective_date: string | null;
          id: string;
          note: string | null;
          state: string;
          updated_at: string | null;
          zip_code: string;
        };
        Insert: {
          address?: string;
          address_2?: string | null;
          address_type?: Database["public"]["Enums"]["address_type"] | null;
          approved?: boolean;
          city?: string;
          company_name?: string | null;
          country?: string;
          created_at?: string;
          customer_id?: string | null;
          default?: boolean;
          effective_date?: string | null;
          id?: string;
          note?: string | null;
          state?: string;
          updated_at?: string | null;
          zip_code?: string;
        };
        Update: {
          address?: string;
          address_2?: string | null;
          address_type?: Database["public"]["Enums"]["address_type"] | null;
          approved?: boolean;
          city?: string;
          company_name?: string | null;
          country?: string;
          created_at?: string;
          customer_id?: string | null;
          default?: boolean;
          effective_date?: string | null;
          id?: string;
          note?: string | null;
          state?: string;
          updated_at?: string | null;
          zip_code?: string;
        };
        Relationships: [
          {
            foreignKeyName: "billing_addresses_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
        ];
      };
      business_details: {
        Row: {
          account_payable_contact: string | null;
          account_payable_phone: string | null;
          authorized_contact_name: string | null;
          business_nature: string | null;
          business_type: string | null;
          buyer_name: string | null;
          buyer_phone: string | null;
          created_at: string;
          has_mechanic: string | null;
          id: string;
          maxton_account: string | null;
          number_of_elevators: number | null;
          technical_contact: string | null;
          technical_contact_phone: string | null;
          technician: string | null;
          updated_at: string | null;
          user_id: string | null;
          website: string | null;
        };
        Insert: {
          account_payable_contact?: string | null;
          account_payable_phone?: string | null;
          authorized_contact_name?: string | null;
          business_nature?: string | null;
          business_type?: string | null;
          buyer_name?: string | null;
          buyer_phone?: string | null;
          created_at?: string;
          has_mechanic?: string | null;
          id?: string;
          maxton_account?: string | null;
          number_of_elevators?: number | null;
          technical_contact?: string | null;
          technical_contact_phone?: string | null;
          technician?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          website?: string | null;
        };
        Update: {
          account_payable_contact?: string | null;
          account_payable_phone?: string | null;
          authorized_contact_name?: string | null;
          business_nature?: string | null;
          business_type?: string | null;
          buyer_name?: string | null;
          buyer_phone?: string | null;
          created_at?: string;
          has_mechanic?: string | null;
          id?: string;
          maxton_account?: string | null;
          number_of_elevators?: number | null;
          technical_contact?: string | null;
          technical_contact_phone?: string | null;
          technician?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          website?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "business_details_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      cancellation_requests: {
        Row: {
          comment: string | null;
          created_at: string;
          customer_id: string | null;
          id: string;
          order_id: string | null;
          status:
            | Database["public"]["Enums"]["cancellation_request_status"]
            | null;
          user_id: string | null;
        };
        Insert: {
          comment?: string | null;
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          order_id?: string | null;
          status?:
            | Database["public"]["Enums"]["cancellation_request_status"]
            | null;
          user_id?: string | null;
        };
        Update: {
          comment?: string | null;
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          order_id?: string | null;
          status?:
            | Database["public"]["Enums"]["cancellation_request_status"]
            | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "cancellation_requests_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cancellation_requests_order_id_fkey";
            columns: ["order_id"];
            isOneToOne: false;
            referencedRelation: "orders";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cancellation_requests_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      categories: {
        Row: {
          created_at: string;
          id: string;
          name: string | null;
          parent_category_id: string | null;
          updated_at: string | null;
          value: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          name?: string | null;
          parent_category_id?: string | null;
          updated_at?: string | null;
          value?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          name?: string | null;
          parent_category_id?: string | null;
          updated_at?: string | null;
          value?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "categories_parent_category_id_fkey";
            columns: ["parent_category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
        ];
      };
      credit_cards: {
        Row: {
          card_number: string;
          created_at: string;
          customer_id: string | null;
          exp_month: number;
          exp_year: number;
          full_name: string;
          id: string;
          user_id: string | null;
        };
        Insert: {
          card_number?: string;
          created_at?: string;
          customer_id?: string | null;
          exp_month: number;
          exp_year: number;
          full_name?: string;
          id?: string;
          user_id?: string | null;
        };
        Update: {
          card_number?: string;
          created_at?: string;
          customer_id?: string | null;
          exp_month?: number;
          exp_year?: number;
          full_name?: string;
          id?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "credit_cards_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "credit_cards_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      customer_categories: {
        Row: {
          category_id: string | null;
          created_at: string;
          customer_id: string | null;
          id: string;
          user_id: string | null;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          user_id?: string | null;
        };
        Update: {
          category_id?: string | null;
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "customer_categories_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "customer_categories_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "customer_categories_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      customer_groups: {
        Row: {
          created_at: string;
          customer_id: string;
          group_id: string;
          id: string;
        };
        Insert: {
          created_at?: string;
          customer_id?: string;
          group_id?: string;
          id?: string;
        };
        Update: {
          created_at?: string;
          customer_id?: string;
          group_id?: string;
          id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "customer_groups_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: true;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "customer_groups_group_id_fkey";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "groups";
            referencedColumns: ["id"];
          },
        ];
      };
      customers: {
        Row: {
          company_logo: string;
          company_name: string;
          company_website: string | null;
          cost_percentage: number | null;
          created_at: string;
          credit_limit: number | null;
          customer_number: string;
          id: string;
          payment_options: string | null;
          phone: string;
          primary_contact_name: string;
          role: Database["public"]["Enums"]["customer_role"];
          shipping_notes: string;
          status: Database["public"]["Enums"]["account_status"];
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          company_logo?: string;
          company_name?: string;
          company_website?: string | null;
          cost_percentage?: number | null;
          created_at?: string;
          credit_limit?: number | null;
          customer_number?: string;
          id?: string;
          payment_options?: string | null;
          phone?: string;
          primary_contact_name?: string;
          role?: Database["public"]["Enums"]["customer_role"];
          shipping_notes?: string;
          status?: Database["public"]["Enums"]["account_status"];
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          company_logo?: string;
          company_name?: string;
          company_website?: string | null;
          cost_percentage?: number | null;
          created_at?: string;
          credit_limit?: number | null;
          customer_number?: string;
          id?: string;
          payment_options?: string | null;
          phone?: string;
          primary_contact_name?: string;
          role?: Database["public"]["Enums"]["customer_role"];
          shipping_notes?: string;
          status?: Database["public"]["Enums"]["account_status"];
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "customers_user_id_fkey1";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      discounts: {
        Row: {
          code: string | null;
          created_at: string;
          id: string;
          info: string | null;
          value: number | null;
        };
        Insert: {
          code?: string | null;
          created_at?: string;
          id?: string;
          info?: string | null;
          value?: number | null;
        };
        Update: {
          code?: string | null;
          created_at?: string;
          id?: string;
          info?: string | null;
          value?: number | null;
        };
        Relationships: [];
      };
      groups: {
        Row: {
          created_at: string;
          description: string | null;
          id: string;
          name: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: string;
          name?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: string;
          name?: string | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      moq: {
        Row: {
          created_at: string;
          id: string;
          label: string | null;
          type: string;
          value: number | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          label?: string | null;
          type?: string;
          value?: number | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          label?: string | null;
          type?: string;
          value?: number | null;
        };
        Relationships: [];
      };
      nevada_tax_rates: {
        Row: {
          city: string | null;
          created_at: string;
          id: string;
          tax_rate: number | null;
        };
        Insert: {
          city?: string | null;
          created_at?: string;
          id?: string;
          tax_rate?: number | null;
        };
        Update: {
          city?: string | null;
          created_at?: string;
          id?: string;
          tax_rate?: number | null;
        };
        Relationships: [];
      };
      notifications: {
        Row: {
          body: string | null;
          created_at: string;
          dismissed: boolean | null;
          expires_at: string | null;
          header: string | null;
          id: string;
          link: string | null;
          type: Database["public"]["Enums"]["notification_type"];
        };
        Insert: {
          body?: string | null;
          created_at?: string;
          dismissed?: boolean | null;
          expires_at?: string | null;
          header?: string | null;
          id?: string;
          link?: string | null;
          type?: Database["public"]["Enums"]["notification_type"];
        };
        Update: {
          body?: string | null;
          created_at?: string;
          dismissed?: boolean | null;
          expires_at?: string | null;
          header?: string | null;
          id?: string;
          link?: string | null;
          type?: Database["public"]["Enums"]["notification_type"];
        };
        Relationships: [];
      };
      order_items: {
        Row: {
          calculator_data: Json | null;
          created_at: string;
          customer_id: string;
          id: string;
          item_price: number | null;
          options: Json[] | null;
          order_id: string;
          product_id: string;
          quantity: number;
        };
        Insert: {
          calculator_data?: Json | null;
          created_at?: string;
          customer_id?: string;
          id?: string;
          item_price?: number | null;
          options?: Json[] | null;
          order_id?: string;
          product_id?: string;
          quantity: number;
        };
        Update: {
          calculator_data?: Json | null;
          created_at?: string;
          customer_id?: string;
          id?: string;
          item_price?: number | null;
          options?: Json[] | null;
          order_id?: string;
          product_id?: string;
          quantity?: number;
        };
        Relationships: [
          {
            foreignKeyName: "order_items_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "order_items_order_id_fkey";
            columns: ["order_id"];
            isOneToOne: false;
            referencedRelation: "orders";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "order_items_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
        ];
      };
      order_statuses: {
        Row: {
          created_at: string;
          customer_id: string | null;
          id: string;
          order_id: string | null;
          status: Database["public"]["Enums"]["order_status"];
        };
        Insert: {
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          order_id?: string | null;
          status?: Database["public"]["Enums"]["order_status"];
        };
        Update: {
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          order_id?: string | null;
          status?: Database["public"]["Enums"]["order_status"];
        };
        Relationships: [
          {
            foreignKeyName: "order_statuses_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "order_statuses_order_id_fkey";
            columns: ["order_id"];
            isOneToOne: false;
            referencedRelation: "orders";
            referencedColumns: ["id"];
          },
        ];
      };
      orders: {
        Row: {
          billing_address_city: string | null;
          billing_address_country: string | null;
          billing_address_id: string | null;
          billing_address_name: string | null;
          billing_address_state: string | null;
          billing_address_zip_code: string | null;
          created_at: string;
          customer_id: string | null;
          delivery_method: string | null;
          id: string;
          invoice: string | null;
          metadata: Json | null;
          notes: string | null;
          payment_type: string | null;
          po_attachment: string | null;
          purchase_order: string | null;
          ship_collect: boolean | null;
          shipping_address_city: string | null;
          shipping_address_country: string | null;
          shipping_address_id: string | null;
          shipping_address_name: string | null;
          shipping_address_state: string | null;
          shipping_address_zip_code: string | null;
          shipping_cost: number | null;
          tax_amount: number | null;
          tax_exempt: boolean | null;
          tax_rate: number | null;
          total_amount: number | null;
          tracking_link: string | null;
          updated_at: string | null;
          ups_account_number: string | null;
          user_id: string | null;
        };
        Insert: {
          billing_address_city?: string | null;
          billing_address_country?: string | null;
          billing_address_id?: string | null;
          billing_address_name?: string | null;
          billing_address_state?: string | null;
          billing_address_zip_code?: string | null;
          created_at?: string;
          customer_id?: string | null;
          delivery_method?: string | null;
          id?: string;
          invoice?: string | null;
          metadata?: Json | null;
          notes?: string | null;
          payment_type?: string | null;
          po_attachment?: string | null;
          purchase_order?: string | null;
          ship_collect?: boolean | null;
          shipping_address_city?: string | null;
          shipping_address_country?: string | null;
          shipping_address_id?: string | null;
          shipping_address_name?: string | null;
          shipping_address_state?: string | null;
          shipping_address_zip_code?: string | null;
          shipping_cost?: number | null;
          tax_amount?: number | null;
          tax_exempt?: boolean | null;
          tax_rate?: number | null;
          total_amount?: number | null;
          tracking_link?: string | null;
          updated_at?: string | null;
          ups_account_number?: string | null;
          user_id?: string | null;
        };
        Update: {
          billing_address_city?: string | null;
          billing_address_country?: string | null;
          billing_address_id?: string | null;
          billing_address_name?: string | null;
          billing_address_state?: string | null;
          billing_address_zip_code?: string | null;
          created_at?: string;
          customer_id?: string | null;
          delivery_method?: string | null;
          id?: string;
          invoice?: string | null;
          metadata?: Json | null;
          notes?: string | null;
          payment_type?: string | null;
          po_attachment?: string | null;
          purchase_order?: string | null;
          ship_collect?: boolean | null;
          shipping_address_city?: string | null;
          shipping_address_country?: string | null;
          shipping_address_id?: string | null;
          shipping_address_name?: string | null;
          shipping_address_state?: string | null;
          shipping_address_zip_code?: string | null;
          shipping_cost?: number | null;
          tax_amount?: number | null;
          tax_exempt?: boolean | null;
          tax_rate?: number | null;
          total_amount?: number | null;
          tracking_link?: string | null;
          updated_at?: string | null;
          ups_account_number?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "orders_billing_address_id_fkey";
            columns: ["billing_address_id"];
            isOneToOne: false;
            referencedRelation: "billing_addresses";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "orders_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "orders_shipping_address_id_fkey";
            columns: ["shipping_address_id"];
            isOneToOne: false;
            referencedRelation: "shipping_addresses";
            referencedColumns: ["id"];
          },
        ];
      };
      password_resets: {
        Row: {
          created_at: string;
          id: string;
          token: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          token?: string;
          user_id?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          token?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "password_resets_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      permissions: {
        Row: {
          allowed_actions: string[];
          created_at: string;
          id: string;
          role: Database["public"]["Enums"]["customer_role"] | null;
        };
        Insert: {
          allowed_actions: string[];
          created_at?: string;
          id?: string;
          role?: Database["public"]["Enums"]["customer_role"] | null;
        };
        Update: {
          allowed_actions?: string[];
          created_at?: string;
          id?: string;
          role?: Database["public"]["Enums"]["customer_role"] | null;
        };
        Relationships: [];
      };
      product_arrangements: {
        Row: {
          category_id: string | null;
          created_at: string;
          id: string;
          position: number | null;
          product_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string;
          id?: string;
          position?: number | null;
          product_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          category_id?: string | null;
          created_at?: string;
          id?: string;
          position?: number | null;
          product_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "product_arrangements_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "product_arrangements_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
        ];
      };
      product_categories: {
        Row: {
          category_id: string | null;
          created_at: string;
          id: string;
          product_id: string | null;
        };
        Insert: {
          category_id?: string | null;
          created_at?: string;
          id?: string;
          product_id?: string | null;
        };
        Update: {
          category_id?: string | null;
          created_at?: string;
          id?: string;
          product_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "product_categories_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "product_categories_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
        ];
      };
      product_group_prices: {
        Row: {
          created_at: string;
          custom_price: number | null;
          group_id: string | null;
          hash: string;
          id: string;
          product_id: string | null;
        };
        Insert: {
          created_at?: string;
          custom_price?: number | null;
          group_id?: string | null;
          hash: string;
          id?: string;
          product_id?: string | null;
        };
        Update: {
          created_at?: string;
          custom_price?: number | null;
          group_id?: string | null;
          hash?: string;
          id?: string;
          product_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "product_group_prices_group_id_fkey";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "groups";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "product_group_prices_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
        ];
      };
      products: {
        Row: {
          additional_images: string[] | null;
          available: boolean;
          brand: string | null;
          created_at: string;
          delivery_and_shipping: Json | null;
          description: string | null;
          draft: boolean | null;
          helpful_hints: Json | null;
          id: string;
          image: string | null;
          installation_instructions: Json | null;
          is_quote: boolean | null;
          name: string;
          options: Json[] | null;
          price: number;
          sku: string | null;
          specifications: Json | null;
          tags: string[] | null;
          updated_at: string | null;
          user_id: string | null;
          variant: string | null;
          warranty: Json | null;
        };
        Insert: {
          additional_images?: string[] | null;
          available?: boolean;
          brand?: string | null;
          created_at?: string;
          delivery_and_shipping?: Json | null;
          description?: string | null;
          draft?: boolean | null;
          helpful_hints?: Json | null;
          id?: string;
          image?: string | null;
          installation_instructions?: Json | null;
          is_quote?: boolean | null;
          name?: string;
          options?: Json[] | null;
          price?: number;
          sku?: string | null;
          specifications?: Json | null;
          tags?: string[] | null;
          updated_at?: string | null;
          user_id?: string | null;
          variant?: string | null;
          warranty?: Json | null;
        };
        Update: {
          additional_images?: string[] | null;
          available?: boolean;
          brand?: string | null;
          created_at?: string;
          delivery_and_shipping?: Json | null;
          description?: string | null;
          draft?: boolean | null;
          helpful_hints?: Json | null;
          id?: string;
          image?: string | null;
          installation_instructions?: Json | null;
          is_quote?: boolean | null;
          name?: string;
          options?: Json[] | null;
          price?: number;
          sku?: string | null;
          specifications?: Json | null;
          tags?: string[] | null;
          updated_at?: string | null;
          user_id?: string | null;
          variant?: string | null;
          warranty?: Json | null;
        };
        Relationships: [];
      };
      quotes: {
        Row: {
          created_at: string;
          id: number;
          product_id: string;
          quantity: number | null;
          quote_text: string | null;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: number;
          product_id: string;
          quantity?: number | null;
          quote_text?: string | null;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: number;
          product_id?: string;
          quantity?: number | null;
          quote_text?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quotes_product_id_fkey";
            columns: ["product_id"];
            isOneToOne: false;
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "quotes_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      rejected_users: {
        Row: {
          company: string | null;
          created_at: string;
          email: string | null;
          first_name: string | null;
          full_name: string | null;
          id: number;
          last_name: string | null;
          notes: string | null;
        };
        Insert: {
          company?: string | null;
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          full_name?: string | null;
          id?: number;
          last_name?: string | null;
          notes?: string | null;
        };
        Update: {
          company?: string | null;
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          full_name?: string | null;
          id?: number;
          last_name?: string | null;
          notes?: string | null;
        };
        Relationships: [];
      };
      rfq: {
        Row: {
          created_at: string;
          customer_id: string | null;
          id: string;
          important_properties: string | null;
          metadata: Json | null;
          other_details: string | null;
          product_family: string | null;
          proudct_development: string | null;
          read_status: string | null;
          volume_needed: string | null;
        };
        Insert: {
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          important_properties?: string | null;
          metadata?: Json | null;
          other_details?: string | null;
          product_family?: string | null;
          proudct_development?: string | null;
          read_status?: string | null;
          volume_needed?: string | null;
        };
        Update: {
          created_at?: string;
          customer_id?: string | null;
          id?: string;
          important_properties?: string | null;
          metadata?: Json | null;
          other_details?: string | null;
          product_family?: string | null;
          proudct_development?: string | null;
          read_status?: string | null;
          volume_needed?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "rfq_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
        ];
      };
      shipping_addresses: {
        Row: {
          address: string | null;
          address_2: string | null;
          address_type: Database["public"]["Enums"]["address_type"] | null;
          city: string | null;
          contact_email: string | null;
          contact_name: string | null;
          contact_number: string | null;
          country: string | null;
          created_at: string;
          customer_id: string | null;
          default: boolean | null;
          id: string;
          option_name: string | null;
          state: string | null;
          updated_at: string | null;
          zip_code: string | null;
        };
        Insert: {
          address?: string | null;
          address_2?: string | null;
          address_type?: Database["public"]["Enums"]["address_type"] | null;
          city?: string | null;
          contact_email?: string | null;
          contact_name?: string | null;
          contact_number?: string | null;
          country?: string | null;
          created_at?: string;
          customer_id?: string | null;
          default?: boolean | null;
          id?: string;
          option_name?: string | null;
          state?: string | null;
          updated_at?: string | null;
          zip_code?: string | null;
        };
        Update: {
          address?: string | null;
          address_2?: string | null;
          address_type?: Database["public"]["Enums"]["address_type"] | null;
          city?: string | null;
          contact_email?: string | null;
          contact_name?: string | null;
          contact_number?: string | null;
          country?: string | null;
          created_at?: string;
          customer_id?: string | null;
          default?: boolean | null;
          id?: string;
          option_name?: string | null;
          state?: string | null;
          updated_at?: string | null;
          zip_code?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "ship_to_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
        ];
      };
      users: {
        Row: {
          created_at: string;
          email: string | null;
          first_name: string | null;
          has_subscribed_to_newsletter: boolean | null;
          id: string;
          last_name: string | null;
          notes: string | null;
          phone: string | null;
          role: Database["public"]["Enums"]["user_role"];
          status: Database["public"]["Enums"]["account_status"];
          updated_at: string | null;
        };
        Insert: {
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          has_subscribed_to_newsletter?: boolean | null;
          id?: string;
          last_name?: string | null;
          notes?: string | null;
          phone?: string | null;
          role: Database["public"]["Enums"]["user_role"];
          status: Database["public"]["Enums"]["account_status"];
          updated_at?: string | null;
        };
        Update: {
          created_at?: string;
          email?: string | null;
          first_name?: string | null;
          has_subscribed_to_newsletter?: boolean | null;
          id?: string;
          last_name?: string | null;
          notes?: string | null;
          phone?: string | null;
          role?: Database["public"]["Enums"]["user_role"];
          status?: Database["public"]["Enums"]["account_status"];
          updated_at?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      latest_order_statuses: {
        Row: {
          created_at: string | null;
          id: string | null;
          order_id: string | null;
          status: Database["public"]["Enums"]["order_status"] | null;
        };
        Relationships: [
          {
            foreignKeyName: "order_statuses_order_id_fkey";
            columns: ["order_id"];
            isOneToOne: false;
            referencedRelation: "orders";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Functions: {
      full_name: {
        Args: { u: Database["public"]["Tables"]["users"]["Row"] };
        Returns: string;
      };
      get_latest_order_statuses_by_status: {
        Args: { p_status: string };
        Returns: {
          order_id: string;
          status: string;
          created_at: string;
        }[];
      };
      get_order_ids_with_count: {
        Args: {
          search: string;
          limit_rows?: number;
          page_num?: number;
          sort_order?: string;
          order_by_col?: string;
          status_filter?: string;
        };
        Returns: {
          total_count: number;
          ids: string[];
        }[];
      };
      get_orders: {
        Args: {
          search: string;
          limit_rows?: number;
          page_num?: number;
          sort_order?: string;
          order_by_col?: string;
          status_filter?: string;
        };
        Returns: string[];
      };
      gtrgm_compress: {
        Args: { "": unknown };
        Returns: unknown;
      };
      gtrgm_decompress: {
        Args: { "": unknown };
        Returns: unknown;
      };
      gtrgm_in: {
        Args: { "": unknown };
        Returns: unknown;
      };
      gtrgm_options: {
        Args: { "": unknown };
        Returns: undefined;
      };
      gtrgm_out: {
        Args: { "": unknown };
        Returns: unknown;
      };
      set_limit: {
        Args: { "": number };
        Returns: number;
      };
      show_limit: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      show_trgm: {
        Args: { "": string };
        Returns: string[];
      };
    };
    Enums: {
      account_status: "pending" | "approved" | "rejected" | "banned";
      address_type: "residential" | "commercial";
      cancellation_request_status: "approved" | "rejected" | "pending";
      customer_role: "admin" | "customer" | "staff";
      notification_type: "info" | "warning" | "error";
      order_status:
        | "pending"
        | "awaiting_payment"
        | "shipped"
        | "delivered"
        | "processing"
        | "completed"
        | "canceled"
        | "denied"
        | "canceled_reversal"
        | "failed"
        | "refunded"
        | "reversed"
        | "chargeback"
        | "expired"
        | "processed"
        | "voided";
      user_role: "admin" | "manager" | "staff";
    };
    CompositeTypes: {
      order_with_count: {
        id: string | null;
        total_count: number | null;
      };
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  "public"
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {
      account_status: ["pending", "approved", "rejected", "banned"],
      address_type: ["residential", "commercial"],
      cancellation_request_status: ["approved", "rejected", "pending"],
      customer_role: ["admin", "customer", "staff"],
      notification_type: ["info", "warning", "error"],
      order_status: [
        "pending",
        "awaiting_payment",
        "shipped",
        "delivered",
        "processing",
        "completed",
        "canceled",
        "denied",
        "canceled_reversal",
        "failed",
        "refunded",
        "reversed",
        "chargeback",
        "expired",
        "processed",
        "voided",
      ],
      user_role: ["admin", "manager", "staff"],
    },
  },
} as const;
