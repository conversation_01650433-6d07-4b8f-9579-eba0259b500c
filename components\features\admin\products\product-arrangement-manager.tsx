import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import {
  useBulkUpdateArrangementsMutation,
  useCreateProductArrangementMutation,
  useGetArrangementsByCategoryQuery,
  useGetAllProductsQuery,
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { ProductArrangementWithRelations } from "@/supabase/types";
import { useGetImage } from "@/queries/customer-queries";
import {
  AlertCircle,
  GripVertical,
  Package,
  RefreshCw,
  Save
} from "lucide-react";

import { useCallback, useEffect, useState } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { cn } from "@/lib/utils";

interface ProductArrangementManagerProps {
  categoryId: string;
  categoryName: string;
}

interface DraggableProductItemProps {
  arrangement: ProductArrangementWithRelations;
  index: number;
  moveItem: (dragIndex: number, hoverIndex: number) => void;
}

const ItemType = "PRODUCT_ARRANGEMENT";

function DraggableProductItem({ arrangement, index, moveItem }: DraggableProductItemProps) {
  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ItemType,
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveItem(item.index, index);
        item.index = index;
      }
    },
  });

  const product = arrangement.products;

  // Use the useGetImage hook for product images
  const imagePath = product?.image || "";
  const imageQuery = useGetImage(imagePath);
  const imageUrl = imageQuery.data || product?.image || "";

  return (
    <div
      ref={(node) => drag(drop(node))}
      className={`bg-white border rounded-lg p-4 cursor-move transition-all ${isDragging ? "opacity-50 scale-95" : "hover:shadow-md"
        } ${arrangement.isVirtual ? "border-dashed border-orange-300 bg-orange-50/50" : ""}`}
    >
      <div className="flex items-center gap-4">
        <div className="flex-shrink-0">
          <GripVertical className="h-5 w-5 text-muted-foreground" />
        </div>

        <div className="flex-shrink-0">
          {imageUrl ? (
            <div
              className="w-16 h-16 bg-gray-200 rounded-md border"
              style={{
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            />
          ) : (
            <div className="w-16 h-16 bg-muted rounded-md flex items-center justify-center border">
              <Package className="h-6 w-6 text-muted-foreground" />
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="font-medium truncate">{product?.name || "Unknown Product"}</h4>
          <div className="flex items-center gap-2 mt-1">
            {product?.sku && (
              <Badge variant="outline" className="text-xs">
                SKU: {product.sku}
              </Badge>
            )}
            {product?.price && (
              <Badge variant="secondary" className="text-xs">
                ${product.price.toFixed(2)}
              </Badge>
            )}
            {arrangement.isVirtual && (
              <Badge variant="outline" className="text-xs text-orange-600 border-orange-300">
                New Position
              </Badge>
            )}
          </div>
        </div>

        <div className="flex-shrink-0">
          <Badge variant="outline">
            Position {index + 1}
          </Badge>
        </div>
      </div>
    </div>
  );
}

export function ProductArrangementManager({ categoryId, categoryName }: ProductArrangementManagerProps) {
  const { toast } = useToast();
  const token = useAuthStore((state) => state.token);
  const [arrangements, setArrangements] = useState<ProductArrangementWithRelations[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Get all products
  const {
    data: productsData,
    isLoading: isProductsLoading,
  } = useGetAllProductsQuery(1, 1000, token);

  // Get existing arrangements for this category
  const {
    data: arrangementsData,
    isLoading: isArrangementsLoading,
    isError,
    refetch,
  } = useGetArrangementsByCategoryQuery(categoryId, token);

  const isLoading = isProductsLoading || isArrangementsLoading;

  const bulkUpdateMutation = useBulkUpdateArrangementsMutation(token);
  const createArrangementMutation = useCreateProductArrangementMutation(token);

  // Update local state when data changes
  useEffect(() => {
    if (productsData?.products && arrangementsData) {
      // Filter products that belong to this category
      const categoryProducts = productsData.products.filter((product: any) =>
        product.product_categories?.some((pc: any) =>
          pc.category_data?.id === categoryId
        )
      );

      // Create a map of existing arrangements by product_id
      const existingArrangements = arrangementsData.data || [];
      const arrangementMap = new Map(
        existingArrangements.map((arr: any) => [arr.product_id, arr])
      );

      // Create arrangements for all products in this category
      const allArrangements: ProductArrangementWithRelations[] = categoryProducts.map((product: any) => {
        const existingArrangement = arrangementMap.get(product.id);

        if (existingArrangement) {
          // Use existing arrangement
          return existingArrangement;
        } else {
          // Create virtual arrangement for products without arrangements
          return {
            id: `virtual-${product.id}`,
            product_id: product.id,
            category_id: categoryId,
            position: 999999, // Put at end initially
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            products: product,
            categories: null,
            isVirtual: true,
          };
        }
      });

      // Sort by position (arranged products first, then virtual ones)
      allArrangements.sort((a, b) => {
        // Real arrangements come first, sorted by position
        let aPos = a.position ?? 0;
        let bPos = b.position ?? 0;
        if (!a.isVirtual && !b.isVirtual) {
          return aPos - bPos;
        }
        // Real arrangements before virtual ones
        if (!a.isVirtual && b.isVirtual) {
          return -1;
        }
        if (a.isVirtual && !b.isVirtual) {
          return 1;
        }
        // Both virtual, sort by product name
        const nameA = a.products?.name || '';
        const nameB = b.products?.name || '';
        return nameA.localeCompare(nameB);
      });

      // Reassign positions to be sequential
      allArrangements.forEach((arrangement, index) => {
        arrangement.position = index;
      });

      setArrangements(allArrangements);
      setHasChanges(false);
    }
  }, [productsData, arrangementsData, categoryId]);

  const moveItem = useCallback((dragIndex: number, hoverIndex: number) => {
    setArrangements((prevArrangements) => {
      const newArrangements = [...prevArrangements];
      const draggedItem = newArrangements[dragIndex];

      // Remove the dragged item
      newArrangements.splice(dragIndex, 1);
      // Insert it at the new position
      newArrangements.splice(hoverIndex, 0, draggedItem);

      setHasChanges(true);
      return newArrangements;
    });
  }, []);

  const handleSave = async () => {
    if (!hasChanges || arrangements.length === 0) return;

    setIsSaving(true);
    try {
      // First, create any virtual arrangements that need to be persisted
      const virtualArrangements = arrangements.filter(arr => arr.isVirtual);
      const realArrangements = arrangements.filter(arr => !arr.isVirtual);

      // Bulk create virtual arrangements if there are any
      if (virtualArrangements.length > 0) {
        const productIds = virtualArrangements.map(arr => arr.product_id ?? "").filter(id => id);

        if (productIds.length > 0) {
          try {
            await createArrangementMutation.mutateAsync({
              product_ids: productIds,
              category_id: categoryId,
            });
          } catch (error) {
            console.error(`Failed to create arrangements for products:`, error);
            throw error; // Re-throw to stop the save process
          }
        }
      }

      // Update existing arrangements if there are any
      if (realArrangements.length > 0) {
        const updates = realArrangements.map((arrangement, index) => ({
          id: arrangement.id,
          position: arrangements.findIndex(arr => arr.id === arrangement.id), // Use position in the full array
        }));

        await bulkUpdateMutation.mutateAsync({
          arrangements: updates,
        });
      }

      // Refresh the data to get the latest state
      await refetch();
      setHasChanges(false);

      toast({
        title: "Success",
        description: "Product arrangements saved successfully.",
      });
    } catch (error) {
      console.error("Error saving arrangements:", error);
      toast({
        title: "Error",
        description: "Failed to save product arrangements. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    if (arrangementsData?.data) {
      setArrangements(arrangementsData.data);
      setHasChanges(false);
      toast({
        title: "Reset",
        description: "Changes have been reset to the saved state.",
      });
    }
  };

  const handleRefresh = () => {
    refetch();
    toast({
      title: "Refreshed",
      description: "Product arrangements have been refreshed.",
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading arrangements for {categoryName}...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            Error Loading Arrangements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Failed to load product arrangements for {categoryName}.
          </p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {categoryName} - Product Arrangements
            </CardTitle>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  Unsaved Changes
                </Badge>
              )}
              <Button
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                disabled={isSaving}
              >
                <RefreshCw className={cn("h-4 w-4", { "animate-spin": isSaving })} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {arrangements.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Products Found</h3>
              <p className="text-muted-foreground mb-4">
                There are no products in this category yet.
              </p>
              <p className="text-sm text-muted-foreground">
                Add products to this category first, then they will appear here for arrangement.
              </p>
            </div>
          ) : (
            <>
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <GripVertical className="h-5 w-5 text-blue-600 mt-0.5" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 mb-1">How to use:</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Drag and drop products to reorder them</li>
                      <li>• The order shown here will be reflected on the store products page</li>
                      <li>• Click "Save Changes" to apply your new arrangement</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-3 mb-6">
                {arrangements.map((arrangement, index) => (
                  <DraggableProductItem
                    key={arrangement.id}
                    arrangement={arrangement}
                    index={index}
                    moveItem={moveItem}
                  />
                ))}
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  {arrangements.length} product{arrangements.length !== 1 ? "s" : ""} in this category
                </div>
                <div className="flex items-center gap-2">
                  {hasChanges && (
                    <Button
                      onClick={handleReset}
                      variant="outline"
                      disabled={isSaving}
                    >
                      Reset Changes
                    </Button>
                  )}
                  <Button
                    onClick={handleSave}
                    disabled={!hasChanges || isSaving}
                    className="min-w-[120px]"
                  >
                    {isSaving ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </DndProvider>
  );
}