import { But<PERSON> } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import Image from "next/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableTextBlock } from "sanity";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-6 leading-loose text-5xl sm:text-7xl">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return (
        <Heading type="h2" className="mb-4 text-xl">
          {children}
        </Heading>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-6 text-xl md:text-3xl leading-loose text-gray-900">
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-2 text-base sm:text-xl font-semibold leading-loose text-gray-900">
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <Text className="mb-3 font-body text-sm sm:text-base text-gray-900 lg:text-left text-justify">
          {children}
        </Text>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-secondary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Header_J({
  mainImage,
  title,
  description,
  primaryButton,
  secondaryButton,
  firstColumn,
}: HeaderProps) {
  return (
    <Section className="lg:pt-32 pt-20 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex
          align="start"
          className="lg:gap-10 gap-4 flex-col md:flex-row items-center justify-center"
        >
          <MainImage mainImage={mainImage} />

          <Flex align="center" direction="col" className="w-full basis-1/2">
            <div className="max-w-xl mx-auto text-center lg:text-left">
              <TitleAndDescription title={title} firstColumn={firstColumn} />
              <Buttons
                primaryButton={primaryButton}
                secondaryButton={secondaryButton}
              />
            </div>
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  firstColumn,
}: {
  title?: string;
  firstColumn: PortableTextBlock;
}) {
  return (
    <React.Fragment>
      {title && (
        <Heading
          type="h2"
          className="mb-6 lg:text-3xl text-xl lg:text-left text-center"
        >
          {title}
        </Heading>
      )}

      {firstColumn && (
        <div className="mb-2 text-xs md:mb-0 lg:text-base">
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </React.Fragment>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      justify="center"
      gap={2}
      direction="col"
      className="lg:justify-start items-center !flex-row"
    >
      {primaryButton?.label && (
        <Button
          variant="unstyled"
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.label}
        >
          <Image
            src="/assets/elements/downloads/download.png"
            alt="Maxton App store"
            width={150}
            height={100}
            quality={100}
            priority
          />
          {/* {primaryButton?.label} */}
        </Button>
      )}

      {secondaryButton?.label && (
        <Button
          as="link"
          variant="unstyled"
          link={secondaryButton}
          ariaLabel={secondaryButton?.label}
        >
          <Image
            src="/assets/elements/downloads/googleplay.png"
            alt="Maxton App store"
            width={140}
            height={100}
            quality={100}
            priority
          />
          {/* {secondaryButton?.label} */}
        </Button>
      )}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="relative w-full max-w-sm">
      <Image
        className="object-cover relative"
        src={`${mainImage.image}`}
        sizes="(min-width: 420px) 448px, 80vw"
        width={448}
        height={448}
        alt={mainImage.alt ?? "header-main-image"}
        property=""
        quality={100}
      />
    </div>
  );
}

export { Header_J };
