import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";

const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

export default checkAdmin(matchRoute({
    GET: getDashboardData
}));

export interface SalesData {
    name: string;
    total: number;
}

export interface GetDashboardDataResponse {
    totalSales?: number;
    totalOrders?: number;
    totalCustomers?: number;
    totalPendingCustomers?: number;
    totalProducts?: number;
    totalDiscounts?: number;
    salesData?: SalesData[];
    error?: string;
}

async function getDashboardData(req: NextApiRequest, res: NextApiResponse<GetDashboardDataResponse>) {
    try {

        const supabaseAdminClient = createSupabaseAdminClient();

        // Get current year
        const currentYear = new Date().getFullYear();
        const startOfYear = new Date(currentYear, 0, 1).toISOString();
        const endOfYear = new Date(currentYear, 11, 31, 23, 59, 59, 999).toISOString();

        const { data: _totalOrdersData, count: totalOrders } = await supabaseAdminClient
            .from("orders")
            .select('id', { count: 'exact' })
            .gte('created_at', startOfYear)
            .lte('created_at', endOfYear);

        // Get order items for current year only by joining with orders
        const orderItems = await supabaseAdminClient
            .from("order_items")
            .select(`
            quantity,
            products (id, price),
            order_id,
            orders!inner (created_at)
        `)
            .gte('orders.created_at', startOfYear)
            .lte('orders.created_at', endOfYear);

        const totalSales = orderItems.error ? 0 : orderItems.data.reduce((sum, item) => {
            const quantity = item.quantity || 0;
            const price = item.products?.price || 0;
            return sum + (quantity * price);
        }, 0);

        const { data: _totalCustomersData, count: totalCustomers } = await supabaseAdminClient
            .from("customers")
            .select('id', { count: 'exact' })
            .eq("status", "approved");

        const { data: _totalPendingCustomersData, count: totalPendingCustomers } = await supabaseAdminClient
            .from("customers")
            .select('id', { count: 'exact' })
            .eq("status", "pending");

        const { data: _totalProductsData, count: totalProducts } = await supabaseAdminClient
            .from("products")
            .select('id', { count: 'exact' });

        const { data: _totalDiscountsData, count: totalDiscounts } = await supabaseAdminClient
            .from("discounts")
            .select('id', { count: 'exact' });

        const salesDataQuery = await supabaseAdminClient
            .from("orders")
            .select(`created_at`)
            .gte('created_at', startOfYear)
            .lte('created_at', endOfYear);

        // Group sales data by month (already filtered for current year)
        const salesData = salesDataQuery.data?.reduce((acc, order) => {
            const orderDate = new Date(order.created_at);
            const month = orderDate.toLocaleString('default', { month: 'short' });
            const total = acc[month] || 0;
            acc[month] = total + 1;
            return acc;
        }, {} as Record<string, number>);

        const salesDataArray: SalesData[] = [];

        // fill the empty months with 0
        months.forEach(month => {
            const total = salesData?.[month] || 0;
            salesDataArray.push({ name: month, total });
        });

        return res.status(200).json({
            totalSales: totalSales || 0,
            totalOrders: totalOrders || 0,
            totalCustomers: totalCustomers || 0,
            totalPendingCustomers: totalPendingCustomers || 0,
            totalProducts: totalProducts || 0,
            totalDiscounts: totalDiscounts || 0,
            salesData: salesDataArray
        });

    } catch (error) {
        return res.status(500).json({ error: "Failed to fetch dashboard data" });
    }
}

