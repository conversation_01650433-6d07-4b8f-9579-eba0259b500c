import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/shadcn-button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useToast } from "@/hooks/use-toast";
import { cn, formatPrice } from "@/lib/utils";
import { useGetCustomerQuery, useGetImage } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore, { CartItem } from "@/stores/cart-store";
import { AlertCircle, Minus, Plus, ShoppingCart, Trash2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/router";
import { Fragment, useEffect, useState } from "react";

// Minimum order amount in dollars
const MINIMUM_ORDER_AMOUNT = 50;

export function CartContent() {
  const router = useRouter();
  const isAdmin = useAuthStore((state) => state.isAdmin)();
  const isStaff = useAuthStore((state) => state.isStaff)();

  const items = useCartStore((state) => state.items);
  const cartSubtotal = useCartStore((state) => state.getSubtotal)();
  const clearCart = useCartStore((state) => state.clearCart);
  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const {
    data: customerData,
    isLoading: _isCustomerLoading,
    isError: _isCustomerError,
  } = useGetCustomerQuery(userId);
  const [showMinOrderAlert, setShowMinOrderAlert] = useState(false);
  const { toast } = useToast();

  const additionalAmount = useCartStore((state) => state.getAdditionalAmount)();
  const total = cartSubtotal + additionalAmount;

  const handleCheckoutClick = (e) => {
    if (total < MINIMUM_ORDER_AMOUNT) {
      e.preventDefault();
      setShowMinOrderAlert(true);
    }
  };

  const handleClearCart = () => {
    if (items.length === 0) return;

    clearCart();
    toast({
      title: "Cart cleared",
      description: "All items have been removed from your cart.",
      variant: "default",
    });
  };

  const [openSheet, setOpenSheet] = useState(false);

  useEffect(() => {
    setOpenSheet(showMinOrderAlert);
  }, [showMinOrderAlert]);

  const handleOnOpenSheetChange = (open: boolean) => {
    setOpenSheet(open);
  };

  const handleOpenSheet = () => {
    setOpenSheet(true);
  };

  const closeDialog = () => {
    setShowMinOrderAlert(false);
    router.push("/store/products");
  };

  if (isAdmin || isStaff) return null;

  return (
    <Sheet open={openSheet} onOpenChange={handleOnOpenSheetChange}>
      <SheetTrigger asChild>
        <Button
          onClick={handleOpenSheet}
          variant="outline"
          size="sm"
          className={cn(
            "relative flex items-center gap-2 transition-all duration-300 border-2 rounded-full h-12 w-12 border-none",
            items.length > 0
              ? "bg-primary/10 border-primary text-primary/70 hover:bg-primary/20 hover:text-primary"
              : "border-muted-foreground/20"
          )}
        >
          <ShoppingCart
            className={cn(
              `h-5 w-5`,
              items.length > 0 ? "text-primary" : "text-muted-foreground"
            )}
          />
          {/* <span className={`font-medium ${!items.length && 'sr-only'}`}>
                    {items.length > 0 ? `Cart (${items.length})` : '0Cart'}
                </span> */}
          <span className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-bold text-white">
            {items.length ?? 0}
          </span>
        </Button>
      </SheetTrigger>
      <SheetContent className="min-w-xl pt-20 flex flex-col h-full">
        <div className="flex flex-col gap-1 pb-4">
          <div className="flex justify-between items-center">
            <SheetTitle>Shopping Cart</SheetTitle>
            {items.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearCart}
                className="text-sm text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
          </div>
          <SheetDescription>
            Review the items in your cart and proceed to checkout.
          </SheetDescription>
        </div>

        <div className="flex-1 overflow-hidden">
          {items.length === 0 ? (
            <h3 className="text-sm">You don't have any items in your cart.</h3>
          ) : (
            <ScrollArea className="h-full pr-2">
              {items.map((item) => (
                <Fragment key={`cart-item-${item.id}-${item.quantity}`}>
                  <CardContentItemCard item={item} />
                </Fragment>
              ))}
            </ScrollArea>
          )}
        </div>

        <div className="pt-6">
          <Card className="p-6">
            <h2 className="text-xl font-bold mb-4">Order Summary</h2>
            <div className="space-y-4">
              {/* <div className="flex justify-between">
                            <span>Subtotal</span>
                            <span>{formatPrice(cartSubtotal)}</span>
                        </div> */}

              <Separator />

              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>{formatPrice(total)}</span>
              </div>

              {total < MINIMUM_ORDER_AMOUNT && total > 0 && (
                <div className="flex items-center gap-2 text-amber-600 text-sm mt-2">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    Minimum order amount is {formatPrice(MINIMUM_ORDER_AMOUNT)}
                  </span>
                </div>
              )}

              <Button
                asChild={total >= MINIMUM_ORDER_AMOUNT}
                className="w-full"
                size="lg"
                disabled={cartSubtotal === 0}
                onClick={
                  total < MINIMUM_ORDER_AMOUNT ? handleCheckoutClick : undefined
                }
              >
                {total >= MINIMUM_ORDER_AMOUNT ? (
                  <Link href="/store/checkout">Proceed to Checkout</Link>
                ) : (
                  "Proceed to Checkout"
                )}
              </Button>
            </div>
          </Card>
        </div>
      </SheetContent>

      {/* Minimum Order Alert Dialog */}
      <AlertDialog open={showMinOrderAlert} onOpenChange={setShowMinOrderAlert}>
        <AlertDialogContent className="sm:max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-amber-700">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Minimum Order Requirement
            </AlertDialogTitle>
            <AlertDialogDescription>
              Your order total must be at least{" "}
              {formatPrice(MINIMUM_ORDER_AMOUNT)} to proceed to checkout.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Current total:</span>
              <span className="text-lg">{formatPrice(total)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Amount needed:</span>
              <span className="text-lg text-amber-600">
                {formatPrice(MINIMUM_ORDER_AMOUNT - total)}
              </span>
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={closeDialog}>Continue Shopping</Button>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </Sheet>
  );
}

function CardContentItemCard({ item }: { item: CartItem }) {
  const selectedOptionsKey = item.selectedOptions
    ?.map((option) => option.value)
    .join("-");
  const image = useGetImage(item.image ?? "").data ?? item.image;

  return (
    <>
      <CartItemCard
        id={item.id}
        image={image}
        name={item.name}
        sku={item.sku}
        price={item.price}
        quantity={item.quantity}
        selected={item.selected}
        selectedOptions={item.selectedOptions}
      />
      <Separator className="my-2 bg-transparent" />
    </>
  );
}

interface QuantityCounterProps {
  quantity: number;
  onIncrease: () => void;
  onDecrease: () => void;
  min?: number;
  max?: number;
}

export function QuantityCounter({
  quantity,
  onIncrease,
  onDecrease,
  min = 1,
  max = 99,
}: QuantityCounterProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="icon"
        className="h-8 w-8"
        onClick={onDecrease}
        disabled={quantity <= min}
      >
        <Minus className="h-4 w-4" />
        <span className="sr-only">Decrease quantity</span>
      </Button>
      <div className="w-12 text-center tabular-nums">{quantity}</div>
      <Button
        variant="outline"
        size="icon"
        className="h-8 w-8"
        onClick={onIncrease}
        disabled={quantity >= max}
      >
        <Plus className="h-4 w-4" />
        <span className="sr-only">Increase quantity</span>
      </Button>
    </div>
  );
}

export function CartItemCard({
  id,
  image,
  name,
  sku,
  price,
  quantity,
  selectedOptions,
}: CartItem) {
  const updateQuantity = useCartStore((state) => state.updateQuantity);
  const removeItem = useCartStore((state) => state.removeItem);

  const totalPrice = price * quantity;
  const totalPriceFormat = formatPrice(totalPrice);

  return (
    <Card key={id} className="p-4 relative max-w-lg">
      <div className="absolute right-2 top-2 z-[5]">
        <Button
          variant="ghost"
          size="icon"
          className="h-9 w-9 text-destructive hover:text-red-500 hover:bg-red-500/10"
          onClick={() => removeItem(id, selectedOptions || [])}
        >
          <Trash2 className="h-5 w-5" />
          <span className="sr-only">Delete item</span>
        </Button>
      </div>
      <div className="relative w-full flex flex-col gap-4 pt-8">
        <div className="w-full flex gap-4">
          <div className="min-w-20 max-w-20 w-full h-20 bg-muted rounded-md overflow-hidden">
            <img
              src={image}
              alt={name}
              width={80}
              height={80}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <h3 className="font-medium overflow-ellipsis">{name}</h3>
            <div className="flex flex-col gap-1 pt-2">
              <p className="font-bold text-sm text-muted-foreground">
                SKU: <span className="font-normal">{sku}</span>
              </p>
              {selectedOptions?.map((option) => {
                const price = option.price
                  ? ` (+${formatPrice(option.price * quantity)})`
                  : "";

                return (
                  <div
                    key={option.name}
                    className="text-sm text-muted-foreground"
                  >
                    <span className="font-bold uppercase">{option.name}:</span>{" "}
                    <span className="font-normal">
                      {option.value}
                      <span className="text-green-500 font-bold text-sm">
                        {price}
                      </span>
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <span className="text-sm text-muted-foreground">Price:</span>
            <span className="font-medium">{totalPriceFormat}</span>
          </div>
          <QuantityCounter
            quantity={quantity}
            onIncrease={() =>
              updateQuantity(id, quantity + 1, selectedOptions || [])
            }
            onDecrease={() =>
              updateQuantity(id, quantity - 1, selectedOptions || [])
            }
          />
        </div>
      </div>
    </Card>
  );
}
