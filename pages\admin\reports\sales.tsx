import AdminLayout from "@/components/features/admin/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useGetSalesReportQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { format, subDays, subMonths, subWeeks } from "date-fns";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>Axis,
} from "recharts";

// Define type for date range options
interface DateRangeOption {
  label: string;
  value: string;
}

// Skeleton component for the chart
const ChartSkeleton = () => (
  <div className="h-96 w-full">
    <Skeleton className="w-full h-full" />
  </div>
);

// Skeleton component for the table
const TableSkeleton = () => (
  <div className="space-y-2">
    {/* Header skeleton */}
    <Skeleton className="h-10 w-full rounded" />

    {/* Row skeletons */}
    {[...Array(6)].map((_, index) => (
      <Skeleton key={index} className="h-12 w-full rounded" />
    ))}

    {/* Summary row skeleton */}
    <Skeleton className="h-12 w-full rounded mt-4" />
  </div>
);

export default function SalesReportPage() {
  const router = useRouter();
  const { period: routePeriod } = router.query;
  const { toast } = useToast();
  const token = useAuthStore((state) => state.token);

  // Default to 'day' if no period is specified
  const [period, setPeriod] = useState<"day" | "week" | "month">("day");
  const [dateRange, setDateRange] = useState<string>("30");

  // Set period from route query parameter when available
  useEffect(() => {
    if (
      routePeriod &&
      ["day", "week", "month"].includes(routePeriod as string)
    ) {
      setPeriod(routePeriod as "day" | "week" | "month");

      // Set default range based on period
      if (routePeriod === "day") setDateRange("30");
      else if (routePeriod === "week") setDateRange("12");
      else setDateRange("12");
    }
  }, [routePeriod]);

  // Calculate date range based on selection
  const today = new Date();
  const endDate = format(today, "yyyy-MM-dd");
  let startDate: string;

  if (period === "day") {
    startDate = format(subDays(today, parseInt(dateRange)), "yyyy-MM-dd");
  } else if (period === "week") {
    startDate = format(subWeeks(today, parseInt(dateRange)), "yyyy-MM-dd");
  } else {
    startDate = format(subMonths(today, parseInt(dateRange)), "yyyy-MM-dd");
  }

  const { data, isLoading, isError, error } = useGetSalesReportQuery(
    token,
    period,
    dateRange,
    startDate,
    endDate
  );

  // Date range options based on selected period
  const dateRangeOptions: Record<string, DateRangeOption[]> = {
    day: [
      { label: "Last 7 days", value: "7" },
      { label: "Last 30 days", value: "30" },
      { label: "Last 60 days", value: "60" },
      { label: "Last 90 days", value: "90" },
    ],
    week: [
      { label: "Last 4 weeks", value: "4" },
      { label: "Last 8 weeks", value: "8" },
      { label: "Last 12 weeks", value: "12" },
      { label: "Last 24 weeks", value: "24" },
    ],
    month: [
      { label: "Last 3 months", value: "3" },
      { label: "Last 6 months", value: "6" },
      { label: "Last 12 months", value: "12" },
      { label: "Last 24 months", value: "24" },
    ],
  };

  if (isError) {
    toast({
      variant: "destructive",
      title: "Error",
      description: error?.message || "Failed to fetch sales data",
    });
  }

  // Format data for the chart
  const getChartData = () => {
    if (!data?.data) return [];

    return data.data.map((item: any) => {
      let label = item.label;

      // Format the label for display
      if (period === "day") {
        label = format(new Date(item.label), "dd MMM");
      } else if (period === "week") {
        label = `W${item.label.split("-W")[1]}`;
      } else if (period === "month") {
        const [year, month] = item.label.split("-");
        label = format(
          new Date(parseInt(year), parseInt(month) - 1, 1),
          "MMM yyyy"
        );
      }

      return {
        name: label,
        grossSales: item.gross_sales,
        netSales: item.net_sales,
      };
    });
  };

  const handlePeriodChange = (value: string) => {
    // Update URL with new period
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, period: value },
      },
      undefined,
      { shallow: true }
    );

    setPeriod(value as "day" | "week" | "month");

    // Reset date range to default for the selected period
    if (value === "day") setDateRange("30");
    else if (value === "week") setDateRange("12");
    else setDateRange("12");
  };

  const handleDateRangeChange = (value: string) => {
    setDateRange(value);
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format period-specific data for display in the table
  const formatLabel = (item: any) => {
    if (period === "day") {
      return format(new Date(item.label), "dd MMM yyyy");
    } else if (period === "week") {
      return `${item.label} (${format(
        new Date(item.week_start),
        "dd MMM"
      )} - ${format(new Date(item.week_end), "dd MMM yyyy")})`;
    } else {
      const [year, month] = item.label.split("-");
      return format(
        new Date(parseInt(year), parseInt(month) - 1, 1),
        "MMMM yyyy"
      );
    }
  };

  const getPageTitle = () => {
    return `Sales by ${period}`;
  };

  return (
    <AdminLayout>
      <Head>
        <title>Reports | Sales by {period}</title>
      </Head>
      <div className="p-6">
        {/* Back button */}
        <div className="mb-4">
          <Link href="/admin/reports">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Reports
            </Button>
          </Link>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold">{getPageTitle()}</h1>
            <p className="text-gray-500">
              View detailed reports on your{" "}
              {period !== "day" ? ` ${period}ly` : `daily`} sales
            </p>
          </div>
          <div className="flex items-center gap-4 flex-wrap">
            <Select value={period} onValueChange={handlePeriodChange}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">By Day</SelectItem>
                <SelectItem value="week">By Week</SelectItem>
                <SelectItem value="month">By Month</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                {dateRangeOptions[period].map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Sales Chart</CardTitle>
            <CardDescription>
              Sales performance from{" "}
              {format(new Date(startDate), "dd MMM yyyy")} to{" "}
              {format(new Date(endDate), "dd MMM yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              {isLoading ? (
                <ChartSkeleton />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={getChartData()}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis tickFormatter={(value) => `$${value}`} />
                    <Tooltip
                      formatter={(value: number, name: string) => {
                        return [
                          formatCurrency(value),
                          name === "Gross Sales" ? "Gross Sales" : "Net Sales",
                        ];
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="grossSales"
                      name="Gross Sales"
                      stroke="#6366f1"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="netSales"
                      name="Net Sales"
                      stroke="#22c55e"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sales Data</CardTitle>
            <CardDescription>
              Detailed breakdown of{" "}
              {period !== "day" ? ` ${period}ly` : `daily`} sales
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <TableSkeleton />
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      {period === "day"
                        ? "Date"
                        : period === "week"
                        ? "Week"
                        : "Month"}
                    </TableHead>
                    {period === "week" && <TableHead>Period</TableHead>}
                    <TableHead>Orders</TableHead>
                    <TableHead>Gross Sales</TableHead>
                    <TableHead>Discounts</TableHead>
                    <TableHead>Net Sales</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.data?.map((item: any) => (
                    <TableRow key={item.label}>
                      <TableCell>{formatLabel(item)}</TableCell>
                      {period === "week" && (
                        <TableCell>
                          {format(new Date(item.week_start), "dd MMM")} -{" "}
                          {format(new Date(item.week_end), "dd MMM yyyy")}
                        </TableCell>
                      )}
                      <TableCell>{item.orders}</TableCell>
                      <TableCell>{formatCurrency(item.gross_sales)}</TableCell>
                      <TableCell>
                        {formatCurrency(item.item_discounts)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(item.net_sales)}
                      </TableCell>
                    </TableRow>
                  ))}

                  {/* Summary row */}
                  {data?.summary && (
                    <TableRow className="bg-gray-50 font-bold">
                      <TableCell colSpan={period === "week" ? 2 : 1}>
                        Summary
                      </TableCell>
                      <TableCell>{data.summary.orders}</TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.gross_sales)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.item_discounts)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.net_sales)}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
