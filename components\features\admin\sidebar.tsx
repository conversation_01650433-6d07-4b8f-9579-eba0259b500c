import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useNavigationQuery } from "@/queries/component-queries";
import { ChevronDown, ChevronRight } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useState, useEffect } from "react";
import { navData } from "../store/nav";

interface SidebarProps {
  open: boolean;
  onClose: () => void;
  onToggle: () => void;
}

export function AdminSidebar({ open, onClose, onToggle }: SidebarProps) {
  const router = useRouter();
  const pathname = router.pathname;
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    {}
  );

  const navigationData = useNavigationQuery();
  const adminNav = navData.admin?.nav || [];

  const isActive = (link?: string) => {
    if (!link) return false;
    return (
      pathname === link.toLowerCase() ||
      pathname.startsWith(link.toLowerCase() + "/")
    );
  };

  const isGroupActive = (group: any) => {
    if (group.link && isActive(group.link)) return true;
    if (group.children) {
      return group.children.some((child: any) => isActive(child.link));
    }
    return false;
  };

  // Auto-expand active group on mount and when pathname changes
  useEffect(() => {
    const newExpandedGroups: Record<string, boolean> = {};

    adminNav.forEach((item) => {
      if (
        item.children &&
        item.children.some((child) => isActive(child.link))
      ) {
        newExpandedGroups[item.title] = true;
      }
    });

    setExpandedGroups((prev) => ({
      ...prev,
      ...newExpandedGroups,
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, adminNav]);

  const toggleGroup = (title: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  return (
    <Sidebar
      collapsible="icon"
      className={cn(
        "max-h-screen border-r bg-white shadow-sm transition-all duration-200 ease-in-out",
        open ? "min-w-72 max-w-72" : "w-14"
      )}
    >
      <SidebarHeader>
        <div className="w-full flex h-14 items-center justify-center border-b">
          <Link
            href="/admin/dashboard"
            className={cn(
              "flex items-center transition-opacity",
              !open && "justify-center"
            )}
          >
            {navigationData.data?.data?.variants?.logo ? (
              <img
                src={navigationData.data?.data?.variants?.logo?.image ?? ""}
                alt="Maxton Admin"
                className={cn("h-6", !open && "mx-auto")}
              />
            ) : (
              <h2 className={cn("text-xl font-bold px-4", !open && "sr-only")}>
                Maxton
              </h2>
            )}
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel
            className={cn(
              "text-xs uppercase font-semibold text-muted-foreground tracking-wider px-4 pt-3 pb-1",
              !open && "sr-only"
            )}
          >
            Ecommerce
          </SidebarGroupLabel>

          <SidebarGroupContent className="flex flex-col">
            <SidebarMenu>
              {adminNav.map((item) => (
                <SidebarMenuItem key={item.title}>
                  {!item.children ? (
                    // Regular menu item without children
                    <SidebarMenuButton asChild>
                      <Link
                        data-active={isActive(item.link)}
                        href={item.link || "#"}
                        className={cn(
                          "flex items-center hover:bg-gray-100 transition-colors",
                          open ? "px-4 py-2" : "p-2.5 justify-center",
                          isActive(item.link) && "bg-gray-100"
                        )}
                        title={!open ? item.title : undefined}
                      >
                        {item.icon && (
                          <item.icon
                            className={cn(
                              "h-5 w-5 text-gray-600",
                              open && "mr-2"
                            )}
                          />
                        )}
                        <span
                          className={cn(
                            "text-sm font-medium text-gray-700",
                            !open && "sr-only"
                          )}
                        >
                          {item.title}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  ) : (
                    // Dropdown group with children
                    <>
                      <SidebarMenuButton
                        className={cn(
                          "flex w-full hover:bg-gray-100 transition-colors",
                          open
                            ? "px-4 py-2 justify-between"
                            : "p-2.5 justify-center",
                          isGroupActive(item) && "bg-gray-100"
                        )}
                        onClick={() => toggleGroup(item.title)}
                      >
                        <div
                          className={cn(
                            "flex items-center",
                            !open && "justify-center"
                          )}
                        >
                          {item.icon && (
                            <item.icon
                              className={cn(
                                "h-5 w-5 text-gray-600",
                                open && "mr-3"
                              )}
                            />
                          )}
                          <span
                            className={cn(
                              "text-sm font-medium text-gray-700",
                              !open && "sr-only"
                            )}
                          >
                            {item.title}
                          </span>
                        </div>
                        {open && (
                          <ChevronRight
                            className={cn(
                              "h-4 w-4 text-gray-500 transition-transform duration-200",
                              expandedGroups[item.title] && "rotate-90"
                            )}
                          />
                        )}
                      </SidebarMenuButton>

                      {/* Using SidebarMenuSub for nested items */}
                      {expandedGroups[item.title] && open && (
                        <SidebarMenuSub className="border-l-0 pl-7 pr-2 py-0">
                          {item.children.map((child) => (
                            <SidebarMenuItem key={child.title}>
                              <SidebarMenuButton asChild>
                                <Link
                                  data-active={isActive(child.link)}
                                  href={child.link || "#"}
                                  className={cn(
                                    "flex items-center justify-between text-sm hover:bg-gray-100 py-2 px-3",
                                    isActive(child.link) && "bg-gray-100"
                                  )}
                                >
                                  <div className="flex items-center">
                                    {child.icon && (
                                      <child.icon className="mr-2 h-4 w-4 text-gray-600" />
                                    )}
                                    <span className="font-medium text-gray-700">
                                      {child.title}
                                    </span>
                                  </div>
                                  {child.badge !== undefined && (
                                    <Badge variant="outline" className="ml-2">
                                      {child.badge}
                                    </Badge>
                                  )}
                                </Link>
                              </SidebarMenuButton>
                            </SidebarMenuItem>
                          ))}
                        </SidebarMenuSub>
                      )}

                      {/* Collapsed state for active child */}
                      {!open && isGroupActive(item) && (
                        <div className="py-1">
                          {item.children.map((child) =>
                            isActive(child.link) ? (
                              <SidebarMenuButton
                                key={child.title}
                                asChild
                                className="w-full"
                              >
                                <Link
                                  href={child.link || "#"}
                                  className="flex justify-center p-2 bg-gray-100"
                                  title={child.title}
                                >
                                  {child.icon && (
                                    <child.icon className="h-4 w-4 text-gray-600" />
                                  )}
                                </Link>
                              </SidebarMenuButton>
                            ) : null
                          )}
                        </div>
                      )}
                    </>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
