import { cn } from "@/lib/utils";
import { Minus, Plus } from "lucide-react";
import { Button } from "./shadcn-button";

interface QuantitySelectorProps {
    quantity: number;
    onDecrement: () => void;
    onIncrement: () => void;
    className?: string;
}

export function QuantitySelector({ quantity, onDecrement, onIncrement, className }: QuantitySelectorProps) {
    return (
        <div className={cn("flex items-center justify-center", className)}>
            <div className="flex items-center rounded-md border">
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-10 rounded-r-none"
                    onClick={onDecrement}
                    disabled={quantity <= 1}
                >
                    <Minus className="h-5 w-5" />
                    <span className="sr-only">Decrease quantity</span>
                </Button>
                <div className="h-8 w-12 flex items-center justify-center text-center tabular-nums border-l border-r bg-background text-sm">
                    {quantity}
                </div>
                <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-10 rounded-l-none"
                    onClick={onIncrement}
                >
                    <Plus className="h-5 w-5" />
                    <span className="sr-only">Increase quantity</span>
                </Button>
            </div>
        </div>
    );
} 