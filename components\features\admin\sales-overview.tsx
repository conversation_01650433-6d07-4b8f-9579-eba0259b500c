"use client";

import { SalesData } from "@/pages/api/admin/dashboard";
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    payload: SalesData;
  }>;
  label?: string;
}

const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 dark:text-gray-100">{`${label}`}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          <span className="inline-block w-3 h-3 bg-primary rounded-full mr-2"></span>
          Orders: <span className="font-semibold text-primary">{data.value}</span>
        </p>
      </div>
    );
  }
  return null;
};

const EmptyState = () => (
  <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
    <div className="text-4xl mb-2">📊</div>
    <p className="text-sm font-medium">No data available</p>
    <p className="text-xs text-gray-400 dark:text-gray-500">Orders will appear here once available</p>
  </div>
);

export function SalesOverview({ salesData }: { salesData: SalesData[] }) {
  // Handle empty or invalid data
  if (!salesData || salesData.length === 0) {
    return (
      <div className="w-full h-[350px] flex items-center justify-center">
        <EmptyState />
      </div>
    );
  }

  // Check if all values are zero
  const hasData = salesData.some(item => item.total > 0);

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart
        data={salesData}
        margin={{
          top: 20,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <defs>
          <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#1155a3" stopOpacity={0.8}/>
            <stop offset="100%" stopColor="#1155a3" stopOpacity={0.3}/>
          </linearGradient>
        </defs>

        <CartesianGrid
          strokeDasharray="3 3"
          stroke="#e5e7eb"
          strokeOpacity={0.5}
          className="dark:stroke-gray-600"
        />

        <XAxis
          dataKey="name"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          className="dark:stroke-gray-400"
          tick={{ fill: '#6b7280' }}
        />

        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          className="dark:stroke-gray-400"
          tick={{ fill: '#6b7280' }}
          tickFormatter={(value) => value.toString()}
          label={{
            value: 'Orders',
            angle: -90,
            position: 'insideLeft',
            style: { textAnchor: 'middle', fill: '#6b7280', fontSize: '12px' }
          }}
        />

        <Bar
          dataKey="total"
          fill="url(#colorGradient)"
          radius={[6, 6, 0, 0]}
          className="hover:opacity-80 transition-opacity duration-200"
          stroke="#1155a3"
          strokeWidth={1}
        />

        <Tooltip
          content={<CustomTooltip />}
          cursor={{ fill: 'rgba(17, 85, 163, 0.1)' }}
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
