import StoreLayout from "@/components/features/store/layout";
import {
  OrderDataWithStatusAndItems,
  OrderStatusBadge,
} from "@/components/features/store/orders-table";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatPrice } from "@/lib/utils";
import {
  useGetCustomerDashboardDataQuery,
  useGetCustomerQuery,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import {
  ArrowRight,
  Package,
  ShoppingCart,
  Clock,
  CreditCard,
} from "lucide-react";
import Link from "next/link";
import Head from "next/head";
import { formatId } from "@/lib/utils/order-helpers";

export default function Dashboard() {
  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const customerDashboardData = useGetCustomerDashboardDataQuery(userId || "");

  return (
    <StoreLayout>
      <Head>
        <title>Dashboard</title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section with Welcome */}
          <div className="flex flex-col space-y-6">
            <div className="flex flex-col space-y-2">
              <h2 className="text-3xl font-bold tracking-tight">
                Welcome back, {userData?.email?.split("@")[0] || "Customer"}
              </h2>
              <p className="text-muted-foreground">
                Here's an overview of your account and recent activity
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <span className="text-primary">Customer Account</span>
              </Badge>
              <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                <Clock className="mr-2 h-4 w-4" /> Last login:{" "}
                {new Date().toLocaleDateString()}
              </Badge>
            </div>
          </div>

          {/* Quick Stats Grid */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            {customerDashboardData.isLoading ? (
              <>
                <QuickStatSkeleton />
                <QuickStatSkeleton />
                <QuickStatSkeleton />
                <QuickStatSkeleton />
              </>
            ) : (
              <>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Orders
                    </CardTitle>
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {customerDashboardData.data?.totalOrders || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Orders placed
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Available Products
                    </CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {customerDashboardData.data?.totalProducts || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Products to browse
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Recent Order
                    </CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatPrice(
                        customerDashboardData.data?.recentOrders?.[0]
                          ?.total_amount || 0
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Last purchase amount
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Account Status
                    </CardTitle>
                    <Badge variant="default">Active</Badge>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">Customer</div>
                    <p className="text-xs text-muted-foreground">
                      Account type
                    </p>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            <Card className="hover:shadow-md transition-all">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2"
                  asChild
                >
                  <Link href="/store/products">
                    <Package className="h-6 w-6" />
                    <span>Browse Products</span>
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex flex-col items-center justify-center space-y-2"
                  asChild
                >
                  <Link href="/store/orders">
                    <ShoppingCart className="h-6 w-6" />
                    <span>View Orders</span>
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Account Summary */}
            <Card className="hover:shadow-md transition-all">
              <CardHeader>
                <CardTitle>Account Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Email</span>
                  <span className="font-medium">{userData?.email}</span>
                </div>
                {customerDashboardData.isLoading ? (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Member Since
                    </span>
                    <Skeleton className="h-6 w-24" />
                    {/* <span className="font-medium">{new Date(customerDashboardData?.data?.memberSince || '').toLocaleDateString()}</span> */}
                  </div>
                ) : (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Member Since
                    </span>
                    <span className="font-medium">
                      {new Date(
                        customerDashboardData?.data?.memberSince || ""
                      ).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Orders Section */}
          {customerDashboardData.isLoading ? (
            <RecentOrdersSkeleton />
          ) : customerDashboardData.data?.recentOrders?.length ? (
            <Card className="hover:shadow-md transition-all">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-xl">Recent Orders</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Your latest purchases and their status
                  </p>
                </div>
                <Button variant="ghost" size="sm" className="ml-auto" asChild>
                  <Link href="/store/orders">
                    View All
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customerDashboardData.data?.recentOrders
                    ?.slice(0, 5)
                    .map((order: OrderDataWithStatusAndItems) => {
                      const statuses = order.order_statuses?.sort((a, b) => {
                        let aDate = new Date(a.created_at);
                        let bDate = new Date(b.created_at);

                        return bDate.getTime() - aDate.getTime(); // Sort in descending order (newest first)
                      });
                      const latestStatus = statuses.at(0);
                      const status = latestStatus?.status ?? "pending";
                      const totalAmount = order.total_amount ?? 0;

                      return (
                        <div
                          key={order.id}
                          className="flex items-center justify-between p-4 hover:bg-muted/50 rounded-lg transition-colors border"
                        >
                          <div className="space-y-1">
                            <div className="font-medium">
                              Order #{formatId(order.id)}
                            </div>
                            <div className="text-sm text-muted-foreground flex items-center gap-2">
                              {formatPrice(totalAmount)} •{" "}
                              <OrderStatusBadge status={status} />
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="ml-auto"
                            asChild
                          >
                            <Link href={`/store/orders/${order.id}`}>
                              <ArrowRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No Orders Yet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Start shopping to see your orders here
                </p>
                <Button asChild>
                  <Link href="/store/products">Browse Products</Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </StoreLayout>
  );
}

function QuickStatSkeleton() {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-4 w-4 rounded-full" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-7 w-[60px] mb-1" />
        <Skeleton className="h-4 w-[100px]" />
      </CardContent>
    </Card>
  );
}

function RecentOrdersSkeleton() {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-6 w-[150px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
        <Skeleton className="h-9 w-[100px]" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <OrderItemSkeleton />
          <OrderItemSkeleton />
          <OrderItemSkeleton />
        </div>
      </CardContent>
    </Card>
  );
}

function OrderItemSkeleton() {
  return (
    <div className="flex items-center justify-between p-4 rounded-lg border">
      <div className="space-y-2">
        <Skeleton className="h-5 w-[100px]" />
        <Skeleton className="h-4 w-[150px]" />
      </div>
      <Skeleton className="h-8 w-8 rounded-full" />
    </div>
  );
}
