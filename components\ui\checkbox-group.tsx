import * as React from "react";
import { CheckedState } from "@radix-ui/react-checkbox";
import { Checkbox } from "./checkbox";
import { cn } from "@/lib/utils";

interface CheckboxGroupContextValue {
  value: string[];
  onValueChange: (value: string[]) => void;
}

const CheckboxGroupContext = React.createContext<CheckboxGroupContextValue | undefined>(undefined);

interface CheckboxGroupProps {
  value: string[];
  onValueChange: (value: string[]) => void;
  className?: string;
  children: React.ReactNode;
}

export function CheckboxGroup({
  value,
  onValueChange,
  className,
  children,
}: CheckboxGroupProps) {
  return (
    <CheckboxGroupContext.Provider value={{ value, onValueChange }}>
      <div className={cn("space-y-2", className)}>{children}</div>
    </CheckboxGroupContext.Provider>
  );
}

interface CheckboxItemProps {
  value: string;
  id: string;
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
}

export function CheckboxItem({
  value,
  id,
  className,
  children,
  disabled,
}: CheckboxItemProps) {
  const context = React.useContext(CheckboxGroupContext);

  if (!context) {
    throw new Error("CheckboxItem must be used within a CheckboxGroup");
  }

  const { value: selectedValues, onValueChange } = context;
  const checked = selectedValues.includes(value);

  const handleCheckedChange = (checked: CheckedState) => {
    if (checked) {
      onValueChange([...selectedValues, value]);
    } else {
      onValueChange(selectedValues.filter((v) => v !== value));
    }
  };

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <Checkbox
        id={id}
        checked={checked}
        onCheckedChange={handleCheckedChange}
        disabled={disabled}
      />
      <label
        htmlFor={id}
        className={cn(
          "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          disabled && "cursor-not-allowed opacity-70"
        )}
      >
        {children}
      </label>
    </div>
  );
} 