import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase";
import { Product } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: getAllProductsHandler,
  POST: checkAdmin(checkPermission("create:products", createProductHandler)),
});

export interface GetAllProductsResponse {
  error?: string;
  products?: Product[];
  total?: number;
  totalPages?: number;
  categories?: any[];
}

async function getAllProductsHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetAllProductsResponse>,
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;
  const category = query.category ? (query.category as string) : null;
  const search = query.search ? (query.search as string) : null;

  try {
    // First, fetch all unique categories for the response
    const { data: categoriesData, error: categoriesError } =
      await supabaseClient
        .from("categories")
        .select("id, name, value, parent_category_id")
        .order("name");

    const categories = categoriesData || [];

    if (categoriesError) {
      return res.status(400).json({ error: categoriesError.message });
    }

    // If a search term is provided, we need to search across all products
    if (search) {
      const searchLower = search.toLowerCase();

      // First, get all products (without pagination) - For admin, don't filter by draft or available status
      const { data: allProducts, error: searchError } = await supabaseClient
        .from("products")
        .select(
          `
                    *,
                    product_categories (
                        category_data:category_id (
                            id, name, value, parent_category_id
                        )
                    ),
                    product_arrangements (
                      id, product_id, category_id, position
                    ),
                    options
                `,
        );

      if (searchError) {
        return res.status(400).json({ error: searchError.message });
      }

      // Handle category filter if provided
      let filteredProducts = allProducts || [];
      if (category) {
        filteredProducts = filteredProducts.filter((product) =>
          product.product_categories.some(
            (cat) =>
              cat.category_data?.name === category ||
              // Also check for subcategories
              (cat.category_data?.parent_category_id &&
                categories.some(
                  (c) =>
                    c.id === cat.category_data?.parent_category_id &&
                    c.name === category,
                )),
          ),
        );
      }

      // Filter by search term
      const searchResults = filteredProducts.filter((product) => {
        // Search in product name
        if (product.name && product.name.toLowerCase().includes(searchLower))
          return true;

        // Search in product SKU
        if (product.sku && product.sku.toLowerCase().includes(searchLower))
          return true;

        // Search functionality limited to only name and SKU for more relevant results
        // Description, tags and categories searches have been removed

        return false;
      });

      // Apply pagination to search results
      const paginatedResults = searchResults.slice(
        (page - 1) * limit,
        page * limit,
      );
      const total = searchResults.length;

      return res.status(200).json({
        products: paginatedResults,
        categories,
        total,
        totalPages: Math.ceil(total / limit),
      });
    }

    if (category) {
      // First, get all products for the main category
      const { data: mainCategoryProducts, error: mainCategoryError } =
        await supabaseClient
          .from("products")
          .select(
            `
                    *,
                    product_categories!inner (
                        category_id!inner (
                            id, name, value, parent_category_id
                        )
                    ),
                    product_arrangements (
                      id, product_id, category_id, position
                    ),
                    options
                `,
          )
          .filter("product_categories.category_id.name", "eq", category)
          .eq("draft", false)
          .eq("available", true);

      if (mainCategoryError) {
        return res.status(400).json({ error: mainCategoryError.message });
      }

      // Check if we have subcategories
      const subcategories = categories.filter((cat) => {
        return (
          cat.parent_category_id &&
          categories.some(
            (p) => p.id === cat.parent_category_id && p.name === category,
          )
        );
      });

      // If no subcategories, just return the main category products with pagination
      if (subcategories.length === 0) {
        const paginatedProducts = (mainCategoryProducts || []).slice(
          (page - 1) * limit,
          page * limit,
        );
        const total = (mainCategoryProducts || []).length;

        return res.status(200).json({
          products: paginatedProducts,
          categories,
          total,
          totalPages: Math.ceil(total / limit),
        });
      }

      // Get all subcategory products individually to avoid filter issues
      let allSubcategoryProducts: any[] = [];

      // Process subcategories one by one to avoid complex filters
      for (const subcat of subcategories) {
        if (!subcat.name) continue;

        const { data: subcatProducts, error: subcatError } =
          await supabaseClient
            .from("products")
            .select(
              `
                        *,
                        product_categories!inner (
                            category_id!inner (
                                id, name, value, parent_category_id
                            )
                        ),
                        product_arrangements (
                          id, product_id, category_id, position
                        ),
                        options
                    `,
            )
            .filter("product_categories.category_id.name", "eq", subcat.name)
            .eq("draft", false)
            .eq("available", true);

        if (subcatError) {
          console.error(
            `Error fetching products for subcategory ${subcat.name}:`,
            subcatError,
          );
          continue; // Skip this subcategory but continue with others
        }

        if (subcatProducts && subcatProducts.length > 0) {
          allSubcategoryProducts = [
            ...allSubcategoryProducts,
            ...subcatProducts,
          ];
        }
      }

      // Combine all products and remove duplicates
      const productMap = new Map();

      // Add main category products first
      (mainCategoryProducts || []).forEach((product) => {
        productMap.set(product.id, product);
      });

      // Add subcategory products (if not already added)
      allSubcategoryProducts.forEach((product) => {
        if (!productMap.has(product.id)) {
          productMap.set(product.id, product);
        }
      });

      // Convert map back to array
      const allProducts = Array.from(productMap.values());

      // Apply pagination to the combined results
      const paginatedProducts = allProducts.slice(
        (page - 1) * limit,
        page * limit,
      );
      const total = allProducts.length;

      return res.status(200).json({
        products: paginatedProducts,
        categories,
        total,
        totalPages: Math.ceil(total / limit),
      });
    } else {
      // For all products, use left joins so we get everything
      const productQuery = supabaseClient
        .from("products")
        .select(
          `
                    *,
                    product_categories (
                        category_data:category_id (
                            id, name, value, parent_category_id
                        )
                    ),
                    product_arrangements (
                      id, product_id, category_id, position
                    ),
                    options
                `,
          { count: "exact" },
        )
        .range((page - 1) * limit, page * limit - 1)
        .order("updated_at", { ascending: false });

      const { data, count: totalProducts, error } = await productQuery;

      if (error) {
        return res.status(400).json({ error: error.message });
      }

      const total = totalProducts ?? 0;
      const totalPages = Math.ceil(total / limit);

      return res.status(200).json({
        products: data,
        categories,
        total,
        totalPages,
      });
    }
  } catch (error: any) {
    return res.status(400).json({ error: error.message });
  }
}

export const createProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  price: z.number().min(0, "Price must be a positive number"),
  sku: z.string().nullable().optional(),
  available: z.boolean().default(true),
  is_quote: z.boolean().default(false),
  image: z.string().nullable().optional(),
  additional_images: z.array(z.string()).nullable().optional(),
  draft: z.boolean().default(false),
  variant: z.string().nullable().optional(),
  options: z.array(z.any()).nullable().optional(),
  specifications: z.any().nullable().optional(),
  helpful_hints: z.any().nullable().optional(),
  installation_instructions: z.any().nullable().optional(),
  delivery_and_shipping: z.any().nullable().optional(),
  warranty: z.any().nullable().optional(),
  user_id: z.string().nullable().optional(),
  category_id: z.string().nullable().optional(),
  group_prices: z
    .array(
      z.object({
        group_id: z.string(),
        custom_price: z.number(),
      }),
    )
    .nullable()
    .optional(),
  tags: z.array(z.string()).nullable().optional(),
});

type CreateProductRequest = z.infer<typeof createProductSchema>;

export interface CreateProductResponse {
  error?: string;
  product?: Product;
}

async function createProductHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateProductResponse>,
) {
  try {
    const validation = createProductSchema.safeParse(req.body);

    if (!validation.success) {
      return res.status(400).json({ error: validation.error.message });
    }

    const productData = validation.data;
    // Extract the category_id before creating the product
    const { category_id, group_prices, ...productFields } = productData;

    const supabaseAdminClient = createSupabaseAdminClient();

    // First, create the product
    const { data, error } = await supabaseAdminClient
      .schema("public")
      .from("products")
      .insert(productFields)
      .select()
      .single();

    if (error) {
      console.error("Error creating product:", error);
      return res.status(400).json({ error: error.message });
    }

    // If a category was selected, create the product_category relationship
    if (category_id && data) {
      const { error: categoryError } = await supabaseAdminClient
        .from("product_categories")
        .insert({
          product_id: data.id,
          category_id: category_id,
        });

      if (categoryError) {
        console.error(
          "Error creating product category relationship:",
          categoryError,
        );
      }
    }

    if (group_prices && data) {
      const group_prices_data = group_prices.map((price: any) => ({
        product_id: data.id,
        group_id: price.group_id,
        custom_price: price.custom_price,
        hash: `${data.id}-${price.group_id}`,
      }));

      const { error: groupPriceError } = await supabaseAdminClient
        .from("product_group_prices")
        .insert(group_prices_data);

      if (groupPriceError) {
        console.error("Error creating product group prices:", groupPriceError);
      }
    }

    return res.status(201).json({ product: data });
  } catch (error) {
    return res.status(400).json({ error: "Failed to create product" });
  }
}
