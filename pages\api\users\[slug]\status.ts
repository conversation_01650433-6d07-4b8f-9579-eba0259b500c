import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createAccountStatusTemplate } from "@/lib/mailer/templates";
import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    POST: checkPermission("update:users", updateUserStatusHandler),
  })
);

export interface UpdateUserStatusResponse {
  error?: string;
  id?: string;
}

const updateUserStatusSchema = z.object({
  status: z.enum(["approved", "pending", "rejected", "banned"]),
  notes: z.string().optional(),
});

export type UpdateUserStatusRequest = z.infer<typeof updateUserStatusSchema>;

async function updateUserStatusHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateUserStatusResponse>
) {
  const { slug } = req.query;
  const id = slug?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing id" });
  }

  const status = updateUserStatusSchema.safeParse(req.body);

  if (!status.success) {
    return res.status(400).json({ error: status.error.issues[0].message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const now = new Date();
  const updatedAt = now.toISOString();

  if (status.data.status === "rejected") {
    const getUser = await supabaseAdminClient
      .schema("public")
      .from("users")
      .select("*")
      .eq("id", id)
      .single();

    if (getUser.error) {
      return res.status(400).json({ error: getUser.error.message });
    }

    const { data: userDetail } = getUser;

    const { data: customerData, error: customerError } =
      await supabaseAdminClient
        .schema("public")
        .from("customers")
        .select("*")
        .eq("user_id", id)
        .single();

    if (customerError) {
      return res.status(400).json({ error: customerError.message });
    }

    await supabaseAdminClient
      .schema("public")
      .from("rejected_users")
      .insert({
        first_name: userDetail.first_name,
        last_name: userDetail.last_name,
        full_name: `${userDetail.first_name} ${userDetail.last_name}`.trim(),
        email: userDetail.email,
        company: customerData.company_name,
        notes: status.data.notes,
      });

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(200).json({ id });
  }

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("users")
    .update({
      status: status.data.status,
      updated_at: updatedAt,
      notes: status.data.notes || null,
    })
    .eq("id", id)
    .select("id")
    .single();

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  const { data: _customerData, error: customerError } =
    await supabaseAdminClient
      .schema("public")
      .from("customers")
      .update({ status: status.data.status, updated_at: updatedAt })
      .eq("user_id", id)
      .select("id")
      .single();

  if (customerError) {
    return res.status(400).json({ error: customerError.message });
  }

  const userDetails = await supabaseAdminClient
    .schema("public")
    .from("users")
    .select("*")
    .eq("id", id)
    .single();

  if (userDetails.error) {
    await supabaseAdminClient
      .schema("public")
      .from("users")
      .update({ status: "pending" })
      .eq("id", id);

    return res.status(400).json({ error: userDetails.error.message });
  }

  const customerDetails = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("*")
    .eq("user_id", id)
    .single();

  const businessDetails = await supabaseAdminClient
    .schema("public")
    .from("business_details")
    .select("*")
    .eq("user_id", id)
    .single();

  let addressData = {
    address: "",
    city: "",
    state: "",
    zip_code: "",
    country: "",
  };

  if (customerDetails.data?.id) {
    const addressDetails = await supabaseAdminClient
      .schema("public")
      .from("billing_addresses")
      .select("*")
      .eq("customer_id", customerDetails.data.id)
      .eq("default", true)
      .single();

    if (addressDetails.data) {
      addressData = {
        address: addressDetails.data.address || "",
        city: addressDetails.data.city || "",
        state: addressDetails.data.state || "",
        zip_code: addressDetails.data.zip_code || "",
        country: addressDetails.data.country || "",
      };
    }
  }

  const email = userDetails.data?.email;

  if (email && status.data.status === "approved") {
    const firstName = userDetails.data?.first_name || "";
    const lastName = userDetails.data?.last_name || "";
    const fullName = `${firstName} ${lastName}`.trim();

    const elevatorCount = businessDetails.data?.number_of_elevators;
    const numberOfElevators =
      elevatorCount !== null ? elevatorCount : undefined;

    const emailTemplate = createAccountStatusTemplate({
      to: email,
      name: fullName,
      email: userDetails.data?.email ?? "",
      accountId: userDetails.data?.id,
      accountType: "customer",
      address: addressData.address,
      city: addressData.city,
      state: addressData.state,
      zip_code: addressData.zip_code,
      country: addressData.country,
      companyName: customerDetails.data?.company_name ?? "",
      businessType: businessDetails.data?.business_type ?? "",
      businessNature: businessDetails.data?.business_nature ?? "",
      website:
        businessDetails.data?.website ??
        customerDetails.data?.company_website ??
        "",
      maxtonAccount:
        businessDetails.data?.maxton_account ??
        customerDetails.data?.customer_number ??
        "",
      numberOfElevators: numberOfElevators,
      status: status.data.status,
      statusDate: new Date().toISOString(),
      loginUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/log-in`,
      additionalNotes: "Thank you for choosing Maxton Manufacturing Company.",
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  }

  return res.status(200).json({ id: data?.id });
}
