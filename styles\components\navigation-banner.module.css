/* Top banner styles */
.topBanner {
  background-color: var(--primary-color, #1155a3);
  color: white;
}

/* Smaller language dropdown for the banner */
.topBanner .desktop-dropdown .selected-option {
  min-width: 120px;
  padding: 4px 4px;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.topBanner .desktop-dropdown .dropdown-arrow {
  font-size: 10px;
}

.topBanner .desktop-options {
  min-width: 180px;
}

.topBanner .desktop-options .dropdown-option {
  font-size: 12px;
}

/* Cart badge */
.cartBadge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary-color, #3476b5);
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom Language Dropdown Styling */
.customDropdownContainer {
  position: relative;
  width: 100%;
  z-index: 50; /* Increased z-index to appear above logo */
}

.selectedOption {
  display: flex;
  align-items: center;
  padding: 8px 0px;
  background-color: rgb(255, 255, 255, 0.5);
  color: black;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}

.desktopDropdown .selectedOption {
  min-width: 160px;
  padding: 8px;
  font-size: 14px;
  border-radius: 4px;
  border: none;
  background-color: white;
}

.mobileDropdown .selectedOption {
  border: none;
}

.selectedOption img {
  width: 25px;
  height: 25px;
  margin-right: 8px;
}

.selectedOption .dropdownArrow {
  margin-left: auto;
}

.optionsContainer {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: none;
  z-index: 50; /* Increased z-index */
}

.dropdownOption {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dropdownOption:hover {
  background-color: #f5f5f5;
}

.dropdownOption img {
  width: 25px;
  height: 25px;
  margin-right: 8px;
}

/* Desktop-specific dropdown styling */
.desktopDropdown .selectedOption {
  min-width: 160px;
  padding: 8px;
  font-size: 14px;
  border-radius: 4px;
  border: none;
  background-color: white;
}

.desktopDropdown .dropdownArrow {
  font-size: 12px;
  color: #666;
}

.desktopOptions {
  min-width: 220px;
  overflow-y: auto;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.desktopOptions .dropdownOption {
  font-size: 14px;
  padding: 8px;
}

.desktopOptions .dropdownOption:last-child {
  border-bottom: none;
}

.desktopOptions .dropdownOption:hover {
  background-color: #f5f5f5;
}
