import { Badge } from "@/components/ui/badge";
import { Card, CardFooter, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useGetImage } from "@/queries/customer-queries";
import useCartStore, { SelectedOption } from "@/stores/cart-store";
import { OptionSelectProps } from "@/supabase/types";
import { FolderIcon, Tag } from "lucide-react";
import Link from "next/link";
import { useEffect, useState, useMemo } from "react";
import { ProductOptionProps } from "./product-option";
import useAuthStore from "@/stores/auth-store";
import { CustomerWithRelations } from "@/pages/api/customers/[id]";

interface ProductVariant {
  id: string;
  name: string;
  image: string;
  sku: string;
}

interface ProductCardProps {
  id: string;
  name: string;
  sku: string;
  price: number;
  discount?: number;
  image: string;
  variants: ProductVariant[];
  slug: string;
  categories?: string[];
  options?: ProductOptionProps[];
  customerData?: CustomerWithRelations | null;
  productCategories?: any[];
  isQuote?: boolean;
  currentCategory?: string | null;
}

export function ProductCard({
  id,
  name,
  sku,
  price,
  discount,
  image,
  variants,
  slug,
  categories,
  options,
  customerData,
  productCategories,
  isQuote,
  currentCategory,
}: ProductCardProps) {
  const discountedPrice = price >= 0 ? price * (1 - (discount ?? 0) / 100) : -1;
  const [selectedVariant, setSelectedVariant] = useState<string>(
    variants[0]?.id || ""
  );
  const [previewImage, setPreviewImage] = useState(image);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOption[]>([]);
  const [optionErrors, setOptionErrors] = useState<{ [key: string]: boolean }>(
    {}
  );
  const addItem = useCartStore((state) => state.addItem);
  const { toast } = useToast();

  useEffect(() => {
    if (options && options.length > 0) {
      const defaultOptions = options.map((option) => {
        const defaultValue = option.options?.at(0) ?? null;

        return {
          name: option.name,
          value: defaultValue?.name ?? "",
          price: defaultValue?.price ?? 0,
        };
      });
      setSelectedOptions(defaultOptions);
    }
  }, [options]);

  const productUrl = useGetImage(image);
  const productImage = productUrl.data ?? image ?? "";

  // const [quantity, setQuantity] = useState(1)
  // const incrementQuantity = () => setQuantity((prev) => prev + 1)
  // const decrementQuantity = () => setQuantity((prev) => Math.max(1, prev - 1))

  // Check if the customer has access to this product's category
  const customerCategories = useMemo(() => {
    return customerData?.categories ?? [];
  }, [customerData]);

  const customerCategoriesId = useMemo(() => {
    return customerCategories.map((category) => category.id);
  }, [customerCategories]);

  const hasCategory = useMemo(() => {
    if (!productCategories || productCategories.length === 0) return false;
    return productCategories.some((category) =>
      customerCategoriesId.includes(category.category_id?.id ?? "")
    );
  }, [productCategories, customerCategoriesId]);

  // Update preview image when variant is selected
  useEffect(() => {
    const variant = variants.find((v) => v.id === selectedVariant);
    if (variant) {
      setPreviewImage(variant.image);
    }
  }, [selectedVariant, variants]);

  const handleOptionChange = (option: OptionSelectProps) => {
    const newSelectedOption: SelectedOption = {
      name: option.name,
      value: option.value,
      price: option.price,
    };

    setSelectedOptions((prev) => {
      // Check if this option name already exists
      const existingOptionIndex = prev.findIndex(
        (opt) => opt.name === option.name
      );

      if (existingOptionIndex >= 0) {
        // Update existing option
        const updatedOptions = [...prev];
        updatedOptions[existingOptionIndex] = newSelectedOption;
        return updatedOptions;
      } else {
        // Add new option
        return [...prev, newSelectedOption];
      }
    });

    // Clear error for this option if it was previously marked as error
    if (optionErrors[option.name]) {
      setOptionErrors((prev) => ({
        ...prev,
        [option.name]: false,
      }));
    }
  };

  const handleAddToCart = () => {
    const selected = variants.find((v) => v.id === selectedVariant) || {
      id,
      sku,
      name,
      image,
    }; // Fallback to product details if no variant

    // Check for unselected options and set errors
    let hasErrors = false;
    const newErrors: { [key: string]: boolean } = {};

    options?.forEach((option) => {
      const isSelected = selectedOptions.some(
        (opt) => opt.name === option.name && opt.value !== ""
      );

      if (!isSelected) {
        newErrors[option.name] = true;
        hasErrors = true;
      }
    });

    setOptionErrors(newErrors);

    if (hasErrors) {
      return;
    }

    addItem({
      id: selected.id,
      name,
      price,
      quantity: 1,
      sku: selected.sku,
      image: selected.image,
      selected: false,
      discount: discount,
      selectedOptions: selectedOptions,
    });

    toast({
      title: "Added to cart",
      description: `${name} - ${selected.sku} added to your cart.`,
      variant: "success",
    });
  };

  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const visitorGroup = !!customerData?.group?.name
    ?.toLowerCase()
    .includes("visitor");

  // Create the href with "from" parameter based on current category context
  const productHref = currentCategory
    ? `/store/products/${sku}?from=${encodeURIComponent(currentCategory)}`
    : `/store/products/${sku}?from=all`;

  return (
    <Card className="h-full min-w-xs flex flex-col justify-between border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 !rounded-none group">
      <Link href={productHref} target="_self" className=" h-full flex flex-col">
        <div className="relative p-4 flex flex-col flex-grow group">
          {productUrl.isLoading ? (
            <Skeleton className="aspect-square w-full h-64 !rounded-none" />
          ) : (
            <div className="flex flex-col h-full">
              <div className="w-full h-64 overflow-hidden mb-4 border-b pb-4">
                <img
                  src={productImage}
                  alt={name}
                  className="object-contain w-full h-full transition-transform duration-300 group-hover:scale-105"
                />
              </div>
              <div className="flex flex-col gap-2">
                <h3 className="font-medium text-base group-hover:text-primary transition-colors">
                  {name}
                </h3>
                <p className="text-sm text-secondary">SKU: {sku}</p>
              </div>
            </div>
          )}
        </div>

        {/* Pricing at the bottom but inside the link */}
        <div className="p-4 pt-0 mt-auto">
          {/* Temporary hide pricing for EMV10T */}
          {isAuthenticated && sku === "EMV10T" ? (
            <p className="text-base text-gray-500 italic">
              Contact for pricing
            </p>
          ) : isAuthenticated && hasCategory ? (
            <>
              {!visitorGroup && discountedPrice >= 0 && !isQuote ? (
                <p className="text-xl font-bold text-primary group-hover:text-primary/80 transition-colors">
                  ${discountedPrice.toFixed(2)}
                </p>
              ) : (
                <p className="text-base text-gray-500 italic">
                  Contact for pricing
                </p>
              )}
            </>
          ) : isAuthenticated && !hasCategory ? (
            <p className="text-base text-gray-500 italic">
              Contact for pricing
            </p>
          ) : (
            ""
          )}
        </div>
      </Link>

      {/* <CardHeader>
                <div className="flex flex-col gap-1">
                    <h3 className="font-normal text-lg">{name}</h3>
                    <div className="flex gap-1">
                        <p className="text-2xl text-muted-foreground font-bold">${discountedPrice.toFixed(2)}</p>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                        <Badge variant="secondary" className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            <span>Product Code: {sku}</span>
                        </Badge>
                        {categories && categories.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                                {categories.map((category) => (
                                    <Badge variant="outline" className="flex items-center gap-1" key={category}>
                                        <FolderIcon className="h-3 w-3" />
                                        <span>{category}</span>
                                    </Badge>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </CardHeader> */}

      {/* <CardFooter>
                <Link href={`/store/products/${slug}`} target="_self" className="w-full text-center bg-primary text-white rounded-md py-2 text-sm font-medium">
                    View Product
                </Link>
            </CardFooter> */}
    </Card>
  );
}
