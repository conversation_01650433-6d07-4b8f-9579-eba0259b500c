import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { BillingAddress } from "@/supabase/types";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
  matchRoute({
    DELETE: deleteBillingAddressHandler,
    PATCH: updateBillingAddressHandler,
  })
);

export interface DeleteBillingAddressResponse {
  error?: string;
  billing_address?: BillingAddress;
}

async function deleteBillingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<DeleteBillingAddressResponse>
) {
  const user_id = req.query.id?.toString();
  const billing_id = req.query.billing_id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  if (!billing_id) {
    return res.status(400).json({ error: "Missing billing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const customerId = customer.data.id;

  const data = await supabaseAdminClient
    .from("billing_addresses")
    .delete()
    .eq("id", billing_id)
    .eq("customer_id", customerId)
    .select("*")
    .single();

  if (data.error) {
    return res.status(400).json({ error: data.error.message });
  }

  return res.status(200).json({ billing_address: data.data });
}

const updateBillingAddressSchema = z.object({
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  state: z.string().optional(),
  zip_code: z.string().optional(),
  default: z.boolean(),
});

export type UpdateBillingAddress = z.infer<typeof updateBillingAddressSchema>;

export interface UpdateBillingAddressResponse {
  error?: string;
  billing_address?: BillingAddress;
}

async function updateBillingAddressHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<UpdateBillingAddressResponse>
) {
  const user_id = req.query.id?.toString();
  const billing_id = req.query.billing_id?.toString();

  if (!user_id) {
    return res.status(400).json({ error: "Missing customer id" });
  }

  if (!billing_id) {
    return res.status(400).json({ error: "Missing billing id" });
  }

  const userId = req.user?.id.toString();

  if (!userId) {
    return res.status(403).json({ error: "Forbidden" });
  }

  if (userId !== user_id) {
    return res.status(403).json({ error: "Forbidden" });
  }

  const data = updateBillingAddressSchema.safeParse(req.body);

  if (data.error) {
    return res.status(400).json({ error: data.error.issues[0].message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const billingAddress = data.data as UpdateBillingAddress;

  const customer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .select("id")
    .eq("user_id", userId)
    .single();

  if (customer.error) {
    return res.status(400).json({ error: customer.error.message });
  }

  const customerId = customer.data.id;
  const now = new Date();
  const updatedAt = now.toISOString();

  if (billingAddress.default) {
    // set default to false and approved to false for this billing address
    // so that it will be sent for approval again
    await supabaseAdminClient
      .from("billing_addresses")
      .update({ default: false, approved: false })
      .eq("id", billing_id)
      .eq("customer_id", customerId);
  }

  const updatedBillingAddress = await supabaseAdminClient
    .from("billing_addresses")
    .update({
      ...billingAddress,
      default: false,
      approved: false,
      updated_at: updatedAt,
    })
    .eq("id", billing_id)
    .eq("customer_id", customerId)
    .select("*")
    .single();

  if (updatedBillingAddress.error) {
    return res.status(400).json({ error: updatedBillingAddress.error.message });
  }

  return res.status(200).json({ billing_address: updatedBillingAddress.data });
}
