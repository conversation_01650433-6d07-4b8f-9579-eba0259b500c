import {
  blockContentNormalStyle,
  title,
  mainImage,
  description,
  subtitle,
  primaryButton,
  arrayOfImages,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const textComponentSchema = [
  // Variant A
  title(),
  subtitle(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
    ])
  ),
  description(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),

  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
    ])
  ),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn([])
  ),

  //   Variant B
  blockContentNormalStyle(
    "secondColumn",
    "Second Column",
    "Add content in the second column.",
    hideIfVariantIn(["variant_a", "variant_d", "variant_e", "variant_f"])
  ),

  //   Variant C
  blockContentNormalStyle(
    "thirdColumn",
    "Third Column",
    "Add content in the third column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),
  mainImage(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),
  arrayOfImages(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),
];
