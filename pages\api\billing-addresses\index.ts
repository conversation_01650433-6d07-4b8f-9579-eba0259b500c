import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { supabaseClient } from "@/supabase";
import { BillingAddress } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(matchRoute({
    GET: getBillingAddressesHandler,
    POST: checkPermission("update:billing_addresses", approveBillingAddressHandler),
}));

export interface GetBillingAddressesResponse {
    error?: string;
    billingAddresses?: BillingAddress[];
    total?: number;
    totalPages?: number;
}

async function getBillingAddressesHandler(req: NextApiRequest, res: NextApiResponse<GetBillingAddressesResponse>) {
    const query = req.query;
    const page = query.page ? parseInt(query.page as string) : 1;
    const limit = query.limit ? parseInt(query.limit as string) : 10;

    const { data, error } = await supabaseClient
        .schema("public")
        .from("billing_addresses")
        .select("*", { count: "exact" })
        .range((page - 1) * limit, page * limit - 1);

    if (error) {
        return res.status(500).json({ error: error.message });
    }

    const total = data?.length;
    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({ billingAddresses: data, total, totalPages });
}

export const approveBillingAddressSchema = z.object({
    id: z.string().uuid("ID is required"),
});

export type ApproveBillingAddressRequest = z.infer<typeof approveBillingAddressSchema>;

export interface ApproveBillingAddressResponse {
    error?: string;
    billingAddress?: BillingAddress;
}

async function approveBillingAddressHandler(req: NextApiRequest, res: NextApiResponse<ApproveBillingAddressResponse>) {
    const { id } = approveBillingAddressSchema.parse(req.body);

    const { data, error } = await supabaseClient
        .schema("public")
        .from("billing_addresses")
        .update({ approved: true })
        .eq("id", id)
        .select("*")
        .single();

    if (error) {
        return res.status(500).json({ error: error.message });
    }

    return res.status(200).json({ billingAddress: data });
}



