import { SvgSpinners90Ring } from "@/components/common/icons";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatPrice } from "@/lib/utils";
import { CheckoutFormValues } from "@/pages/store/checkout";
import {
    useGetImage
} from "@/queries/customer-queries";
import { useTaxRateCalculation } from "@/queries/tax-rate-queries";
import useCartStore, { CartItem } from "@/stores/cart-store";
import {
    calculateTaxAmount
} from "@/utils/nevada-tax-rates";
import {
    ShoppingBag,
    ShoppingCart
} from "lucide-react";
import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";

export interface CheckoutOrderSummaryProps {
    items: CartItem[];
    activeTab: string;
    orderConfirmed: boolean;
    isSubmitting: boolean;
    form: UseFormReturn<CheckoutFormValues>;
    uploadFileMutation: any;
    addNewOrderMutation: any;
    onSubmit: (values: CheckoutFormValues) => void;
    shippingAddresses: any;
}

export function CheckoutOrderSummary({
    items,
    activeTab,
    orderConfirmed,
    isSubmitting,
    form,
    uploadFileMutation,
    addNewOrderMutation,
    onSubmit,
    shippingAddresses,
}: CheckoutOrderSummaryProps) {
    const cartSubtotal = useCartStore((state) => state.getSubtotal)();
    const additionalAmount = useCartStore((state) => state.getAdditionalAmount)();
    const [subtotal, setSubtotal] = useState(0);
    const [taxAmount, setTaxAmount] = useState(0);
    const [taxRate, setTaxRate] = useState(0);
    const [total, setTotal] = useState(0);

    // Get shipping address for tax calculation
    const shippingAddressId = form.watch("shippingAddressId");
    const isTaxExempt = form.watch("tax_exempt");
    const shippingAddress = shippingAddresses.data?.find(
        (addr: any) => addr.id === shippingAddressId
    );

    // Use the new tax rate calculation hook
    const currentTaxRate = useTaxRateCalculation(
        shippingAddress?.city || "",
        shippingAddress?.state || ""
    );

    useEffect(
        function calculateTotals() {
            const orderSubtotal = cartSubtotal + additionalAmount;
            setSubtotal(cartSubtotal);

            let currentTaxAmount = 0;

            if (
                shippingAddress?.state === "Nevada" ||
                shippingAddress?.state === "NV"
            ) {
                currentTaxAmount = calculateTaxAmount(
                    orderSubtotal,
                    currentTaxRate,
                    isTaxExempt
                );
            }

            setTaxRate(currentTaxRate);
            setTaxAmount(currentTaxAmount);
            setTotal(orderSubtotal + currentTaxAmount);
        },
        [
            cartSubtotal,
            additionalAmount,
            currentTaxRate,
            isTaxExempt,
            shippingAddress,
        ]
    );

    return (
        <Card className="sticky top-44">
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="h-5 w-5" />
                    Order Summary
                </CardTitle>
                <CardDescription>Review your order details</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {items.map((item, index) => {
                        return (
                            <OrderSummaryItem
                                key={`order-summary-item-${item.id}-${index}`}
                                item={item}
                            />
                        );
                    })}

                    <Separator />

                    <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Subtotal</span>
                            <span>{formatPrice(subtotal)}</span>
                        </div>
                        {additionalAmount > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">Options Total</span>
                                <span className="text-green-600">
                                    +{formatPrice(additionalAmount)}
                                </span>
                            </div>
                        )}
                        {taxRate > 0 && (
                            <div className="flex justify-between text-sm">
                                <span className="text-muted-foreground">
                                    Tax ({taxRate}%)
                                    {form.watch("tax_exempt") && (
                                        <span className="text-orange-600 ml-1">(Exempt)</span>
                                    )}
                                </span>
                                <span
                                    className={
                                        form.watch("tax_exempt")
                                            ? "line-through text-muted-foreground"
                                            : ""
                                    }
                                >
                                    {formatPrice(taxAmount)}
                                </span>
                            </div>
                        )}
                    </div>

                    <Separator />

                    <div className="flex justify-between font-medium">
                        <span>Total</span>
                        <span className="text-lg">{formatPrice(total)}</span>
                    </div>

                    <div className="text-sm text-muted-foreground">
                        <p>Shipping costs will be calculated at time of shipment.</p>
                        <p>For shipping estimates, please contact sales.</p>
                    </div>

                    {/* Place Order Button - Only show on payment tab and not after order is confirmed */}
                    {activeTab === "payment" && !orderConfirmed && (
                        <div className="pt-4">
                            {(() => {
                                const isPlacingOrder =
                                    isSubmitting ||
                                    uploadFileMutation.isPending ||
                                    addNewOrderMutation.isPending ||
                                    form.formState.isSubmitting ||
                                    form.formState.isLoading;

                                // Check form validity in much more detail
                                const validationState = {
                                    isValid: form.formState.isValid,
                                    isDirty: form.formState.isDirty,
                                    errors: form.formState.errors,
                                    dirtyFields: form.formState.dirtyFields,
                                    touchedFields: form.formState.touchedFields,
                                    defaultValues: form.formState.defaultValues,
                                    isUpsAccountRequired:
                                        form.getValues("ship_collect") === true &&
                                        (!form.getValues("ups_account_number") ||
                                            form.getValues("ups_account_number")?.trim() === ""),
                                    isPONumberOrFileRequired:
                                        form.getValues("paymentType") === "purchase_order" &&
                                        !form.getValues("poFile") &&
                                        !form.getValues("poNumber"),
                                    ship_collect: form.getValues("ship_collect"),
                                    delivery_method: form.getValues("delivery_method"),
                                    poNumber: form.getValues("poNumber"),
                                    poFile: form.getValues("poFile") !== undefined,
                                    ups_account_number: form.getValues("ups_account_number"),
                                    billingAddressId: form.getValues("billingAddressId"),
                                    shippingAddressId: form.getValues("shippingAddressId"),
                                    paymentType: form.getValues("paymentType"),
                                };

                                console.log("Detailed validation state:", validationState);

                                return (
                                    <Button
                                        variant="default"
                                        disabled={isPlacingOrder}
                                        aria-disabled={isPlacingOrder}
                                        type="button"
                                        onClick={async () => {
                                            console.log("Place order button clicked");

                                            // Check if shipping address is for a special country
                                            const shippingAddressId =
                                                form.getValues("shippingAddressId");
                                            const shippingAddress = shippingAddresses.data?.find(
                                                (addr) => addr.id === shippingAddressId
                                            );

                                            const shippingCountry =
                                                shippingAddress?.country?.toLowerCase();
                                            const shippingState =
                                                shippingAddress?.state?.toLowerCase();
                                            const isSpecialCountry =
                                                shippingCountry === "mexico" ||
                                                shippingCountry === "puerto rico" ||
                                                shippingState === "puerto rico" ||
                                                shippingState === "mexico";

                                            // Check UPS account number if ship_collect is true (not checking for undefined anymore)
                                            if (
                                                !isSpecialCountry &&
                                                form.getValues("ship_collect") === undefined
                                            ) {
                                                form.setError("ship_collect", {
                                                    message:
                                                        "Please select whether to ship collect via UPS",
                                                });

                                                // Scroll to the error
                                                setTimeout(() => {
                                                    const errorElement = document.querySelector(
                                                        '[aria-invalid="true"], .text-destructive'
                                                    );
                                                    if (errorElement) {
                                                        errorElement.scrollIntoView({
                                                            behavior: "smooth",
                                                            block: "center",
                                                        });
                                                    }
                                                }, 100);

                                                return;
                                            } else if (
                                                !isSpecialCountry &&
                                                form.getValues("ship_collect") === true &&
                                                (!form.getValues("ups_account_number") ||
                                                    form.getValues("ups_account_number")?.trim() === "")
                                            ) {
                                                form.setError("ups_account_number", {
                                                    message: "Please enter your UPS account number",
                                                });

                                                // Scroll to the error
                                                setTimeout(() => {
                                                    const errorElement = document.querySelector(
                                                        '[aria-invalid="true"], .text-destructive'
                                                    );
                                                    if (errorElement) {
                                                        errorElement.scrollIntoView({
                                                            behavior: "smooth",
                                                            block: "center",
                                                        });
                                                    }
                                                }, 100);

                                                return;
                                            }

                                            // Manual validation before submission - await the promise
                                            const isValid = await form.trigger();
                                            console.log("Form trigger result:", isValid);

                                            if (isValid) {
                                                // Only proceed if validation passed
                                                const data = form.getValues();
                                                console.log("Form data after validation:", data);
                                                onSubmit(data);
                                            } else {
                                                console.log("Form validation failed, not submitting");
                                                // Scroll to the first error
                                                setTimeout(() => {
                                                    const errorElement = document.querySelector(
                                                        '[aria-invalid="true"], .text-destructive'
                                                    );
                                                    if (errorElement) {
                                                        errorElement.scrollIntoView({
                                                            behavior: "smooth",
                                                            block: "center",
                                                        });
                                                    }
                                                }, 100);
                                            }
                                        }}
                                        className="w-full h-12 text-lg"
                                    >
                                        {isPlacingOrder ? (
                                            <>
                                                <SvgSpinners90Ring className="mr-2" />
                                                Processing...
                                            </>
                                        ) : (
                                            <>
                                                Place Order
                                                <ShoppingCart className="ml-2 h-5 w-5" />
                                            </>
                                        )}
                                    </Button>
                                );
                            })()}
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

interface OrderSummaryItemProps {
    item: CartItem;
}

function OrderSummaryItem({ item }: OrderSummaryItemProps) {
    const totalPrice = item.price * item.quantity;
    const optionPriceTotal =
        item.selectedOptions?.reduce(
            (total, option) => total + (option.price || 0) * item.quantity,
            0
        ) || 0;

    const image = useGetImage(item.image ?? "").data ?? item.image;

    return (
        <div className="flex items-start gap-4">
            <div className="h-16 w-16 rounded-lg border bg-muted/50 overflow-hidden">
                {item.image && (
                    <img
                        src={image}
                        alt={item.name}
                        className="h-full w-full object-cover"
                    />
                )}
            </div>
            <div className="flex-1 space-y-1">
                <div className="flex justify-between">
                    <p className="font-medium line-clamp-1">{item.name}</p>
                    <p className="font-medium">{formatPrice(totalPrice)}</p>
                </div>
                <p className="text-sm text-muted-foreground">SKU: {item?.sku}</p>
                <p className="text-sm text-muted-foreground">Qty: {item.quantity}</p>
                {item.selectedOptions?.map((option, index) => {
                    const price = option.price
                        ? ` (+${formatPrice(option.price * item.quantity)})`
                        : "";
                    return (
                        <p
                            key={`${item.id}-${option.name}-${option.value}-${index}`}
                            className="text-sm text-muted-foreground flex justify-between"
                        >
                            <span>
                                {option.name}: {option.value}
                            </span>
                            {price && <span className="text-green-600">{price}</span>}
                        </p>
                    );
                })}
            </div>
        </div>
    );
}

export function CheckoutOrderSummarySkeleton() {
    return (
        <Card className="sticky top-6">
            <CardHeader>
                <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <Skeleton className="h-6 w-32" />
                </div>
                <Skeleton className="h-4 w-40 mt-1" />
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {[1, 2].map((i) => (
                        <div key={i} className="flex items-start gap-4">
                            <Skeleton className="h-16 w-16 rounded-lg" />
                            <div className="flex-1 space-y-2">
                                <div className="flex justify-between">
                                    <Skeleton className="h-5 w-32" />
                                    <Skeleton className="h-5 w-20" />
                                </div>
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-4 w-40" />
                            </div>
                        </div>
                    ))}

                    <Separator />

                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-4 w-20" />
                        </div>
                        <div className="flex justify-between">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-4 w-20" />
                        </div>
                    </div>

                    <Separator />

                    <div className="flex justify-between">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-6 w-24" />
                    </div>

                    <div className="space-y-1">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}