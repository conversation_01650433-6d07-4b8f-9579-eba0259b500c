import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import { useDeleteCategoryMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Category } from "@/supabase/types";
import { Loader2, Trash2 } from "lucide-react";

interface DeleteCategoryDialogProps {
    category: Category;
}

export function DeleteCategoryDialog({ category }: DeleteCategoryDialogProps) {
    const token = useAuthStore((state) => state.token);
    const { mutate: deleteCategory, isPending } = useDeleteCategoryMutation(token);
    const { toast } = useToast();
    const permissions = useAuthStore((state) => state.permissions);
    const hasPermission = checkUserHasPermission(permissions, "delete:categories");

    const handleDelete = () => {
        if (!hasPermission) {
            toast({
                title: "Error",
                description: "You are not authorized to delete categories",
                variant: "destructive",
            });
            return;
        }

        deleteCategory(category.id, {
            onSuccess: () => {
                toast({
                    title: "Success",
                    description: "Category deleted successfully",
                });
            },
            onError: (error) => {
                toast({
                    title: "Error",
                    description: error.message || "Failed to delete category",
                    variant: "destructive",
                });
            },
        });
    };

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <Button variant="destructive" size="icon" disabled={!hasPermission}>
                    <Trash2 className="h-4 w-4" />
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Delete Category</AlertDialogTitle>
                    <AlertDialogDescription>
                        Are you sure you want to delete this category? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <Button variant="destructive" onClick={handleDelete} disabled={isPending || !hasPermission}>
                        {isPending ? "Deleting..." : "Delete"}
                        {isPending && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                    </Button>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
} 