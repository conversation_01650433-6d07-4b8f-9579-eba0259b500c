import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { UseFormReturn } from "react-hook-form"
import HelpfulHintsTab from "./tabs/helpful-hints-tab"
import InstallationTab from "./tabs/installation-tab"
import ShippingTab from "./tabs/shipping-tab"
import SpecificationsTab from "./tabs/specifications-tab"
import WarrantyTab from "./tabs/warranty-tab"

interface Specification {
    key: string;
    value: string;
}

interface UrlLabelItem {
    url: string;
    label: string;
}

interface AdditionalInformationProps {
    form: UseFormReturn<any>

    // Specifications props
    specs: Specification[]
    addSpecification: () => void
    removeSpecification: (index: number) => void
    updateSpecification: (index: number, field: keyof Specification, value: string) => void

    // Features props
    features: string[]
    addFeature: () => void
    removeFeature: (index: number) => void
    updateFeature: (index: number, value: string) => void

    // Helpful hints props
    helpfulHints: UrlLabelItem[]
    addHelpfulHint: () => void
    removeHelpfulHint: (index: number) => void
    updateHelpfulHint: (index: number, field: keyof UrlLabelItem, value: string) => void

    // Installation props
    installationFile: File | null
    handleInstallationFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    setInstallationFile: React.Dispatch<React.SetStateAction<File | null>>

    // Shipping props
    shipping: UrlLabelItem
    setShipping: React.Dispatch<React.SetStateAction<UrlLabelItem>>

    // Warranty props
    warranty: UrlLabelItem
    setWarranty: React.Dispatch<React.SetStateAction<UrlLabelItem>>
}

export default function AdditionalInformation({
    form,
    specs,
    addSpecification,
    removeSpecification,
    updateSpecification,
    features,
    addFeature,
    removeFeature,
    updateFeature,
    helpfulHints,
    addHelpfulHint,
    removeHelpfulHint,
    updateHelpfulHint,
    installationFile,
    handleInstallationFileChange,
    setInstallationFile,
    shipping,
    setShipping,
    warranty,
    setWarranty
}: AdditionalInformationProps) {
    return (
        <Card className="rounded-lg">
            <CardHeader>
                <CardTitle>Additional Information</CardTitle>
            </CardHeader>
            <CardContent>
                <Tabs defaultValue="specifications">
                    <TabsList className="grid grid-cols-2 md:grid-cols-5">
                        <TabsTrigger value="specifications">Specifications</TabsTrigger>
                        <TabsTrigger value="helpfulHints">Helpful Hints</TabsTrigger>
                        <TabsTrigger value="installation">Installation</TabsTrigger>
                        <TabsTrigger value="shipping">Shipping</TabsTrigger>
                        <TabsTrigger value="warranty">Warranty</TabsTrigger>
                    </TabsList>

                    <TabsContent value="specifications" className="pt-5">
                        <SpecificationsTab
                            form={form}
                            specs={specs}
                            addSpecification={addSpecification}
                            removeSpecification={removeSpecification}
                            updateSpecification={updateSpecification}
                            features={features}
                            addFeature={addFeature}
                            removeFeature={removeFeature}
                            updateFeature={updateFeature}
                        />
                    </TabsContent>

                    <TabsContent value="helpfulHints" className="pt-5">
                        <HelpfulHintsTab
                            helpfulHints={helpfulHints}
                            addHelpfulHint={addHelpfulHint}
                            removeHelpfulHint={removeHelpfulHint}
                            updateHelpfulHint={updateHelpfulHint}
                        />
                    </TabsContent>

                    <TabsContent value="installation" className="pt-5">
                        <InstallationTab
                            installationFile={installationFile}
                            handleInstallationFileChange={handleInstallationFileChange}
                            setInstallationFile={setInstallationFile}
                        />
                    </TabsContent>

                    <TabsContent value="shipping" className="pt-5">
                        <ShippingTab
                            shipping={shipping}
                            setShipping={setShipping}
                        />
                    </TabsContent>

                    <TabsContent value="warranty" className="pt-5">
                        <WarrantyTab
                            warranty={warranty}
                            setWarranty={setWarranty}
                        />
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    )
} 