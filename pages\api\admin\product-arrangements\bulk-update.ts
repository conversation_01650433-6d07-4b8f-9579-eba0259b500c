import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

// Validation schema
const BulkUpdateArrangementSchema = z.object({
  arrangements: z.array(z.object({
    id: z.string().uuid("Invalid arrangement ID"),
    position: z.number().int().min(0, "Position must be a non-negative integer"),
  })).min(1, "At least one arrangement is required"),
});

export type BulkUpdateArrangement = z.infer<typeof BulkUpdateArrangementSchema>;

export interface BulkUpdateArrangementsResponse {
  updated?: number;
  error?: string;
}

async function bulkUpdateArrangements(
  req: NextApiRequest,
  res: NextApiResponse<BulkUpdateArrangementsResponse>
) {
  try {
    const validationResult = BulkUpdateArrangementSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        error: `Validation error: ${validationResult.error.errors
          .map((e) => e.message)
          .join(", ")}`,
      });
    }

    const { arrangements } = validationResult.data;
    const supabaseAdminClient = createSupabaseAdminClient();

    // Verify all arrangements exist
    const arrangementIds = arrangements.map(a => a.id);
    const { data: existingArrangements, error: fetchError } = await supabaseAdminClient
      .from("product_arrangements")
      .select("id")
      .in("id", arrangementIds);

    if (fetchError) {
      console.error("Error fetching arrangements:", fetchError);
      return res.status(500).json({ error: "Failed to verify arrangements" });
    }

    if (!existingArrangements || existingArrangements.length !== arrangements.length) {
      return res.status(404).json({ error: "One or more arrangements not found" });
    }

    // Update each arrangement
    let updatedCount = 0;
    for (const arrangement of arrangements) {
      const { error } = await supabaseAdminClient
        .from("product_arrangements")
        .update({ position: arrangement.position })
        .eq("id", arrangement.id);

      if (error) {
        console.error(`Error updating arrangement ${arrangement.id}:`, error);
        // Continue with other updates but log the error
      } else {
        updatedCount++;
      }
    }

    if (updatedCount === 0) {
      return res.status(500).json({ error: "Failed to update any arrangements" });
    }

    return res.status(200).json({
      updated: updatedCount,
    });
  } catch (error) {
    console.error("Error in bulkUpdateArrangements:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

export default checkAdmin(matchRoute({
  PATCH: bulkUpdateArrangements,
}));