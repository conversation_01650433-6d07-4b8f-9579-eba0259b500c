import EditProductForm from "@/components/features/admin/edit-product-form";
import AdminLayout from "@/components/features/admin/layout";
import { Button } from "@/components/ui/shadcn-button";
import { ArrowLeft } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";

export default function EditProduct() {
    const router = useRouter();
    const { id } = router.query;
    const productId = typeof id === 'string' ? id : '';

    return (
        <AdminLayout>
            <Head>
                <title>Edit Product | {productId}</title>
            </Head>
            <div className="flex flex-col gap-4">
                <div className="mb-4">
                    <Link href="/admin/products" passHref>
                        <Button variant="ghost" className="gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Back to Products
                        </Button>
                    </Link>
                </div>
                {productId && <EditProductForm productId={productId} />}
            </div>
        </AdminLayout>
    );
}
