{
  "compilerOptions": {
    "baseUrl": ".",
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "incremental": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "strictNullChecks": true,
    "paths": {
      "@/*": [
        "./*"
      ],
      "@/components/*": [
        "components/*"
      ],
      "@/ui*": [
        "components/ui/*"
      ],
      "@/lib/*": [
        "lib/*"
      ],
      "@/utils*": [
        "lib/utils/*"
      ],
      "@/hooks/*": [
        "hooks/*"
      ],
      "@/supabase/*": [
        "supabase/*"
      ]
    },
  },
  "include": [
    "next-env.d.ts",
    "modules.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "**/*.js",
    "**/*.jsx",
    "context/EcwidContext.tjs",
    "ecwid.d.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}