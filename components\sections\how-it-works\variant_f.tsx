import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { HowItWorksProps } from ".";
import { ArrayOfTitleAndText } from "../../../types";

export default function HowItWorks_F({
  subtitle,
  title,
  text,
  steps,
}: HowItWorksProps) {
  // Group steps by year ranges
  const timeRanges = [
    { range: "1992-1998", start: 1992, end: 1998 },
    { range: "2000-2007", start: 2000, end: 2007 },
    { range: "2011-2017", start: 2011, end: 2017 },
  ];

  const filterStepsByRange = (start: number, end: number) => {
    return steps?.filter((step) => {
      const year = parseInt(step.title?.split(" ")[0] || "0");
      return year >= start && year <= end;
    });
  };

  return (
    <Section className="py-20 bg-gray-100">
      <Container maxWidth={1280}>
        <div className="mb-12 text-center">
          {subtitle && (
            <Text weight="bold" className="text-secondary mb-2 ">
              {subtitle}
            </Text>
          )}
          {title && (
            <Heading type="h2" fontSize="3xl" weight="bold">
              {title}
            </Heading>
          )}
        </div>

        <div className="space-y-16">
          {timeRanges.map((timeRange, index) => (
            <div key={index} className="relative">
              <div className="flex flex-col lg:flex-row">
                {/* Column 1: Time Range */}
                <div className="w-full lg:w-1/6 mb-4 md:mb-0">
                  <Heading
                    type="h3"
                    fontSize="2xl"
                    weight="bold"
                    className="!text-black"
                  >
                    {timeRange.range}
                  </Heading>
                </div>

                {/* Column 2 & 3: Timeline & Content */}
                <div className="relative w-full lg:w-5/6 mt-5 md:mt-10">
                  {/* Vertical Timeline Line */}
                  <div className="absolute left-0 top-0 h-full w-0.5 bg-gray-200" />

                  <div className="space-y-12">
                    {filterStepsByRange(timeRange.start, timeRange.end)?.map(
                      (step, stepIndex) => (
                        <div
                          key={step._key || stepIndex}
                          className="relative flex flex-col md:flex-row pl-8"
                        >
                          {/* Timeline Indicator */}
                          <div className="absolute left-[-6px] top-2 w-3 h-3 bg-primary rounded-full" />

                          {/* Column 2: Title & Subtitle */}
                          <div className="w-full md:w-1/3 md:pr-6">
                            <Text
                              weight="bold"
                              fontSize="base"
                              className="mb-2 text-primary"
                            >
                              {step?.title}
                            </Text>
                            {step?.subtitle && (
                              <Text
                                fontSize="xl"
                                weight="bold"
                                className="text-gray-700"
                              >
                                {step?.subtitle}
                              </Text>
                            )}
                          </div>

                          {/* Column 3: Plain Text */}
                          <div className="w-full md:w-2/3 md:pl-6 lg:pl-16 max-w-[650px] mt-5 md:mt-0">
                            <Text
                              muted
                              className="leading-loose !text-sm md:!text-base"
                            >
                              {step?.plainText}
                            </Text>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Container>
    </Section>
  );
}

export { HowItWorks_F };
