import {
  arrayOfImages,
  arrayOfImageTitleAndText,
  arrayOfTitleAndDescription,
  blockContentNormalStyle,
  description,
  featuredItems,
  hasCalculator,
  mainImage,
  primaryButton,
  secondaryButton,
  subtitle,
  tags,
  title,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const featuresSchema = [
  subtitle(
    hideIfVariantIn([
      "variant_e",
      "variant_k",
      "variant_n",
      "variant_p",
      "variant_t",
    ])
  ),
  title(hideIfVariantIn(["variant_e"])),
  description(
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_i",
      "variant_j",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_t",
      "variant_v",
      "variant_w",
    ])
  ),
  {
    name: "jobOpportunities",
    title: "Job Offers",
    description: "Add a role and position on the job you are offering.",
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
    ]),
    type: "object",
    fields: [
      {
        name: "jobPosition",
        title: "Title",
        type: "string",
        description: "Click add item to add job position/role opportunity.",
        placeholder: "Employment Opportunities",
        hidden: hideIfVariantIn(["variant_x"]),
      },
      // Job Offers / Roles Available on that certain Position.
      {
        name: "jobOffers",
        title: "Job Offers",
        description: "Add a role and position on the job you are offering.",
        type: "array",
        of: [
          {
            type: "object",
            fields: [
              {
                name: "jobRole",
                title: "Job Role",
                type: "string",
              },
              blockContentNormalStyle(
                "jobDescription",
                "Content",
                "Add text content in a single column."
              ),
            ],
          },
        ],
      },
    ],
  },
  blockContentNormalStyle(
    "secondColumn",
    "Content",
    "Add second text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
    ])
  ),
  blockContentNormalStyle(
    "sideContent",
    "Side Card Content",
    "Add side card content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
    ])
  ),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add first text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_r",
      "variant_s",
      "variant_u",
      "variant_x",
    ])
  ),
  mainImage(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  arrayOfImageTitleAndText(
    "Features",
    "Click the 'Add item' button to add a feature. If you want to edit added features, click this ⋮ icon found on its right.",
    hideIfVariantIn([
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_j",
      "variant_i",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_x",
    ])
  ),

  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_k",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_q",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  secondaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  arrayOfImages(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  featuredItems(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_f",
      "variant_o",
      "variant_q",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  tags(
    "Featured Items",
    null,
    hideIfVariantIn([
      "variant_a",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_g",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ])
  ),

  {
    name: "arrayOfLinks",
    title: "Array of Links",
    description:
      "Click the 'Add item' button to add a link. If you want to edit added link, click this ⋮ icon found on its right.",
    type: "array",
    of: [primaryButton()],
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ]),
  },

  {
    name: "quickForms",
    title: "Quick Forms",
    type: "object",
    hidden: hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_p",
      "variant_q",
      "variant_t",
      "variant_u",
      "variant_v",
      "variant_w",
      "variant_x",
    ]),
    fields: [
      subtitle(),
      title(),
      description(),
      {
        name: "quickLinks",
        title: "Form Links",
        description:
          "Click the 'Add item' button to add a form. If you want to edit added form, click this ⋮ icon found on its right.",
        type: "array",
        of: [
          {
            type: "object",
            fields: [title(), description(), primaryButton()],
          },
        ],
      },
    ],
  },

  hasCalculator(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
      "variant_k",
      "variant_l",
      "variant_m",
      "variant_n",
      "variant_o",
      "variant_p",
      "variant_q",
      "variant_r",
      "variant_s",
      "variant_u",
      "variant_w",
      "variant_x",
    ])
  ),
];
