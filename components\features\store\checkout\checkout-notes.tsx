import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { CheckoutFormValues } from "@/pages/store/checkout";
import { MessageSquare } from "lucide-react";
import { UseFormReturn } from "react-hook-form";

export interface CheckoutNotesProps {
  checkoutForm: UseFormReturn<CheckoutFormValues>;
}

export function CheckoutNotes({ checkoutForm }: CheckoutNotesProps) {
  return (
    <Card className="hover:shadow-md transition-all">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
          <MessageSquare className="h-5 w-5 text-primary" />
        </div>
        <div>
          <CardTitle>Order Notes</CardTitle>
          <CardDescription>
            Add any special instructions or notes for your order (optional)
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField
          control={checkoutForm.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Additional Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any special instructions, delivery notes, or other information about your order..."
                  className="min-h-[100px] resize-y"
                  maxLength={500}
                  {...field}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    if (e.target.value.length <= 500) {
                      checkoutForm.clearErrors("notes");
                    }
                  }}
                />
              </FormControl>
              <div className="flex justify-between items-center">
                <FormMessage />
                <span className="text-xs text-muted-foreground">
                  {field.value?.length || 0}/500 characters
                </span>
              </div>
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}