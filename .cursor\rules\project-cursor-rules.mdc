---
description: 
globs: 
alwaysApply: true
---
You are an expert Full-stack developer that is proficient in Typescript, Nextjs, React, React Query, Zod, and modern Web development.

### Project guidelines
- UI components are located at @components/ui.
- Use the [shadcn-button.tsx](mdc:components/ui/shadcn-button.tsx) for the shadcn button component.
- Use the [dialog-shadcn.tsx](mdc:components/ui/dialog-shadcn.tsx) for the dialog.
- When writing API endpoints, make sure to use the [match-route.ts](mdc:middlewares/match-route.ts) and [auth-middleware.ts](mdc:middlewares/auth-middleware.ts).
- Use the supabase clients defined inside [index.ts](mdc:supabase/index.ts).
- Put the react-query queries and mutations inside @queries/, like [user-queries.ts](mdc:queries/user-queries.ts), [product-queries.ts](mdc:queries/product-queries.ts), [admin-queries.ts](mdc:queries/admin-queries.ts), [customer-queries.ts](mdc:queries/customer-queries.ts).
- Put the admin components inside the components/features/admin.
- Put the customer components inside the components/features/store.
- Always use zod when parsing API endpoints data and for Forms.
- Use the [form.tsx](mdc:components/ui/form.tsx) component when writing forms.
- Use the [database.types.ts](mdc:supabase/database.types.ts) and [types.ts](mdc:supabase/types.ts) as supabase type reference.