import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, A11y } from "swiper/modules";
import { ButtonProps, HeaderProps } from ".";
import { FaArrowRightLong } from "react-icons/fa6";

export default function Header_I({
  title,
  subtitle,
  description,
}: HeaderProps): JSX.Element {
  return (
    <Section className="relative py-20 lg:py-40">
      <div
        className="absolute inset-0 transition-all duration-[2000ms] ease-in-out"
        style={{
          backgroundImage: `linear-gradient(to top right, #0154a2 , rgba(255, 255, 255, 1) )`,

          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      ></div>

      {/* Content */}
      <Container maxWidth={1280} className="relative z-10 h-full">
        <TitleAndDescription
          title={title}
          subtitle={subtitle}
          description={description}
        />
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  subtitle,
  description,
}: {
  title?: string;
  subtitle?: string;
  description?: string;
}) {
  return (
    <div className="max-w-7xl">
      {title ? (
        <Heading
          className={`${
            description && "mb-5"
          } text-white !leading-tight text-center text-3xl md:text-5xl`}
          type="h1"
        >
          {title}
        </Heading>
      ) : null}

      {description && (
        <Text
          className="mb-5 text-gray-100 text-center max-w-6xl"
          fontSize="lg"
        >
          {description}
        </Text>
      )}
    </div>
  );
}

export { Header_I };
