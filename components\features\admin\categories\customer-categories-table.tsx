import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import { useGetCustomersQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { CustomerStatusBadge } from "@/components/features/admin/customers/customer-status-badge";
import { ColumnDef, ColumnFiltersState, SortingState, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table";
import { useState } from "react";

const customerCategoryColumns: ColumnDef<any>[] = [
    {
        header: "Company Name",
        accessorKey: "company_name",
        cell: ({ row }) => {
            const companyName = row.original.company_name || "N/A";
            return (
                <div className="font-medium">
                    {companyName}
                </div>
            );
        }
    },
    {
        header: "Primary Contact",
        accessorKey: "primary_contact_name",
    },
    {
        header: "Status",
        accessorKey: "status",
        cell: ({ row }) => {
            const status = row.original.status;
            return (
                <CustomerStatusBadge status={status} />
            );
        }
    },
    {
        header: "Categories",
        accessorKey: "catgegories",
        cell: ({ row }) => {
            const categories = row.original.catgegories;
            return categories?.map((cat: any) => cat.category_data.name).join(", ") || "N/A";
        }
    },
    {
        header: "Created",
        accessorKey: "created_at",
        cell: ({ row }) => {
            const date = new Date(row.original.created_at);
            return <div>{date.toISOString().split('T')[0]}</div>;
        }
    },
];

export function CustomerCategoriesDataTable() {
    const accessToken = useAuthStore((state) => state.token);
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const { data, isLoading } = useGetCustomersQuery(page, limit, accessToken);

    const table = useReactTable({
        data: data?.customers ?? [],
        columns: customerCategoryColumns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true,
        pageCount: Math.ceil((data?.total ?? 0) / limit),
        onPaginationChange: updater => {
            if (typeof updater === 'function') {
                const newPagination = updater({
                    pageIndex: page - 1,
                    pageSize: limit,
                });
                setPage(newPagination.pageIndex + 1);
                setLimit(newPagination.pageSize);
            } else {
                setPage(updater.pageIndex + 1);
                setLimit(updater.pageSize);
            }
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        state: {
            sorting,
            columnFilters,
            pagination: {
                pageIndex: page - 1,
                pageSize: limit,
            },
        },
    });

    if (isLoading) return <DataTableSkeleton />;

    return (
        <Card className="container mx-auto">
            <CardHeader>
                <div>
                    <h3 className="text-lg font-bold">Customer Categories</h3>
                    <p className="text-sm text-muted-foreground">
                        View all customers and their category associations.
                    </p>
                </div>
            </CardHeader>
            <CardContent>
                <DataTable
                    columns={customerCategoryColumns}
                    data={data?.customers ?? []}
                    filterColumn="primary_contact_name"
                    filterPlaceholder="Search customers..."
                    table={table}
                />
            </CardContent>
        </Card>
    );
} 