import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, A11y } from "swiper/modules";
import { ButtonProps, HeaderProps } from ".";
import { FaArrowRightLong } from "react-icons/fa6";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Header_P({
  title,
  subtitle,
  description,
  mainImage,
}: HeaderProps): JSX.Element {
  console.log(mainImage?.image);
  return (
    <Section className="relative py-20 lg:py-40">
      <div
        className="absolute inset-0 transition-all duration-[2000ms] ease-in-out"
        style={{
          backgroundImage: `linear-gradient(rgba(1, 84, 162, 0.3), rgba(1, 84, 162, 0.3)), url(${mainImage?.image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      ></div>

      {/* Content */}
      <Container maxWidth={1280} className="relative z-10 h-full !px-[10px]">
        <TitleAndDescription
          title={title}
          subtitle={subtitle}
          description={description}
        />
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  subtitle,
  description,
}: {
  title?: string;
  subtitle?: string;
  description?: string;
}) {
  const path = usePathname();

  const home =
    path.includes("-v2") || path.includes("2") ? "home-version2" : "/";

  return (
    <div className="w-full !lg:mx-0 cursor-default">
      <div className="">
        <Link
          href={home}
          className="md:mb-4 mb-2 text-white hover:underline textShadow uppercase w-full text-sm md:text-lg"
        >
          {subtitle ? subtitle : "home"}
        </Link>
        <span className="text-lg px-2 lg:text-xl text-white">{">"}</span>
      </div>

      {title ? (
        <Heading
          type="h1"
          className={`${
            description && "mb-4"
          }   text-white !leading-tight textShadow uppercase md:text-5xl lg:text-5xl text-3xl`}
        >
          {title}
        </Heading>
      ) : null}

      {description && (
        <Text
          className="mb-4 textShadow text-gray-100 text-left max-w-3xl"
          fontSize="lg"
        >
          {description}
        </Text>
      )}
    </div>
  );
}

export { Header_P };
