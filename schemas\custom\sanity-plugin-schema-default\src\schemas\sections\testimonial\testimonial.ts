import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdFormatQuote } from "react-icons/md";
import { testimonialVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";

// Images
import variantEImage from "./images/variant_e.png";
import variantFImage from "./images/variant_f.png";
import initialValue from "./initialValue";
import { testimonialSchema } from "./schema";

export const variantsList = [
  ...baseVariantsList,
  {
    title: "Variant E",
    value: "variant_e",
    description:
      "Display every testimonial as Card that has a previous and next control button",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    value: "variant_f",
    description:
      "Display every testimonial as Card that has a previous and next control button",
    image: variantFImage.src,
  },
];

export default rootSchema(
  "testimonial",
  "Testimonial",
  MdFormatQuote,
  variantsList,
  testimonialSchema,
  initialValue
);
