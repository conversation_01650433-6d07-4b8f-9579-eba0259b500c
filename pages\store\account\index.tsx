import { AccountInformation } from "@/components/features/account/account-information"
import { BillingInformation } from "@/components/features/account/billing-information"
import { ChangePassword } from "@/components/features/account/change-password"
import { ShippingInformation } from "@/components/features/account/shipping-information"
import StoreLayout from "@/components/features/store/layout"
import Head from "next/head"
// import { Separator } from "@/components/ui/separator"
// import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
// import { usePathname } from "next/navigation"
import { useRouter } from "next/router"
// import { useEffect, useState } from "react"

export default function Account() {
    const router = useRouter();
    const query = router.query;
    const currentTab = query.tab as string || "account-information";

    const page = {
        "account-information": <AccountInformation />,
        "billing-information": <BillingInformation />,
        "shipping-information": <ShippingInformation />,
        "change-password": <ChangePassword />
    };
    const currentPage = page[currentTab];

    return (
        <StoreLayout>
            <Head>
                <title>My Account</title>
            </Head>
            <div className="relative w-full h-full min-h-screen p-4 lg:p-8 max-w-7xl">
                <div className="relative w-full h-full flex flex-col gap-8">
                    {currentPage}
                    {/* <AccountSettings /> */}
                </div>
            </div>
        </StoreLayout>
    )
}

// function AccountSettings() {
//     const router = useRouter();
//     const query = router.query;
//     const currentTab = query.tab as string || "account-information";
//     const [isLoading, setIsLoading] = useState(false);

//     const handleTabChange = (tab: string) => {
//         router.push({
//             pathname: router.pathname,
//             query: { ...query, tab }
//         });
//     }

//     useEffect(() => {
//         setIsLoading(true);
//         setTimeout(() => {
//             setIsLoading(false);
//         }, 1000);
//     }, [currentTab]);

//     return (
//         <div className="relative w-full h-full">
//             <h1 className="text-3xl font-bold text-primary">My Account</h1>
//             <Separator className="my-4" />
//             <Tabs value={currentTab} defaultValue="account-information" className="w-full h-full grid grid-cols-[254px,1fr] gap-12 pt-12">
//                 <TabsList className="w-fit h-full flex flex-col items-start justify-start gap-4 bg-transparent min-w-[254px]">
//                     <TabsTrigger onClick={() => handleTabChange("account-information")} value="account-information" className="w-full h-fit bg-transparent data-[state=active]:bg-zinc-100 data-[state=active]:text-primary text-primary text-md justify-start">
//                         Account Information
//                     </TabsTrigger>
//                     <TabsTrigger onClick={() => handleTabChange("billing-information")} value="billing-information" className="w-full h-fit bg-transparent data-[state=active]:bg-zinc-100 data-[state=active]:text-primary text-primary text-md justify-start">
//                         Billing Addresses
//                     </TabsTrigger>
//                     <TabsTrigger onClick={() => handleTabChange("shipping-information")} value="shipping-information" className="w-full h-fit bg-transparent data-[state=active]:bg-zinc-100 data-[state=active]:text-primary text-primary text-md justify-start">
//                         Shipping Addresses
//                     </TabsTrigger>
//                     <TabsTrigger onClick={() => handleTabChange("change-password")} value="change-password" className="w-full h-fit bg-transparent data-[state=active]:bg-zinc-100 data-[state=active]:text-primary text-primary text-md justify-start">
//                         Change Password
//                     </TabsTrigger>
//                 </TabsList>
//                 <TabsContent value="account-information">
//                     <AccountInformation />
//                 </TabsContent>
//                 <TabsContent value="billing-information">
//                     <BillingInformation />
//                 </TabsContent>
//                 <TabsContent value="shipping-information">
//                     <ShippingInformation />
//                 </TabsContent>
//                 <TabsContent value="change-password">
//                     <ChangePassword />
//                 </TabsContent>
//             </Tabs>
//         </div>
//     )
// }