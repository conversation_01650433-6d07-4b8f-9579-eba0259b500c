import DownloadAttachmentButton from "@/components/features/store/download-attachment-button";
import StoreLayout from "@/components/features/store/layout";
import { OrderStatusBadge } from "@/components/features/store/orders-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { useMissingProductsDetection } from "@/hooks/use-missing-products-detection";
import { formatPrice } from "@/lib/utils";
import {
  OrderItemOption,
  OrderItemsWithProduct,
  useGetImage,
  useGetOrderByIdQuery,
  useGetCustomerQuery,
  useGetBillingAddressesQuery,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore from "@/stores/cart-store";
import {
  ArrowLeft,
  CreditCard,
  ExternalLink,
  FileText,
  Package,
  ShoppingCart,
  Truck,
  Receipt,
  ShoppingBag,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { formatId } from "@/utils/order-helpers";

export default function OrderDetails() {
  const router = useRouter();
  const { slug } = router.query;

  const [isLoading, setIsLoading] = useState(true);

  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const order = useGetOrderByIdQuery(userId, slug as string);
  const orderData = order.data;
  const orderedDate = new Date(orderData?.created_at ?? Date.now());
  const orderShipping = orderData?.shipping_addresses;
  const orderItems = orderData?.order_items;

  // Fetch customer and billing address data
  const customerData = useGetCustomerQuery(userId);
  const billingAddresses = useGetBillingAddressesQuery(userId);
  const billingAddress = billingAddresses.data?.find(
    (addr) => addr.id === orderData?.billing_address_id
  );

  const addItem = useCartStore((state) => state.addItem);
  const totalAmount = orderData?.total_amount ?? 0;

  // Missing products detection
  const missingProductsDetection = useMissingProductsDetection(
    orderItems,
    totalAmount,
    (orderData as any)?.tax_rate || 0,
    orderData?.tax_exempt || false
  );

  const formattedDate = Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
  }).format(orderedDate);

  // Helper function to format payment method string
  const formatPaymentMethod = (method: string): string => {
    return method
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Calculate totals for display
  const calculateItemTotals = () => {
    let subtotal = 0;
    let optionsTotal = 0;

    orderItems?.forEach((item) => {
      const itemPrice = item.item_price || item.products.price || 0;
      const itemSubtotal = itemPrice * item.quantity;
      subtotal += itemSubtotal;

      // Calculate options total
      const options = (item.options as unknown as OrderItemOption[]) ?? [];
      options.forEach((option) => {
        const optionPrice = option.price || 0;
        optionsTotal += optionPrice * item.quantity;
      });
    });

    return {
      subtotal,
      optionsTotal,
      merchandiseTotal: subtotal + optionsTotal,
    };
  };

  const { subtotal, optionsTotal, merchandiseTotal } = calculateItemTotals();
  const orderSubtotal = merchandiseTotal;
  const orderTaxAmount = (orderData as any)?.tax_amount || 0;
  const grandTotal = totalAmount;

  const reorderHandler = () => {
    if (!orderItems) return;

    orderItems.forEach((item) => {
      const itemPrice = item.item_price ?? item.products.price;

      addItem({
        id: item.products.id,
        name: item.products.name,
        price: itemPrice,
        quantity: item.quantity,
        image: item.products.image ?? "",
        sku: item.products.sku ?? "",
        selected: false,
        discount: 0,
        selectedOptions:
          item.options?.map((option) => ({
            name: option.name,
            value: option.value,
            price: option.price,
          })) || [],
      });
    });

    toast({
      title: "Items added to cart",
      description: "Items from this order have been added to your cart.",
      variant: "success",
      duration: 3000,
    });
  };

  useEffect(
    function handleOrderLoading() {
      if (order.isSuccess) {
        setIsLoading(false);
      }
    },
    [order.isSuccess]
  );

  return (
    <StoreLayout>
      <Head>
        <title>
          Order Details | #{orderData?.id.split("-").at(0)}...
          {orderData?.id.split("-").at(-1)}
        </title>
      </Head>
      <div className="min-h-screen bg-white">
        <div className="flex-1 space-y-8 p-8 pt-6">
          {/* Header Section */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="shrink-0"
                  asChild
                >
                  <Link href="/store/orders">
                    <ArrowLeft className="h-5 w-5" />
                    <span className="sr-only">Back to orders</span>
                  </Link>
                </Button>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">
                    Order Details
                  </h1>
                  <p className="text-muted-foreground">
                    View and manage your order information
                  </p>
                </div>
              </div>
              {!isLoading && (
                <OrderStatusBadge
                  status={orderData?.order_statuses?.[0]?.status ?? "pending"}
                />
              )}
            </div>
          </div>

          {isLoading ? (
            <OrderDetailsSkeleton />
          ) : (
            <div className="grid gap-6">
              {/* Quick Stats */}
              <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Order ID
                    </CardTitle>
                    <Package className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      #{formatId(orderData?.id)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Placed on {formattedDate}
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Amount
                    </CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatPrice(totalAmount)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {orderData?.payment_type === "credit_card"
                        ? "Paid with Credit Card"
                        : orderData?.payment_type === "purchase_order"
                          ? "Purchase Order"
                          : "Payment Method"}
                    </p>
                  </CardContent>
                </Card>
                <Card className="hover:shadow-md transition-all">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Delivery Method
                    </CardTitle>
                    <Truck className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {orderData?.delivery_method || "Standard"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {orderData?.ship_collect
                        ? "Ship Collect UPS"
                        : "Standard Shipping"}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Order Details Card */}
              <Card className="hover:shadow-md transition-all">
                <CardHeader>
                  <CardTitle>Order Items</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Missing Products Info */}
                  {missingProductsDetection.hasMissingProducts &&
                    missingProductsDetection.isValid && (
                      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <svg
                              className="w-5 h-5 text-orange-600 mt-0.5"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-orange-800 mb-1">
                              Some products in this order are no longer
                              available.
                            </h4>
                            <p className="text-sm text-orange-700">
                              This order may have contained additional products
                              that are no longer available in our system.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                  {/* Order Items Table */}
                  <div className="border rounded-lg overflow-hidden">
                    <table className="w-full">
                      <thead className="bg-muted/50">
                        <tr>
                          <th className="text-left p-4 font-medium">Product</th>
                          <th className="text-center p-4 font-medium">
                            Quantity
                          </th>
                          <th className="text-right p-4 font-medium">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        {orderItems && orderItems.length > 0 ? (
                          orderItems.map((item) => (
                            <OrderDetailItem
                              key={`order-detail-item-${item.id}`}
                              item={item}
                            />
                          ))
                        ) : (
                          <tr>
                            <td
                              colSpan={3}
                              className="p-4 text-center text-muted-foreground"
                            >
                              No items found.
                            </td>
                          </tr>
                        )}
                      </tbody>
                      <tfoot className="bg-muted/30">
                        <tr>
                          <td
                            colSpan={2}
                            className="p-4 text-right font-medium"
                          >
                            Subtotal:
                          </td>
                          <td className="p-4 text-right font-medium">
                            ${orderSubtotal.toFixed(2)}
                          </td>
                        </tr>
                        {(orderData as any)?.tax_rate &&
                        (orderData as any).tax_rate > 0 &&
                        (orderShipping?.state === "Nevada" ||
                          orderShipping?.state === "NV") ? (
                          <tr>
                            <td colSpan={2} className="p-4 text-right">
                              Tax ({(orderData as any).tax_rate.toFixed(3)}%)
                              {orderData?.tax_exempt && (
                                <span className="text-orange-600 ml-1">
                                  (Exempt)
                                </span>
                              )}
                            </td>
                            <td
                              className={`p-4 text-right ${
                                orderData?.tax_exempt
                                  ? "line-through text-muted-foreground"
                                  : ""
                              }`}
                            >
                              {orderData?.tax_exempt
                                ? "$0.00"
                                : "$" + orderTaxAmount.toFixed(2)}
                            </td>
                          </tr>
                        ) : (
                          ""
                        )}
                        <tr className="border-t-2">
                          <td
                            colSpan={2}
                            className="p-4 text-right text-lg font-bold"
                          >
                            Grand Total:
                          </td>
                          <td className="p-4 text-right text-lg font-bold">
                            ${grandTotal.toFixed(2)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>

                  <Separator />

                  {/* Order Details Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">
                        Order Details
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            Date Purchased:
                          </span>
                          <span className="font-medium">{formattedDate}</span>
                        </div>
                        {orderData?.payment_type && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Payment Method:
                            </span>
                            <span className="font-medium">
                              {formatPaymentMethod(orderData.payment_type)}
                            </span>
                          </div>
                        )}
                        {orderData?.purchase_order && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Purchase Order Number:
                            </span>
                            <span className="font-medium">
                              {orderData.purchase_order}
                            </span>
                          </div>
                        )}
                        {orderData?.delivery_method && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Shipping Method:
                            </span>
                            <span className="font-medium">
                              {orderData?.delivery_method}
                            </span>
                          </div>
                        )}
                        {/* Check if shipping address is for a special country */}
                        {(() => {
                          const isSpecialCountry =
                            orderShipping?.country === "mexico" ||
                            orderShipping?.country === "puerto rico" ||
                            orderShipping?.state === "puerto rico" ||
                            orderShipping?.state === "mexico";

                          // Only show Ship Collect UPS info if not a special country
                          return !isSpecialCountry &&
                            orderData?.ship_collect !== undefined ? (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                Ship Collect UPS:
                              </span>
                              <span className="font-medium">
                                {orderData.ship_collect ? "Yes" : "No"}
                              </span>
                            </div>
                          ) : null;
                        })()}
                        {orderData?.ship_collect &&
                          orderData?.ups_account_number && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                UPS Account Number:
                              </span>
                              <span className="font-medium">
                                {orderData.ups_account_number}
                              </span>
                            </div>
                          )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground">
                        Account Information
                      </h3>
                      <div className="space-y-2 text-sm">
                        {customerData.data?.customer?.customer_number && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Account ID:
                            </span>
                            <span className="font-medium">
                              {formatId(
                                customerData.data.customer.customer_number
                              )}
                            </span>
                          </div>
                        )}
                        {customerData.data?.customer?.primary_contact_name && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Name:</span>
                            <span className="font-medium">
                              {customerData.data.customer.primary_contact_name}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Email:</span>
                          <span className="font-medium">{userData.email}</span>
                        </div>
                        {customerData.data?.customer?.company_name && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Company Name:
                            </span>
                            <span className="font-medium">
                              {customerData.data.customer.company_name}
                            </span>
                          </div>
                        )}
                        {customerData.data?.customer?.phone && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Phone Number:
                            </span>
                            <span className="font-medium">
                              {customerData.data.customer.phone}
                            </span>
                          </div>
                        )}
                        {customerData.data?.customer?.shipping_notes && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">
                              Shipping Notes:
                            </span>
                            <span className="font-medium">
                              {customerData.data.customer.shipping_notes}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Addresses Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Billing Address */}
                    <Card className="space-y-4 border-2">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                          <Receipt className="h-5 w-5 text-primary" />
                          Billing Address
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-sm text-muted-foreground">
                        {billingAddress ? (
                          <>
                            {billingAddress.company_name && (
                              <div className="font-medium">
                                {billingAddress.company_name}
                              </div>
                            )}
                            <div>{billingAddress.address}</div>
                            {billingAddress.address_2 && (
                              <div>{billingAddress.address_2}</div>
                            )}
                            <div>
                              {billingAddress.city}, {billingAddress.state}{" "}
                              {billingAddress.zip_code}
                            </div>
                            <div>{billingAddress.country}</div>
                          </>
                        ) : (
                          <div className="text-muted-foreground">
                            Billing address not available
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Shipping Address */}
                    <Card className="space-y-4 border-2">
                      <CardHeader>
                        <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                          <Truck className="h-5 w-5 text-primary" />
                          Shipping Address
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-sm text-muted-foreground">
                        {customerData.data?.customer?.company_name && (
                          <div className="font-medium">
                            Company: {customerData.data.customer.company_name}
                          </div>
                        )}
                        {orderShipping?.contact_name && (
                          <div>{orderShipping.contact_name}</div>
                        )}
                        {orderShipping?.address && (
                          <div>{orderShipping.address}</div>
                        )}
                        {orderShipping?.city &&
                          orderShipping?.state &&
                          orderShipping?.zip_code && (
                            <div>
                              {orderShipping.city}, {orderShipping.state}{" "}
                              {orderShipping.zip_code}
                            </div>
                          )}
                        {orderShipping?.country && (
                          <div>{orderShipping.country}</div>
                        )}
                        {orderShipping?.contact_number && (
                          <div>Phone: {orderShipping.contact_number}</div>
                        )}
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />
                  <div className="flex flex-col items-start justify-between font-medium w-full">
                    <span className="text-lg">Order Notes</span>
                    <span className="text-sm text-muted-foreground mt-1 p-2 border rounded bg-gray-50 w-full">
                      {orderData?.notes}
                    </span>
                  </div>

                  <Separator />

                  {/* Tax Information */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div className="flex flex-col gap-2">
                        <p className="text-sm font-medium">Tax Information</p>
                        <div className="text-sm text-muted-foreground">
                          {orderData?.tax_exempt ? "Tax Exempt" : "Taxable"}
                          {(orderData as any)?.tax_rate &&
                          (orderData as any).tax_rate > 0 ? (
                            <>
                              <br />
                              Tax Rate: {(orderData as any).tax_rate.toFixed(3)}
                              %
                              <br />
                              Tax Amount:{" "}
                              {(orderData as any).tax_amount
                                ? formatPrice((orderData as any).tax_amount)
                                : "$0.00"}
                            </>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>

                {/* Actions */}
                <div className="p-6 bg-muted/50 rounded-b-lg">
                  <Button onClick={reorderHandler} className="w-full" size="lg">
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    Reorder Items
                  </Button>
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </StoreLayout>
  );
}

function OrderDetailsSkeleton() {
  return (
    <div className="grid gap-6">
      {/* Quick Stats Skeleton */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-[100px]" />
              <Skeleton className="h-4 w-4 rounded-full" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-[120px] mb-1" />
              <Skeleton className="h-4 w-[140px]" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Order Details Skeleton */}
      <Card className="hover:shadow-md transition-all">
        <CardHeader>
          <Skeleton className="h-6 w-[150px]" />
        </CardHeader>
        <CardContent className="space-y-6">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow>
                <TableHead className="w-[80px]">
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
                <TableHead>
                  <Skeleton className="h-4 w-full" />
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2].map((i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="w-16 h-16 rounded-lg" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[200px] mb-2" />
                    <Skeleton className="h-4 w-[150px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[50px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-5 w-[100px] ml-auto" />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[150px] ml-auto" />
                      <Skeleton className="h-4 w-[100px] ml-auto" />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Other sections skeleton */}
          {["Shipping Details", "Payment Information", "Order Summary"].map(
            (section, i) => (
              <div key={section}>
                <Separator />
                <div className="py-4">
                  <Skeleton className="h-6 w-[200px] mb-4" />
                  <Card className="bg-muted/50">
                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-[250px]" />
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-4 w-[150px]" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )
          )}
        </CardContent>

        <div className="p-6 bg-muted/50 rounded-b-lg">
          <Skeleton className="h-11 w-full" />
        </div>
      </Card>
    </div>
  );
}

function OrderDetailItem({ item }: { item: OrderItemsWithProduct }) {
  const options = (item.options as unknown as OrderItemOption[]) ?? [];
  const itemPrice = item.item_price || item.products.price || 0;
  const itemSubtotal = itemPrice * item.quantity;
  let itemOptionsTotal = 0;

  // Calculate options total for this item
  options.forEach((option) => {
    const optionPrice = option.price || 0;
    itemOptionsTotal += optionPrice * item.quantity;
  });

  const itemTotal = itemSubtotal + itemOptionsTotal;
  const imageQuery = useGetImage(item.products.image ?? "");
  const image = (imageQuery.data ?? item.products.image) || "";

  return (
    <tr className="border-t">
      <td className="p-4">
        <div className="flex items-start gap-4">
          <div className="aspect-square h-16 w-16 rounded-lg border overflow-hidden">
            {item.products.image && (
              <img
                src={image}
                alt={item.products.name}
                className="h-full w-full object-cover"
              />
            )}
          </div>
          <div className="flex-1 space-y-2">
            <div className="font-medium">{item.products.name}</div>
            <div className="text-sm text-muted-foreground">
              SKU: {item.products.sku}
            </div>
            {options && options.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs font-medium text-muted-foreground">
                  Selected Options:
                </div>
                {options.map((option, index) => {
                  const optionPrice = option.price || 0;
                  const optionTotalPrice = optionPrice * item.quantity;
                  return (
                    <div
                      key={`${item.products.id}-${option.name}-${option.value}-${index}`}
                      className="text-xs text-muted-foreground flex justify-start gap-2"
                    >
                      <span>
                        {option.name}: {option.value}
                      </span>
                      {optionPrice > 0 && (
                        <span className="text-green-600">
                          +${optionPrice.toFixed(2)} × {item.quantity} = $
                          {optionTotalPrice.toFixed(2)}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </td>
      <td className="p-4 text-center">
        <div className="font-medium">{item.quantity}</div>
      </td>
      <td className="p-4 text-right">
        <div className="space-y-1">
          <div className="font-medium">${itemPrice.toFixed(2)}</div>
          {itemOptionsTotal > 0 && (
            <div className="text-sm text-green-600">
              Options: ${(itemOptionsTotal / item.quantity).toFixed(2)}
            </div>
          )}
          <div className="text-sm font-medium border-t pt-1">
            Item Total: ${(itemTotal / item.quantity).toFixed(2)}
          </div>
        </div>
      </td>
    </tr>
  );
}
