// SHOWS THE DEFAULT VARIANTS OF FREQUENTLY ASKED QUESTIONS
// ONLY EDIT THIS FILE IF YOU HAVE A NEW VARIANT

import type { Meta, StoryObj } from "@storybook/react";
import { Components } from "components/list";
import { Sections } from "types";
import {
  faqsSchema,
  faqsInitialValue,
} from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { filterArgsByVariant } from "components/common";
import { renameVariantKeys } from "utils/schemas";

const args = {
  ...faqsInitialValue,
};

const FAQsComponent = Components.faqs;

const meta = {
  title: "Components/FAQs/Defaults",
  component: FAQsComponent,
  tags: ["autodocs"],
  render: ({ variant, ...args }) => {
    const data = {
      variant: variant,
      variants: args,
    };

    return <FAQsComponent data={renameVariantKeys(data)} />;
  },
} satisfies Meta<typeof FAQsComponent> & any;

export default meta;
type Story = StoryObj<Sections>;

const filterArgs = (variant: string) => {
  return {
    args: {
      variant: variant,
      ...filterArgsByVariant(faqsSchema, args, variant),
    },
  };
};

export const variant_a: Story = filterArgs("variant_a");
export const variant_b: Story = filterArgs("variant_b");
export const variant_c: Story = filterArgs("variant_c");
