import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { groq } from "next-sanity";
import { PreviewSuspense } from "next-sanity/preview";
import { sanityClient, getClient } from "lib/sanity.client";
import {
  allCategories,
  postsByTag,
  globalSEOQuery,
  allArchives,
  tagQuery,
  variants,
} from "pages/api/query";
import { usePreview } from "lib/sanity.preview";
import TagSection from "components/tag";
import { PreviewBanner } from "components/PreviewBanner";
import { PreviewNoContent } from "components/PreviewNoContent";
import { filterDataToSingleItem } from "components/list";
import PageNotFound from "pages/404";
import InlineEditorContextProvider from "context/InlineEditorContext";
import { GetStaticPaths, GetStaticProps } from "next";
import {
  CommonPageData,
  BlogsData,
  PostCategory,
  DefaultSeoData,
  Categories,
  ArchiveYearAndMonth,
  PostTag,
} from "types";
import { ArchivePageData } from "pages/archive/[year]/[month]";

interface PageBySlugProps {
  data: TagPageData | null;
  preview: boolean;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

interface DocumentWithPreviewProps {
  data: TagPageData | null;
  slug: string | string[] | undefined;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

export interface TagPageData {
  tag: PostTag;
  posts: BlogsData[];
  categories: Categories[];
  allArchives?: {
    years: any[];
    months: any[];
    validCombinations: Array<{
      year: {
        _id: string;
        title: string;
        slug: string;
      };
      month: {
        _id: string;
        title: string;
        slug: string;
      };
    }>;
  };
  archive: ArchiveYearAndMonth[];
}

export interface PageData extends CommonPageData {
  collections: any;
  slug: string | string[];
  title: string;
}

export function TagPage({
  data,
  preview,
  token,
  source,
  defaultSeo,
}: PageBySlugProps) {
  const router = useRouter();
  const slug = router.query.slug;
  const showInlineEditor = source === "studio";

  if (preview) {
    return (
      <>
        <PreviewBanner />
        <PreviewSuspense fallback="Loading...">
          <InlineEditorContextProvider showInlineEditor={showInlineEditor}>
            <DocumentWithPreview
              {...{ data, token, slug, source, defaultSeo }}
            />
          </InlineEditorContextProvider>
        </PreviewSuspense>
      </>
    );
  }

  return <Document {...{ data, defaultSeo }} />;
}

/**
 *
 * @param {data} Data from getStaticProps based on current slug value
 *
 * @returns Document with published data
 */
function Document({
  data,
  defaultSeo,
}: {
  data: TagPageData | null;
  defaultSeo: DefaultSeoData;
}) {
  if (!data || !data.tag) {
    return <PageNotFound />;
  }

  const { title, seo } = data.tag;
  const capitalizeWords = (str) =>
    str
      ?.split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

  return (
    <>
      <Head>
        <title>
          Tag | {seo?.seoTitle ?? capitalizeWords(title) ?? "WebriQ Studio"}
        </title>
      </Head>

      {data?.tag && (
        <TagSection
          tag={data.tag}
          posts={data.posts || []}
          categories={data.categories || []}
          allArchives={data.allArchives}
          archive={data.archive || []}
        />
      )}
    </>
  );
}

/**
 *
 * @param data Data from getStaticProps based on current slug value
 * @param slug Slug value from getStaticProps
 * @param token Token value supplied via `/api/preview` route
 * @param source Source value supplied via `/api/preview` route
 *
 * @returns Document with preview data
 */
function DocumentWithPreview({
  data,
  slug,
  token,
  defaultSeo,
}: DocumentWithPreviewProps) {
  if (!data || !data.tag) {
    return <PageNotFound />;
  }

  const { title, seo } = data.tag;

  return (
    <>
      <Head>
        <title>{seo?.seoTitle ?? title ?? "WebriQ Studio"} | Tag</title>
      </Head>

      {data?.tag && (
        <TagSection
          tag={data.tag}
          posts={data.posts || []}
          categories={data.categories || []}
          allArchives={data.allArchives}
          archive={data.archive || []}
        />
      )}
    </>
  );
}

export const getStaticProps: GetStaticProps<PageBySlugProps> = async ({
  params,
  preview = false,
  previewData = {},
}: any) => {
  if (!params?.slug) {
    console.error("No slug provided to getStaticProps");
    return {
      notFound: true,
    };
  }

  const client =
    preview && previewData?.token
      ? getClient(false).withConfig({ token: previewData.token })
      : getClient(preview);

  try {
    // Fetch the tag document and posts using the tag slug
    const [tag, posts, categories, globalSEO, archives] = await Promise.all([
      client
        .fetch(tagQuery, { slug: params.slug })
        .then((results) => filterDataToSingleItem(results, preview)),
      client.fetch(postsByTag, { slug: params.slug }),
      client.fetch(allCategories),
      client.fetch(globalSEOQuery),
      client.fetch(allArchives),
    ]);

    // If no tag found, return 404
    if (!tag) {
      return {
        notFound: true,
      };
    }

    const props = {
      preview,
      token: (preview && previewData.token) || "",
      source: (preview && previewData?.source) || "",
      data: {
        tag: tag,
        posts: posts || [],
        categories: categories || [],
        allArchives: archives || null,
        archive: [],
      },
      defaultSeo: globalSEO
        ? {
            defaultSeoTitle: globalSEO.defaultSeoTitle || null,
            defaultSeoDescription: globalSEO.defaultSeoDescription || null,
            defaultSeoKeywords: globalSEO.defaultSeoKeywords || null,
            defaultSeoSynonyms: globalSEO.defaultSeoSynonyms || null,
            defaultSeoImage: globalSEO.defaultSeoImage || null,
          }
        : {
            defaultSeoTitle: null,
            defaultSeoDescription: null,
            defaultSeoKeywords: null,
            defaultSeoSynonyms: null,
            defaultSeoImage: null,
          },
    };

    return {
      props,
      revalidate: 60,
    };
  } catch (error) {
    console.error("Error in getStaticProps:", error);
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
      });
    }
    return {
      notFound: true,
    };
  }
};

export const getStaticPaths: GetStaticPaths = async () => {
  // Fetch all tag documents with slugs
  const tagSlugs = await sanityClient.fetch<string[]>(
    groq`*[_type == "tag" && defined(slug.current)].slug.current`
  );

  const paths = tagSlugs.map((slug) => ({
    params: { slug },
  }));

  return {
    paths,
    fallback: true,
  };
};

export default React.memo(TagPage);
