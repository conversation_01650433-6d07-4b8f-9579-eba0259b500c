# API Documentation - Google Docs Compatible Version

## Overview
This document provides comprehensive API documentation for the Maxton Valve application. All endpoints require proper authentication unless otherwise specified.

**Base URL:** `https://your-domain.com`

**Authentication:** Bearer token in Authorization header

---

## Table of Contents

1. [Authentication Endpoints](#authentication-endpoints)
2. [User Management](#user-management)
3. [Product Endpoints](#product-endpoints)
4. [Category Endpoints](#category-endpoints)
5. [Customer Endpoints](#customer-endpoints)
6. [Order Management](#order-management)
7. [Address Management](#address-management)
8. [Group Management](#group-management)
9. [Discount Management](#discount-management)
10. [Admin Dashboard](#admin-dashboard)
11. [Tax Management](#tax-management)

---

## Authentication Endpoints

### POST /api/auth/sign-in
**Purpose:** Authenticate a user and create session

**Request Payload:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword123"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "access_token_expires_in": 3600,
  "access_token_expires_at": 1703123456789,
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  },
  "role": "customer",
  "permissions": ["read:products", "create:orders"]
}
```

**Error Response:**
```json
{
  "error": "Invalid credentials"
}
```

**Status Codes:** 200 (Success), 400 (Invalid credentials), 401 (Unauthorized), 500 (Server error)

---

### POST /api/auth/refresh
**Purpose:** Refresh user session token

**Request:** Requires refresh token in cookies (no body payload)

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "access_token_expires_in": 3600,
  "access_token_expires_at": 1703123456789,
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

**Error Response:**
```json
{
  "error": "Session expired"
}
```

**Status Codes:** 200 (Success), 403 (Session expired), 500 (Server error)

---

### POST /api/auth/callback
**Purpose:** Handle email verification callback

**Request Payload:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": "3600",
  "refresh_token": "refresh_token_string",
  "token_type": "Bearer",
  "type": "signup"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verification successful"
}
```

**Error Response:**
```json
{
  "error": "Invalid token"
}
```

**Status Codes:** 200 (Success), 400 (Invalid token), 500 (Server error)

---

### POST /api/auth/forgot-password
**Purpose:** Initiate password reset process

**Request Payload:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset email sent successfully"
}
```

**Error Response:**
```json
{
  "error": "Email not found"
}
```

**Status Codes:** 200 (Success), 400 (Invalid email/Email not found), 500 (Server error)

---

## User Management

### GET /api/users
**Purpose:** Fetch all users (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/users?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**
```json
{
  "users": [
    {
      "id": "user123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "status": "active",
      "role": "customer",
      "created_at": "2023-12-01T10:00:00Z"
    }
  ],
  "total": 25
}
```

**Error Response:**
```json
{
  "error": "Unauthorized access"
}
```

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### GET /api/users/pending
**Purpose:** Fetch users with pending status (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/users/pending?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "data": [
    {
      "id": "user789",
      "email": "<EMAIL>",
      "first_name": "Alice",
      "last_name": "Johnson",
      "status": "pending",
      "created_at": "2023-12-01T12:00:00Z",
      "phone": "+**********"
    }
  ],
  "total": 5
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/users
**Purpose:** Create new user account

**Request Payload:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "first_name": "Jane",
  "last_name": "Smith",
  "phone": "+**********"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User account created successfully",
  "user": {
    "id": "user456",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "status": "pending"
  }
}
```

**Error Response:**
```json
{
  "error": "Email already exists"
}
```

**Status Codes:** 200 (Success), 400 (Invalid data), 500 (Server error)

---

### PUT /api/users/[slug]/status
**Purpose:** Update user status (Admin only)
**Authentication:** Required (Admin)

**Request Payload:**
```json
{
  "status": "approved"
}
```

**Response (200 - Success):**
```json
{
  "success": true,
  "message": "User status updated successfully",
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "status": "approved",
    "updated_at": "2023-12-01T16:30:00Z"
  }
}
```

**Response (400 - Invalid Status):**
```json
{
  "error": "Invalid status value. Allowed values: pending, approved, rejected"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (404 - User Not Found):**
```json
{
  "error": "User not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/users/[userId]
**Purpose:** Fetch user data by ID
**Authentication:** Required

**Example Request:**
```
GET /api/users/user123
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "data": {
    "id": "user123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+**********",
    "status": "active",
    "role": "customer",
    "created_at": "2023-11-15T10:00:00Z",
    "updated_at": "2023-12-01T14:30:00Z"
  }
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (404 - User Not Found):**
```json
{
  "error": "User not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/rejected-users
**Purpose:** Fetch rejected users (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/rejected-users?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "data": [
    {
      "id": "user456",
      "email": "<EMAIL>",
      "first_name": "Bob",
      "last_name": "Wilson",
      "status": "rejected",
      "rejection_reason": "Incomplete documentation",
      "created_at": "2023-11-20T09:00:00Z",
      "rejected_at": "2023-11-25T15:30:00Z"
    }
  ],
  "total": 3
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

## Product Endpoints

### GET /api/products
**Purpose:** Fetch paginated public products

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |
| category | string | - | Filter by category |
| search | string | - | Search term |

**Example Request:**
```
GET /api/products?page=1&limit=5&category=valves&search=ball
```

**Response:**
```json
{
  "products": [
    {
      "id": "prod123",
      "name": "Ball Valve 1/2 inch",
      "description": "High-quality brass ball valve",
      "price": 29.99,
      "sku": "BV-12-001",
      "stock": 150,
      "slug": "ball-valve-half-inch",
      "images": ["image1.jpg", "image2.jpg"],
      "categories": ["valves", "brass-fittings"]
    }
  ],
  "categories": [
    {"id": "cat1", "name": "Valves", "value": "valves"},
    {"id": "cat2", "name": "Fittings", "value": "fittings"}
  ],
  "total": 45,
  "totalPages": 9
}
```

**Error Response:**
```json
{
  "error": "Internal server error"
}
```

**Status Codes:** 200 (Success), 500 (Server error)

---

### GET /api/products/[slug]
**Purpose:** Fetch single product by slug
**Note:** Updated from /:id to /[slug] to match actual implementation

**Example Request:**
```
GET /api/products/ball-valve-half-inch
```

**Response (200 - Success):**
```json
{
  "product": {
    "id": "prod123",
    "name": "Ball Valve 1/2 inch",
    "slug": "ball-valve-half-inch",
    "description": "High-quality brass ball valve with full port design",
    "price": 29.99,
    "sku": "BV-12-001",
    "stock": 150,
    "images": ["product_main.jpg", "product_detail1.jpg"],
    "options": [
      {
        "name": "Material",
        "values": ["Brass", "Stainless Steel"]
      }
    ],
    "categories": [
      {
        "id": "cat1",
        "name": "Valves",
        "value": "valves"
      }
    ],
    "group_pricing": [
      {
        "group_id": "wholesale",
        "price": 24.99
      }
    ],
    "created_at": "2023-11-01T10:00:00Z"
  }
}
```

**Response (404 - Product Not Found):**
```json
{
  "error": "Product not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/products
**Purpose:** Create new product (Admin only)
**Authentication:** Required (Admin with create:products permission)

**Request Payload:**
```json
{
  "name": "Ball Valve 3/4 inch",
  "description": "Premium brass ball valve with full port design",
  "price": 45.99,
  "sku": "BV-34-002",
  "stock": 75,
  "options": [
    {
      "name": "Material",
      "values": ["Brass", "Stainless Steel"]
    },
    {
      "name": "Connection Type",
      "values": ["Threaded", "Soldered"]
    }
  ],
  "images": ["product_main.jpg", "product_detail1.jpg", "product_detail2.jpg"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Product created successfully",
  "product": {
    "id": "prod789",
    "name": "Ball Valve 3/4 inch",
    "slug": "ball-valve-three-quarter-inch",
    "price": 45.99,
    "sku": "BV-34-002",
    "created_at": "2023-12-01T16:00:00Z"
  }
}
```

**Error Response:**
```json
{
  "error": "SKU already exists"
}
```

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### PUT /api/products/[slug]
**Purpose:** Update existing product (Admin only)
**Authentication:** Required (Admin)
**Note:** This endpoint exists but was missing from original documentation

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Product not found), 500 (Server error)

---

### GET /api/customers/[userId]/products
**Purpose:** Fetch products for authenticated customer
**Authentication:** Required

**Query Parameters:** Same as GET /api/products

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/products/[slug]/categories
**Purpose:** Assign categories to product (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| category_ids | array | Yes | Array of category IDs |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Product not found), 500 (Server error)

---

### DELETE /api/products/[slug]/categories/[category_id]
**Purpose:** Remove category from product (Admin only)
**Authentication:** Required (Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Product/category not found), 500 (Server error)

---

## Category Endpoints

### GET /api/categories
**Purpose:** Fetch all categories with pagination

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/categories?page=1&limit=10
```

**Response (200 - Success):**
```json
{
  "categories": [
    {
      "id": "cat1",
      "name": "Valves",
      "value": "valves",
      "description": "All types of valves",
      "parent_category_id": null,
      "parent_category": null,
      "created_at": "2023-10-01T10:00:00Z"
    },
    {
      "id": "cat2",
      "name": "Ball Valves",
      "value": "ball-valves",
      "description": "Ball valve subcategory",
      "parent_category_id": "cat1",
      "parent_category": {
        "id": "cat1",
        "name": "Valves",
        "value": "valves"
      },
      "created_at": "2023-10-01T10:30:00Z"
    }
  ],
  "total": 15,
  "totalPages": 2
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/categories
**Purpose:** Create new category (Admin only)
**Authentication:** Required (Admin with create:categories permission)

**Request Payload:**
```json
{
  "name": "Gate Valves",
  "value": "gate-valves",
  "description": "Gate valve subcategory",
  "parent_category_id": "cat1"
}
```

**Response (200 - Success):**
```json
{
  "success": true,
  "message": "Category created successfully",
  "category": {
    "id": "cat3",
    "name": "Gate Valves",
    "value": "gate-valves",
    "description": "Gate valve subcategory",
    "parent_category_id": "cat1",
    "created_at": "2023-12-01T17:00:00Z"
  }
}
```

**Response (400 - Invalid Request):**
```json
{
  "error": "Category value already exists"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Insufficient permissions"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### PATCH /api/categories/[id]
**Purpose:** Update existing category (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | No | Category name |
| value | string | No | Category value |
| parent_category_id | string | No | Parent category ID |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Category not found), 500 (Server error)

---

### DELETE /api/categories/[id]
**Purpose:** Delete category (Admin only)
**Authentication:** Required (Admin with delete:categories permission)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Category not found), 500 (Server error)

---

## Customer Endpoints

### GET /api/customers
**Purpose:** Fetch all customers (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/customers?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "customers": [
    {
      "id": "cust123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "status": "active",
      "phone": "+**********",
      "created_at": "2023-11-01T10:00:00Z",
      "group": {
        "id": "group1",
        "name": "Wholesale"
      },
      "categories": [
        {
          "id": "cat1",
          "name": "Valves"
        }
      ]
    }
  ],
  "total": 50
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/customers/[id]
**Purpose:** Fetch customer details
**Authentication:** Required (Owner or Admin)

**Example Request:**
```
GET /api/customers/cust123
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "customer": {
    "id": "cust123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+**********",
    "status": "active",
    "created_at": "2023-11-01T10:00:00Z",
    "group": {
      "id": "group1",
      "name": "Wholesale",
      "discount_percentage": 15
    },
    "categories": [
      {
        "id": "cat1",
        "name": "Valves",
        "value": "valves"
      }
    ]
  }
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/customers/[customerId]/categories
**Purpose:** Fetch categories assigned to customer
**Authentication:** Required (Admin)

**Example Request:**
```
GET /api/customers/cust123/categories
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "customer_categories": [
    {
      "id": "cat1",
      "name": "Valves",
      "value": "valves",
      "assigned_at": "2023-11-15T14:30:00Z"
    },
    {
      "id": "cat2",
      "name": "Fittings",
      "value": "fittings",
      "assigned_at": "2023-11-20T09:15:00Z"
    }
  ]
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/customers/[customerId]/categories
**Purpose:** Assign categories to customer (Admin only)
**Authentication:** Required (Admin with create:customer_categories permission)

**Request Payload:**
```json
{
  "category_ids": ["cat1", "cat2", "cat3"]
}
```

**Example Request:**
```
POST /api/customers/cust123/categories
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "category_ids": ["cat1", "cat2"]
}
```

**Response (200 - Success):**
```json
{
  "success": true,
  "message": "Categories assigned successfully",
  "assigned_categories": [
    {
      "id": "cat1",
      "name": "Valves",
      "value": "valves"
    },
    {
      "id": "cat2",
      "name": "Fittings",
      "value": "fittings"
    }
  ]
}
```

**Response (400 - Invalid Request):**
```json
{
  "error": "Invalid category IDs provided"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Insufficient permissions"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### DELETE /api/customers/[customerId]/categories/[categoryId]
**Purpose:** Remove category from customer (Admin only)
**Authentication:** Required (Admin with delete:customer_categories permission)

**Example Request:**
```
DELETE /api/customers/cust123/categories/cat1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "success": true,
  "message": "Category removed from customer successfully"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Insufficient permissions"
}
```

**Response (404 - Customer/Category Not Found):**
```json
{
  "error": "Customer or category not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/customers/dashboard
**Purpose:** Fetch customer dashboard data
**Authentication:** Required

**Example Request:**
```
GET /api/customers/dashboard
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "totalProducts": 150,
  "totalOrders": 25,
  "recentOrders": [
    {
      "id": "order123",
      "order_number": "ORD-2023-001234",
      "status": "shipped",
      "total_amount": 299.99,
      "created_at": "2023-11-28T10:00:00Z",
      "items_count": 3
    },
    {
      "id": "order124",
      "order_number": "ORD-2023-001235",
      "status": "pending",
      "total_amount": 149.50,
      "created_at": "2023-12-01T14:30:00Z",
      "items_count": 2
    }
  ],
  "memberSince": "2023-01-15T09:00:00Z"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

## Order Management

### GET /api/customers/[id]/orders
**Purpose:** Fetch customer's orders
**Authentication:** Required (Owner or Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/customers/cust123/orders?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "orders": [
    {
      "id": "order123",
      "order_number": "ORD-2023-001234",
      "status": "shipped",
      "total_amount": 299.99,
      "tax_amount": 24.00,
      "subtotal": 275.99,
      "created_at": "2023-11-28T10:00:00Z",
      "updated_at": "2023-11-29T14:30:00Z",
      "tracking_number": "1Z999AA**********",
      "items": [
        {
          "id": "item1",
          "product_id": "prod123",
          "product_name": "Ball Valve 1/2 inch",
          "quantity": 2,
          "unit_price": 29.99,
          "total_price": 59.98
        }
      ]
    }
  ],
  "total": 25
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### GET /api/customers/[id]/orders/[order_id]
**Purpose:** Fetch specific order details
**Authentication:** Required (Owner or Admin)

**Example Request:**
```
GET /api/customers/cust123/orders/order123
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "order": {
    "id": "order123",
    "order_number": "ORD-2023-001234",
    "status": "shipped",
    "total_amount": 299.99,
    "tax_amount": 24.00,
    "subtotal": 275.99,
    "shipping_cost": 15.00,
    "payment_type": "credit_card",
    "delivery_method": "standard_shipping",
    "tracking_number": "1Z999AA**********",
    "tracking_url": "https://tracking.example.com/1Z999AA**********",
    "created_at": "2023-11-28T10:00:00Z",
    "updated_at": "2023-11-29T14:30:00Z",
    "billing_address": {
      "id": "addr_billing_123",
      "address": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zip_code": "12345",
      "country": "USA"
    },
    "shipping_address": {
      "id": "addr_shipping_456",
      "address_line_1": "456 Oak Ave",
      "city": "Somewhere",
      "state": "CA",
      "postal_code": "67890",
      "country": "USA"
    },
    "items": [
      {
        "id": "item1",
        "product_id": "prod123",
        "product_name": "Ball Valve 1/2 inch",
        "product_sku": "BV-12-001",
        "quantity": 2,
        "unit_price": 29.99,
        "total_price": 59.98
      }
    ],
    "notes": "Handle with care"
  }
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (404 - Order Not Found):**
```json
{
  "error": "Order not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/orders
**Purpose:** Create new order
**Authentication:** Required

**Request Payload:**
```json
{
  "billingAddressId": "addr_billing_123",
  "shippingAddressId": "addr_shipping_456",
  "items": [
    {
      "product_id": "prod123",
      "quantity": 2,
      "price": 29.99
    },
    {
      "product_id": "prod456",
      "quantity": 1,
      "price": 45.50
    }
  ],
  "paymentType": "credit_card",
  "delivery_method": "standard_shipping",
  "ship_collect": false,
  "ups_account_number": null,
  "poNumber": "PO-2023-001",
  "poFile": "purchase_order.pdf",
  "tax_exempt": false,
  "notes": "Please handle with care"
}
```

**Response:**
```json
{
  "order": {
    "id": "order789",
    "order_number": "ORD-2023-001234",
    "status": "pending",
    "total_amount": 105.48,
    "tax_amount": 8.44,
    "subtotal": 97.04,
    "items": [
      {
        "product_id": "prod123",
        "product_name": "Ball Valve 1/2 inch",
        "quantity": 2,
        "unit_price": 29.99,
        "total_price": 59.98
      }
    ],
    "billing_address": {
      "id": "addr_billing_123",
      "address": "123 Main St",
      "city": "Anytown",
      "state": "CA",
      "zip_code": "12345"
    },
    "created_at": "2023-12-01T15:30:00Z"
  }
}
```

**Error Response:**
```json
{
  "error": "Product not found: prod999"
}
```

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 404 (Product/address not found), 500 (Server error)

---

### PUT /api/orders/[id]/status
**Purpose:** Update order status (Admin only)
**Authentication:** Required (Admin)

**Request Payload:**
```json
{
  "status": "shipped",
  "tracking_number": "1Z999AA**********",
  "tracking_url": "https://tracking.ups.com/1Z999AA**********"
}
```

**Example Request:**
```
PUT /api/orders/order123/status
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "status": "shipped",
  "tracking_number": "1Z999AA**********"
}
```

**Response (200 - Success):**
```json
{
  "success": true,
  "message": "Order status updated successfully",
  "order": {
    "id": "order123",
    "order_number": "ORD-2023-001234",
    "status": "shipped",
    "tracking_number": "1Z999AA**********",
    "tracking_url": "https://tracking.ups.com/1Z999AA**********",
    "updated_at": "2023-12-01T16:30:00Z"
  }
}
```

**Response (400 - Invalid Status):**
```json
{
  "error": "Invalid status. Allowed values: pending, processing, shipped, delivered, cancelled"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (404 - Order Not Found):**
```json
{
  "error": "Order not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### PATCH /api/customers/[id]/orders/[order_id]
**Purpose:** Cancel order (Customer or Admin)
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 400 (Cannot be cancelled), 401 (Unauthorized), 403 (Forbidden), 404 (Order not found), 500 (Server error)

---

## Address Management

### GET /api/customers/[id]/billing-address
**Purpose:** Fetch customer's billing addresses
**Authentication:** Required (Owner or Admin)

**Example Request:**
```
GET /api/customers/cust123/billing-address
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "billing_address": [
    {
      "id": "addr_billing_123",
      "address": "123 Main St",
      "address2": "Suite 100",
      "city": "Anytown",
      "state": "CA",
      "zip_code": "12345",
      "country": "USA",
      "company_name": "Acme Corp",
      "address_type": "business",
      "effective_date": "2023-01-01",
      "note": "Main office address",
      "created_at": "2023-01-15T10:00:00Z"
    }
  ]
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

### POST /api/customers/[id]/billing-address
**Purpose:** Add new billing address
**Authentication:** Required (Owner or Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address | string | Yes | Address line 1 |
| address2 | string | No | Address line 2 |
| city | string | Yes | City |
| state | string | Yes | State |
| zip_code | string | Yes | ZIP code |
| country | string | Yes | Country |
| company_name | string | No | Company name |
| address_type | string | No | Address type |
| effective_date | string | No | Effective date |
| note | string | No | Additional notes |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### PATCH /api/customers/[id]/billing-address/[billing_id]
**Purpose:** Update billing address
**Authentication:** Required (Owner or Admin)

**Fields:** Same as POST (all optional)

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### DELETE /api/customers/[id]/billing-address/[billing_id]
**Purpose:** Delete billing address
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### GET /api/customers/[id]/shipping-address
**Purpose:** Fetch customer's shipping addresses
**Authentication:** Required (Owner or Admin)

**Example Request:**
```
GET /api/customers/cust123/shipping-address
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "shipping_address": [
    {
      "id": "addr_shipping_456",
      "address_line_1": "456 Oak Ave",
      "address_line_2": "Apt 2B",
      "city": "Somewhere",
      "state": "CA",
      "postal_code": "67890",
      "country": "USA",
      "is_default": true,
      "created_at": "2023-01-20T11:00:00Z"
    }
  ]
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Access denied"
}
```

**Response (404 - Customer Not Found):**
```json
{
  "error": "Customer not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### POST /api/customers/[id]/shipping-address
**Purpose:** Add new shipping address
**Authentication:** Required (Owner or Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address_line_1 | string | Yes | Address line 1 |
| address_line_2 | string | No | Address line 2 |
| city | string | Yes | City |
| state | string | Yes | State |
| postal_code | string | Yes | Postal code |
| country | string | Yes | Country |
| is_default | boolean | No | Default address flag |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Customer not found), 500 (Server error)

---

### PATCH /api/customers/[id]/shipping-address/[shipping_id]
**Purpose:** Update shipping address
**Authentication:** Required (Owner or Admin)

**Fields:** Same as POST (all optional)

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### DELETE /api/customers/[id]/shipping-address/[shipping_id]
**Purpose:** Delete shipping address
**Authentication:** Required (Owner or Admin)

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

### GET /api/billing-addresses/pending
**Purpose:** Fetch pending billing addresses (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/billing-addresses/pending?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "billing_addresses": [
    {
      "id": "addr_pending_789",
      "customer_id": "cust123",
      "customer_name": "John Doe",
      "address": "789 New St",
      "city": "Newtown",
      "state": "CA",
      "zip_code": "54321",
      "status": "pending",
      "submitted_at": "2023-12-01T10:00:00Z"
    }
  ],
  "total": 5
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/billing-addresses/pending
**Purpose:** Approve/deny pending billing address (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| address_id | string | Yes | Address ID |
| status | string | Yes | 'approved' or 'denied' |
| notes | string | No | Admin notes |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 404 (Address not found), 500 (Server error)

---

## Group Management

### GET /api/groups
**Purpose:** Fetch all groups (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/groups?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "groups": [
    {
      "id": "group1",
      "name": "Wholesale",
      "description": "Wholesale customer group",
      "discount_percentage": 15,
      "member_count": 25,
      "created_at": "2023-01-01T10:00:00Z"
    },
    {
      "id": "group2",
      "name": "Retail",
      "description": "Retail customer group",
      "discount_percentage": 5,
      "member_count": 45,
      "created_at": "2023-01-01T10:30:00Z"
    }
  ],
  "total": 8
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/groups
**Purpose:** Create new group (Admin only)
**Authentication:** Required (Admin)

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| name | string | Yes | Group name |
| description | string | No | Group description |

**Status Codes:** 200 (Success), 400 (Invalid request), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

## Discount Management

### GET /api/discounts
**Purpose:** Fetch all discounts (Admin only)
**Authentication:** Required (Admin)

**Query Parameters:**
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | number | 1 | Page number |
| limit | number | 10 | Items per page |

**Example Request:**
```
GET /api/discounts?page=1&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "discounts": [
    {
      "id": "disc1",
      "code": "SAVE10",
      "description": "10% off all orders",
      "discount_percentage": 10,
      "is_active": true,
      "valid_from": "2023-12-01T00:00:00Z",
      "valid_until": "2023-12-31T23:59:59Z",
      "usage_count": 45,
      "created_at": "2023-11-01T10:00:00Z"
    }
  ],
  "total": 8,
  "totalPages": 1
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

**Status Codes:** 200 (Success), 401 (Unauthorized), 403 (Forbidden), 500 (Server error)

---

### POST /api/discounts
**Purpose:** Create new discount
**Authentication:** Required

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| code | string | Yes | Discount code |
| description | string | No | Discount description |
| discount_percentage | number | Yes | Discount percentage |

**Status Codes:** 200 (Success), 400 (Invalid request), 500 (Server error)

---

## Admin Dashboard

### GET /api/admin/dashboard
**Purpose:** Fetch admin dashboard data
**Authentication:** Required (Admin)

**Example Request:**
```
GET /api/admin/dashboard
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response (200 - Success):**
```json
{
  "totalSales": 125750.50,
  "totalOrders": 342,
  "totalCustomers": 89,
  "totalPendingCustomers": 12,
  "totalProducts": 156,
  "totalDiscounts": 8,
  "salesData": [
    {
      "month": "2023-11",
      "sales": 15420.75,
      "orders": 45
    },
    {
      "month": "2023-12",
      "sales": 18950.25,
      "orders": 52
    }
  ],
  "recentOrders": [
    {
      "id": "order123",
      "order_number": "ORD-2023-001234",
      "customer_name": "John Doe",
      "total_amount": 299.99,
      "status": "pending",
      "created_at": "2023-12-01T14:30:00Z"
    }
  ]
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

## Tax Management

### PUT /api/tax-rates/[id]
**Purpose:** Update tax rate for specific city (Admin only)
**Authentication:** Required (Admin)

**Request Payload:**
```json
{
  "tax_rate": 8.75
}
```

**Example Request:**
```
PUT /api/tax-rates/city123
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "tax_rate": 8.75
}
```

**Response (200 - Success):**
```json
{
  "taxRate": {
    "id": "city123",
    "city": "Las Vegas",
    "state": "NV",
    "tax_rate": 8.75,
    "updated_at": "2023-12-01T17:00:00Z"
  }
}
```

**Response (400 - Invalid Request):**
```json
{
  "error": "Tax rate must be between 0 and 100"
}
```

**Response (401 - Unauthorized):**
```json
{
  "error": "Authentication required"
}
```

**Response (403 - Forbidden):**
```json
{
  "error": "Admin access required"
}
```

**Response (404 - Tax Rate Not Found):**
```json
{
  "error": "Tax rate not found"
}
```

**Response (500 - Server Error):**
```json
{
  "error": "Internal server error"
}
```

---

## Status Codes Reference

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid data or parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 405 | Method Not Allowed |
| 500 | Internal Server Error |

---

## Authentication Notes

1. **Bearer Token:** Include in Authorization header as `Bearer <token>`
2. **Session Management:** Tokens expire and need refresh via `/api/auth/refresh`
3. **Permissions:** Some endpoints require specific permissions beyond role-based access
4. **Admin Access:** Admin endpoints require admin role and proper permissions

---

## Updates Made in This Version

**Corrections from Original Documentation:**
1. Updated `/api/products/:id` to `/api/products/[slug]` to match actual implementation
2. Added missing `PUT /api/products/[slug]` endpoint for product updates
3. Added missing `GET /api/users/pending` endpoint
4. Added missing `GET /api/rejected-users` endpoint
5. Added missing category management endpoints (`PATCH` and `DELETE /api/categories/[id]`)
6. Added missing order status update endpoint (`PUT /api/orders/[id]/status`)
7. Added missing individual order endpoint (`GET /api/customers/[id]/orders/[order_id]`)
8. Added missing tax rate management endpoint (`PUT /api/tax-rates/[id]`)
9. Corrected billing address endpoints to match actual implementation
10. Updated response structures to match actual API responses

**Format Improvements for Google Docs:**
1. Used tables instead of code blocks for better readability
2. Simplified formatting for Google Docs compatibility
3. Added clear section dividers
4. Organized information in a more structured way
5. Added comprehensive status codes reference
6. Added authentication notes section

---

*Last Updated: [Current Date]*
*Version: 2.0 - Google Docs Compatible*
