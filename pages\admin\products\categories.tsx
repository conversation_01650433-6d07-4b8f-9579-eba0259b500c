import AdminLayout from "@/components/features/admin/layout";
import { CategoriesDataTable } from "@/components/features/admin/categories/categories-table";
// import { ProductCategoriesDataTable } from "@/components/features/admin/categories/product-categories-table";
import { CustomerCategoriesDataTable } from "@/components/features/admin/categories/customer-categories-table";
import Head from "next/head";

export default function Categories() {
    return (
        <AdminLayout>
            <Head>
                <title>Categories</title>
            </Head>
            <CategoriesDataTable />
            {/* <ProductCategoriesDataTable /> */}
            {/* <CustomerCategoriesDataTable /> */}
        </AdminLayout>
    );
}