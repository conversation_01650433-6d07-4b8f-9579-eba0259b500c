import { createMailer, createMailerOptions } from "@/lib/mailer";
import { sendEmail } from "@/lib/mailer/sender";
import { createRegistrationConfirmationTemplate } from "@/lib/mailer/templates";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient, supabaseClient } from "@/supabase/index";
import {
  BusinessDetail,
  Customer,
  Group,
  PublicUser,
  UserRole,
  UserStatus,
} from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: checkAdmin(getAllUsersHandler),
  POST: createNewUserHandler,
});

const createPublicUserSchema = z.object({
  user_details: z.object({
    first_name: z.string().min(1, { message: "First name is required" }),
    last_name: z.string().min(1, { message: "Last name is required" }),
    email: z.string().email({ message: "Invalid email" }),
    telephone: z.string().min(1, { message: "Telephone is required" }),
    company_name: z.string().min(1, { message: "Company name is required" }),
    address: z.string().min(1, { message: "Address is required" }),
    address_2: z.string().optional(),
    address_type: z
      .enum(["residential", "commercial"])
      .refine((value) => value === "residential" || value === "commercial", {
        message:
          "Invalid address type, must be one of 'residential' or 'commercial'",
      }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    zip_code: z.string().min(1, { message: "Zip code is required" }),
    country: z.string().min(1, { message: "Country is required" }),
    effective_date: z
      .string()
      .datetime({
        message: "Invalid effective date format",
      })
      .refine(
        (dateStr) => {
          const date = new Date(dateStr);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return date >= today;
        },
        {
          message: "Effective date cannot be in the past",
        }
      )
      .optional(),
    password: z.string().min(1, { message: "Password is required" }),
    subscribe_newsletter: z.boolean().default(false),
  }),
  business_details: z.object({
    business_type: z.string().optional(),
    business_nature: z.string().optional(),
    website: z.string().optional(),
    maxton_account: z.string().optional(),
    has_mechanic: z.enum(["yes", "no", "use service company"], {
      required_error: "Mechanic status is required",
    }),
    number_of_elevators: z.coerce.number().optional(),
    authorized_contact_name: z.string().optional(),
    buyer_name: z.string().optional(),
    buyer_phone: z.string().optional(),
    technical_contact: z.string().optional(),
    technical_contact_phone: z.string().optional(),
    account_payable_contact: z.string().optional(),
    account_payable_phone: z.string().optional(),
    technician: z.string().optional(),
  }),
});

export type CreateUserDTO = z.infer<typeof createPublicUserSchema>;

export interface CreateUserResponse {
  data: { id: string };
  error?: string;
}

async function createNewUserHandler(req: NextApiRequest, res: NextApiResponse) {
  const { data, error } = createPublicUserSchema.safeParse(req.body);

  if (error) {
    return res.status(400).json({
      error: "Invalid request",
    });
  }

  const { user_details, business_details }: CreateUserDTO = data;

  const {
    first_name,
    last_name,
    email,
    telephone,
    password,
    address,
    address_2,
    address_type,
    company_name,
    city,
    state,
    zip_code,
    country,
    effective_date,
    subscribe_newsletter,
  } = user_details;

  const supabaseAdminClient = createSupabaseAdminClient();

  const getUser = await supabaseAdminClient
    .schema("public")
    .from("users")
    .select("id")
    .eq("email", email)
    .single();

  if (!!getUser.data?.id) {
    return res.status(400).json({
      error: "User already exists",
    });
  }

  const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL;

  const signup = await supabaseClient.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${SITE_URL}/auth/callback`,
    },
  });

  if (signup.error) {
    return res.status(400).json({
      error: signup.error.message,
    });
  }

  const id = signup.data.user?.id;

  if (!id) {
    console.log("signup error", signup.error);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const newUser = await supabaseAdminClient
    .schema("public")
    .from("users")
    .insert({
      id,
      first_name,
      last_name,
      role: "manager",
      status: "pending",
      email: email,
      phone: telephone,
      has_subscribed_to_newsletter: subscribe_newsletter,
    });

  if (
    newUser.error &&
    newUser.error.message.includes("violates foreign key constraint")
  ) {
    console.log("newUser error violates foreign key constraint", newUser.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "User doesn't exist",
    });
  }

  if (
    newUser.error &&
    newUser.error.message.includes(
      "duplicate key value violates unique constraint"
    )
  ) {
    console.log(
      "newUser error duplicate key value violates unique constraint",
      newUser.error
    );

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "User already exists",
    });
  }

  if (newUser.error) {
    console.log("newUser error", newUser.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const tempCustomer = await supabaseAdminClient
    .schema("public")
    .from("customers")
    .insert({
      user_id: id,
      primary_contact_name: `${first_name} ${last_name}`,
      status: "pending",
      role: "customer",
      company_name: company_name,
      credit_limit: 0,
    })
    .select("id")
    .single();

  if (tempCustomer.error) {
    console.log("tempCustomer error", tempCustomer.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const billingInfo = await supabaseAdminClient
    .schema("public")
    .from("billing_addresses")
    .insert({
      customer_id: tempCustomer.data.id,
      address,
      address_2,
      address_type,
      company_name,
      city,
      state,
      zip_code,
      country,
      effective_date: effective_date || new Date().toISOString(),
      default: true,
      approved: true,
    });

  if (billingInfo.error) {
    console.log("billingInfo error", billingInfo.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  const businessDetails = await supabaseAdminClient
    .from("business_details")
    .insert({
      ...business_details,
      user_id: id,
    });

  if (businessDetails.error) {
    console.log("businessDetails error", businessDetails.error);

    await supabaseAdminClient.auth.admin.deleteUser(id);

    return res.status(400).json({
      error: "Something went wrong, please contact support",
    });
  }

  // Send registration confirmation email
  try {
    const loginUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/log-in`;

    const emailTemplate = createRegistrationConfirmationTemplate({
      to: email,
      name: `${first_name} ${last_name}`,
      email: email,
      address: address,
      city: city,
      state: state,
      zip_code: zip_code,
      country: country,
      companyName: company_name,
      businessType: business_details.business_type,
      businessNature: business_details.business_nature,
      website: business_details.website,
      maxtonAccount: business_details.maxton_account,
      numberOfElevators: business_details.number_of_elevators,
      registrationDate: new Date().toISOString(),
      loginUrl: loginUrl,
      additionalNotes:
        "Our team is reviewing your application. Thank you for your patience.",
    });

    const mailerOptions = createMailerOptions();
    const mailer = createMailer(mailerOptions);
    await sendEmail(mailer, emailTemplate);
  } catch (emailError) {
    console.error("Error sending registration confirmation email:", emailError);
    // Continue with the response even if the email fails
  }

  return res.status(200).json({
    data: { id },
  });
}

export interface CustomerWithGroup extends Customer {
  group_data?: { data: Group };
}

export interface PublicUserWithCustomer extends Omit<PublicUser, "notes"> {
  notes?: string | null;
  customer_data?: CustomerWithGroup[];
  business_details?: BusinessDetail[] | null;
}

export interface GetAllUsersResponse {
  data?: PublicUserWithCustomer[];
  error?: string;
  total?: number;
}

/**
 * Get all users handler with optional role-based filtering
 *
 * @param roles - Optional query parameter to filter users by role(s).
 *                Supports single role (e.g., "admin") or comma-separated multiple roles (e.g., "admin,staff").
 *                If not provided, returns all users regardless of role.
 */
async function getAllUsersHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetAllUsersResponse>
) {
  const query = req.query;
  const page = query.page ? parseInt(query.page as string) : 1;
  const limit = query.limit ? parseInt(query.limit as string) : 10;
  const sortBy = query.sortBy ? String(query.sortBy) : "created_at";
  const sortOrder = query.sortOrder ? String(query.sortOrder) : "desc";

  const fromIndex = (page - 1) * limit;
  const toIndex = fromIndex + limit - 1;

  const status = query.status as string | undefined;
  const groupId = query.group_id as string | undefined;

  const rolesParam = query.roles as string;
  const validUserRoles: UserRole[] = ["admin", "staff", "manager"];
  const roles: UserRole[] = rolesParam
    ? (rolesParam
        .split(",")
        .map((r) => r.trim().toLowerCase())
        .filter((r) => validUserRoles.includes(r as UserRole)) as UserRole[])
    : [];

  const supabaseAdminClient = createSupabaseAdminClient();

  let customersCount = 0;

  try {
    let userIdsFromGroupFilter: string[] | null = null;

    if (groupId && groupId !== "all") {
      const { data: customerGroupEntries, error: cgError } =
        await supabaseAdminClient
          .from("customer_groups")
          .select("customer_id")
          .eq("group_id", groupId);

      if (cgError) {
        console.error("Error fetching customer group data:", cgError);
        return res
          .status(500)
          .json({ error: "Failed to fetch customer group data" });
      }

      if (!customerGroupEntries || customerGroupEntries.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }

      const customerIdsFromGroup = customerGroupEntries.map(
        (cg) => cg.customer_id
      );

      if (customerIdsFromGroup.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }

      customersCount = customerIdsFromGroup.length;
      const customerIds = customerIdsFromGroup.slice(fromIndex, toIndex + 1);

      const { data: customersInGroup, error: custError } =
        await supabaseAdminClient
          .from("customers")
          .select("user_id")
          .in("id", customerIds);

      if (custError) {
        console.error("Error fetching customers in group:", custError);
        return res
          .status(500)
          .json({ error: "Failed to fetch customers in group" });
      }

      if (!customersInGroup || customersInGroup.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }
      userIdsFromGroupFilter = customersInGroup
        .map((c) => c.user_id)
        .filter(Boolean) as string[];

      if (userIdsFromGroupFilter.length === 0) {
        return res.status(200).json({ data: [], total: 0 });
      }
    }

    const countQuery = supabaseAdminClient
      .from("users")
      .select("id", { count: "exact", head: true });

    const usersQuery = supabaseAdminClient.from("users").select(
      `
        id,
        created_at,
        updated_at,
        status,
        role,
        first_name,
        last_name,
        email,
        notes,
        phone
        `
    );

    // Apply status filtering
    if (status && status !== "all") {
      if (["approved", "pending", "rejected"].includes(status)) {
        countQuery.eq("status", status as UserStatus);
        usersQuery.eq("status", status as UserStatus);
      }
      // If status is provided but not 'all' and not a recognized specific status,
      // then no users will likely be returned, which is implicitly handled by Supabase.
    } else if (status === "all") {
      // Explicitly do nothing for status filtering if "all" is selected,
      // meaning all users, including rejected, will be considered based on other filters.
    } else {
      // If status is not provided at all (undefined), also do nothing for status filtering by default.
      // This ensures all users (including rejected) are fetched if no status is specified.
    }

    if (roles && roles.length > 0) {
      countQuery.in("role", roles);
      usersQuery.in("role", roles);
    }

    if (userIdsFromGroupFilter && userIdsFromGroupFilter.length > 0) {
      countQuery.in("id", userIdsFromGroupFilter);
      usersQuery.in("id", userIdsFromGroupFilter);
    }

    // Apply sorting - handle company name sorting with proper server-side sorting
    const ascending = sortOrder === "asc";
    let usersResult;
    let countResult = await countQuery;

    if (sortBy === "companyName") {
      // For company name sorting, we need to sort the entire dataset before pagination
      // Step 1: Get all users with their company names and apply filters
      let allUsersQuery = supabaseAdminClient.from("users").select(
        `
          id,
          created_at,
          updated_at,
          status,
          role,
          first_name,
          last_name,
          email,
          notes,
          phone,
          customers(company_name)
          `
      );

      // Apply status filtering
      if (status && status !== "all") {
        if (["approved", "pending", "rejected"].includes(status)) {
          allUsersQuery = allUsersQuery.eq("status", status as UserStatus);
        }
      }

      if (roles && roles.length > 0) {
        allUsersQuery = allUsersQuery.in("role", roles);
      }

      if (userIdsFromGroupFilter && userIdsFromGroupFilter.length > 0) {
        allUsersQuery = allUsersQuery.in("id", userIdsFromGroupFilter);
      }

      const { data: allUsersWithCompany, error: allUsersError } =
        await allUsersQuery;

      if (allUsersError) {
        return res.status(400).json({ error: allUsersError.message });
      }

      if (!allUsersWithCompany || allUsersWithCompany.length === 0) {
        return res
          .status(200)
          .json({ data: [], total: countResult.count ?? 0 });
      }

      // Step 2: Sort by company name (handling nulls and empty strings)
      const sortedUsers = allUsersWithCompany.sort((a, b) => {
        const aCompanyName = a.customers?.[0]?.company_name?.trim() || "";
        const bCompanyName = b.customers?.[0]?.company_name?.trim() || "";

        // Handle empty company names - treat them as "N/A" and put at end for asc, beginning for desc
        if (!aCompanyName && bCompanyName) {
          return ascending ? 1 : -1;
        }
        if (!bCompanyName && aCompanyName) {
          return ascending ? -1 : 1;
        }

        // Both empty or both have values
        const comparison = aCompanyName.localeCompare(bCompanyName, "en", {
          sensitivity: "base",
        });
        return ascending ? comparison : -comparison;
      });

      // Step 3: Apply pagination to the sorted results
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedUserIds = sortedUsers
        .slice(startIndex, endIndex)
        .map((user) => user.id);

      // Step 4: Fetch the detailed user data for the paginated results
      const paginatedUsersQuery = supabaseAdminClient
        .from("users")
        .select(
          `
        id,
        created_at,
        updated_at,
        status,
        role,
        first_name,
        last_name,
        email,
        notes,
        phone
        `
        )
        .in("id", paginatedUserIds);

      const { data: paginatedUsers, error: paginatedError } =
        await paginatedUsersQuery;

      if (paginatedError) {
        return res.status(400).json({ error: paginatedError.message });
      }

      // Maintain the sorted order by mapping the IDs back to users
      const orderedUsers = paginatedUserIds
        .map((id) => paginatedUsers?.find((user) => user.id === id))
        .filter(Boolean);

      usersResult = { data: orderedUsers, error: null };
    } else {
      // For other columns, apply direct database sorting
      const sortColumnMap: Record<string, string> = {
        name: "first_name", // Sort by first name for name column
        status: "status",
        email: "email",
        created_at: "created_at",
        updated_at: "updated_at",
      };

      const dbSortColumn = sortColumnMap[sortBy] || "created_at";
      const userQueryWithSorting = usersQuery
        .order(dbSortColumn, { ascending })
        .range((page - 1) * limit, page * limit - 1);

      usersResult = await userQueryWithSorting;
    }

    const count = groupId ? customersCount : countResult.count;
    const { data: users, error: usersError } = usersResult;

    if (usersError) {
      return res.status(400).json({
        error: usersError.message,
      });
    }

    if (!users || users.length === 0) {
      return res.status(200).json({
        data: [],
        total: count ?? 0,
      });
    }

    const userIds = users.map((user) => user.id);

    // Get business details and customers concurrently
    const [businessDetailsResult, customersResult] = await Promise.all([
      supabaseAdminClient
        .from("business_details")
        .select(
          `
          id,
          user_id,
          business_type,
          business_nature,
          website,
          maxton_account,
          number_of_elevators,
          authorized_contact_name,
          buyer_name,
          buyer_phone,
          technical_contact,
          technical_contact_phone,
          account_payable_contact,
          account_payable_phone,
          technician
        `
        )
        .in("user_id", userIds),
      supabaseAdminClient
        .from("customers")
        .select(
          `
          id,
          role,
          phone,
          status,
          user_id,
          created_at,
          company_logo,
          company_name,
          credit_limit,
          shipping_notes,
          company_website,
          cost_percentage,
          customer_number,
          payment_options,
          primary_contact_name,
          updated_at
        `
        )
        .in("user_id", userIds),
    ]);

    const { data: businessDetails } = businessDetailsResult;
    const { data: customers } = customersResult;

    if (!customers?.length) {
      return res.status(200).json({
        data: users.map((user) => ({
          // use users
          ...user,
          business_details:
            businessDetails?.find((bd) => bd.user_id === user.id) || null,
          customer_data: [],
        })) as unknown as PublicUserWithCustomer[],
        total: count ?? 0,
      });
    }

    const customerIds = customers.map((c) => c.id);

    // Get all related data for customers concurrently
    const [
      customerGroupsResult,
      billingAddressesResult,
      customerCategoriesResult,
    ] = await Promise.all([
      supabaseAdminClient
        .from("customer_groups")
        .select(
          `
          customer_id,
          groups (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("billing_addresses")
        .select(
          `
          id,
          city,
          state,
          address,
          country,
          default,
          approved,
          zip_code,
          created_at,
          customer_id
        `
        )
        .in("customer_id", customerIds),
      supabaseAdminClient
        .from("customer_categories")
        .select(
          `
          customer_id,
          categories (
            id,
            name,
            description
          )
        `
        )
        .in("customer_id", customerIds),
    ]);

    const { data: customerGroupsData } = customerGroupsResult;
    const { data: billingAddressesData } = billingAddressesResult;
    const { data: customerCategoriesData } = customerCategoriesResult;

    const customersWithDetails = users.map((user) => {
      // use users
      const userBusinessDetails = businessDetails?.find(
        (bd) => bd.user_id === user.id
      );

      const userCustomers = customers
        ?.filter((c) => c.user_id === user.id)
        .map((customer) => {
          const customerGroupInfo = customerGroupsData?.find(
            (cg) => cg.customer_id === customer.id
          );
          const customerBillingAddresses = billingAddressesData?.filter(
            (ba) => ba.customer_id === customer.id
          );
          const customerCategories = customerCategoriesData?.filter(
            (cc) => cc.customer_id === customer.id
          );

          return {
            ...customer,
            group_data:
              customerGroupInfo && customerGroupInfo.groups
                ? { data: customerGroupInfo.groups }
                : null,
            billing_addresses: customerBillingAddresses || [],
            categories:
              customerCategories?.map((cc) => ({
                category_data: cc.categories,
              })) || [],
          };
        });

      return {
        ...user,
        business_details: userBusinessDetails || null,
        customer_data: userCustomers || [],
      };
    }) as unknown as PublicUserWithCustomer[];

    return res.status(200).json({
      data: customersWithDetails,
      total: count ?? 0,
    });
  } catch (error) {
    console.error("Error in getAllUsersHandler:", error);
    return res.status(400).json({
      error: "An error occurred while fetching users data",
    });
  }
}
