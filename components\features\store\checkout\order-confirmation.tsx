import { OrderStatusBadge } from "@/components/features/store/orders-table";
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { formatPrice } from "@/lib/utils";
import {
    OrderItemOption,
    OrderWithItems,
    useGetBillingAddressesQuery,
    useGetCustomerQuery,
    useGetImage,
    useGetOrderByIdQuery
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { formatId } from "@/utils/order-helpers";
import {
    FileText,
    Package,
    Receipt,
    ShoppingBag,
    ShoppingCart,
    Truck
} from "lucide-react";
import { useEffect, useState } from "react";

export function CheckoutOrderConfirmationDetails({ orderId }: { orderId: string }) {
    const userData = useAuthStore((state) => state.data);
    const userId = userData?.id;
    const order = useGetOrderByIdQuery(userId, orderId);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(
        function hideLoading() {
            if (order.isSuccess) {
                setIsLoading(false);
            }
        },
        [order.isSuccess]
    );

    if (isLoading) {
        return <OrderDetailsSkeleton />;
    }

    return <OrderDetails order={order.data ?? ({} as OrderWithItems)} />;
}

interface OrderDetailsProps {
    order: OrderWithItems;
}

function OrderDetails({ order }: OrderDetailsProps) {
    const orderId = order.id;
    const items = order.order_items ?? [];
    const shippingAddress = order.shipping_addresses ?? [];
    const userData = useAuthStore((state) => state.data);
    const customerData = useGetCustomerQuery(userData.id);

    // Fetch billing address using the billing_address_id from the order
    const billingAddresses = useGetBillingAddressesQuery(userData.id);
    const billingAddress = billingAddresses.data?.find(
        (addr) => addr.id === order.billing_address_id
    );

    const date = order.created_at;
    const dateFormat = new Intl.DateTimeFormat("en-US", {
        dateStyle: "medium",
        timeStyle: "short",
    });

    const dateString = date
        ? dateFormat.format(new Date(date))
        : new Date(date).toString();

    const orderStatus = order.order_statuses?.at(0);
    const totalAmount = order.total_amount ?? 0;

    // Helper function to format payment method string
    const formatPaymentMethod = (method: string): string => {
        return method
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
    };

    // Calculate totals for display
    const calculateItemTotals = () => {
        let subtotal = 0;
        let optionsTotal = 0;

        items.forEach((item) => {
            const itemPrice = item.item_price || item.products.price || 0;
            const itemSubtotal = itemPrice * item.quantity;
            subtotal += itemSubtotal;

            // Calculate options total
            const options = (item.options as unknown as OrderItemOption[]) ?? [];
            options.forEach((option) => {
                const optionPrice = option.price || 0;
                optionsTotal += optionPrice * item.quantity;
            });
        });

        return {
            subtotal,
            optionsTotal,
            merchandiseTotal: subtotal + optionsTotal,
        };
    };

    const { subtotal, optionsTotal, merchandiseTotal } = calculateItemTotals();
    const orderSubtotal = merchandiseTotal;
    const orderTaxAmount = (order as any).tax_amount || 0;
    const grandTotal = totalAmount;

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-muted-foreground" /> Order #
                    {formatId(orderId)}
                </CardTitle>
                <OrderStatusBadge status={orderStatus?.status ?? "pending"} />
            </CardHeader>
            <CardContent className="grid gap-6">
                {/* Order Details Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-foreground">
                            Order Details
                        </h3>
                        <div className="space-y-2 text-sm">
                            <div className="flex justify-between"></div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Date Purchased:</span>
                                <span className="font-medium">{dateString}</span>
                            </div>
                            {order.payment_type && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Payment Method:</span>
                                    <span className="font-medium">
                                        {formatPaymentMethod(order.payment_type)}
                                    </span>
                                </div>
                            )}
                            {order.purchase_order && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">
                                        Purchase Order Number:
                                    </span>
                                    <span className="font-medium">{order.purchase_order}</span>
                                </div>
                            )}
                            {/* Check if shipping address is for a special country */}
                            {(() => {
                                const isSpecialCountry =
                                    shippingAddress?.country === "mexico" ||
                                    shippingAddress?.country === "puerto rico" ||
                                    shippingAddress?.state === "puerto rico" ||
                                    shippingAddress.state === "mexico";

                                // Only show Ship Collect UPS info if not a special country
                                return !isSpecialCountry && order.ship_collect !== undefined ? (
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">
                                            Ship Collect UPS:
                                        </span>
                                        <span className="font-medium">
                                            {order.ship_collect ? "Yes" : "No"}
                                        </span>
                                    </div>
                                ) : null;
                            })()}
                            {order.ship_collect && order.ups_account_number && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">
                                        UPS Account Number:
                                    </span>
                                    <span className="font-medium">
                                        {order.ups_account_number}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold text-foreground">
                            Account Information
                        </h3>
                        <div className="space-y-2 text-sm">
                            {customerData.data?.customer?.customer_number && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Account ID:</span>
                                    <span className="font-medium">
                                        {formatId(customerData.data.customer.customer_number)}
                                    </span>
                                </div>
                            )}
                            {customerData.data?.customer?.primary_contact_name && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Name:</span>
                                    <span className="font-medium">
                                        {customerData.data.customer.primary_contact_name}
                                    </span>
                                </div>
                            )}
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Email:</span>
                                <span className="font-medium">{userData.email}</span>
                            </div>
                            {customerData.data?.customer?.company_name && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Company Name:</span>
                                    <span className="font-medium">
                                        {customerData.data.customer.company_name}
                                    </span>
                                </div>
                            )}
                            {customerData.data?.customer?.phone && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Phone Number:</span>
                                    <span className="font-medium">
                                        {customerData.data.customer.phone}
                                    </span>
                                </div>
                            )}
                            {customerData.data?.customer?.shipping_notes && (
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Shipping Notes:</span>
                                    <span className="font-medium">
                                        {customerData.data.customer.shipping_notes}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                <Separator />

                {/* Shipping Method and Total Price Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Shipping Method */}
                    <Card className="border-2">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center gap-2">
                                <Package className="h-5 w-5 text-primary" />
                                Shipping Method
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-sm space-y-2">
                                <div className="font-medium">{order.delivery_method}</div>
                                {order.ship_collect && (
                                    <div className="text-muted-foreground">
                                        Ship Collect UPS
                                        {order.ups_account_number && (
                                            <div>Account Number: {order.ups_account_number}</div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Total Price */}
                    <Card className="border-2">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-lg">Total Amount</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {formatPrice(grandTotal)}
                            </div>
                        </CardContent>
                    </Card>
                </div>
                <Separator />

                {/* Addresses Section */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Billing Address */}
                    <Card className="space-y-4 border-2">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <Receipt className="h-5 w-5 text-primary" />
                                Billing Address
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-muted-foreground">
                            {billingAddress ? (
                                <>
                                    {billingAddress.company_name && (
                                        <div className="font-medium">
                                            {billingAddress.company_name}
                                        </div>
                                    )}
                                    <div>{billingAddress.address}</div>
                                    {billingAddress.address_2 && (
                                        <div>{billingAddress.address_2}</div>
                                    )}
                                    <div>
                                        {billingAddress.city}, {billingAddress.state}{" "}
                                        {billingAddress.zip_code}
                                    </div>
                                    <div>{billingAddress.country}</div>
                                </>
                            ) : (
                                <div className="text-muted-foreground">
                                    Billing address not available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Shipping Address */}
                    <Card className="space-y-4 border-2">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <Truck className="h-5 w-5 text-primary" />
                                Shipping Address
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-muted-foreground">
                            {customerData.data?.customer?.company_name && (
                                <div className="font-medium">
                                    Company: {customerData.data.customer.company_name}
                                </div>
                            )}
                            {shippingAddress?.contact_name && (
                                <div>{shippingAddress.contact_name}</div>
                            )}
                            {shippingAddress?.address && <div>{shippingAddress.address}</div>}
                            {shippingAddress?.city &&
                                shippingAddress?.state &&
                                shippingAddress?.zip_code && (
                                    <div>
                                        {shippingAddress.city}, {shippingAddress.state}{" "}
                                        {shippingAddress.zip_code}
                                    </div>
                                )}
                            {shippingAddress?.country && <div>{shippingAddress.country}</div>}
                            {shippingAddress?.contact_number && (
                                <div>Phone: {shippingAddress.contact_number}</div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                <Separator />

                {/* Items Section */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        <ShoppingBag className="h-5 w-5 text-primary" />
                        Order Items
                    </h3>
                    <div className="border rounded-lg overflow-hidden">
                        <table className="w-full">
                            <thead className="bg-muted/50">
                                <tr>
                                    <th className="text-left p-4 font-medium">Product</th>
                                    <th className="text-center p-4 font-medium">Quantity</th>
                                    <th className="text-right p-4 font-medium">Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                {items && items.length > 0 ? (
                                    items.map((item) => {
                                        const options =
                                            (item.options as unknown as OrderItemOption[]) ?? [];
                                        const itemPrice =
                                            item.item_price || item.products.price || 0;
                                        const itemSubtotal = itemPrice * item.quantity;
                                        let itemOptionsTotal = 0;

                                        // Calculate options total for this item
                                        options.forEach((option) => {
                                            const optionPrice = option.price || 0;
                                            itemOptionsTotal += optionPrice * item.quantity;
                                        });

                                        const itemTotal = itemSubtotal + itemOptionsTotal;
                                        const imageQuery = useGetImage(item.products.image ?? "");
                                        const image =
                                            (imageQuery.data ?? item.products.image) || "";

                                        return (
                                            <tr
                                                key={`${item.products.id}-${item.quantity}-${item.products.name}`}
                                                className="border-t"
                                            >
                                                <td className="p-4">
                                                    <div className="flex items-start gap-4">
                                                        <div className="aspect-square h-16 w-16 rounded-lg border overflow-hidden">
                                                            {item.products.image && (
                                                                <img
                                                                    src={image}
                                                                    alt={item.products.name}
                                                                    className="h-full w-full object-cover"
                                                                />
                                                            )}
                                                        </div>
                                                        <div className="flex-1 space-y-2">
                                                            <div className="font-medium">
                                                                {item.products.name}
                                                            </div>
                                                            <div className="text-sm text-muted-foreground">
                                                                SKU: {item.products.sku}
                                                            </div>
                                                            {options && options.length > 0 && (
                                                                <div className="space-y-1">
                                                                    <div className="text-xs font-medium text-muted-foreground">
                                                                        Selected Options:
                                                                    </div>
                                                                    {options.map((option, index) => {
                                                                        const optionPrice = option.price || 0;
                                                                        const optionTotalPrice =
                                                                            optionPrice * item.quantity;
                                                                        return (
                                                                            <div
                                                                                key={`${item.products.id}-${option.name}-${option.value}-${index}`}
                                                                                className="text-xs text-muted-foreground flex justify-start gap-2"
                                                                            >
                                                                                <span>
                                                                                    {option.name}: {option.value}
                                                                                </span>
                                                                                {optionPrice > 0 && (
                                                                                    <span className="text-green-600">
                                                                                        +${optionPrice.toFixed(2)} ×{" "}
                                                                                        {item.quantity} = $
                                                                                        {optionTotalPrice.toFixed(2)}
                                                                                    </span>
                                                                                )}
                                                                            </div>
                                                                        );
                                                                    })}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="p-4 text-center">
                                                    <div className="font-medium">{item.quantity}</div>
                                                </td>
                                                <td className="p-4 text-right">
                                                    <div className="space-y-1">
                                                        <div className="font-medium">
                                                            ${itemPrice.toFixed(2)}
                                                        </div>
                                                        {itemOptionsTotal > 0 && (
                                                            <div className="text-sm text-green-600">
                                                                Options: $
                                                                {(itemOptionsTotal / item.quantity).toFixed(2)}
                                                            </div>
                                                        )}
                                                        <div className="text-sm font-medium border-t pt-1">
                                                            Item Total: $
                                                            {(itemTotal / item.quantity).toFixed(2)}
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })
                                ) : (
                                    <tr>
                                        <td
                                            colSpan={3}
                                            className="p-4 text-center text-muted-foreground"
                                        >
                                            No items in order
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                            <tfoot className="bg-muted/30">
                                <tr>
                                    <td colSpan={2} className="p-4 text-right font-medium">
                                        Subtotal:
                                    </td>
                                    <td className="p-4 text-right font-medium">
                                        ${orderSubtotal.toFixed(2)}
                                    </td>
                                </tr>
                                {(order as any).tax_rate &&
                                    (order as any).tax_rate > 0 &&
                                    (shippingAddress?.state === "Nevada" ||
                                        shippingAddress?.state === "NV") ? (
                                    <tr>
                                        <td colSpan={2} className="p-4 text-right">
                                            Tax ({(order as any).tax_rate.toFixed(3)}%)
                                            {order.tax_exempt && (
                                                <span className="text-orange-600 ml-1">(Exempt)</span>
                                            )}
                                        </td>
                                        <td
                                            className={`p-4 text-right ${order.tax_exempt
                                                ? "line-through text-muted-foreground"
                                                : ""
                                                }`}
                                        >
                                            {order.tax_exempt
                                                ? "$0.00"
                                                : "$" + orderTaxAmount.toFixed(2)}
                                        </td>
                                    </tr>
                                ) : (
                                    ""
                                )}
                                <tr className="border-t-2">
                                    <td colSpan={2} className="p-4 text-right text-lg font-bold">
                                        Grand Total:
                                    </td>
                                    <td className="p-4 text-right text-lg font-bold">
                                        ${grandTotal.toFixed(2)}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <Separator />

                {/* Tax Information */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <FileText className="h-5 w-5 text-muted-foreground" />
                        <div className="flex flex-col gap-2">
                            <p className="text-sm font-medium">Tax Information</p>
                            <div className="text-sm text-muted-foreground">
                                {order.tax_exempt ? "Tax Exempt" : "Taxable"}
                                {(order as any).tax_rate && (order as any).tax_rate > 0 ? (
                                    <>
                                        <br />
                                        Tax Rate: {(order as any).tax_rate.toFixed(3)}%
                                        <br />
                                        Tax Amount:{" "}
                                        {(order as any).tax_amount
                                            ? formatPrice((order as any).tax_amount)
                                            : "$0.00"}
                                    </>
                                ) : (
                                    ""
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {
                    order.notes ? (
                        <div className="mt-4">
                            <div className="text-xl font-medium">Order Notes</div>
                            <div className="text-sm text-muted-foreground mt-1 p-2 border rounded bg-gray-50">
                                {order.notes}
                            </div>
                        </div>
                    ) : null
                }
            </CardContent>
        </Card>
    );
}

function OrderDetailsSkeleton() {
    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-base font-medium">
                    <Skeleton className="h-6 w-24" />
                </CardTitle>
                <Skeleton className="h-6 w-24" />
            </CardHeader>
            <CardContent className="grid gap-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Skeleton className="h-12 w-12" />
                        <div className="flex flex-col gap-2">
                            <Skeleton className="h-6 w-24" />
                            <Skeleton className="h-6 w-24" />
                        </div>
                    </div>
                    <Skeleton className="h-5 w-5" />
                </div>
                <Separator />
                <div className="flex flex-col gap-4">
                    <Skeleton className="h-6 w-24" />
                    <div className="grid gap-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-start gap-4">
                                <Skeleton className="h-16 w-16" />
                                <div className="flex flex-col gap-2">
                                    <Skeleton className="h-6 w-96" />
                                    <Skeleton className="h-6 w-96" />
                                </div>
                            </div>
                            <Skeleton className="h-6 w-12" />
                        </div>
                    </div>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Skeleton className="h-12 w-12" />
                        <div className="flex flex-col gap-2">
                            <Skeleton className="h-6 w-96" />
                            <Skeleton className="h-24 w-24" />
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
