import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import { useDeleteProductCategoryMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Loader2, Trash2 } from "lucide-react";

interface DeleteProductCategoryButtonProps {
    productId: string;
    categoryId: string;
}

export function DeleteProductCategoryButton({ productId, categoryId }: DeleteProductCategoryButtonProps) {
    const token = useAuthStore((state) => state.token);
    const { mutate: deleteProductCategory, isPending } = useDeleteProductCategoryMutation(productId, token);
    const { toast } = useToast();
    const permissions = useAuthStore((state) => state.permissions);
    const hasPermission = checkUserHasPermission(permissions, "delete:product_categories");

    const handleDelete = () => {
        if (!hasPermission) {
            toast({
                title: "Error",
                description: "You are not authorized to delete categories",
                variant: "destructive",
            });
            return;
        }

        deleteProductCategory(categoryId, {
            onSuccess: () => {
                toast({
                    title: "Success",
                    description: "Product category removed successfully",
                });
            },
            onError: (error) => {
                toast({
                    title: "Error",
                    description: error.message || "Failed to remove product category",
                    variant: "destructive",
                });
            },
        });
    };

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <Button disabled={!hasPermission} variant="ghost" size="icon" className="h-6 w-6">
                    <Trash2 className="h-3 w-3 text-red-500" />
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Remove Product Category</AlertDialogTitle>
                    <AlertDialogDescription>
                        Are you sure you want to remove this category from the product? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <Button variant="destructive" onClick={handleDelete} disabled={isPending || !hasPermission}>
                        {isPending ? "Removing..." : "Remove"}
                        {isPending && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                    </Button>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
} 