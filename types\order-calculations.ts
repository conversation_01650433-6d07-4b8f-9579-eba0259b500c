/**
 * Breakdown of order total calculation
 */
export interface OrderCalculationBreakdown {
  subtotal: number;
  taxAmount: number;
  total: number;
}

/**
 * Result of missing products detection
 */
export interface MissingProductsDetection {
  hasMissingProducts: boolean;
  difference: number;
  calculatedTotal: number;
  storedTotal: number;
  calculatedBreakdown: OrderCalculationBreakdown;
  isValid: boolean;
  error: string | null;
}

/**
 * Configuration for missing products detection
 */
export interface DetectionConfig {
  tolerance?: number;
  includeShipping?: boolean;
  includeDiscounts?: boolean;
}

/**
 * Props for missing products info component
 */
export interface MissingProductsInfoProps {
  detection: MissingProductsDetection;
  className?: string;
  showDetails?: boolean;
}