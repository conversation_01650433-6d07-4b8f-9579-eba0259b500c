import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button } from "components/ui";

export default function Features_J({
  caption,
  title,
  description,
  featuredItems,
  primaryButton,
  secondaryButton,
}: FeaturesProps) {
  return (
    <Section className="py-20 bg-primary">
      <Container maxWidth={1280}>
        <div className="flex flex-col lg:flex-row justify-start items-start lg:justify-between lg:items-center  mb-8">
          <div className="text-left max-w-xl">
            <CaptionAndTitleSection
              caption={caption}
              title={title}
              description={description}
            />
          </div>
          <div className="mt-4 lg:mt-0 text-right">
            <Buttons
              primaryButton={primaryButton}
              secondaryButton={secondaryButton}
            />
          </div>
        </div>

        <FeatureItems features={featuredItems} />
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {title && (
        <Heading fontSize="3xl" type="h2" className="!text-white mb-4">
          {title}
        </Heading>
      )}
      {caption && (
        <Text fontSize="md" className="!text-white">
          {caption}
        </Text>
      )}
    </>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center gap-2 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          size="lg"
          className="bg-white  hover:bg-white/80 text-primary rounded-full px-8 py-3"
        >
          {primaryButton.label}
        </Button>
      ) : null}
      {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="solid"
          className="bg-transparent border-2 border-white hover:bg-white/50 text-white rounded-full px-8 py-3"
        >
          <span>{secondaryButton.label}</span>
          {/* <FaArrowRightLong className="animate-blink" /> */}
        </Button>
      ) : null}
    </Flex>
  );
}
function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <Flex wrap justify="center" className="-mx-4">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index + 1} />
      ))}
    </Flex>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  return (
    <div className="w-full px-4 md:w-1/2 lg:w-1/4 relative">
      <Card
        className="h-full md:h-[200px] px-6 py-12 bg-white text-left mb-4 lg:mb-0"
        borderRadius="md"
      >
        <div className="absolute top-4 left-10 text-7xl font-bold text-secondary opacity-10">
          {index < 10 ? `0${index}` : index}
        </div>

        <Text fontSize="xl" weight="bold" className="px-0 mb-4 text-gray-800">
          {feature.title}
        </Text>
        <Text muted className="px-0 !text-sm !text-gray-700">
          {feature.description}
        </Text>
      </Card>
    </div>
  );
}

export { Features_J };
