import { useState } from 'react';
import type { ValveData, FlashMessage, ValveProduct } from '../types/valve';
import { calculateInch, calculateMetric, knownGPMInch, knownGPMMetric, selectValve } from '../utils/valveCalculations';
import { valveProducts, osvProducts } from '../utils/valveProducts';

export function useValveCalculator() {
  const [calculationResults, setCalculationResults] = useState<ValveData | null>(null);
  const [upperFlashMessages, setUpperFlashMessages] = useState<FlashMessage[]>([]);
  const [lowerFlashMessages, setLowerFlashMessages] = useState<FlashMessage[]>([]);
  const [activeCalculator, setActiveCalculator] = useState<'upper' | 'lower' | null>(null);

  const validateInput = (data: ValveData): boolean => {
    const messages: FlashMessage[] = [];

    if (data.formType === 'form1') {
      if (!data.jackType) {
        messages.push({ message: 'You must select a Jack Type' });
      }
      if (!data.numJack) {
        messages.push({ message: 'You must select a Jack Number' });
      }
      if (!data.jackDiameter || data.jackDiameter <= 0) {
        messages.push({ message: 'Invalid Piston Diameter' });
      }
      if (!data.carSpeed || data.carSpeed <= 0) {
        messages.push({ message: 'Invalid Car Speed' });
      }
      if (!data.emptyWeight || data.emptyWeight <= 0) {
        messages.push({ message: 'Invalid Empty Car Weight' });
      }
      if (data.capacity !== undefined && data.capacity <= 0) {
        messages.push({ message: 'Invalid Capacity' });
      }
    } else {
      if (!data.emptyStaticPressure || data.emptyStaticPressure <= 0) {
        messages.push({ message: 'Invalid Empty Static Pressure' });
      }
      if (data.loadedCarPressure !== undefined && data.loadedCarPressure <= 0) {
        messages.push({ message: 'Invalid Loaded Car Pressure' });
      }
      if (!data.ratedFlow || data.ratedFlow <= 0) {
        messages.push({ message: 'Invalid Rated Flow' });
      }
    }

    setUpperFlashMessages(messages);
    setLowerFlashMessages(messages);
    return messages.length === 0;
  };

  const calculateValve = (data: ValveData): boolean => {
    try {
      const isUpperCalculator = data.formType === 'form1';


      setActiveCalculator(isUpperCalculator ? 'upper' : 'lower');

      let calculatedData: ValveData = { ...data };

      if (isUpperCalculator) {
        calculatedData = data.units === 'inch'
          ? calculateInch(data)
          : calculateMetric(data);
      } else {
        calculatedData = data.units === 'inch'
          ? knownGPMInch(data)
          : knownGPMMetric(data);
      }

      if (calculatedData.sngOutMaxPSI && calculatedData.sngOutMinPSI &&
        calculatedData.sngOutMaxPSI > (calculatedData.sngOutMinPSI * 2.5)) {
        const message = {
          message: 'Pressure ratio exceeds 2.5 to 1.\nConsult Maxton at (775) 782-1700.'
        };

        if (isUpperCalculator) {

          setUpperFlashMessages([message]);
          setLowerFlashMessages([]);
        } else {

          setLowerFlashMessages([message]);
          setUpperFlashMessages([]);
        }
        return false;
      }

      const result = selectValve(calculatedData);

      if (result.noteText) {
        const message = { message: result.noteText };
        if (isUpperCalculator) {

          setUpperFlashMessages([message]);
          setLowerFlashMessages([]);
        } else {

          setLowerFlashMessages([message]);
          setUpperFlashMessages([]);
        }
        return false;
      }

      const products: ValveProduct[] = [];
      if (result.valveType && valveProducts[result.valveType]) {
        products.push(...valveProducts[result.valveType]);
      }
      if (result.osvValve && osvProducts[result.osvValve]) {
        products.push(osvProducts[result.osvValve]);
      }

      setCalculationResults({
        ...result,
        products,
        formType: data.formType
      });
      setUpperFlashMessages([]);
      setLowerFlashMessages([]);
      return true;
    } catch (error) {
      const isUpperCalculator = data.formType === 'form1';
      if (error instanceof Error) {
        const message = { message: error.message };
        if (isUpperCalculator) {

          setUpperFlashMessages([message]);
          setLowerFlashMessages([]);
        } else {

          setLowerFlashMessages([message]);
          setUpperFlashMessages([]);
        }
      } else {
        const message = { message: 'An unexpected error occurred' };
        console.log('Unexpected Error:', message);
        if (isUpperCalculator) {
          setUpperFlashMessages([message]);
          setLowerFlashMessages([]);
        } else {
          setLowerFlashMessages([message]);
          setUpperFlashMessages([]);
        }
      }
      return false;
    }
  };

  const resetCalculator = () => {
    setCalculationResults(null);
    setUpperFlashMessages([]);
    setLowerFlashMessages([]);
    setActiveCalculator(null);
  };

  const resetFormFields = () => {
    setCalculationResults(null);
    setActiveCalculator(null);
  };

  return {
    calculateValve,
    calculationResults,
    resetCalculator,
    resetFormFields,
    upperFlashMessages,
    lowerFlashMessages,
    activeCalculator
  };
} 