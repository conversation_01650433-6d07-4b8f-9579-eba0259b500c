import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { useGetGroupsQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { UseQueryResult } from "@tanstack/react-query";
import { PlusCircle, X } from "lucide-react";
import { useMemo, useState } from "react";

interface ProductGroupPricesProps {
  groupPrices: Record<string, number>;
  setGroupPrices: React.Dispatch<React.SetStateAction<Record<string, number>>>;
  basePrice: number;
}

export default function GroupPricing({
  groupPrices,
  setGroupPrices,
  basePrice,
}: ProductGroupPricesProps) {
  const token = useAuthStore((state) => state.token);
  const groupsQuery = useGetGroupsQuery(1, 100, token);
  const [selectedGroupId, setSelectedGroupId] = useState<string>("");
  const [newGroupPrice, setNewGroupPrice] = useState<number>(basePrice);

  const handlePriceChange = (groupId: string, price: number) => {
    setGroupPrices((prev) => ({
      ...prev,
      [groupId]: price,
    }));
  };

  const handleAddGroup = () => {
    if (!selectedGroupId || groupPrices[selectedGroupId] !== undefined) return;

    setGroupPrices(prev => ({
      ...prev,
      [selectedGroupId]: parseFloat(newGroupPrice.toString())
    }));

    // Reset selection after adding
    setSelectedGroupId("");
    setNewGroupPrice(basePrice);
  };

  const handleRemoveGroup = (groupId: string) => {
    setGroupPrices((prev) => {
      const newPrices = { ...prev };
      delete newPrices[groupId];
      return newPrices;
    });
  };

  // Reset price to base price when a new group is selected
  const handleGroupSelection = (groupId: string) => {
    setSelectedGroupId(groupId);
    setNewGroupPrice(basePrice);
  };

  const availableGroups = useMemo(() => {
    if (!groupsQuery.data?.groups) return [];
    return groupsQuery.data.groups.filter(
      (group: any) => groupPrices[group.id] === undefined
    );
  }, [groupsQuery.data?.groups, groupPrices]);

  return (
    <Card className="rounded-lg">
      <CardHeader>
        <CardTitle>Group Pricing</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="border rounded-md p-4 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="group">Select Group</Label>
            <div className="flex flex-col gap-4">
              <Select
                value={selectedGroupId}
                onValueChange={handleGroupSelection}
                disabled={groupsQuery.isLoading || availableGroups.length === 0}
              >
                <SelectTrigger className="">
                  <SelectValue placeholder="Select a group" />
                </SelectTrigger>
                <SelectContent className="rounded-lg">
                  {availableGroups.map((group: any) => (
                    <SelectItem key={group.id} value={group.id}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedGroupId && (
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor="new-group-price"
                    className="whitespace-nowrap"
                  >
                    Price:
                  </Label>
                  <Input
                    id="new-group-price"
                    type="number"
                    className="h-10 "
                    value={newGroupPrice}
                    onChange={(e) =>
                      setNewGroupPrice(parseFloat(e.target.value) || 0)
                    }
                    placeholder="Price"
                    onWheel={(e) => e.currentTarget.blur()}
                  />
                </div>
              )}

              <Button
                type="button"
                variant="primary"
                onClick={handleAddGroup}
                disabled={
                  !selectedGroupId ||
                  availableGroups.length === 0 ||
                  groupsQuery.isLoading
                }
                className=" h-10 w-fit"
              >
                <PlusCircle className="h-4 w-4" />
                Add Price for Group
              </Button>
            </div>
          </div>

          {groupsQuery.isLoading ? (
            <div className="text-sm p-4 text-center">Loading groups...</div>
          ) : groupsQuery.isError ? (
            <div className="text-sm text-red-500 p-4 text-center">
              Error loading groups: {groupsQuery.error?.toString()}
            </div>
          ) : groupsQuery.data?.groups?.length === 0 ? (
            <div className="text-sm p-4 text-center">
              No groups available. Create customer groups first.
            </div>
          ) : null}
        </div>

        {Object.entries(groupPrices).length > 0 && (
          <div className="space-y-2">
            <Label>Added Group Prices</Label>
            <div className="space-y-2">
              {Object.entries(groupPrices).map(([groupId, price]) => {
                const group = groupsQuery.data?.groups?.find(
                  (g: any) => g.id === groupId
                );
                if (!group) return null;

                return (
                  <div
                    key={groupId}
                    className="flex justify-between items-center p-2 border rounded-md w-full"
                  >
                    <div className="flex items-center gap-4 px-4">
                      <span className="font-semibold text-sm">
                        {group.name}
                      </span>
                      <div className="w-32">
                        <Input
                          type="number"
                          className="h-8 "
                          value={price || ""}
                          onChange={(e) =>
                            handlePriceChange(
                              groupId,
                              parseFloat(e.target.value) || 0
                            )
                          }
                          placeholder="Price"
                          onWheel={(e) => e.currentTarget.blur()}
                        />
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => handleRemoveGroup(groupId)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
