import {
  arrayOfTitleAndText,
  plainText,
  subtitle,
  title,
  youtubeLink,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const howItWorksSchema = [
  subtitle(),
  title(),
  plainText(hideIfVariantIn(["variant_c", "variant_d", "variant_e", "variant_f"])),
  youtubeLink(
    hideIfVariantIn(["variant_b", "variant_c", "variant_d", "variant_e", "variant_f"])
  ),
  arrayOfTitleAndText(
    "Steps",
    "Click the 'Add item' button to add steps. If you want to edit added steps, click this ⋮ icon found on the bottom right of item."
  ),
];
