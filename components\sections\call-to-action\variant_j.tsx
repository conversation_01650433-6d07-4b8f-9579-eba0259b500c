import { useState } from "react";
import { Container } from "@stackshift-ui/container";
import { Section } from "@stackshift-ui/section";
import { useValveCalculator } from "../../../hooks/useValveCalculator";
import type { ValveData } from "../../../types/valve";
import { CTAProps } from ".";
import { FaArrowRightLong } from "react-icons/fa6";

// This is now a compact sidebar calculator component that can be placed alongside other content
export default function CallToAction_J({}: CTAProps) {
  const [showResults, setShowResults] = useState(false);
  const [units, setUnits] = useState<"inch" | "metric">("inch");
  const [activeTab, setActiveTab] = useState<"upper" | "lower">("upper");

  const {
    calculateValve,
    calculationResults,
    resetCalculator,
    upperFlashMessages,
    lowerFlashMessages,
    activeCalculator,
  } = useValveCalculator();

  const handleSubmit = (formData: ValveData) => {
    const results = calculateValve(formData);
    if (results) {
      setShowResults(true);
    }
  };

  return (
    <Section className="py-4 bg-background">
      <Container maxWidth={1280}>
        <div className="w-full">
          {/* Compact Calculator */}
          <div className="bg-white rounded-md shadow-md border border-gray-200 p-3">
            {/* Calculator Tabs */}
            <div className="flex border-b border-gray-200 mb-3">
              <button
                className={`py-1 px-2 text-xs font-medium border-b-2 ${
                  activeTab === "upper"
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("upper")}
              >
                Upper Calculator
              </button>
              <button
                className={`py-1 px-2 text-xs font-medium border-b-2 ${
                  activeTab === "lower"
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab("lower")}
              >
                Lower Calculator
              </button>
            </div>

            {/* Flash Messages */}
            {activeTab === "upper" && upperFlashMessages.length > 0 && (
              <div className="p-2 mb-2 bg-red-100 rounded-md">
                {upperFlashMessages.map((msg, i) => (
                  <p key={i} className="text-red-600 text-xs">
                    {msg.message}
                  </p>
                ))}
              </div>
            )}

            {activeTab === "lower" && lowerFlashMessages.length > 0 && (
              <div className="p-2 mb-2 bg-red-100 rounded-md">
                {lowerFlashMessages.map((msg, i) => (
                  <p key={i} className="text-red-600 text-xs">
                    {msg.message}
                  </p>
                ))}
              </div>
            )}

            {/* Units Selection */}
            <div className="mb-2">
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Units
              </label>
              <select
                value={units}
                onChange={(e) => setUnits(e.target.value as "inch" | "metric")}
                className="w-full p-1 text-xs border border-gray-300 rounded-md"
              >
                <option value="inch">English Units</option>
                <option value="metric">Metric Units</option>
              </select>
            </div>

            {/* Calculator Forms */}
            {activeTab === "upper" ? (
              <UpperCalculatorForm units={units} onSubmit={handleSubmit} />
            ) : (
              <LowerCalculatorForm units={units} onSubmit={handleSubmit} />
            )}

            {/* Results Section */}
            {showResults && calculationResults && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                {((activeCalculator === "upper" &&
                  activeTab === "upper" &&
                  calculationResults.formType === "form1") ||
                  (activeCalculator === "lower" &&
                    activeTab === "lower" &&
                    calculationResults.formType !== "form1")) && (
                  <ValveCalculatorResults
                    results={calculationResults}
                    units={units}
                    onReset={() => {
                      resetCalculator();
                      setShowResults(false);
                    }}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </Container>
    </Section>
  );
}

function UpperCalculatorForm({
  units,
  onSubmit,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
}) {
  const handleReset = () => {
    const form = document.getElementById("upper-form") as HTMLFormElement;
    form?.reset();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form1",
      units: units as "inch" | "metric",
      jackType: formData.get("jackType") as "direct" | "roped",
      numJack: Number(formData.get("numJack")),
      jackDiameter: Number(formData.get("jackDiameter")),
      carSpeed: Number(formData.get("carSpeed")),
      emptyWeight: Number(formData.get("emptyWeight")),
      emptyWeightUnits: formData.get("emptyWeightUnits") as
        | "PSI"
        | "lbs."
        | "kg"
        | "kg/cm2",
      capacity: Number(formData.get("capacity")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  return (
    <form id="upper-form" onSubmit={handleSubmit} className="space-y-2">
      {/* Upper calculator fields - more compact */}
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Jack Type
        </label>
        <select
          name="jackType"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
        >
          <option value="">Select Jack Type</option>
          <option value="direct">Direct</option>
          <option value="roped">Roped</option>
        </select>
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Jack Number
        </label>
        <select
          name="numJack"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
        >
          <option value="">Select Jack Number</option>
          <option value="1">Single Jack</option>
          <option value="2">Dual Jack</option>
        </select>
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Piston Dia. ({units === "inch" ? "in" : "mm"})
        </label>
        <input
          type="number"
          name="jackDiameter"
          placeholder="Piston Diameter"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Car Speed ({units === "inch" ? "FPM" : "m/s"})
        </label>
        <input
          type="number"
          name="carSpeed"
          placeholder="Car Speed"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Empty Car Weight
        </label>
        <div className="flex gap-1">
          <input
            type="number"
            name="emptyWeight"
            placeholder="Empty Car Weight"
            required
            className="flex-1 p-1 text-xs border border-gray-300 rounded-md"
            step="any"
          />
          <select
            name="emptyWeightUnits"
            className="w-16 p-1 text-xs border border-gray-300 rounded-md"
          >
            {units === "inch" ? (
              <>
                <option value="lbs.">lbs.</option>
                <option value="PSI">PSI</option>
              </>
            ) : (
              <>
                <option value="kg">kg</option>
                <option value="kg/cm2">kg/cm²</option>
              </>
            )}
          </select>
        </div>
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Capacity ({units === "inch" ? "lbs" : "kg"})
        </label>
        <input
          type="number"
          name="capacity"
          placeholder="Capacity"
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div className="flex items-center gap-1 py-1">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="upperDSR-sidebar"
          value="true"
          className="w-3 h-3 text-primary"
        />
        <label htmlFor="upperDSR-sidebar" className="text-xs text-gray-700">
          Down Speed Regulation
        </label>
      </div>

      <div className="flex gap-2 pt-1">
        <button
          type="submit"
          className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-primary rounded-md hover:bg-[#063a6b] focus:outline-none"
          aria-label="Size Valve"
        >
          <span>Size Valve</span>
          <FaArrowRightLong size={10} />
        </button>

        <button
          type="button"
          onClick={handleReset}
          className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary bg-white border border-primary rounded-md hover:bg-gray-50 focus:outline-none"
          aria-label="Reset"
        >
          <span>Reset</span>
          <FaArrowRightLong size={10} />
        </button>
      </div>
    </form>
  );
}

function LowerCalculatorForm({
  units,
  onSubmit,
}: {
  units: string;
  onSubmit: (data: ValveData) => void;
}) {
  const handleReset = () => {
    const form = document.getElementById("lower-form") as HTMLFormElement;
    form?.reset();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const data: ValveData = {
      formType: "form2",
      units: units as "inch" | "metric",
      emptyStaticPressure: Number(formData.get("emptyStaticPressure")),
      loadedCarPressure: Number(formData.get("loadedCarPressure")),
      ratedFlow: Number(formData.get("ratedFlow")),
      downSpeedRegulation: formData.get("downSpeedRegulation") === "true",
    };

    onSubmit(data);
  };

  return (
    <form id="lower-form" onSubmit={handleSubmit} className="space-y-2">
      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Empty Static Pressure ({units === "inch" ? "PSI" : "kg/cm²"})
        </label>
        <input
          type="number"
          name="emptyStaticPressure"
          placeholder="Empty Static Pressure"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Loaded Car Pressure ({units === "inch" ? "PSI" : "kg/cm²"})
        </label>
        <input
          type="number"
          name="loadedCarPressure"
          placeholder="Loaded Car Pressure"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div>
        <label className="block text-xs font-medium text-gray-700 mb-1">
          Rated Flow ({units === "inch" ? "GPM" : "LPM"})
        </label>
        <input
          type="number"
          name="ratedFlow"
          placeholder="Rated Flow"
          required
          className="w-full p-1 text-xs border border-gray-300 rounded-md"
          step="any"
        />
      </div>

      <div className="flex items-center gap-1 py-1">
        <input
          type="checkbox"
          name="downSpeedRegulation"
          id="lowerDSR-sidebar"
          value="true"
          className="w-3 h-3 text-primary"
        />
        <label htmlFor="lowerDSR-sidebar" className="text-xs text-gray-700">
          Down Speed Regulation
        </label>
      </div>

      <div className="flex gap-2 pt-1">
        <button
          type="submit"
          className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-primary rounded-md hover:bg-[#063a6b] focus:outline-none"
          aria-label="Size Valve"
        >
          <span>Size Valve</span>
          <FaArrowRightLong size={10} />
        </button>

        <button
          type="button"
          onClick={handleReset}
          className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary bg-white border border-primary rounded-md hover:bg-gray-50 focus:outline-none"
          aria-label="Reset"
        >
          <span>Reset</span>
          <FaArrowRightLong size={10} />
        </button>
      </div>
    </form>
  );
}

function ValveCalculatorResults({
  results,
  units,
  onReset,
}: {
  results: ValveData | null;
  units: "inch" | "metric";
  onReset: () => void;
}) {
  if (!results) return null;

  return (
    <div className="text-xs">
      <h3 className="text-sm font-semibold mb-2 text-gray-800">
        Provided data:
      </h3>
      <div className="space-y-1 mb-3">
        {results.formType === "form1" ? (
          <>
            <div className="flex items-center">
              <span className="font-medium w-24">Jack Type:</span>
              <span>
                {results.jackType === "direct" ? "Direct" : "Roped"}{" "}
                {results.numJack === 1 ? "Single" : "Dual"} Jack
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium w-24">Piston Dia.:</span>
              <span>
                {results.jackDiameter} {units === "inch" ? "in." : "mm"}
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium w-24">Car Speed:</span>
              <span>
                {results.carSpeed} {units === "inch" ? "FPM" : "m/sec"}
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium w-24">Empty Car:</span>
              <span>
                {results.emptyWeight}
                {results.emptyWeightUnits}
              </span>
            </div>
            {results.capacity && (
              <div className="flex items-center">
                <span className="font-medium w-24">Capacity:</span>
                <span>
                  {results.capacity} {units === "inch" ? "lbs" : "kg"}
                </span>
              </div>
            )}
          </>
        ) : (
          <>
            <div className="flex items-center">
              <span className="font-medium w-24">Rated Flow:</span>
              <span>
                {results.ratedFlow} {units === "inch" ? "GPM" : "LPM"}
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium w-24">Empty Static:</span>
              <span>
                {results.emptyStaticPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </span>
            </div>
            <div className="flex items-center">
              <span className="font-medium w-24">Loaded Car:</span>
              <span>
                {results.loadedCarPressure}{" "}
                {units === "inch" ? "PSI" : "kg/cm²"}
              </span>
            </div>
          </>
        )}
        <div className="flex items-center">
          <span className="font-medium w-24">Down Speed:</span>
          <span>{results.downSpeedRegulation ? "Yes" : "No"}</span>
        </div>
      </div>

      <h3 className="text-sm font-semibold mb-2 text-gray-800">Results:</h3>
      <div className="space-y-1 mb-3">
        <div className="flex items-center">
          <span className="font-medium w-24">Rated Flow:</span>
          <span className="text-blue-600">{results.sngGPM} GPM</span>
        </div>
        <div className="flex items-center">
          <span className="font-medium w-24">Empty PSI:</span>
          <span className="text-blue-600">{results.sngOutMinPSI} PSI</span>
        </div>
        <div className="flex items-center">
          <span className="font-medium w-24">Loaded PSI:</span>
          <span className="text-blue-600">{results.sngOutMaxPSI} PSI</span>
        </div>
      </div>

      <div className="mb-3">
        <h3 className="text-sm font-semibold mb-1 text-gray-800">
          Recommendations:
        </h3>
        <ul className="list-disc pl-5 space-y-0.5">
          {results.products?.map((product, index) => (
            <li key={index} className="text-blue-600">
              {product.text}
            </li>
          ))}
        </ul>
      </div>

      <button
        onClick={onReset}
        className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-white bg-primary rounded-md hover:bg-[#063a6b] focus:outline-none"
        aria-label="Calculate another valve"
      >
        Calculate Another
      </button>
    </div>
  );
}
