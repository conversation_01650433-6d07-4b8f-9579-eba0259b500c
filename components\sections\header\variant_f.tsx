import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, A11y } from "swiper/modules";
import { ButtonProps, HeaderProps } from ".";
import { FaArrowRightLong } from "react-icons/fa6";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

export default function Header_F({
  title,
  subtitle,
  description,
  primaryButton,
  secondaryButton,
  images,
}: HeaderProps): JSX.Element {
  const [activeIndex, setActiveIndex] = useState(0);
  const [prevImage, setPrevImage] = useState(images?.[0]?.image);
  const [currentImage, setCurrentImage] = useState(images?.[0]?.image);

  useEffect(() => {
    if (!images || images.length === 0) return;

    const interval = setInterval(() => {
      setPrevImage(currentImage);
      setCurrentImage(images[(activeIndex + 1) % images.length]?.image);
      setActiveIndex((prev) => (prev + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images, activeIndex, currentImage]);

  return (
    <Section className="relative py-20 lg:py-40">
      <div
        className="absolute inset-0 transition-all duration-300 ease-in-out"
        style={{
          backgroundImage: `linear-gradient(to bottom right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url(${prevImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          filter: "blur(2px)", // Smooths out the transition
          opacity: 1,
        }}
      ></div>

      <div
        className="absolute inset-0 transition-all duration-300 ease-in-out"
        style={{
          backgroundImage: `linear-gradient(to bottom right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0)), url(${currentImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          opacity: 1,
        }}
      ></div>

      {/* Content */}
      <Container maxWidth={1280} className="relative z-10 h-full">
        <Flex
          align="center"
          justify="between"
          className="flex-col h-full"
          gap={4}
        >
          <Container className="mx-auto items-center text-center" maxWidth="xl">
            <TitleAndDescription
              title={title}
              subtitle={subtitle}
              description={description}
            />
            <Buttons
              primaryButton={primaryButton}
              secondaryButton={secondaryButton}
            />
          </Container>
        </Flex>

        {images && images.length > 0 && (
          <div className="absolute -bottom-20 lg:-bottom-32 left-1/2 -translate-x-1/2 flex gap-3 mb-8">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === activeIndex
                    ? "bg-transparent border-2 border-white"
                    : "bg-white/90"
                }`}
                onClick={() => {
                  setPrevImage(currentImage);
                  setCurrentImage(images[index]?.image);
                  setActiveIndex(index);
                }}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  subtitle,
  description,
}: {
  title?: string;
  subtitle?: string;
  description?: string;
}) {
  return (
    <React.Fragment>
      {title ? (
        <Heading
          fontSize="5xl"
          className="mb-5 text-white !leading-tight textShadow"
          type="h1"
        >
          {title}
        </Heading>
      ) : null}
      {subtitle && (
        <Text className="mb-5 text-gray-100 textShadow" fontSize="lg">
          {subtitle}
        </Text>
      )}
    </React.Fragment>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center gap-4 flex-row"
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          size="lg"
          className="bg-primary  hover:bg-primary/50 text-white rounded-full px-8 py-3"
        >
          {primaryButton.label}
        </Button>
      ) : null}
      {/* {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="solid"
          className="text-white rounded-full px-6 py-3 flex items-center gap-2 animate-blink"
        >
          <span>{secondaryButton.label}</span>
          <FaArrowRightLong className="animate-blink" />
        </Button>
      ) : null} */}
    </Flex>
  );
}

function ImageSlider({
  mainImage,
  images,
  onSlideChange,
}: {
  mainImage?: { image?: string | any; alt?: string };
  images?: any[];
  onSlideChange: (index: number) => void;
}) {
  if (!images?.length) return null;

  return (
    <div className="w-full">
      <Swiper
        modules={[Navigation, Pagination, A11y]}
        spaceBetween={20}
        slidesPerView={1}
        navigation
        pagination={{ clickable: true }}
        className="rounded-md"
        onSlideChange={(swiper) => onSlideChange(swiper.activeIndex)}
      >
        {images.map((img, index) => (
          <SwiperSlide key={index}>
            <Image
              alt={img.alt ?? `header-image-${index + 1}`}
              className="rounded-md aspect-square object-cover"
              height={500}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 40vw"
              src={`${img?.image}`}
              style={{ objectFit: "cover" }}
              width={500}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}

export { Header_F };
