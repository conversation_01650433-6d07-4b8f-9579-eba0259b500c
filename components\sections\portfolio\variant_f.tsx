import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { PortableText } from "@portabletext/react";
import React, { useState, useEffect } from "react";
import { CiSearch } from "react-icons/ci";
import { Input } from "@stackshift-ui/input";
import { FaArrowRight, FaArrowLeft } from "react-icons/fa6";
import { FaCheck } from "react-icons/fa";
import { PortfolioProps } from ".";
import * as ReactDOM from "react-dom/client";
import {
  LabeledRoute,
  MyPortableTextComponents,
  PortfoliosWithCategories,
} from "types";
import ValveCalculator from "../../calculator/sideCalculator";

// Add this context near the top of the file
const ListContext = React.createContext<{ isSubcontent: boolean }>({
  isSubcontent: false,
});

// Define Content type since it's not exported from types.ts
interface Content {
  _key?: string;
  title?: string | null;
  description?: string | null;
  subtitle?: string | null;
  firstColumn?: any[];
  secondColumn?: any[];
  arrayOfTitleAndDescription?: {
    _key?: string;
    title?: string | null;
    firstColumn?: any[];
    secondColumn?: any[];
  }[];
}

// Define a proper type for props received by custom block components
interface CustomComponentProps {
  children?: React.ReactNode;
  value?: any;
  isInline?: boolean;
  [key: string]: any;
}

// This tracks which list items are checked in each subcontent
const useBulletChecklist = (hasSubContents: boolean) => {
  // For each subcontent, track what steps are checked
  const [checkedItems, setCheckedItems] = React.useState<{
    [key: string]: boolean[];
  }>({});
  // Keep track of max visible steps for each subcontent
  const [maxVisibleStep, setMaxVisibleStep] = React.useState<{
    [key: string]: number;
  }>({});
  // Track if final message should be shown
  const [showFinalMessage, setShowFinalMessage] = React.useState<{
    [key: string]: boolean;
  }>({});

  // Initialize max visible step when subcontent changes
  const initializeSubcontent = (subcontentKey: string, totalSteps: number) => {
    // Only initialize if not already done for this subcontent
    if (maxVisibleStep[subcontentKey] === undefined) {
      setMaxVisibleStep((prev) => ({
        ...prev,
        [subcontentKey]: 1, // Initially only show the first step
      }));

      // Initialize the checked state array for all items in this subcontent
      setCheckedItems((prev) => ({
        ...prev,
        [subcontentKey]: Array(totalSteps).fill(false),
      }));

      // Initialize the final message visibility
      setShowFinalMessage((prev) => ({
        ...prev,
        [subcontentKey]: false,
      }));
    }
  };

  // Handle checkbox toggle
  const handleCheckboxToggle = (
    subcontentKey: string,
    index: number,
    totalSteps: number
  ) => {
    const newCheckedItems = { ...checkedItems };
    newCheckedItems[subcontentKey] = [...checkedItems[subcontentKey]];
    newCheckedItems[subcontentKey][index] =
      !newCheckedItems[subcontentKey][index];

    setCheckedItems(newCheckedItems);

    // If checked and not the last item, show the next step
    if (newCheckedItems[subcontentKey][index] && index < totalSteps - 1) {
      setMaxVisibleStep((prev) => ({
        ...prev,
        [subcontentKey]: Math.max(prev[subcontentKey], index + 2), // Show next item (1-based)
      }));
    }

    // If last item is checked, show the final message
    if (newCheckedItems[subcontentKey][index] && index === totalSteps - 1) {
      setShowFinalMessage((prev) => ({
        ...prev,
        [subcontentKey]: true,
      }));
    }
  };

  return {
    checkedItems,
    maxVisibleStep,
    showFinalMessage,
    initializeSubcontent,
    handleCheckboxToggle,
  };
};

// Custom component to render a checklist item
const CheckableListItem = ({
  children,
  index,
  subcontentKey,
  isChecked,
  totalSteps,
  maxVisible,
  onToggle,
}: {
  children: React.ReactNode;
  index: number;
  subcontentKey: string;
  isChecked: boolean;
  totalSteps: number;
  maxVisible: number;
  onToggle: (subcontentKey: string, index: number, totalSteps: number) => void;
}) => {
  // Don't render items beyond the currently visible ones
  if (index + 1 > maxVisible) return null;

  return (
    <li
      className={`mb-3 leading-loose ${
        isChecked ? "text-gray-400" : "text-gray-900"
      } flex items-start gap-1`}
    >
      <div className="flex items-center mt-[2px] mr-3">
        <input
          type="checkbox"
          checked={isChecked}
          onChange={() => onToggle(subcontentKey, index, totalSteps)}
          className="form-checkbox h-6 w-6 text-primary rounded border-gray-300 focus:ring-primary"
        />
      </div>
      <div>{children}</div>
    </li>
  );
};

const customComponents = {
  list: {
    bullet: (props: CustomComponentProps) => {
      // Use context to determine if we're in subcontent
      const { isSubcontent } = React.useContext(ListContext);

      return (
        <ul
          className={`pl-5 mb-3 leading-loose text-gray-900 ${
            isSubcontent ? "list-none checklist-container" : "list-none"
          }`}
        >
          {props.children}
        </ul>
      );
    },
    number: ({ children }: CustomComponentProps) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children, index }: CustomComponentProps) => {
      // Use context to determine if we're in subcontent
      const { isSubcontent } = React.useContext(ListContext);

      if (isSubcontent) {
        // For subcontent, just render the item without number circle
        return (
          <li className="mb-3 leading-loose text-gray-900 flex items-start gap-1 checklist-item">
            <div>{children}</div>
          </li>
        );
      } else {
        // For main content, add a numbered circle
        return (
          <li className="mb-3 leading-loose text-gray-900 flex items-start gap-1">
            <span className="text-white font-bold bg-primary rounded-full w-7 h-7 shrink-0 flex items-center justify-center mt-[2px] mr-3">
              {(index || 0) + 1}
            </span>
            <div>{children}</div>
          </li>
        );
      }
    },
  },
  block: {
    h1: ({ children }: CustomComponentProps) => (
      <h1 className="mb-6 leading-loose text-gray-900 text-7xl">{children}</h1>
    ),
    h2: ({ children }: CustomComponentProps) => (
      <h2 className="mb-4 text-5xl text-primary">{children}</h2>
    ),
    h3: ({ children }: CustomComponentProps) => (
      <h3 className="mb-4 text-2xl text-gray-800">{children}</h3>
    ),
    h4: ({ children }: CustomComponentProps) => (
      <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
    ),
    h5: ({ children }: CustomComponentProps) => (
      <h5 className="mb-6 text-xl leading-loose text-gray-900">{children}</h5>
    ),
    h6: ({ children }: CustomComponentProps) => (
      <h6 className="mb-6 text-xl leading-loose text-gray-900">{children}</h6>
    ),
    normal: ({ children }: CustomComponentProps) => (
      <p className="mb-5 font-body text-lg text-gray-800 leading-loose">
        {children}
      </p>
    ),
    blockquote: ({ children }: CustomComponentProps) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
        - {children}
      </blockquote>
    ),
  },
  code: ({ value }: CustomComponentProps) => {
    return (
      <pre data-language={value?.language}>
        <code>{value?.code}</code>
      </pre>
    );
  },
  marks: {
    strong: ({ children }: CustomComponentProps) => (
      <strong className="font-bold">{children}</strong>
    ),
    em: ({ children }: CustomComponentProps) => (
      <em className="italic">{children}</em>
    ),
    code: ({ children }: CustomComponentProps) => <code>{children}</code>,
    link: ({ children, value }: CustomComponentProps) => {
      const target = (value?.href || "").startsWith("http")
        ? "_blank"
        : undefined;
      return (
        <a
          href={value?.href}
          target={target}
          rel={target === "_blank" ? "noopener noreferrer" : undefined}
          className="text-primary hover:text-primary/70 border-b border-primary"
        >
          {children}
        </a>
      );
    },
  },
  types: {
    addTable: ({ value }: CustomComponentProps) => {
      if (!value?.columns || !value?.rows) {
        console.error("Missing table data:", value);
        return null;
      }

      // Get the number of columns from the header
      const columnCount = value.columns.length;

      return (
        <div className="overflow-x-auto my-6">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 table-fixed">
            <thead className="bg-gray-50">
              <tr>
                {value.columns.map(
                  (column: { title: string }, index: number) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-semibold text-gray-800 uppercase tracking-wider border border-gray-200"
                    >
                      {column.title || "\u00A0"}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {value.rows.map((row: { cells: string[] }, rowIndex: number) => {
                // Create a new array with the correct number of cells
                const normalizedCells = Array(columnCount).fill("");

                // Fill in existing data
                if (row.cells && Array.isArray(row.cells)) {
                  row.cells.forEach((cell, idx) => {
                    if (idx < columnCount) {
                      normalizedCells[idx] = cell;
                    }
                  });
                }

                return (
                  <tr key={rowIndex} className="bg-white">
                    {normalizedCells.map((cell, cellIndex) => {
                      // Ensure cell content is treated as a string and check if it's empty
                      const cellContent = String(cell || "");
                      const isEmpty = !cellContent.trim();

                      return (
                        <td
                          key={cellIndex}
                          className="px-6 py-4 text-xs md:text-sm text-gray-500 bg-white border border-gray-200"
                          style={{
                            minHeight: "40px",
                            height: isEmpty ? "40px" : "auto",
                          }}
                          dangerouslySetInnerHTML={{
                            __html: isEmpty
                              ? "&nbsp;" // Non-breaking space for empty cells
                              : cellContent
                                  .replace(
                                    /\n\n/g,
                                    "<hr class='my-2 border-gray-300' />"
                                  )
                                  .replace(/\n/g, "<br />"),
                          }}
                        />
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      );
    },
  },
};

export default function Portfolio_F({
  caption,
  title,
  portfoliosWithCategory,
  hasCalculator,
}: PortfolioProps): React.JSX.Element {
  const [activeTab, setActiveTab] = React.useState<string | null | undefined>(
    portfoliosWithCategory?.[0]?.category
  );
  const [selectedContentIndex, setSelectedContentIndex] = React.useState(0);
  const [selectedSubContentIndex, setSelectedSubContentIndex] = React.useState<
    number | null
  >(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const contentRef = React.useRef<HTMLDivElement>(null);
  const contentDisplayRef = React.useRef<HTMLDivElement>(null);

  const portfoliosPerCategory = portfoliosWithCategory?.find(
    (portfolio) => portfolio?.category === activeTab
  );

  // Move selectedContent declaration before its usage in useEffect
  const selectedContent =
    portfoliosPerCategory?.content?.[selectedContentIndex];

  // Update selected index when content changes
  React.useEffect(() => {
    // If the selected content has subcontents, automatically select the first one
    if (
      selectedContent?.arrayOfTitleAndDescription &&
      Array.isArray(selectedContent.arrayOfTitleAndDescription) &&
      selectedContent.arrayOfTitleAndDescription.length > 0
    ) {
      setSelectedSubContentIndex(0);
    } else {
      setSelectedSubContentIndex(null);
    }
  }, [selectedContent]);

  // Update the search results type and state
  const [searchResults, setSearchResults] = React.useState<
    Array<{ index: number; subIndex: number | null }>
  >([]);

  // Replace the existing searchResults useMemo with this new implementation
  const filteredResults = React.useMemo(() => {
    if (!searchQuery) return [];

    const contents = portfoliosPerCategory?.content;
    if (!contents) return [];

    const results: Array<{ index: number; subIndex: number | null }> = [];

    contents.forEach((content, index) => {
      // Check main title
      if (content.title?.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push({ index, subIndex: null });
      }

      // Check sub-content titles
      content.arrayOfTitleAndDescription?.forEach((subContent, subIndex) => {
        if (
          subContent.title?.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          results.push({ index, subIndex });
        }
      });
    });

    return results;
  }, [portfoliosPerCategory?.content, searchQuery]);

  // Update the useEffect to handle multiple results
  React.useEffect(() => {
    setSearchResults(filteredResults);
    if (filteredResults.length > 0) {
      setSelectedContentIndex(filteredResults[0].index);
      setSelectedSubContentIndex(filteredResults[0].subIndex);
    }
  }, [filteredResults]);

  // Modify the scroll effect to scroll to the content display area
  React.useEffect(() => {
    const scrollToContent = () => {
      if (contentDisplayRef.current) {
        // Move the scrollIntoView target higher than the content to show tabs
        // Try to find the parent tabs container
        const tabsContainer = document.querySelector(".py-20.bg-background");
        const element = contentDisplayRef.current;

        // If we found the tabs container, scroll to it instead
        if (tabsContainer) {
          tabsContainer.scrollIntoView({ behavior: "smooth", block: "start" });
        } else {
          // Fallback to content with the block: 'center' option to show more context above
          element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }
    };

    // Call immediately and also after a delay
    scrollToContent();
    const timeoutId = setTimeout(scrollToContent, 150);

    return () => clearTimeout(timeoutId);
  }, [selectedContentIndex, selectedSubContentIndex]);

  // Get the total number of subcontent items for the current content
  const totalSubItems =
    selectedContent?.arrayOfTitleAndDescription?.length || 0;

  // Determine if we should show subcontent navigation
  const showSubContentNav = totalSubItems > 0;

  const handleNext = () => {
    if (!selectedContent) return;

    if (selectedSubContentIndex !== null) {
      // If we're at the last subcontent item and there are more content items
      if (selectedSubContentIndex >= totalSubItems - 1) {
        // Move to the next content's first subcontent if available
        if (
          selectedContentIndex <
          (portfoliosPerCategory?.content?.length || 0) - 1
        ) {
          const nextContent =
            portfoliosPerCategory?.content?.[selectedContentIndex + 1];
          if (nextContent?.arrayOfTitleAndDescription?.length) {
            setSelectedContentIndex(selectedContentIndex + 1);
            setSelectedSubContentIndex(0);
          }
        }
      } else {
        // Move to the next subcontent item
        setSelectedSubContentIndex(selectedSubContentIndex + 1);
      }
    } else if (totalSubItems > 0) {
      // If we're at the main content and it has subcontent items
      setSelectedSubContentIndex(0);
    } else if (
      selectedContentIndex <
      (portfoliosPerCategory?.content?.length || 0) - 1
    ) {
      // Move to the next content if it has no subcontent items
      const nextContent =
        portfoliosPerCategory?.content?.[selectedContentIndex + 1];
      setSelectedContentIndex(selectedContentIndex + 1);
      setSelectedSubContentIndex(
        nextContent?.arrayOfTitleAndDescription?.length ? 0 : null
      );
    }
  };

  const handlePrevious = () => {
    if (!selectedContent) return;

    if (selectedSubContentIndex !== null && selectedSubContentIndex > 0) {
      // If we're not at the first subcontent item
      setSelectedSubContentIndex(selectedSubContentIndex - 1);
    } else if (selectedSubContentIndex === 0) {
      // If we're at the first subcontent item, go to the previous content's last subcontent
      if (selectedContentIndex > 0) {
        const prevContent =
          portfoliosPerCategory?.content?.[selectedContentIndex - 1];
        const prevSubItems =
          prevContent?.arrayOfTitleAndDescription?.length || 0;

        setSelectedContentIndex(selectedContentIndex - 1);
        setSelectedSubContentIndex(prevSubItems > 0 ? prevSubItems - 1 : null);
      }
    } else if (selectedContentIndex > 0) {
      // If we're at the main content, go to the previous content
      const prevContent =
        portfoliosPerCategory?.content?.[selectedContentIndex - 1];
      const prevSubItems = prevContent?.arrayOfTitleAndDescription?.length || 0;

      setSelectedContentIndex(selectedContentIndex - 1);
      setSelectedSubContentIndex(prevSubItems > 0 ? prevSubItems - 1 : null);
    }
  };

  return (
    <Section className="py-20 bg-background" ref={contentRef}>
      <Container maxWidth={1536}>
        <Flex direction="col" justify="between" className="lg:flex-row">
          <div
            className={`relative w-full  ${
              hasCalculator
                ? "lg:w-[70%] lg:pr-8 lg:border-r border-gray-300"
                : ""
            }`}
          >
            {/* Title */}
            <div className="mb-10">
              <CaptionAndTitleText caption={caption} title={title} />
            </div>

            <div className="mt-8">
              {/* Category Tabs */}
              <div className="mb-8 overflow-x-auto">
                <PortfolioCategories
                  categories={portfoliosWithCategory}
                  activeTab={activeTab}
                  onClickFn={setActiveTab}
                />
              </div>

              {/* Main content section */}
              <Flex className="flex-col lg:flex-row gap-6">
                {/* Table of Contents - left sidebar for desktop, top on mobile */}
                <div className="w-full lg:w-1/4 lg:sticky lg:top-28 lg:self-start">
                  <TableOfContents
                    contents={portfoliosPerCategory?.content}
                    selectedIndex={selectedContentIndex}
                    selectedSubIndex={selectedSubContentIndex}
                    onSelect={setSelectedContentIndex}
                    onSelectSub={setSelectedSubContentIndex}
                  />
                </div>

                {/* Right side content display */}
                <div className="w-full lg:w-3/4">
                  {/* Search implementation */}
                  <div className="mb-8">
                    <div className="relative w-full max-w-md mx-auto">
                      <Input
                        type="text"
                        placeholder="Search content..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        // className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                        className="w-full border border-primary/80 rounded-none pr-10 !py-1 focus:border-primary focus:ring-1 focus:ring-primary focus:rounded-none"
                      />
                      <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    </div>
                  </div>

                  {/* Search results */}
                  {searchQuery && (
                    <div className="mb-6">
                      {filteredResults.length > 0 ? (
                        <Text className="text-gray-600">
                          Found {filteredResults.length} result(s) for "
                          {searchQuery}"
                        </Text>
                      ) : (
                        <Text className="text-gray-600">
                          No results found for "{searchQuery}"
                        </Text>
                      )}
                    </div>
                  )}

                  {/* Content display area */}
                  <div className="min-h-[300px]" ref={contentDisplayRef}>
                    {searchQuery && filteredResults.length > 0 ? (
                      // Show search results
                      filteredResults.map((result, idx) => {
                        const content =
                          portfoliosPerCategory?.content?.[result.index];
                        return (
                          <ContentDisplay
                            key={`${content?._key}-${result.subIndex}-${idx}`}
                            content={content}
                            selectedSubIndex={result.subIndex}
                          />
                        );
                      })
                    ) : (
                      // Show normal single content when not searching
                      <ContentDisplay
                        content={selectedContent}
                        selectedSubIndex={selectedSubContentIndex}
                        onNext={showSubContentNav ? handleNext : undefined}
                        onPrevious={
                          showSubContentNav ? handlePrevious : undefined
                        }
                        currentIndex={
                          selectedSubContentIndex !== null
                            ? selectedSubContentIndex + 1
                            : 0
                        }
                        totalItems={totalSubItems}
                        contentTitle={selectedContent?.title}
                      />
                    )}
                  </div>
                </div>
              </Flex>
            </div>
          </div>
          {hasCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
              <ValveCalculator />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

function TableOfContents({
  contents,
  selectedIndex,
  selectedSubIndex,
  onSelect,
  onSelectSub,
}: {
  contents?: Content[] | null;
  selectedIndex: number;
  selectedSubIndex: number | null;
  onSelect: (index: number) => void;
  onSelectSub: (index: number | null) => void;
}) {
  const [openDropdowns, setOpenDropdowns] = React.useState<{
    [key: string]: boolean;
  }>({});

  if (!contents || !Array.isArray(contents)) return null;

  const toggleDropdown = (key: string) => {
    // Close all other dropdowns when opening a new one
    setOpenDropdowns((prev) => {
      const newState = { ...prev };

      // If we're opening this dropdown, close all others
      if (!prev[key]) {
        Object.keys(newState).forEach((k) => {
          newState[k] = k === key;
        });
      } else {
        // Just toggle this one
        newState[key] = !prev[key];
      }

      return newState;
    });
  };

  // Auto-open the dropdown for the selected content and close others
  React.useEffect(() => {
    if (contents[selectedIndex]?._key) {
      setOpenDropdowns((prev) => {
        const newState = {};
        // Close all dropdowns
        Object.keys(prev).forEach((key) => {
          newState[key] = false;
        });
        // Open only the selected one
        newState[contents[selectedIndex]._key || ""] = true;
        return newState;
      });
    }
  }, [selectedIndex, contents]);

  return (
    <div className="sticky top-24 bg-gray-100 p-6 rounded-lg">
      <Text fontSize="lg" weight="bold" className="mb-6 !text-gray-900">
        Table of Contents
      </Text>
      <div className="space-y-2">
        {contents.map((content, index) => (
          <div key={content?._key} className="border-b border-gray-100">
            <Button
              variant="ghost"
              as="button"
              ariaLabel={
                content?.title ?? `Table of Contents button ${index + 1}`
              }
              className={`w-full text-left flex justify-between items-center px-0 py-3 !font-semibold ${
                selectedIndex === index ? "text-primary " : "text-gray-700 "
              }`}
              onClick={() => {
                onSelect(index);
                // If this content has subcontents, set selectedSubIndex to 0 (first subcontent)
                if (content?.arrayOfTitleAndDescription?.length) {
                  onSelectSub(0);
                  toggleDropdown(content?._key || "");
                } else {
                  onSelectSub(null);
                }
              }}
            >
              <div className="flex items-center">
                <span
                  className={`border-l-4 pl-1 ${
                    selectedIndex === index
                      ? "border-primary"
                      : "border-transparent"
                  }`}
                >
                  {content?.title}
                </span>
              </div>
              {content?.arrayOfTitleAndDescription?.length > 0 && (
                <svg
                  className={`w-4 h-4 transition-transform ${
                    openDropdowns[content?._key || ""] ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              )}
            </Button>

            {openDropdowns[content?._key || ""] &&
              content?.arrayOfTitleAndDescription && (
                <div className="pl-4 py-2 space-y-5">
                  {content.arrayOfTitleAndDescription.map((item, subIndex) => (
                    <Button
                      key={item?._key}
                      variant="ghost"
                      as="button"
                      ariaLabel={item?.title || `Subcontent ${subIndex + 1}`}
                      className={`w-full text-left text-sm py-0 pl-1 !font-semibold flex items-center border-l-4 rounded-none ${
                        selectedIndex === index && selectedSubIndex === subIndex
                          ? "text-primary/80 border-primary"
                          : "text-gray-600 border-transparent"
                      }`}
                      onClick={() => {
                        onSelect(index);
                        onSelectSub(subIndex);
                      }}
                    >
                      {item?.title}
                    </Button>
                  ))}
                </div>
              )}
          </div>
        ))}
      </div>
    </div>
  );
}

function ContentDisplay({
  content,
  selectedSubIndex,
  onNext,
  onPrevious,
  currentIndex,
  totalItems,
  contentTitle,
}: {
  content?: Content;
  selectedSubIndex: number | null;
  onNext?: () => void;
  onPrevious?: () => void;
  currentIndex?: number;
  totalItems?: number;
  contentTitle?: string | null;
}) {
  if (!content) return null;

  // Check if this content has subcontents
  const hasSubContents =
    content?.arrayOfTitleAndDescription &&
    Array.isArray(content.arrayOfTitleAndDescription) &&
    content.arrayOfTitleAndDescription.length > 0;

  // If we're showing subcontent or if we should show subcontent (selectedSubIndex is null but there are subcontents)
  if (
    (selectedSubIndex !== null && hasSubContents) ||
    (selectedSubIndex === null && hasSubContents)
  ) {
    // If selectedSubIndex is null but we have subcontents, show the first one
    const effectiveSubIndex = selectedSubIndex !== null ? selectedSubIndex : 0;
    // Make sure arrayOfTitleAndDescription exists before accessing it
    const subContent =
      hasSubContents && content.arrayOfTitleAndDescription
        ? content.arrayOfTitleAndDescription[effectiveSubIndex]
        : null;

    if (!subContent) return null;

    // Create our own checkbox style list state instead of using PortableText
    const [checkedItems, setCheckedItems] = React.useState<boolean[]>([]);
    const [visibleItems, setVisibleItems] = React.useState(1); // Start with 1 visible item
    const [listItems, setListItems] = React.useState<React.ReactNode[]>([]);
    const [showHelp, setShowHelp] = React.useState(false);

    // Reset checklist state when content or subIndex changes
    React.useEffect(() => {
      setCheckedItems([]);
      setVisibleItems(1);
      setShowHelp(false);
      setListItems([]);

      // Wait for the next render to ensure the hidden divs are populated
      const timer = setTimeout(() => {
        // First column
        const firstPortableTextEl = document.getElementById(
          `portable-text-${content._key}-${effectiveSubIndex}-first`
        );

        // Second column
        const secondPortableTextEl = document.getElementById(
          `portable-text-${content._key}-${effectiveSubIndex}-second`
        );

        if (firstPortableTextEl) {
          // Extract all bullet list items from subcontent
          const bulletItems = Array.from(
            firstPortableTextEl.querySelectorAll("li")
          ).map((li) => li.textContent || "");

          if (bulletItems.length > 0) {
            setListItems(bulletItems);
            setCheckedItems(new Array(bulletItems.length).fill(false));
            setVisibleItems(1);
          }
        }
      }, 100);

      return () => clearTimeout(timer);
    }, [content._key, effectiveSubIndex]);

    const handleCheckboxToggle = (index: number) => {
      const newCheckedItems = [...checkedItems];
      newCheckedItems[index] = !newCheckedItems[index];
      setCheckedItems(newCheckedItems);

      // Show the next item when an item is checked
      if (
        newCheckedItems[index] &&
        index + 1 < listItems.length &&
        visibleItems === index + 1
      ) {
        setVisibleItems(index + 2);
      }

      // Show help message when all items are checked
      if (newCheckedItems.every((item) => item) && listItems.length > 0) {
        setShowHelp(true);
      } else {
        setShowHelp(false);
      }
    };

    return (
      <div className="relative">
        {/* Navigation controls */}
        {onNext &&
          onPrevious &&
          currentIndex !== undefined &&
          totalItems !== undefined && (
            <div className="flex justify-between items-center mb-6">
              <div>
                <Text className="text-sm text-gray-500 mb-1">
                  {contentTitle ?? ""}
                </Text>
                <Heading
                  fontSize="3xl"
                  type="h2"
                  className="!text-primary uppercase"
                >
                  {subContent?.title ?? ""}
                </Heading>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  as="button"
                  variant="primaryHover"
                  ariaLabel="Previous"
                  className="rounded-none !p-2 sm:!p-3"
                  size="sm"
                  onClick={onPrevious}
                >
                  <FaArrowLeft className="text-sm sm:text-base" />
                </Button>
                <p className="text-base sm:text-xl font-bold font-mono text-primary">
                  {`${(effectiveSubIndex + 1)
                    .toString()
                    .padStart(2, "0")}/${totalItems
                    .toString()
                    .padStart(2, "0")}`}
                </p>
                <Button
                  as="button"
                  variant="primaryHover"
                  ariaLabel="Next"
                  className="rounded-none !p-2 sm:!p-3"
                  size="sm"
                  onClick={onNext}
                >
                  <FaArrowRight className="text-sm sm:text-base" />
                </Button>
              </div>
            </div>
          )}

        <div className="transition-all duration-300">
          <div className="content-slide">
            <div key={`subcontent-${content._key}-${effectiveSubIndex}`}>
              {!onNext && (
                <Heading fontSize="2xl" type="h2" className="mb-6">
                  {subContent?.title}
                </Heading>
              )}
              <div className="w-full border-b border-primary h-1 mb-6">
                <div className="h-1 w-10 bg-primary mb-6"></div>
              </div>

              {/* Render original content in hidden elements so we can extract list items */}
              {subContent?.firstColumn && (
                <div
                  className="hidden"
                  id={`portable-text-${content._key}-${effectiveSubIndex}-first`}
                >
                  <ListContext.Provider value={{ isSubcontent: true }}>
                    <PortableText
                      value={subContent.firstColumn}
                      components={customComponents}
                      onMissingComponent={false}
                    />
                  </ListContext.Provider>
                </div>
              )}

              {subContent?.secondColumn && (
                <div
                  className="hidden"
                  id={`portable-text-${content._key}-${effectiveSubIndex}-second`}
                >
                  <ListContext.Provider value={{ isSubcontent: true }}>
                    <PortableText
                      value={subContent.secondColumn}
                      components={customComponents}
                      onMissingComponent={false}
                    />
                  </ListContext.Provider>
                </div>
              )}

              {/* Render content without bullet lists */}
              <div className="prose max-w-none">
                {subContent?.firstColumn && (
                  <ListContext.Provider value={{ isSubcontent: true }}>
                    <PortableText
                      value={subContent.firstColumn.filter(
                        (block) =>
                          block._type !== "block" ||
                          (block._type === "block" &&
                            block.listItem !== "bullet")
                      )}
                      components={customComponents}
                      onMissingComponent={false}
                    />
                  </ListContext.Provider>
                )}

                {/* Render our custom checklist */}
                {listItems.length > 0 && (
                  <ul className="pl-0 mb-6">
                    {listItems.slice(0, visibleItems).map((item, idx) => (
                      <li
                        key={idx}
                        className={`mb-4 leading-loose ${
                          checkedItems[idx] ? "text-gray-400" : "text-gray-900"
                        } flex items-start gap-3`}
                      >
                        <div className="flex items-center mt-[2px]">
                          <input
                            type="checkbox"
                            checked={checkedItems[idx] || false}
                            onChange={() => handleCheckboxToggle(idx)}
                            className="form-checkbox h-6 w-6 text-primary rounded border-gray-300 focus:ring-primary cursor-pointer"
                          />
                        </div>
                        <div>{item}</div>
                      </li>
                    ))}
                  </ul>
                )}

                {subContent?.secondColumn && (
                  <ListContext.Provider value={{ isSubcontent: true }}>
                    <PortableText
                      value={subContent.secondColumn.filter(
                        (block) =>
                          block._type !== "block" ||
                          (block._type === "block" &&
                            block.listItem !== "bullet")
                      )}
                      components={customComponents}
                      onMissingComponent={false}
                    />
                  </ListContext.Provider>
                )}
              </div>

              {/* Show the help message when all steps are checked */}
              {showHelp && (
                <div className="mt-8 p-4 bg-gray-100 border-l-4 border-primary">
                  <Text className="text-gray-700">
                    For additional help please contact Maxton tech support at{" "}
                    <a
                      href="tel:**************"
                      className="text-primary underline hover:text-primary/40"
                    >
                      **************
                    </a>
                  </Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If we're showing main content (no subcontent available)
  return (
    <div className="relative">
      <div className="transition-all duration-300">
        <div className="content-slide">
          <Heading
            fontSize="3xl"
            type="h2"
            className="mb-6 !text-primary uppercase"
          >
            {content?.title}
          </Heading>
          <div className="w-full border-b border-primary h-1 mb-6">
            <div className="h-1 w-10 bg-primary mb-6"></div>
          </div>
          {content?.firstColumn && (
            <div className="prose max-w-none">
              <ListContext.Provider value={{ isSubcontent: false }}>
                <PortableText
                  value={content.firstColumn}
                  components={customComponents}
                  onMissingComponent={false}
                />
              </ListContext.Provider>
            </div>
          )}
          {content?.secondColumn && (
            <div className="prose max-w-none mt-6">
              <ListContext.Provider value={{ isSubcontent: false }}>
                <PortableText
                  value={content.secondColumn}
                  components={customComponents}
                  onMissingComponent={false}
                />
              </ListContext.Provider>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function CaptionAndTitleText({
  caption,
  title,
}: {
  caption?: string | null;
  title?: string | null;
}) {
  if (!caption && !title) return null;

  return (
    <div className="mb-8 text-center">
      {caption && <Text weight="semibold">{caption}</Text>}
      {title && (
        <Heading className="mt-2" type="h2" fontSize="3xl">
          {title}
        </Heading>
      )}
    </div>
  );
}

function PortfolioCategories({
  categories,
  activeTab,
  onClickFn,
}: {
  categories?: PortfoliosWithCategories[] | null;
  activeTab?: string | null;
  onClickFn?: React.Dispatch<React.SetStateAction<string | null | undefined>>;
}) {
  if (!categories || categories?.length === 0) return null;

  return (
    <Flex className="flex flex-col md:flex-row text-lg  items-center md:items-start gap-2 md:gap-0">
      {categories?.map((content, index) => (
        <Button
          variant="unstyled"
          as="button"
          ariaLabel={content?.category ?? `Category button ${index + 1}`}
          key={content?._key}
          onClick={() => onClickFn?.(content?.category)}
          className={`px-4 py-2 font-medium  ${
            activeTab === content?.category
              ? "bg-primary text-white"
              : "text-gray-700 hover:text-primary hover:bg-primary/10 bg-gray-300"
          } ${index !== 0 ? "border-l-2 border-white" : ""}`}
        >
          {content?.category}
        </Button>
      ))}
    </Flex>
  );
}

// Create a component to handle the checklist wrapper for subcontents
function ChecklistWrapper({
  children,
  contentKey,
  subIndex,
}: {
  children: React.ReactNode;
  contentKey: string;
  subIndex: number | null;
}) {
  // Only use the checklist functionality for subcontent sections
  const hasSubContents = subIndex !== null;
  const {
    checkedItems,
    maxVisibleStep,
    showFinalMessage,
    initializeSubcontent,
    handleCheckboxToggle,
  } = useBulletChecklist(hasSubContents);

  // Define a unique key for this subcontent
  const subcontentKey = `${contentKey}-${subIndex}`;

  // Extract bullet points from children and convert them to checkable items
  useEffect(() => {
    // This only applies to subcontent sections (not main content)
    if (!hasSubContents || !children) return;

    try {
      // Create a temporary element to render the content
      const tempDiv = document.createElement("div");
      document.body.appendChild(tempDiv);

      // Render content temporarily so we can examine it
      const tempRoot = ReactDOM.createRoot(tempDiv);
      tempRoot.render(<>{children}</>);

      // Find all bullet list items - look specifically for our marked classes
      const bulletLists = tempDiv.querySelectorAll(".checklist-container");
      const listItems = tempDiv.querySelectorAll(".checklist-item");
      const totalSteps = listItems.length;

      console.log("Found checklist items:", totalSteps);

      // Initialize the checklist state for this subcontent if we found steps
      if (totalSteps > 0) {
        initializeSubcontent(subcontentKey, totalSteps);
      }

      // Clean up
      tempRoot.unmount();
      document.body.removeChild(tempDiv);
    } catch (error) {
      console.error("Error in ChecklistWrapper:", error);
    }
  }, [children, subcontentKey, hasSubContents, initializeSubcontent]);

  // If this is not a subcontent section, just render the children normally
  if (!hasSubContents) {
    return <>{children}</>;
  }

  // Function to process and transform React elements recursively
  const processElement = (element: React.ReactElement): React.ReactNode => {
    // Base case: not a valid React element
    if (!React.isValidElement(element)) {
      return element;
    }

    // Check if this is a list container
    const isListContainer =
      (typeof element.type === "string" && element.type === "ul") ||
      (element.props.className &&
        element.props.className.includes("checklist-container"));

    // If this is a list container, transform its children into checkable items
    if (isListContainer) {
      const childArray = React.Children.toArray(element.props.children);
      const totalItems = childArray.length;

      // Create transformed list items
      const transformedItems = childArray.map((child, idx) => {
        if (!React.isValidElement(child)) return child;

        const isListItem =
          (typeof child.type === "string" && child.type === "li") ||
          (child.props.className &&
            child.props.className.includes("checklist-item"));

        if (isListItem) {
          // Get the index from data attribute or use position
          const itemIndex =
            child.props["data-index"] !== undefined
              ? parseInt(child.props["data-index"], 10)
              : idx;

          // Create a checkable list item
          return (
            <CheckableListItem
              key={`${subcontentKey}-item-${itemIndex}`}
              index={itemIndex}
              subcontentKey={subcontentKey}
              isChecked={checkedItems[subcontentKey]?.[itemIndex] || false}
              totalSteps={totalItems}
              maxVisible={maxVisibleStep[subcontentKey] || 1}
              onToggle={handleCheckboxToggle}
            >
              {child.props.children[1] || child.props.children}
            </CheckableListItem>
          );
        }

        // For other child elements, process recursively
        return processElement(child);
      });

      // Return a new list with our processed items
      return React.cloneElement(
        element,
        { className: "pl-0 mb-3 leading-loose checklist-steps" },
        transformedItems
      );
    }

    // For other elements, recursively process their children
    if (element.props && element.props.children) {
      // Handle both single children and arrays of children
      if (React.Children.count(element.props.children) > 0) {
        const newChildren = React.Children.map(
          element.props.children,
          (child) =>
            React.isValidElement(child) ? processElement(child) : child
        );
        return React.cloneElement(element, element.props, newChildren);
      }
    }

    return element;
  };

  // Transform the children to replace bullet lists with checkable lists
  const transformedChildren = React.Children.map(children, (child) =>
    React.isValidElement(child) ? processElement(child) : child
  );

  return (
    <div className="checklist-wrapper">
      {transformedChildren}

      {/* Show the final message when the last step is checked */}
      {showFinalMessage[subcontentKey] && (
        <div className="mt-8 p-4 bg-gray-100 border-l-4 border-primary">
          <Text className="text-gray-700">
            For additional help please contact Maxton tech support at
            **************
          </Text>
        </div>
      )}
    </div>
  );
}

export { Portfolio_F };
