import React, { useState } from "react";
import { Button } from "@/components/ui/shadcn-button";
import { useExportProductGroupPricesQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { toast } from "@/hooks/use-toast";
import {
  productGroupPricesToCsv,
  productGroupPricesToCsvLongFormat,
  downloadCsv,
  generateExportFilename,
  validateCsvData,
} from "@/lib/utils/csv-export";
import { Download, Loader2, FileText, AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog-shadcn";
import { Badge } from "@/components/ui/badge";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ProductGroupPricesExportProps {
  className?: string;
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "ghost"
    | "link"
    | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
}

export function ProductGroupPricesExport({
  className = "",
  variant = "outline",
  size = "default",
}: ProductGroupPricesExportProps) {
  const token = useAuthStore((state) => state.token);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [exportData, setExportData] = useState<any>(null);

  const exportMutation = useExportProductGroupPricesQuery(token || "");

  const handleExport = async () => {
    if (!token) {
      toast({
        title: "Error",
        description: "Authentication required",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await exportMutation.mutateAsync();

      if (result.error) {
        toast({
          title: "Export Error",
          description: result.error,
          variant: "destructive",
        });
        return;
      }

      if (!result.data || result.data.length === 0) {
        toast({
          title: "No Data",
          description: "No product group prices found to export",
          variant: "destructive",
        });
        return;
      }

      // Validate the data
      const validation = validateCsvData(result.data);
      if (!validation.isValid) {
        toast({
          title: "Data Validation Error",
          description: `Export failed: ${validation.errors.join(", ")}`,
          variant: "destructive",
        });
        return;
      }

      // Store data for preview
      setExportData(result);
      setShowPreviewDialog(true);
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export Failed",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const handleDownload = () => {
    if (!exportData || !exportData.data || !exportData.groups) {
      return;
    }

    try {
      // Generate CSV content
      const csvContent = productGroupPricesToCsvLongFormat(
        exportData.data,
        exportData.groups,
        { includeHeaders: true }
      );

      // Generate filename with timestamp
      const filename = generateExportFilename("product-group-prices", "csv");

      // Trigger download
      downloadCsv(csvContent, filename);

      toast({
        title: "Export Successful",
        description: `Downloaded ${exportData.data.length} product records`,
      });

      setShowPreviewDialog(false);
      setExportData(null);
    } catch (error) {
      console.error("Download error:", error);
      toast({
        title: "Download Failed",
        description: "Failed to generate CSV file",
        variant: "destructive",
      });
    }
  };

  const isLoading = exportMutation.isPending;

  return (
    <>
      <Button
        onClick={handleExport}
        disabled={isLoading || !token}
        variant={variant}
        size={size}
        className={className}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Download className="h-4 w-4 mr-2" />
        )}
        {isLoading ? "Exporting..." : "Export Prices"}
      </Button>

      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Export Preview
            </DialogTitle>
            <DialogDescription>
              Review the export data before downloading the CSV file
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden flex flex-col">
            {exportData && (
              <div className="space-y-4">
                {/* Export Summary */}
                <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">
                      {exportData.data?.length || 0} Products
                    </Badge>
                    <Badge variant="secondary">
                      {exportData.groups?.length || 0} Price Groups
                    </Badge>
                  </div>
                </div>

                {/* Data Preview */}
                <ScrollArea className="flex-1 h-96">
                  <ScrollBar orientation="horizontal" />
                  <div className="border rounded-lg">
                    <Table className="table-fixed">
                      <TableHeader>
                        <TableRow>
                          <TableHead className="border-r w-48 min-w-[12rem] max-w-[12rem]">
                            Product Code
                          </TableHead>
                          {exportData.groups?.map((group: string) => (
                            <TableHead
                              key={group}
                              className="border-r w-32 min-w-[8rem]"
                            >
                              {group}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {exportData.data?.map((product: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium border-r w-48 min-w-[12rem] max-w-[12rem] truncate">
                              {product.product_code}
                            </TableCell>
                            {exportData.groups?.map((group: string) => (
                              <TableCell
                                key={group}
                                className="border-r w-32 min-w-[8rem] truncate"
                              >
                                {product.group_prices[group] !== null
                                  ? `$${product.group_prices[group]?.toFixed(
                                      2
                                    )}`
                                  : "-"}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </ScrollArea>

                {exportData.data?.length > 10 && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <AlertCircle className="h-4 w-4" />
                    This is a preview of all product group prices. The download
                    will include all {exportData.data?.length} products.
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPreviewDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default ProductGroupPricesExport;
