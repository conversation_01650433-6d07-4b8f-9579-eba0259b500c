import {
  customText,
  mainImage,
  subtitle,
  title,
  rating,
} from "../../../common/fields";
import { RiChatQuoteLine } from "react-icons/ri";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const testimonialSchema = [
  subtitle(hideIfVariantIn(["variant_a", "variant_d"])),
  title(hideIfVariantIn(["variant_a", "variant_d"])),
  mainImage(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
    ])
  ),
  {
    name: "testimonials",
    type: "array",
    title: "Testimonials",
    description:
      "Click the 'Add item' button to add a testimonial. If you want to edit what is added, click this ⋮ icon found on its right.",
    of: [
      {
        type: "object",
        icon: RiChatQuoteLine,
        fields: [
          rating(
            hideIfVariantIn([
              "variant_a",
              "variant_b",
              "variant_c",
              "variant_e",
              "variant_f",
            ])
          ),
          mainImage(hideIfVariantIn(["variant_b", "variant_e"])),
          customText("name", "Full Name", "", "Enter Name Here...", 1),
          customText("jobTitle", "Job Title", "", "Enter Job title Here...", 1),
          {
            name: "testimony",
            title: "Testimony",
            placeholder: "Write a Testimony Here...",
            type: "text",
          },
        ],
      },
    ],
  },
];
