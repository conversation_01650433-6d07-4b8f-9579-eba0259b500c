import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import {
  ArrayOfImageTitleAndText,
  MyPortableTextComponents,
} from "../../../types";
import { PortableTextBlock } from "sanity";
import { PortableText } from "@portabletext/react";
import { primaryButton } from "schemas/custom/sanity-plugin-schema-default/src/schemas/common/fields";
import { Button } from "@stackshift-ui/button";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-6 leading-loose text-5xl sm:text-7xl">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return (
        <Heading type="h2" className="mb-4 text-xl">
          {children}
        </Heading>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-6 text-xl md:text-3xl leading-loose text-gray-900">
          {children}
        </h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-2 text-base sm:text-xl font-semibold leading-loose text-gray-900">
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <Text className="mb-3 font-body text-sm sm:text-base text-gray-900 lg:text-left text-justify">
          {children}
        </Text>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-secondary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
};

export default function Features_W({
  caption,
  title,
  description,
  features,
  tags,
  firstColumn,
}: FeaturesProps) {
  return (
    <Section className="py-20 bg-background min-h-[650px]">
      <Container maxWidth={1280} className="mx-0 px-0">
        <Flex
          align="start"
          className="w-full flex flex-col mx-0"
          justify="start"
          items="start"
        >
          <FeatureInfo
            caption={caption}
            title={title}
            firstColumn={firstColumn}
          />
          <FeaturesLists features={features} />
        </Flex>
      </Container>
    </Section>
  );
}

function FeatureInfo({
  caption,
  title,
  description,
  firstColumn,
}: {
  caption?: string;
  title?: string;
  description?: string;
  firstColumn?: PortableTextBlock;
}) {
  return (
    <React.Fragment>
      {caption && (
        <Text weight="bold" className="text-secondary">
          {caption}
        </Text>
      )}
      {title && <Heading fontSize="3xl">{title}</Heading>}
      {firstColumn && (
        <div className="mb-2 text-xs md:mb-0 lg:text-base">
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </React.Fragment>
  );
}

function FeaturesLists({
  features,
}: {
  features?: ArrayOfImageTitleAndText[];
}) {
  if (!features) return null;
  return (
    <Flex className="w-full h-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 pt-8 group">
      {features?.map((feature, index) => (
        <div className="w-full h-full" key={index}>
          <div className="w-full h-full bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1 group/inner">
            {/* Image Container with Hover Effect - Now using Button */}
            <Button
              link={feature?.primaryButton}
              ariaLabel={feature?.primaryButton?.label || "View form"}
              variant="unstyled"
              as="link"
              className="relative w-full h-48 overflow-hidden cursor-pointer block p-0"
            >
              <div
                className="absolute inset-0 w-full h-full bg-cover bg-center"
                style={{
                  backgroundImage: feature?.mainImage?.image
                    ? `url(${feature.mainImage.image})`
                    : "none",
                  backgroundColor: feature?.mainImage?.image
                    ? "transparent"
                    : "#f3f4f6",
                }}
              >
                {!feature?.mainImage?.image && (
                  <div className="w-full h-full flex items-center justify-center">
                    <Text className="text-gray-400">No image</Text>
                  </div>
                )}
              </div>

              {/* Blue overlay on hover */}
              <div className="absolute inset-0 bg-primary bg-opacity-0 group-hover/inner:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
                <Text className="text-white font-medium opacity-0 group-hover/inner:opacity-100 transition-opacity duration-300">
                  Open Form
                </Text>
              </div>
            </Button>

            {/* Content Section */}
            <div className="p-4">
              {/* Title */}
              <Heading type="h2" className="!text-lg font-semibold mb-2">
                {feature?.title}
              </Heading>

              {/* Description - visible on hover */}
              <div className="max-h-0 h-fit opacity-0 group-hover:max-h-40 group-hover:opacity-100 transition-all duration-300 overflow-hidden">
                <Text className="text-sm text-gray-600 mb-3">
                  {feature?.plainText}
                </Text>
              </div>
            </div>
          </div>
        </div>
      ))}
    </Flex>
  );
}

export { Features_W };
