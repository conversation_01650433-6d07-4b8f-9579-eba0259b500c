import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

// Validation schemas
const CreateProductArrangementSchema = z.object({
  product_ids: z.array(z.string().uuid("Invalid product ID")).min(1, "At least one product ID is required"),
  category_id: z.string().uuid("Invalid category ID"),
});

export type CreateProductArrangement = z.infer<typeof CreateProductArrangementSchema>;

export interface GetProductArrangementsResponse {
  data?: any[];
  total?: number;
  page?: number;
  limit?: number;
  error?: string;
}

export interface CreateProductArrangementResponse {
  data?: any[];
  error?: string;
}

async function getProductArrangements(
  req: NextApiRequest,
  res: NextApiResponse<GetProductArrangementsResponse>
) {
  try {
    const { page = "1", limit = "10" } = req.query;
    const pageNum = parseInt(page as string, 10);
    const limitNum = parseInt(limit as string, 10);
    const offset = (pageNum - 1) * limitNum;

    const supabaseAdminClient = createSupabaseAdminClient();

    // Get total count
    const { count } = await supabaseAdminClient
      .from("product_arrangements")
      .select("*", { count: "exact", head: true });

    // Get paginated data with relations
    const { data, error } = await supabaseAdminClient
      .from("product_arrangements")
      .select(`
        *,
        products:product_id (
          id,
          name,
          sku,
          price,
          image
        ),
        categories:category_id (
          id,
          name
        )
      `)
      .order("position", { ascending: true })
      .range(offset, offset + limitNum - 1);

    if (error) {
      console.error("Error fetching product arrangements:", error);
      return res.status(500).json({ error: "Failed to fetch product arrangements" });
    }

    return res.status(200).json({
      data: data || [],
      total: count || 0,
      page: pageNum,
      limit: limitNum,
    });
  } catch (error) {
    console.error("Error in getProductArrangements:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

async function createProductArrangement(
  req: NextApiRequest,
  res: NextApiResponse<CreateProductArrangementResponse>
) {
  try {
    const validationResult = CreateProductArrangementSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        error: `Validation error: ${validationResult.error.errors
          .map((e) => e.message)
          .join(", ")}`,
      });
    }

    const { product_ids, category_id } = validationResult.data;
    const supabaseAdminClient = createSupabaseAdminClient();

    // Check if category exists
    const { data: category } = await supabaseAdminClient
      .from("categories")
      .select("id")
      .eq("id", category_id)
      .single();

    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }

    // Check if all products exist
    const { data: products } = await supabaseAdminClient
      .from("products")
      .select("id")
      .in("id", product_ids);

    if (!products || products.length !== product_ids.length) {
      const foundProductIds = products?.map(p => p.id) || [];
      const missingProductIds = product_ids.filter(id => !foundProductIds.includes(id));
      return res.status(404).json({ 
        error: `Products not found: ${missingProductIds.join(", ")}` 
      });
    }

    // Check for existing arrangements for this category-product combinations
    const { data: existingArrangements } = await supabaseAdminClient
      .from("product_arrangements")
      .select("product_id")
      .eq("category_id", category_id)
      .in("product_id", product_ids);

    if (existingArrangements && existingArrangements.length > 0) {
      const existingProductIds = existingArrangements.map(a => a.product_id);
      return res.status(409).json({
        error: `Product arrangements already exist for products: ${existingProductIds.join(", ")}`
      });
    }

    // Get the current maximum position for this category
    const { data: maxPositionData } = await supabaseAdminClient
      .from("product_arrangements")
      .select("position")
      .eq("category_id", category_id)
      .order("position", { ascending: false })
      .limit(1)
      .single();

    const startPosition = (maxPositionData?.position ?? -1) + 1;

    // Prepare bulk insert data
    const arrangementsToInsert = product_ids.map((product_id, index) => ({
      product_id,
      category_id,
      position: startPosition + index,
    }));

    // Bulk insert the arrangements
    const { data, error } = await supabaseAdminClient
      .from("product_arrangements")
      .insert(arrangementsToInsert)
      .select(`
        *,
        products:product_id (
          id,
          name,
          sku,
          price,
          image
        ),
        categories:category_id (
          id,
          name
        )
      `);

    if (error) {
      console.error("Error creating product arrangements:", error);
      return res.status(500).json({ error: "Failed to create product arrangements" });
    }

    return res.status(201).json({ data });
  } catch (error) {
    console.error("Error in createProductArrangement:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

export default matchRoute({
  GET: getProductArrangements,
  POST: checkAdmin(createProductArrangement),
});