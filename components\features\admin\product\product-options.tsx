import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { PlusCircle, Trash2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { DraggableOption } from "./draggable-item";

type OptionType = "select" | "text" | "number";

interface OptionValue {
  name: string;
  value: string;
  price?: number;
}

interface ProductOption {
  name: string;
  type: OptionType;
  options?: OptionValue[];
}

interface ProductOptionsProps {
  form: UseFormReturn<any>;
  currentOption: ProductOption;
  setCurrentOption: React.Dispatch<React.SetStateAction<ProductOption>>;
  addOption: () => void;
  removeOption: (index: number) => void;
  addOptionValue: () => void;
  removeOptionValue: (index: number) => void;
  updateOptionValue: (
    index: number,
    field: keyof OptionValue,
    value: string | number
  ) => void;
}

export default function ProductOptions({
  form,
  currentOption,
  setCurrentOption,
  addOption,
  removeOption,
  addOptionValue,
  removeOptionValue,
  updateOptionValue,
}: ProductOptionsProps) {
  const options = form.watch("options") || [];

  const moveOption = (dragIndex: number, hoverIndex: number) => {
    const draggedOption = options[dragIndex];
    const newOptions = [...options];
    newOptions.splice(dragIndex, 1);
    newOptions.splice(hoverIndex, 0, draggedOption);
    form.setValue("options", newOptions);
  };

  const updateOption = (index: number, updatedOption: Record<string, any>) => {
    const newOptions = [...options];
    newOptions[index] = updatedOption;
    form.setValue("options", newOptions);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <Card className="rounded-lg">
        <CardHeader>
          <CardTitle>Product Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="border rounded-md p-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="optionName">Option Name</Label>
                <Input
                  className=""
                  id="optionName"
                  value={currentOption.name}
                  onChange={(e) =>
                    setCurrentOption({ ...currentOption, name: e.target.value })
                  }
                  placeholder="e.g. Size, Color, etc."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="optionType">Option Type</Label>
                <Select
                  value={currentOption.type}
                  onValueChange={(value: OptionType) =>
                    setCurrentOption({
                      ...currentOption,
                      type: value,
                      options:
                        value === "select"
                          ? [{ name: "", value: "" }]
                          : undefined,
                    })
                  }
                >
                  <SelectTrigger className="">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="select">Select</SelectItem>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {currentOption.type === "select" && currentOption.options && (
              <div className="flex flex-col gap-2">
                <Label className="text-sm font-semibold">Option Values</Label>
                <div className="flex flex-col gap-1">
                  {currentOption.options.map((option, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-1 gap-2 md:grid-cols-3 items-center"
                    >
                      <Input
                        className=""
                        placeholder="Display Name"
                        value={option.name}
                        onChange={(e) =>
                          updateOptionValue(index, "name", e.target.value)
                        }
                      />
                      <Input
                        className=""
                        placeholder="Value"
                        value={option.value}
                        onChange={(e) =>
                          updateOptionValue(index, "value", e.target.value)
                        }
                      />
                      <div className="flex gap-2 items-center">
                        <Input
                          type="number"
                          className=""
                          placeholder="Price (optional)"
                          value={option.price || ""}
                          onChange={(e) => {
                            const value = e.target.value
                              ? parseFloat(e.target.value)
                              : undefined;
                            updateOptionValue(index, "price", value as number);
                          }}
                          onWheel={(e) => e.currentTarget.blur()}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeOptionValue(index)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOptionValue}
                  className="mt-2 w-fit"
                >
                  <PlusCircle className="h-4 w-4 mr-1" /> Add Value
                </Button>
              </div>
            )}

            <Button
              type="button"
              variant="primary"
              onClick={addOption}
              disabled={!currentOption.name}
            >
              Add Option
            </Button>
          </div>

          {options.length > 0 && (
            <div className="space-y-2">
              <Label>Added Options</Label>
              <div className="space-y-2">
                {options.map((option: Record<string, any>, index) => (
                  <DraggableOption
                    key={index}
                    option={option}
                    index={index}
                    moveOption={moveOption}
                    removeOption={removeOption}
                    updateOption={updateOption}
                  />
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </DndProvider>
  );
}
