import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Category } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    DELETE: checkPermission("delete:categories", deleteCategoryHandler),
    PATCH: updateCategoryHandler,
  }),
);

export const updateCategorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  value: z.string().min(1, "Value is required"),
  parent_category_id: z.string().optional(),
});

export type UpdateCategoryRequest = z.infer<typeof updateCategorySchema>;

export interface UpdateCategoryResponse {
  error?: string;
  category?: Category;
}

async function updateCategoryHandler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateCategoryResponse>,
) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Category ID is required" });
  }

  const parsedData = updateCategorySchema.safeParse(req.body);

  if (!parsedData.success) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const now = new Date();
  const updatedAt = now.toISOString();

  const { data, error } = await supabaseAdminClient
    .from("categories")
    .update({
      ...parsedData.data,
      updated_at: updatedAt,
    })
    .eq("id", id as string)
    .select("*")
    .single();

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({ category: data });
}

export interface DeleteCategoryResponse {
  error?: string;
  message?: string;
}

async function deleteCategoryHandler(
  req: NextApiRequest,
  res: NextApiResponse<DeleteCategoryResponse>,
) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: "Category ID is required" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  // check if the category is used in any product categories, count the number of rows
  const {
    data: productCategories,
    error: productCategoriesError,
    count,
  } = await supabaseAdminClient
    .from("product_categories")
    .select("id", { count: "exact" })
    .eq("category_id", id as string);

  if (productCategoriesError) {
    return res.status(400).json({ error: productCategoriesError.message });
  }

  if (count && count > 0) {
    return res
      .status(400)
      .json({
        error: `Unable to delete category, it is referenced by ${count} products`,
      });
  }

  const { data: _, error } = await supabaseAdminClient
    .from("categories")
    .delete()
    .eq("id", id as string);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  return res.status(200).json({ message: "Category deleted successfully" });
}
