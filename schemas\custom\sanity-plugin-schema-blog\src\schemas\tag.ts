import { FiTag } from "react-icons/fi";
import { defineField, defineType } from "sanity";

export default defineType({
  name: "tag",
  title: "Tag",
  icon: FiTag,
  type: "document",
  fields: [
    defineField({
      name: "title",
      title: "Title",
      type: "string",
    }),
    defineField({
      name: "slug",
      title: "Slug",
      type: "slug",
      options: {
        source: "title",
        maxLength: 96,
      },
    }),
  ],
});
