import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import {
  ArrayOfImageTitleAndText,
  FeaturedItem,
  LabeledRoute,
} from "../../../types";
import { ButtonProps } from "../header";
import { But<PERSON>, Link } from "components/ui";
import { PortableText } from "@portabletext/react";

import { MyPortableTextComponents } from "types";
import { PortableTextBlock } from "@sanity/types";
import { IoDocumentOutline } from "react-icons/io5";
import { urlFor } from "lib/sanity";

const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-900 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 underline"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Features_N({
  caption,
  title,
  description,
  firstColumn,
  features,
  arrayOfLinks,
  primaryButton,
}: FeaturesProps) {
  return (
    <Section className="pt-10 md:pt-16 lg:pt-20 pb-8 md:pb-10 bg-background">
      <Container maxWidth={1280}>
        <Flex direction="row" justify="center">
          {title && (
            <Heading className="mb-8 md:mb-12 lg:mb-16 text-center text-2xl sm:text-3xl md:text-4xl lg:text-5xl px-4">
              {title}
            </Heading>
          )}
        </Flex>
        <Flex
          direction="col"
          gap={8}
          justify="between"
          className="lg:flex-row px-4 md:px-6 lg:px-8"
        >
          {/* First column */}
          <div className="relative w-full lg:w-2/3 lg:pl-0 xl:pl-10">
            <CaptionAndTitleSection
              title={title}
              firstColumn={firstColumn}
              caption={caption}
              description={description}
              primaryButton={primaryButton}
              arrayOfLinks={arrayOfLinks}
            />
          </div>

          {/* Second column */}
          <Flex direction="col" gap={6} className="w-full lg:basis-1/2">
            <FeatureItems features={features} />
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
  caption,
  description,
  primaryButton,
  arrayOfLinks,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
  caption?: string;
  description?: string;
  primaryButton?: LabeledRoute;
  arrayOfLinks?: LabeledRoute[];
}) {
  return (
    <div className="w-full lg:bg-white lg:rounded-lg lg:shadow-xl md:p-6 lg:p-8">
      <div className="mb-4 md:mb-5 flex justify-center sm:justify-start">
        <Buttons primaryButton={primaryButton} />
      </div>
      {caption && (
        <Text className="text-base md:text-lg mb-2 md:mb-3">{caption}</Text>
      )}
      {description && (
        <Text className="text-base md:text-lg mb-2 md:mb-3">{description}</Text>
      )}
      <div className="flex flex-col sm:grid sm:grid-cols-2 gap-3 my-6 md:my-8 lg:my-10">
        {arrayOfLinks?.map((link, index) => (
          <Button
            key={index}
            as="link"
            link={link}
            ariaLabel={`${link?.label}`}
            variant="solid"
            size="lg"
            className="flex items-center justify-center text-center bg-primary hover:bg-primary/80 text-white rounded px-4 py-2.5 md:py-3 text-base min-w-[200px] mx-auto sm:w-full"
          >
            {link.label}
          </Button>
        ))}
      </div>
      {firstColumn && (
        <div className="prose prose-sm md:prose-base lg:prose-lg max-w-none">
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </div>
  );
}

function Buttons({
  primaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex align="center" className="flex items-center justify-start flex-row">
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          size="lg"
          className="bg-primary  hover:bg-primary/80 text-white rounded px-4 sm:px-8 py-3 text-xl"
        >
          {primaryButton.label}
        </Button>
      ) : null}
    </Flex>
  );
}

function FeatureItems({ features }: { features?: ArrayOfImageTitleAndText[] }) {
  if (!features) return null;

  return (
    <div className="w-full">
      <Heading
        type="h3"
        className="text-lg sm:text-xl lg:text-2xl mb-3"
      ></Heading>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4">
        {features.map((feature, index) => (
          <FeatureItem feature={feature} key={feature._key} />
        ))}
      </div>
    </div>
  );
}

function FeatureItem({ feature }: { feature: ArrayOfImageTitleAndText }) {
  return (
    <div className="relative p-4 md:p-5 border border-black/40 rounded-lg shadow-lg">
      <Image
        className="w-auto h-auto mb-4 md:mb-5"
        width={25}
        height={25}
        src={`${feature?.mainImage?.image}`}
        alt={feature?.mainImage?.alt ?? feature?.mainImage?.image?.asset?._ref}
      />
      <Flex direction="col">
        <div className="items-center gap-2 transition-colors group-hover:border-b border-primary">
          <Text className="text-black font-semibold text-base md:text-lg transition-colors group-hover:text-primary mb-2">
            {feature.title}
          </Text>
          {feature?.firstColumn && (
            <div className="prose prose-sm md:prose-base">
              <PortableText
                value={feature?.firstColumn}
                components={textComponentBlockStyling}
                onMissingComponent={false}
              />
            </div>
          )}
        </div>
      </Flex>
    </div>
  );
}

export { Features_N };
