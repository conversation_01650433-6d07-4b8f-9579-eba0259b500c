import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button } from "components/ui";
import { FaArrowRightLong } from "react-icons/fa6";

export default function Features_M({
  caption,
  title,
  description,
  featuredItems,
}: FeaturesProps) {
  return (
    <Section className="py-20 lg:py-28 bg-background">
      <Container maxWidth={1280}>
        <div className="w-full ">
          <CaptionAndTitleSection
            caption={caption}
            title={title}
            description={description}
          />
        </div>

        <div className="w-full ">
          <FeatureItems features={featuredItems} />
        </div>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Text fontSize="sm" className="text-gray-600 mb-2">
          {caption}
        </Text>
      )}
      {title && (
        <Heading fontSize="3xl" type="h2" className="mb-4">
          {title}
        </Heading>
      )}
    </>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="space-y-16">
      {features.map((feature, index) => (
        <FeatureItem feature={feature} key={feature._key} index={index} />
      ))}
    </div>
  );
}

function FeatureItem({
  feature,
  index,
}: {
  feature: FeaturedItem;
  index: number;
}) {
  const isEven = index % 2 === 0;

  return (
    <div className="w-full md:px-4">
      <div
        className={`flex flex-col md:flex-row gap-y-10 md:gap-y-0 gap-x-5 lg:gap-x-10 ${
          isEven ? "md:flex-row-reverse " : ""
        } h-full`}
      >
        {/* Image Section */}
        {feature?.mainImage?.image && (
          <div className="md:w-1/2 flex-shrink-0">
            <Image
              className="object-cover h-[350px] w-full "
              src={feature?.mainImage.image}
              width={450}
              height={450}
              alt={feature.mainImage.alt ?? `features-image-`}
            />
          </div>
        )}

        {/* Text Section */}
        <div className="md:w-1/2 flex flex-col justify-center md:p-4 gap-y-4 ">
          <Heading type="h3" className="text-2xl lg:text-2xl">
            {feature?.title}
          </Heading>
          <Text muted className="text-sm text-gray-700 leading-loose">
            {feature.description}
          </Text>
        </div>
      </div>
    </div>
  );
}

export { Features_M };
