import AdminLayout from "@/components/features/admin/layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useGetSalesReportQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { format, subMonths } from "date-fns";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Head from "next/head";
import { useRouter } from "next/router";
import { useState } from "react";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

// Skeleton component for the chart
const ChartSkeleton = () => (
  <div className="h-96 w-full">
    <Skeleton className="w-full h-full" />
  </div>
);

// Skeleton component for the table
const TableSkeleton = () => (
  <div className="space-y-2">
    {/* Header skeleton */}
    <Skeleton className="h-10 w-full rounded" />

    {/* Row skeletons */}
    {[...Array(6)].map((_, index) => (
      <Skeleton key={index} className="h-12 w-full rounded" />
    ))}

    {/* Summary row skeleton */}
    <Skeleton className="h-12 w-full rounded mt-4" />
  </div>
);

export default function SalesByMonthPage() {
  const router = useRouter();
  const token = useAuthStore((state) => state.token);
  const [monthRange, setMonthRange] = useState("12"); // Default to 12 months

  // Calculate date range based on selection
  const today = new Date();
  const endDate = format(today, "yyyy-MM-dd");
  const startDate = format(
    subMonths(today, parseInt(monthRange)),
    "yyyy-MM-dd"
  );

  const { data, isLoading, isError } = useGetSalesReportQuery(
    token,
    "month",
    monthRange,
    startDate,
    endDate
  );

  const handleMonthRangeChange = (value: string) => {
    setMonthRange(value);
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format month display
  const formatMonth = (monthStr: string) => {
    const [year, month] = monthStr.split("-");
    return `${new Date(parseInt(year), parseInt(month) - 1, 1).toLocaleString(
      "default",
      { month: "long" }
    )} ${year}`;
  };

  // Get chart data
  const getChartData = () => {
    if (!data?.data) return [];

    return data.data.map((item: any) => {
      const [year, month] = item.label.split("-");
      const label = format(
        new Date(parseInt(year), parseInt(month) - 1, 1),
        "MMM yyyy"
      );

      return {
        name: label,
        grossSales: item.gross_sales,
        netSales: item.net_sales,
      };
    });
  };

  return (
    <AdminLayout>
      <Head>
        <title>Reports | Sales by Month</title>
      </Head>
      <div className="p-6">
        {/* Back button */}
        <div className="mb-4">
          <Link href="/admin/reports">
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Reports
            </Button>
          </Link>
        </div>

        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Sales by month</h1>
            <p className="text-gray-500">
              View detailed reports on your monthly sales
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select value={monthRange} onValueChange={handleMonthRangeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3">Last 3 months</SelectItem>
                <SelectItem value="6">Last 6 months</SelectItem>
                <SelectItem value="12">Last 12 months</SelectItem>
                <SelectItem value="24">Last 24 months</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Monthly Sales Chart</CardTitle>
            <CardDescription>
              Monthly sales performance from{" "}
              {format(new Date(startDate), "MMMM yyyy")} to{" "}
              {format(new Date(endDate), "MMMM yyyy")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              {isLoading ? (
                <ChartSkeleton />
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={getChartData()}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis tickFormatter={(value) => `$${value}`} />
                    <Tooltip
                      formatter={(value: number, name: string) => {
                        return [
                          formatCurrency(value),
                          name === "Gross Sales" ? "Gross Sales" : "Net Sales",
                        ];
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="grossSales"
                      name="Gross Sales"
                      stroke="#6366f1"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="netSales"
                      name="Net Sales"
                      stroke="#22c55e"
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Sales Data</CardTitle>
            <CardDescription>
              Detailed breakdown of monthly sales
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <TableSkeleton />
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Gross Sales</TableHead>
                    <TableHead>Discounts</TableHead>
                    <TableHead>Net Sales</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data?.data?.map((month: any) => (
                    <TableRow key={month.label}>
                      <TableCell>{formatMonth(month.label)}</TableCell>
                      <TableCell>{month.orders}</TableCell>
                      <TableCell>{formatCurrency(month.gross_sales)}</TableCell>
                      <TableCell>
                        {formatCurrency(month.item_discounts)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(month.net_sales)}
                      </TableCell>
                    </TableRow>
                  ))}

                  {/* Summary row */}
                  {data?.summary && (
                    <TableRow className="bg-gray-50 font-bold">
                      <TableCell>Summary</TableCell>
                      <TableCell>{data.summary.orders}</TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.gross_sales)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.item_discounts)}
                      </TableCell>
                      <TableCell>
                        {formatCurrency(data.summary.net_sales)}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
