import { SidebarProvider } from "@/components/ui/sidebar";
// Import commented out but component preserved
// import StoreSidebar from "./sidebar";
import { Footer } from "@/components/sections/footer";
import { Navigation } from "@/components/sections/navigation";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  useFooterQuery,
  useNavigationQuery,
} from "@/queries/component-queries";
import { BarChart3, BookUser, Home, Key, UserIcon } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useRouter } from "next/router";

interface StoreLayoutProps {
  children: React.ReactNode;
}

const sidebarLinks = {
  store: [
    { name: "Dashboard", href: "/store/dashboard", icon: Home },
    { name: "My Orders", href: "/store/orders", icon: BarChart3 },
    { name: "My Account", href: "/store/account", icon: UserIcon },
  ],
  account: [
    { name: "Dashboard", href: "/store/dashboard", icon: Home },
    { name: "My Orders", href: "/store/orders", icon: BarChart3 },
    { name: "Account Information", href: "/store/account", icon: UserIcon },
    {
      name: "Billing Addresses",
      href: "/store/account?tab=billing-information",
      icon: BookUser,
    },
    {
      name: "Shipping Addresses",
      href: "/store/account?tab=shipping-information",
      icon: BookUser,
    },
    {
      name: "Change Password",
      href: "/store/account?tab=change-password",
      icon: Key,
    },
  ],
};

export default function StoreLayout({ children }: Readonly<StoreLayoutProps>) {
  const router = useRouter();
  const query = router.query;
  const currentTab = query.tab?.toString() || undefined;

  const pathname = usePathname();
  const links = ["/store/dashboard", "/store/orders"].includes(pathname ?? "")
    ? sidebarLinks.store
    : pathname?.startsWith("/store/account")
    ? sidebarLinks.account
    : [];

  const navigationData = useNavigationQuery();
  const footerData = useFooterQuery();

  return (
    <SidebarProvider>
      <main className="w-full h-full">
        {navigationData.isLoading ? (
          <div className="sticky top-0 left-0 w-full h-full z-50 shadow-lg">
            <Skeleton className="w-full h-24 bg-zinc-50" />
          </div>
        ) : (
          <div className="sticky top-0 left-0 w-full h-full z-50">
            <Navigation data={navigationData.data?.data} />
          </div>
        )}

        <div className="w-full max-w-[1440px] mx-auto px-4 py-24">
          {links.length > 0 ? (
            /* Render the grid layout */
            <div className="grid grid-cols-[254px,1fr] gap-12">
              <div className="relative w-full h-full pt-8">
                <h1 className="text-3xl font-bold text-primary">Dashboard</h1>
                <Separator className="my-4" />
                <div className="w-full h-full grid grid-cols-[254px,1fr] gap-12 pt-4">
                  <div className="w-fit h-full flex flex-col items-start justify-start gap-2 bg-transparent min-w-[254px]">
                    {links.map((link, index) => {
                      const isActive = currentTab
                        ? link.href === `${pathname}?tab=${currentTab}`
                        : link.href === pathname;

                      return (
                        <div
                          key={`store-layout-sidebar-link-${link.name}-${index}`}
                          data-active={isActive}
                          className="w-full font-medium h-fit bg-transparent data-[active=true]:bg-zinc-100 p-2 rounded-sm data-[active=true]:text-primary text-primary text-md justify-start"
                        >
                          <Link
                            href={link.href}
                            target="_self"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1"
                          >
                            <link.icon className="w-5 h-5 mr-2" />
                            {link.name}
                          </Link>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
              {children}
            </div>
          ) : (
            <>{children}</>
          )}
        </div>

        {footerData.data && (
          <div className="w-full h-full">
            <Footer data={footerData.data?.data} />
          </div>
        )}
      </main>
    </SidebarProvider>
  );
}
