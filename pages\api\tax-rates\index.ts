import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NevadaTaxRate } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";

export default matchRoute({
  GET: getTaxRatesHandler,
});

export interface GetTaxRatesResponse {
  error?: string;
  taxRates?: NevadaTaxRate[];
  total?: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

async function getTaxRatesHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetTaxRatesResponse>,
) {
  const supabaseAdminClient = createSupabaseAdminClient();

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const offset = (page - 1) * limit;

  // Get total count
  const { count, error: countError } = await supabaseAdminClient
    .schema("public")
    .from("nevada_tax_rates")
    .select("*", { count: "exact", head: true });

  if (countError) {
    return res.status(500).json({ error: countError.message });
  }

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("nevada_tax_rates")
    .select("*")
    .order("city", { ascending: true })
    .range(offset, offset + limit - 1);

  if (error) {
    return res.status(500).json({ error: error.message });
  }

  const totalPages = Math.ceil((count || 0) / limit);

  return res.status(200).json({
    taxRates: data as NevadaTaxRate[],
    total: count || 0,
    page,
    limit,
    totalPages,
  });
}