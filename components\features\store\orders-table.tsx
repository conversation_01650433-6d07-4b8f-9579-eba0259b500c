import { SvgSpinners90Ring } from "@/components/common/icons";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DataTable,
  DataTableFilter,
  DataTableSkeleton,
  DataTableToolbar,
} from "@/components/ui/data-table";
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import {
  CancellationRequestDTO,
  cancellationRequestSchema,
} from "@/pages/api/customers/[id]/orders/cancellation-request";
import {
  OrderItemOption,
  useCancelOrderMutation,
  useGetOrdersByUserIdQuery,
  useSubmitCancellationRequestMutation,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore from "@/stores/cart-store";
import { Discount, Order, OrderStatus, Product } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ColumnDef,
  ColumnFiltersState,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Row,
  SortingState,
  Table,
  useReactTable,
} from "@tanstack/react-table";
import {
  Copy,
  ExternalLink,
  MoreHorizontal,
  ShoppingCart,
  SquareArrowOutUpRight,
  Trash,
} from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import DownloadAttachmentButton from "./download-attachment-button";
import { OrderStatusType } from "@/pages/api/orders/[id]/status";
import { formatId } from "@/lib/utils/order-helpers";

export interface OrderDataTable
  extends Pick<
    Order,
    "id" | "created_at" | "purchase_order" | "po_attachment"
  > {
  items: OrderItemData[];
  status: string;
  amount: number;
  tracking_link: string | null;
  tax_exempt: boolean | null;
}

export interface OrderItemData {
  products: Product;
  quantity: number;
  options: OrderItemOption[];
  item_price: number;
}

export interface OrderDataWithStatusAndItems extends Order {
  order_statuses: OrderStatus[];
  order_items: OrderItemData[];
  discounts: Discount;
}

const columns: ColumnDef<OrderDataTable>[] = [
  {
    id: "select",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Select" />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: "ID",
    cell: ({ row }) => {
      const id = row.getValue("id") as string;
      const shortId = formatId(id);

      return <div>{shortId}</div>;
    },
  },
  {
    accessorKey: "totalItems",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Items" />;
    },
    cell: ({ row }) => {
      const order = row.original;
      const totalItems =
        order.items?.reduce((acc, item) => acc + item.quantity, 0) ?? 0;
      return <div>{totalItems}</div>;
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Status" />;
    },
    cell: ({ row }) => {
      const status: OrderStatusType = row.getValue("status");
      return <OrderStatusBadge status={status} />;
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Amount" />;
    },
    cell: ({ row }) => {
      const amount = (row.getValue("amount") as number) ?? 0;
      return <div>{formatPrice(amount)}</div>;
    },
  },
  {
    accessorKey: "purchase_order",
    header: "Purchase Order",
    cell: ({ row }) => {
      const po = row.getValue("purchase_order") as string;
      return <div>{po ? po : "N/A"}</div>;
    },
  },
  {
    accessorKey: "po_attachment",
    header: "PO Attachment",
    cell: ({ row }) => {
      const attachment = row.getValue("po_attachment");

      return (
        <>
          {attachment ? (
            <DownloadAttachmentButton filePath={(attachment as string) ?? ""} />
          ) : (
            <div>N/A</div>
          )}
        </>
      );
    },
  },
  {
    accessorKey: "tax_exempt",
    header: "Tax Exempt",
    cell: ({ row }) => {
      const taxExempt = row.getValue("tax_exempt") as boolean | null;
      return <div>{taxExempt === true ? "Yes" : "No"}</div>;
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Ordered At" />;
    },
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"));
      const formatDate = Intl.DateTimeFormat("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
        hour12: true,
      }).format(date);

      return <div>{formatDate}</div>;
    },
  },
  {
    id: "actions",
    cell: OrderTableActions,
    header: "Actions",
    meta: {
      className: "sticky right-0 bg-gray-100 dark:bg-zinc-950",
    },
  },
];

export function OrdersTable() {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const { data, isLoading, isError } = useGetOrdersByUserIdQuery(
    userId,
    page,
    pageSize
  );

  const orders = data?.orders || [];
  const totalItems = data?.total || 0;
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  const tableData: OrderDataTable[] = useMemo(
    function mapTableData() {
      if (!orders || orders.length === 0) return [];

      return orders.map((orderData: OrderDataWithStatusAndItems) => {
        const totalAmount = orderData.total_amount ?? 0;
        const orderStatus = orderData.order_statuses?.[0]?.status ?? "pending";

        return {
          id: orderData.id,
          created_at: orderData.created_at,
          billing_address_id: orderData.billing_address_id,
          shipping_address_id: orderData.shipping_address_id,
          items: orderData.order_items ?? [],
          status: orderStatus,
          amount: totalAmount,
          purchase_order: orderData.purchase_order,
          po_attachment: orderData.po_attachment,
          tracking_link: orderData.tracking_link,
          tax_exempt: orderData.tax_exempt,
        };
      });
    },
    [orders]
  );

  // Create a table instance with server-side pagination
  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const table = useReactTable({
    data: tableData,
    columns,
    pageCount: totalPages,
    manualPagination: true, // Tell the table we're handling pagination server-side
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (updater) => {
      if (typeof updater === "function") {
        const newPagination = updater({
          pageIndex: page - 1,
          pageSize,
        });
        setPage(newPagination.pageIndex + 1);
        setPageSize(newPagination.pageSize);
      } else {
        setPage(updater.pageIndex + 1);
        setPageSize(updater.pageSize);
      }
    },
    state: {
      sorting,
      rowSelection,
      columnFilters,
      pagination: {
        pageIndex: page - 1, // convert to 0-indexed for the table
        pageSize,
      },
    },
  });

  return (
    <div className="space-y-4">
      {isLoading ? (
        <DataTableSkeleton />
      ) : (
        <DataTable
          data={tableData}
          columns={columns}
          table={table}
          renderToolbar={(table) => (
            <DataTableToolbar table={table}>
              <DataTableFilter
                table={table as Table<OrderDataTable>}
                column="id"
                placeholder="Search orders..."
                className="max-w-lg"
              />
            </DataTableToolbar>
          )}
          className="border-none [&_thead_th]:bg-muted/50 [&_thead_tr]:border-b-2 [&_tr:hover]:bg-muted/50 [&_tbody_tr]:border-b [&_tbody_tr]:transition-colors"
        />
      )}
    </div>
  );
}

interface OrderDataTableRowProps {
  row: Row<OrderDataTable>;
}

export function OrderStatusBadge({ status }: { status: string }) {
  return (
    <Badge
      className="uppercase font-semibold tracking-wider"
      variant={
        status === "pending" ||
        status === "processing" ||
        status === "awaiting_payment" ||
        status === "expired"
          ? "pending"
          : status === "completed" ||
            status === "shipped" ||
            status === "delivered" ||
            status === "processed" ||
            status === "completed" ||
            status === "processed"
          ? "success"
          : status === "canceled" ||
            status === "denied" ||
            status === "canceled_reversal" ||
            status === "failed" ||
            status === "refunded" ||
            status === "reversed" ||
            status === "chargeback" ||
            status === "voided"
          ? "destructive"
          : "default"
      }
    >
      {status}
    </Badge>
  );
}

function OrderTableActions({ row }: OrderDataTableRowProps) {
  const order = row.original;
  const orderItems = order.items;
  const [open, setOpen] = useState(false);

  const copyId = () => {
    navigator.clipboard.writeText(order.id);
    toast({
      title: "Copied to clipboard",
      description: "Order ID copied to clipboard",
    });
    setOpen(false);
  };

  const addItem = useCartStore((state) => state.addItem);
  const orderStatus: OrderStatus["status"] =
    (order.status as OrderStatus["status"]) ?? "pending";
  const isPending = orderStatus === "pending";
  const isCanceled = orderStatus === "canceled";

  // Check if the order has tracking information
  const hasTracking = order.tracking_link;

  const reorderHandler = () => {
    if (!orderItems) return;

    orderItems.forEach((item) => {
      // Use item_price which already includes group pricing and options
      const itemPrice = item.item_price ?? item.products.price;

      addItem({
        id: item.products.id,
        name: item.products.name,
        price: itemPrice,
        quantity: item.quantity,
        image: item.products.image ?? "",
        sku: item.products.sku ?? "",
        selected: false,
        discount: 0,
        selectedOptions:
          item.options?.map((option) => ({
            name: option.name,
            value: option.value,
            price: option.price,
          })) || [],
      });
    });

    toast({
      title: "Items added to cart",
      description: "Items from this order have been added to your cart.",
      variant: "success",
      duration: 3000,
    });

    setOpen(false);
  };

  const handleSuccess = () => {
    setOpen(false);
  };

  return (
    <div className="flex items-center justify-center px-2">
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            asChild
            onSelect={(e) => {
              e.preventDefault();
              setOpen(false);
            }}
          >
            <Link
              href={`/store/orders/${order.id}`}
              className="flex items-center gap-2 w-full"
            >
              <SquareArrowOutUpRight />
              View
            </Link>
          </DropdownMenuItem>
          {hasTracking && (
            <DropdownMenuItem
              asChild
              onSelect={(e) => {
                e.preventDefault();
                setOpen(false);
              }}
            >
              <Link
                href={order.tracking_link || "#"}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 w-full"
              >
                <ExternalLink className="h-4 w-4" />
                Track Order
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={reorderHandler}
            disabled={false}
            className="flex items-center gap-2"
          >
            <ShoppingCart />
            {false ? "Re-ordering..." : "Re-order"}
          </DropdownMenuItem>
          {isPending ? (
            <CancelOrderDialog orderId={order.id} onSuccess={handleSuccess} />
          ) : !isPending && !isCanceled ? (
            <ContactAdminDialog
              orderStatus={orderStatus}
              orderId={order.id}
              onSuccess={handleSuccess}
            />
          ) : null}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

interface CancelOrderDialogProps {
  orderId: string;
  onSuccess?: () => void;
}

function CancelOrderDialog({ orderId, onSuccess }: CancelOrderDialogProps) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const { mutate: cancelOrder, isPending: isCancelling } =
    useCancelOrderMutation(userId);
  const [open, setOpen] = useState(false);

  const handleCancel = () => {
    cancelOrder(orderId, {
      onSuccess: () => {
        setOpen(false);
        toast({
          title: "Order Cancelled",
          description: "Your order has been successfully cancelled.",
          variant: "success",
        });
        onSuccess?.();
      },
      onError: (error) => {
        toast({
          title: "Error Cancelling Order",
          description: error.message,
          variant: "destructive",
        });
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DropdownMenuItem
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setOpen(true);
        }}
        className="flex items-center justify-between text-red-500 cursor-pointer"
      >
        <DialogTrigger className="w-full h-fit">
          <div className="flex items-center gap-2">
            <Trash />
            Cancel
          </div>
        </DialogTrigger>
      </DropdownMenuItem>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Are you sure?</DialogTitle>
          <DialogDescription>This will cancel the order.</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="secondary" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            disabled={isCancelling}
            onClick={handleCancel}
          >
            {isCancelling ? (
              <div className="flex items-center gap-2">
                <SvgSpinners90Ring className="animate-spin" />
                <span>Cancelling...</span>
              </div>
            ) : (
              "Confirm"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface ContactAdminDialogProps {
  orderStatus: OrderStatus["status"];
  orderId: string;
  onSuccess?: () => void;
}

function ContactAdminDialog({
  orderStatus,
  orderId,
  onSuccess,
}: ContactAdminDialogProps) {
  const userData = useAuthStore((state) => state.data);
  const userId = userData.id;
  const { mutate: submitCancellationRequest, isPending: isSubmitting } =
    useSubmitCancellationRequestMutation(userId);
  const [open, setOpen] = useState(false);

  const form = useForm<CancellationRequestDTO>({
    resolver: zodResolver(cancellationRequestSchema),
    defaultValues: {
      orderId: orderId,
      comment: "",
    },
  });

  function onSubmit(data: CancellationRequestDTO) {
    submitCancellationRequest(data, {
      onSuccess: () => {
        setOpen(false);
        form.reset();
        toast({
          title: "Cancellation request submitted",
          description:
            "Your cancellation request has been sent to our team for review.",
          variant: "success",
        });
        onSuccess?.();
      },
      onError: (error) => {
        toast({
          title: "Error submitting cancellation request",
          description: error.message,
          variant: "destructive",
        });
      },
    });
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DropdownMenuItem
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          setOpen(true);
        }}
        className="flex items-center justify-between text-red-500 cursor-pointer"
      >
        <div className="flex items-center justify-between w-full">
          Cancel
          <Trash />
        </div>
      </DropdownMenuItem>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Unable to Cancel Order</DialogTitle>
          <DialogDescription>
            Orders with status "{orderStatus}" cannot be canceled through the
            system. Please contact us at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-primary hover:underline"
            >
              <EMAIL>
            </a>{" "}
            or call us at{" "}
            <a href="tel:+17757821700" className="text-primary hover:underline">
              ************
            </a>{" "}
            if you need to cancel this order.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            id="cancellation-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-4 py-4"
          >
            <FormField
              control={form.control}
              name="comment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Add your comment below for this cancellation:
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please explain why you want to cancel this order..."
                      className="min-h-24"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <DialogFooter>
          <Button
            variant="secondary"
            onClick={() => setOpen(false)}
            disabled={isSubmitting}
          >
            Close
          </Button>
          <Button
            type="submit"
            form="cancellation-form"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <SvgSpinners90Ring className="animate-spin" />
                <span>Submitting...</span>
              </div>
            ) : (
              "Submit Request"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
