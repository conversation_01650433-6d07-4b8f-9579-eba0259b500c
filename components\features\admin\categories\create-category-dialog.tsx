import { <PERSON><PERSON>, DialogContent, DialogDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alog<PERSON>eader, <PERSON>alog<PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog-shadcn";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { useCreateCategoryMutation, useGetAllCategoriesQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const createCategorySchema = z.object({
    name: z.string().min(1, "Name is required"),
    value: z.string().min(1, "Value is required"),
    parent_category_id: z.string().optional(),
});

type CreateCategoryFormValues = z.infer<typeof createCategorySchema>;

export function CreateCategoryDialog() {
    const [open, setOpen] = useState(false);
    const token = useAuthStore((state) => state.token);
    const { mutate: createCategory, isPending } = useCreateCategoryMutation(token);
    const { toast } = useToast();
    const { data: categoriesData } = useGetAllCategoriesQuery(1, 100, token);

    const form = useForm<CreateCategoryFormValues>({
        resolver: zodResolver(createCategorySchema),
        defaultValues: {
            name: "",
            value: "",
            parent_category_id: "",
        },
    });

    const onSubmit = (data: CreateCategoryFormValues) => {
        const formData = {
            ...data,
            parent_category_id: data.parent_category_id === "null"
                || data.parent_category_id === ""
                ? undefined : data.parent_category_id
        };

        createCategory(formData, {
            onSuccess: () => {
                toast({
                    title: "Success",
                    description: "Category created successfully",
                });
                setOpen(false);
                form.reset();
            },
            onError: (error) => {
                toast({
                    title: "Error",
                    description: error.message || "Failed to create category",
                    variant: "destructive",
                });
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="default">Create Category</Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Create New Category</DialogTitle>
                    <DialogDescription>
                        Add a new category to your product catalog.
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter category name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="value"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Value</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter category value" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="parent_category_id"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Parent Category (Optional)</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a parent category" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="null">None</SelectItem>
                                            {categoriesData?.categories?.map((category) => (
                                                <SelectItem key={category.id} value={category.id}>
                                                    {category.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="submit" disabled={isPending} variant="default">
                                {isPending ? "Creating..." : "Create Category"}
                                {isPending && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 