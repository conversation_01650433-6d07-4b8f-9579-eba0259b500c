# API Documentation

## Table of Contents
- [Authentication Endpoints](#authentication-endpoints)
  - [POST /api/auth/refresh](#post-apiauthrefresh)
  - [POST /api/auth/sign-in](#post-apiauthsign-in)
  - [POST /api/auth/callback](#post-apiauthcallback)
  - [POST /api/auth/forgot-password](#post-apiauthforgot-password)
  - [GET /api/users/:userId](#get-apiusersuserid)
- [Product Endpoints](#product-endpoints)
  - [GET /api/products](#get-apiproducts)
  - [GET /api/products/:id](#get-apiproductsid)
  - [POST /api/products](#post-apiproducts)
  - [GET /api/customers/:userId/products](#get-apicustomersuseridproducts)
  - [POST /api/products/:slug/categories](#post-apiproductsslugcategories)
  - [DELETE /api/products/:slug/categories/:category_id](#delete-apiproductsslugcategoriescategory_id)
- [Category Endpoints](#category-endpoints)
  - [GET /api/categories](#get-apicategories)
  - [POST /api/categories](#post-apicategories)
  - [PATCH /api/categories/:id](#patch-apicategoriesid)
  - [DELETE /api/categories/:id](#delete-apicategoriesid)
- [Customer Endpoints](#customer-endpoints)
  - [GET /api/customers](#get-apicustomers)
  - [GET /api/customers/:id](#get-apicustomersid)
  - [GET /api/customers/:customerId/categories](#get-apicustomerscustomeridcategories)
  - [POST /api/customers/:customerId/categories](#post-apicustomerscustomeridcategories)
  - [DELETE /api/customers/:customerId/categories/:categoryId](#delete-apicustomerscustomeridcategoriescategoryid)
  - [Billing Address Endpoints](#billing-address-endpoints)
  - [Shipping Address Endpoints](#shipping-address-endpoints)
  - [Order Endpoints](#order-endpoints)
  - [Dashboard Endpoint](#dashboard-endpoint)
- [Admin Endpoints](#admin-endpoints)
  - [User Management](#user-management)
  - [Order Management](#order-management)
  - [Billing Address Management](#billing-address-management)
  - [Group Management](#group-management)
  - [Discount Management](#discount-management)
  - [Dashboard and Reports](#dashboard-and-reports)

## Authentication Endpoints

### POST /api/auth/refresh
Refreshes the user's session token.

**Response**
```typescript
{
  access_token: string;
  access_token_expires_in: number;
  access_token_expires_at: number;
  user: User;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 403: Session expired
- 500: Server error

### POST /api/auth/sign-in
Authenticates a user.

**Request Body**
```typescript
{
  email: string;
  password: string;
}
```

**Response**
```typescript
{
  access_token: string;
  access_token_expires_in: number;
  access_token_expires_at: number;
  user: User;
  role: UserRole;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid credentials
- 401: Unauthorized
- 500: Server error

### POST /api/auth/callback
Handles sign-up callback after email verification.

**Request Body**
```typescript
{
  token: string;
  email: string;
  type: string;
}
```

**Response**
```typescript
{
  access_token: string;
  access_token_expires_in: number;
  access_token_expires_at: number;
  user: User;
  role: UserRole;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid token
- 500: Server error

### POST /api/auth/forgot-password
Initiates the password reset process.

**Request Body**
```typescript
{
  email: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid email
- 404: Email not found
- 500: Server error

### GET /api/users/:userId
Fetches user data by ID.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  data: {
    phone?: string;
    // other user fields
  }
}
```

**Status Codes**
- 200: Success
- 404: User not found
- 401: Unauthorized
- 500: Server error

## Product Endpoints

### GET /api/products
Fetches paginated public products.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)
- category?: string

**Response**
```typescript
{
  products: Product[];
  categories: Category[];
  total: number;
  totalPages: number;
}
```

**Status Codes**
- 200: Success
- 500: Server error

### GET /api/products/:id
Fetches a single product by ID.

**Response**
```typescript
{
  product: Product;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 404: Product not found
- 500: Server error

### POST /api/products
Creates a new product.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  name: string;
  description: string;
  price: number;
  sku: string;
  stock?: number;
  options?: ProductOption[];
  images?: string[];
  // Other product fields
}
```

**Response**
```typescript
{
  product: Product;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

### GET /api/customers/:userId/products
Fetches paginated products for authenticated users.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)
- category?: string

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  products: Product[];
  categories: Category[];
  total: number;
  totalPages: number;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden
- 500: Server error

### POST /api/products/:slug/categories
Assigns categories to a product.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  category_ids: string[];
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Product not found
- 500: Server error

### DELETE /api/products/:slug/categories/:category_id
Removes a category from a product.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Product or category not found
- 500: Server error

## Category Endpoints

### GET /api/categories
Fetches all categories.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Response**
```typescript
{
  categories: Category[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 500: Server error

### POST /api/categories
Creates a new category.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  name: string;
  value: string;
  description?: string;
  parent_category_id?: string;
}
```

**Response**
```typescript
{
  category: Category;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

### PATCH /api/categories/:id
Updates an existing category.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  name?: string;
  value?: string;
  description?: string;
  parent_category_id?: string;
}
```

**Response**
```typescript
{
  category: Category;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Category not found
- 500: Server error

### DELETE /api/categories/:id
Deletes a category.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Category not found
- 500: Server error

## Customer Endpoints

### GET /api/customers
Fetches all customers (admin only).

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  customers: Customer[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

### GET /api/customers/:id
Fetches customer details.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  data: Customer;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

### GET /api/customers/:customerId/categories
Fetches categories assigned to a customer.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  customer_categories: CustomerCategory[];
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

### POST /api/customers/:customerId/categories
Assigns categories to a customer.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  category_ids: string[];
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Customer not found
- 500: Server error

### DELETE /api/customers/:customerId/categories/:categoryId
Removes a category from a customer.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Customer or category not found
- 500: Server error

### Billing Address Endpoints

#### GET /api/customers/:id/billing-address
Fetches customer's billing addresses.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  billing_address: BillingAddress[];
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

#### POST /api/customers/:id/billing-address
Adds a new billing address.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default?: boolean;
}
```

**Response**
```typescript
{
  billing_address: BillingAddress;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

#### PATCH /api/customers/:id/billing-address/:billing_id
Updates a billing address.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  is_default?: boolean;
}
```

**Response**
```typescript
{
  billing_address: BillingAddress;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Address not found
- 500: Server error

#### DELETE /api/customers/:id/billing-address/:billing_id
Deletes a billing address.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Address not found
- 500: Server error

### Shipping Address Endpoints

#### GET /api/customers/:id/shipping-address
Fetches customer's shipping addresses.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  shipping_address: ShippingAddress[];
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

#### POST /api/customers/:id/shipping-address
Adds a new shipping address.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default?: boolean;
}
```

**Response**
```typescript
{
  shipping_address: ShippingAddress;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

#### PATCH /api/customers/:id/shipping-address/:shipping_id
Updates a shipping address.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  is_default?: boolean;
}
```

**Response**
```typescript
{
  shipping_address: ShippingAddress;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Address not found
- 500: Server error

#### DELETE /api/customers/:id/shipping-address/:shipping_id
Deletes a shipping address.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Address not found
- 500: Server error

### Order Endpoints

#### GET /api/customers/:id/orders
Fetches customer's orders.

**Query Parameters**
- page: number
- limit: number

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  orders: OrderDataWithStatusAndItems[];
  total: number;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Customer not found
- 500: Server error

#### GET /api/customers/:id/orders/:order_id
Fetches a specific order.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  order: OrderWithItems;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Order not found
- 500: Server error

#### POST /api/orders
Creates a new order.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  customer_id: string;
  items: {
    product_id: string;
    quantity: number;
    price: number;
    selected_options?: {
      name: string;
      value: string;
      price?: number;
    }[];
  }[];
  shipping_address_id: string;
  billing_address_id: string;
  payment_method: string;
  shipping_method: string;
  status?: string;
}
```

**Response**
```typescript
{
  order: Order;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 404: Product or address not found
- 500: Server error

#### POST /api/customers/:id/orders/cancellation-request
Submits an order cancellation request.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  order_id: string;
  reason: string;
  additional_notes?: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (not owner)
- 404: Order not found
- 500: Server error

#### POST /api/customers/:id/orders/:order_id/cancel
Cancels an order.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Order cannot be cancelled
- 401: Unauthorized
- 403: Forbidden (not owner or admin)
- 404: Order not found
- 500: Server error

### Dashboard Endpoint

#### GET /api/customers/dashboard
Fetches customer dashboard data.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  data: DashboardData;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden
- 500: Server error

## Admin Endpoints

### User Management

#### GET /api/users
Fetches all users (admin only).

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  users: User[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### GET /api/users/pending
Fetches users with pending status.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  users: User[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### POST /api/users/:slug/status
Updates a user's status.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  status: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid status
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: User not found
- 500: Server error

### Order Management

#### GET /api/orders
Fetches all orders (admin only).

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  orders: Order[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### PUT /api/orders/:id/status
Updates an order's status.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  status: OrderStatus;
  tracking_number?: string;
  tracking_url?: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  order: Order;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid status
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Order not found
- 500: Server error

#### GET /api/orders/cancellation-requests
Fetches order cancellation requests.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  requests: CancellationRequest[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### PATCH /api/orders/cancellation-requests
Updates a cancellation request.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  request_id: string;
  status: 'approved' | 'denied';
  notes?: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Request not found
- 500: Server error

### Billing Address Management

#### GET /api/billing-addresses
Fetches all billing addresses (admin only).

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  billing_addresses: BillingAddress[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### GET /api/billing-addresses/pending
Fetches pending billing addresses.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  billing_addresses: BillingAddress[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### POST /api/billing-addresses/pending
Approves a pending billing address.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  address_id: string;
  status: 'approved' | 'denied';
  notes?: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Address not found
- 500: Server error

### Group Management

#### GET /api/groups
Fetches all groups.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  groups: Group[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### POST /api/groups
Creates a new group.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  name: string;
  description?: string;
}
```

**Response**
```typescript
{
  group: Group;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### GET /api/customer-groups
Fetches all customer groups.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  customer_groups: CustomerGroup[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### POST /api/customer-groups
Assigns a group to a customer.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  customer_id: string;
  group_id: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Customer or group not found
- 500: Server error

#### DELETE /api/customer-groups
Removes a group from a customer.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  customer_id: string;
  group_id: string;
}
```

**Response**
```typescript
{
  success: boolean;
  message: string;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 404: Customer-group relationship not found
- 500: Server error

### Discount Management

#### GET /api/discounts
Fetches all discounts.

**Query Parameters**
- page: number (default: 1)
- limit: number (default: 10)

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  discounts: Discount[];
  total: number;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### POST /api/discounts
Creates a new discount.

**Headers**
- Authorization: Bearer token

**Request Body**
```typescript
{
  code: string;
  description?: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  start_date: string;
  end_date: string;
  min_purchase_amount?: number;
  max_discount_amount?: number;
  usage_limit?: number;
  product_ids?: string[];
  category_ids?: string[];
  customer_ids?: string[];
  group_ids?: string[];
}
```

**Response**
```typescript
{
  discount: Discount;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid request
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

### Dashboard and Reports

#### GET /api/admin/dashboard
Fetches admin dashboard data.

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  data: AdminDashboardData;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error

#### GET /api/admin/reports/sales
Fetches sales reports.

**Query Parameters**
- start_date: string
- end_date: string

**Headers**
- Authorization: Bearer token

**Response**
```typescript
{
  data: SalesReportData;
  error?: string;
}
```

**Status Codes**
- 200: Success
- 400: Invalid date range
- 401: Unauthorized
- 403: Forbidden (non-admin)
- 500: Server error