import { PortableText, PortableTextComponents } from "@portabletext/react";
import Button from "components/ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { FormField } from "@stackshift-ui/form-field";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { Input } from "@stackshift-ui/input";
import React from "react";
import { ContactProps } from ".";
import { thankYouPageLink } from "../../../helper";
import { urlFor } from "lib/sanity";
import { ArrayOfTitleAndDescription, MyPortableTextComponents } from "types";
import { Form as iForm } from "../../../types";
import { Dialog } from "components/ui/dialog";
import { FaArrowRight, FaArrowLeft } from "react-icons/fa6";
import { CiSearch } from "react-icons/ci";
import ValveCalculator from "../../calculator/sideCalculator";
import { useRouter } from "next/router";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-4 leading-relaxed text-gray-900 text-4xl md:text-5xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return (
        <h2 className="mb-3 text-xl md:text-2xl text-primary">{children}</h2>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-3 text-lg md:text-xl text-gray-800">{children}</h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-0 text-base md:text-lg leading-relaxed text-gray-900">
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-2 font-body text-base leading-relaxed text-gray-800 w-full">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-4 italic leading-relaxed text-gray-500 px-8">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-8 mb-3 text-base leading-relaxed text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="ml-5 leading-relaxed text-base text-gray-900 list-decimal [&>li:last-child]:mb-4">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-2 leading-relaxed text-gray-900 text-base">
        {children}
      </li>
    ),
  },
  marks: {
    strong: ({ children }) => (
      <strong className="text-primary">{children}</strong>
    ),
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => {
      return (
        <div>
          <Image
            className="w-[200px] h-full mb-10"
            width={300}
            height={300}
            src={urlFor(value?.image)}
            alt={value?.image?.alt ?? value?.image?.image?.asset?._ref}
          />
        </div>
      );
    },
  },
};

export function Contact_G({
  contactDescription,
  form,
  block,
  title,
  arrayOfTitleAndDescription,
}: ContactProps) {
  const [isFormOpen, setIsFormOpen] = React.useState(false);

  return (
    <Section className="pt-16 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex direction="col" justify="between" className="lg:flex-row">
          <div className="relative w-full lg:w-[70%] lg:pr-8 lg:border-r border-gray-300">
            <Flex direction="col" align="center" className="w-full">
              <div className="max-w-4xl w-full">
                <div className="text-center mb-8">
                  {title && <Heading type="h2">{title}</Heading>}
                  {contactDescription && (
                    <Text muted className="mt-5 leading-loose">
                      {contactDescription}
                    </Text>
                  )}
                </div>

                <div className="w-full flex justify-center mb-8">
                  <Button
                    as="button"
                    ariaLabel={`${form?.name ?? ""}`}
                    variant="solid"
                    onClick={() => setIsFormOpen(true)}
                    // className="px-6 py-3 font-semibold text-white rounded-global bg-primary hover:bg-primary-foreground"
                  >
                    {form?.name}
                  </Button>
                </div>

                <TitleAndDescriptionDropdown
                  arrayOfTitleAndDescription={arrayOfTitleAndDescription}
                />
              </div>
            </Flex>
          </div>

          <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
            <ValveCalculator />
          </div>
        </Flex>
      </Container>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <Dialog.Content className="w-full bg-background">
          <div className="flex justify-end">
            <Dialog.Close onClick={() => setIsFormOpen(false)} />
          </div>
          <div className="max-h-[80vh] overflow-y-auto">
            <FormFields
              form={form}
              block={block}
              blockCustomization={blockCustomization}
            />
          </div>
        </Dialog.Content>
      </Dialog>
    </Section>
  );
}

export default function Contact_G2({
  contactDescription,
  form,
  block,
  title,
  arrayOfTitleAndDescription,
  hasCalculator,
}: ContactProps) {
  const router = useRouter();

  const sectionRef = React.useRef<HTMLDivElement>(null);
  const [isFormOpen, setIsFormOpen] = React.useState(false);
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [selectedTitleIndex, setSelectedTitleIndex] = React.useState<number>(0);
  const [touchStart, setTouchStart] = React.useState<number | null>(null);
  const [touchEnd, setTouchEnd] = React.useState<number | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [activeCategory, setActiveCategory] = React.useState("all");

  // Determine if we should show the calculator
  // If hasCalculator is true, show it, otherwise (false or undefined) don't show it
  const showCalculator = hasCalculator === true;

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  // Filter items based on search query
  const getFilteredItems = () => {
    let items = arrayOfTitleAndDescription || [];

    if (searchQuery) {
      items = items.filter(
        (item) => item?.title?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return items;
  };

  const filteredItems = getFilteredItems();
  const totalItems = filteredItems?.length || 0;

  const handleNext = () => {
    if (totalItems > 0) {
      const nextIndex = (currentIndex + 1) % totalItems;
      setCurrentIndex(nextIndex);
      setSelectedTitleIndex(nextIndex);
    }
  };

  const handlePrevious = () => {
    if (totalItems > 0) {
      const prevIndex = (currentIndex - 1 + totalItems) % totalItems;
      setCurrentIndex(prevIndex);
      setSelectedTitleIndex(prevIndex);
    }
  };

  const handleTitleClick = (index: number) => {
    setSelectedTitleIndex(index);
    setCurrentIndex(index);
    // Scroll to the top of this component
    setTimeout(() => {
      if (sectionRef.current) {
        sectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 0);
  };

  // Handle touch events for swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      handleNext();
    } else if (isRightSwipe) {
      handlePrevious();
    }
  };

  // Create unique categories from items if available
  const categories = React.useMemo(() => {
    const cats = arrayOfTitleAndDescription
      ?.map((item) => item.category)
      .filter(Boolean) as string[];
    return [...new Set(cats)].filter(Boolean);
  }, [arrayOfTitleAndDescription]);

  return (
    <Section className="py-20 bg-background" ref={sectionRef}>
      <Container maxWidth={1280}>
        <Flex direction="col" justify="between" className="lg:flex-row">
          <div
            className={`relative w-full ${
              showCalculator
                ? "lg:w-[70%] lg:pr-8 lg:border-r border-gray-300"
                : "lg:w-full"
            }`}
          >
            <div
              className={`mb-8 text-center ${
                !showCalculator ? "max-w-6xl mx-auto" : ""
              }`}
            >
              {title && (
                <Heading type="h2" className="text-4xl">
                  {title}
                </Heading>
              )}
              {contactDescription && (
                <Text muted className="mt-3">
                  {contactDescription}
                </Text>
              )}
            </div>

            {/* Categories tabs - Similar to FAQs_E */}
            {categories && categories.length > 0 && (
              <div
                className={`mb-8 border-b border-gray-200 ${
                  !showCalculator ? "max-w-6xl mx-auto" : ""
                }`}
              >
                <Flex className="gap-2 justify-start">
                  <Button
                    as="button"
                    variant="unstyled"
                    ariaLabel="All categories"
                    onClick={() => {
                      setActiveCategory("all");
                      setSelectedTitleIndex(0);
                      setCurrentIndex(0);
                    }}
                    className={`px-4 py-2 font-medium ${
                      activeCategory === "all"
                        ? "border-b-2 border-primary bg-primary text-white"
                        : "text-gray-500 hover:text-primary"
                    }`}
                  >
                    All
                  </Button>
                  {categories.map((category, index) => (
                    <Button
                      as="button"
                      key={index}
                      variant="unstyled"
                      ariaLabel={`Category ${category}`}
                      onClick={() => {
                        setActiveCategory(category);
                        setSelectedTitleIndex(0);
                        setCurrentIndex(0);
                      }}
                      className={`px-4 py-2 font-medium ${
                        activeCategory === category
                          ? "border-b-2 border-primary bg-primary text-white"
                          : "text-gray-500 hover:text-primary"
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </Flex>
              </div>
            )}

            {/* Search field and button in a flex container */}
            <div
              className={`flex justify-between items-center mb-8 gap-4 ${
                !showCalculator ? "max-w-6xl mx-auto" : ""
              }`}
            >
              {/* Search field - Similar to FAQs_E but with reduced width */}
              <div className="relative w-full max-w-md">
                <Input
                  type="text"
                  placeholder="Search Tech Tips..."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setSelectedTitleIndex(0);
                    setCurrentIndex(0);
                  }}
                  className="w-full border border-primary/80 !rounded-none pr-10 focus:border-primary focus:!ring-1 focus:!ring-primary focus:!outline-1 focus:!outline-primary"
                />
                <CiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>

              {/* Form button moved inline with search */}
              {form?.name && (
                <Button
                  as="button"
                  variant="maxtonPrimary"
                  ariaLabel={`${form?.name ?? ""}`}
                  className="rounded-none px-6 py-2.5"
                  onClick={() => setIsFormOpen(true)}
                >
                  {form?.name}
                </Button>
              )}
            </div>

            {filteredItems && filteredItems.length === 0 ? (
              <Text className="text-gray-500 text-center">
                No items found matching your search
              </Text>
            ) : (
              <div
                className={`flex flex-col-reverse md:flex-row mt-8 h-full ${
                  !showCalculator ? "max-w-6xl mx-auto" : "max-w-5xl"
                }`}
              >
                {/* Left column - Title list (scrollable side menu like in FAQs) */}
                <div className="hidden md:block md:w-2/5 pr-0 md:pr-10 mb-8 md:mb-0 divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
                  {filteredItems?.map((item, index) => (
                    <div key={index} className="cursor-pointer w-full py-3">
                      <Heading
                        type="h3"
                        className={`text-lg font-semibold border-l-4 pl-3 py-2 transition-all ${
                          index === selectedTitleIndex
                            ? "border-primary !text-primary"
                            : "border-gray-300 !text-black hover:!border-primary/50 hover:!text-primary/70"
                        }`}
                        onClick={() => handleTitleClick(index)}
                      >
                        {item.title}
                      </Heading>
                    </div>
                  ))}
                </div>

                {/* Right column - Selected item content */}
                <div className="md:w-3/5 md:pl-6">
                  {filteredItems?.length > 0 && (
                    <div
                      className="w-full md:w-full transition-all duration-300 opacity-100 p-6 bg-white rounded-lg shadow-sm border border-gray-100"
                      onTouchStart={onTouchStart}
                      onTouchMove={onTouchMove}
                      onTouchEnd={onTouchEnd}
                    >
                      {/* Question counter and navigation */}
                      <div className="flex justify-between items-center mb-4">
                        <p className="text-base font-bold font-mono text-primary">
                          {`${(selectedTitleIndex + 1)
                            .toString()
                            .padStart(2, "0")}/${filteredItems?.length
                            .toString()
                            .padStart(2, "0")}`}
                        </p>

                        {/* Previous/next navigation buttons */}
                        <div className="flex gap-2">
                          <Button
                            as="button"
                            variant="unstyled"
                            ariaLabel="Previous item"
                            onClick={handlePrevious}
                            className="text-primary hover:text-primary/70"
                          >
                            Previous
                          </Button>
                          <Button
                            as="button"
                            variant="unstyled"
                            ariaLabel="Next item"
                            onClick={handleNext}
                            className="text-primary hover:text-primary/70"
                          >
                            Next
                          </Button>
                        </div>
                      </div>

                      {/* Title */}
                      <Heading
                        type="h3"
                        className="text-2xl font-bold mb-4 text-primary"
                      >
                        {filteredItems?.[selectedTitleIndex]?.title}
                      </Heading>

                      {/* Content */}
                      {filteredItems?.[selectedTitleIndex]?.firstColumn && (
                        <div className="prose prose-sm md:prose-base w-full max-w-none">
                          <PortableText
                            value={
                              filteredItems?.[selectedTitleIndex]?.firstColumn
                            }
                            components={textComponentBlockStyling}
                            onMissingComponent={false}
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {showCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
              <ValveCalculator />
            </div>
          )}
        </Flex>
      </Container>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <Dialog.Content className="w-full bg-background rounded-lg">
          <div className="flex justify-end">
            <Dialog.Close onClick={() => setIsFormOpen(false)} />
          </div>
          <div className="max-h-[80vh] overflow-y-auto">
            <FormFields
              form={form}
              block={block}
              blockCustomization={blockCustomization}
            />
          </div>
        </Dialog.Content>
      </Dialog>
    </Section>
  );
}

function ContactTitleAndDescription({
  title,
  contactDescription,
}: {
  title?: string;
  contactDescription?: string;
}) {
  return (
    <div>
      {title && <Heading type="h3">{title}</Heading>}
      {contactDescription && (
        <Text muted className="mt-5 leading-loose">
          {contactDescription}
        </Text>
      )}
    </div>
  );
}

function TitleAndDescriptionDropdown({
  arrayOfTitleAndDescription,
}: {
  arrayOfTitleAndDescription?: ArrayOfTitleAndDescription[];
}) {
  const [openItem, setOpenItem] = React.useState<string | null>(null);

  if (!arrayOfTitleAndDescription?.length) return null;

  const toggleItem = (key: string) => {
    setOpenItem((prev) => (prev === key ? null : key));
  };

  return (
    <div className="space-y-2">
      {arrayOfTitleAndDescription.map((item, index) => (
        <div
          key={item?._key || index}
          className="border rounded-lg overflow-hidden"
        >
          <Button
            as="button"
            variant="unstyled"
            ariaLabel={`Toggle ${item?.title || "item"}`}
            className={`w-full text-left flex justify-between items-center px-[10px] py-3 group ${
              openItem === (item?._key || index.toString())
                ? "bg-primary text-white"
                : "hover:bg-primary hover:text-white text-gray-900"
            }`}
            onClick={() => toggleItem(item?._key || index.toString())}
          >
            {/* <span className="font-semibold">{item?.title}</span> */}
            <Heading
              type="h3"
              className={`text-sm font-semibold ${
                openItem === (item?._key || index.toString())
                  ? " !text-white"
                  : "group-hover:!text-white !text-gray-900"
              }`}
            >
              {item?.title}
            </Heading>
            <svg
              className={`w-4 h-4 transition-transform duration-500 ${
                openItem === (item?._key || index.toString())
                  ? "rotate-180"
                  : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </Button>

          <div
            className={`overflow-hidden transition-all duration-500 ease-in-out ${
              openItem === (item?._key || index.toString())
                ? "max-h-[800px] opacity-100"
                : "max-h-0 opacity-0"
            }`}
          >
            <div className="px-[10px] py-3 bg-white shadow-sm border-t border-gray-100">
              {item?.firstColumn && (
                <div className="prose max-w-none prose-sm">
                  <PortableText
                    value={item.firstColumn}
                    components={textComponentBlockStyling}
                    onMissingComponent={false}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

function SubtitleAndHeadingText({ form }: { form?: iForm }) {
  return (
    <div className="mb-6 text-left  lg:mb-10">
      {form?.name ? (
        <Heading type="h2" className="text-2xl lg:text-2xl">
          {form?.name}
        </Heading>
      ) : null}
      {form?.subtitle && (
        <Text muted className="mt-3 !text-sm leading-loose">
          {form?.subtitle}
        </Text>
      )}
    </div>
  );
}

function FormFields({
  form,
  block,
  blockCustomization,
}: {
  form?: iForm;
  block: any;
  blockCustomization: PortableTextComponents;
}) {
  if (!form) return null;

  // Filter out textarea fields and process the remaining fields
  const nonTextareaFields =
    form.fields?.filter((field) => field.type !== "textarea") || [];
  const textareaFields =
    form.fields?.filter((field) => field.type === "textarea") || [];

  return (
    <div className="w-full rounded-lg shadow-xl p-5 md:p-8">
      <SubtitleAndHeadingText form={form} />
      {form?.fields && (
        <Form
          id={form?.id ?? undefined}
          name="Contact-VariantA-Form"
          className="lg:mx-auto space-y-4 font-semibold"
          thankyouPage={thankYouPageLink(form?.thankYouPage)}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {nonTextareaFields.map((formField, index) => {
              const isLastSingle =
                index === nonTextareaFields.length - 1 &&
                nonTextareaFields.length % 2 !== 0;

              return (
                <div
                  key={index}
                  className={isLastSingle ? "sm:col-span-2" : ""}
                >
                  <div className="flex flex-col">
                    <label
                      htmlFor={formField?.name}
                      className="block text-sm font-medium mb-1"
                    >
                      {formField?.name}
                      {formField?.isRequired && (
                        <span className="text-red-600 ml-0.5">*</span>
                      )}
                    </label>
                    <FormField
                      variant="primary"
                      id={formField?.name}
                      name={formField?.name ?? ""}
                      type={formField?.type}
                      required={formField?.isRequired}
                      noLabel
                      placeholder=""
                      className="!bg-white !text-sm !text-black focus:outline-2 focus:ring-1 focus:outline-primary"
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {textareaFields.length > 0 && (
            <div className="mt-4">
              {textareaFields.map((formField, index) => (
                <div key={index} className="sm:col-span-2">
                  <div className="flex flex-col">
                    <label
                      htmlFor={formField?.name}
                      className="block text-sm font-medium mb-1"
                    >
                      {formField?.name}
                      {formField?.isRequired && (
                        <span className="text-red-600 ml-0.5">*</span>
                      )}
                    </label>
                    <FormField
                      variant="primary"
                      id={formField?.name}
                      name={formField?.name ?? ""}
                      type={formField?.type}
                      required={formField?.isRequired}
                      placeholder=""
                      noLabel
                      className="!bg-white !text-sm !text-black focus:outline-2 focus:ring-1 focus:outline-primary"
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="flex flex-col items-center mt-6">
            <div>
              <div className="webriq-recaptcha" />
            </div>
            {form?.buttonLabel && (
              <div className="w-full flex justify-center mt-5">
                <Button
                  as="button"
                  ariaLabel={form?.buttonLabel ?? "Contact form submit button"}
                  variant="maxtonPrimary"
                  className="w-full"
                  type="submit"
                >
                  {form?.buttonLabel}
                </Button>
              </div>
            )}
          </div>
        </Form>
      )}
    </div>
  );
}

// block styling as props to `components` of the PortableText component
const blockCustomization: PortableTextComponents = {
  marks: {
    internalLink: ({ children, value }) => (
      <Link
        aria-label={value.href ?? "internal link"}
        style={{ color: "red" }}
        href={value.slug.current}
      >
        {children}
      </Link>
    ),
    link: ({ children, value }) =>
      value.blank ? (
        <Link
          aria-label={value.href ?? "external link"}
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      ) : (
        <Link
          aria-label={value.href ?? "external link"}
          style={{ color: "blue" }}
          href={value.href}
        >
          {children}
        </Link>
      ),
  },
};

export { Contact_G2 };
