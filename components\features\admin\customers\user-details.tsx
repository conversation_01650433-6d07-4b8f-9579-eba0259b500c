import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    <PERSON>alog<PERSON>eader,
    <PERSON>alogTitle,
    DialogTrigger
} from "@/components/ui/dialog-shadcn";
import {
    DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/shadcn-button";
import { formatId } from "@/lib/utils/order-helpers";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { UserStatus } from "@/supabase/types";
import {
    Edit,
    User
} from "lucide-react";
import React, { useState } from "react";
import { CustomerStatusBadge } from "./customer-status-badge";
import { EditUserDetails } from "./edit-user-details";

export function ViewUserDetails({ user }: { user: PublicUserWithCustomer }) {
    const [open, setOpen] = useState(false);
    const [isEditing, setEdit] = useState(false);

    const customerData = user.customer_data?.[0];
    const userId = user.id;
    const shortId = formatId(userId);
    const maxtonAccount =
        user.business_details?.maxton_account?.trim();

    const onOpenChangeHandler = (newOpen: boolean) => {
        if (!newOpen) {
            setEdit(false);
        }
        setOpen(newOpen);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChangeHandler}>
            <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <User className="mr-2 h-4 w-4" />
                    <span>View Details</span>
                </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center justify-between">
                        <span>User Details</span>
                        {!isEditing && (
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setEdit(true)}
                            >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </Button>
                        )}
                    </DialogTitle>
                    <DialogDescription>
                        {isEditing
                            ? `Edit information for ${user.first_name} ${user.last_name}`
                            : `Comprehensive information about ${user.first_name} ${user.last_name}`
                        }
                    </DialogDescription>
                </DialogHeader>

                {isEditing ? (
                    <EditUserDetails
                        user={user}
                        onCancel={() => setEdit(false)}
                        onSuccess={() => {
                            setEdit(false);
                            setOpen(false);
                        }}
                    />
                ) : (
                    <>
                        <div className="gap-6 py-4">
                            {/* Personal Information */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold">Personal Information</h3>
                                <div className="grid grid-cols-2 gap-2">
                                    <div className="text-sm font-medium">Customer Account#</div>
                                    <div className="text-sm text-gray-500">{maxtonAccount || shortId}</div>

                                    <div className="text-sm font-medium">Name</div>
                                    <div className="text-sm text-gray-500">
                                        {user.first_name} {user.last_name}
                                    </div>

                                    <div className="text-sm font-medium">Email</div>
                                    <div className="text-sm text-gray-500">{user.email}</div>

                                    <div className="text-sm font-medium">Role</div>
                                    <div className="text-sm text-gray-500">{user.role}</div>

                                    <div className="text-sm font-medium">Status</div>
                                    <div className="text-sm text-gray-500">
                                        <CustomerStatusBadge status={user.status} />
                                    </div>
                                    <div className="text-sm font-medium">Created At</div>
                                    <div className="text-sm text-gray-500">
                                        {new Date(user.created_at).toLocaleDateString()}
                                    </div>
                                    <div className="text-sm font-medium">Notes</div>
                                    <div className="text-sm text-gray-500">{user.notes || "N/A"}</div>
                                </div>
                            </div>
                        </div>

                        {/* Business Details */}
                        <div className="space-y-4 mt-4 border-t pt-4">
                            <h3 className="text-lg font-semibold">Business Details</h3>
                            {user?.business_details ? (
                                <div className="grid grid-cols-2 gap-2">
                                    <div className="text-sm font-medium">Website</div>
                                    <div className="text-sm text-gray-500">
                                        {user?.business_details?.website ? (
                                            <a
                                                href={user?.business_details?.website}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 hover:underline"
                                            >
                                                {user?.business_details?.website}
                                            </a>
                                        ) : (
                                            "N/A"
                                        )}
                                    </div>

                                    <div className="text-sm font-medium">Buyer Name</div>
                                    <div className="text-sm text-gray-500">
                                        {user?.business_details?.buyer_name || "N/A"}
                                    </div>

                                    {Object.entries(user?.business_details)
                                        .filter(
                                            ([key]) =>
                                                ![
                                                    "id",
                                                    "user_id",
                                                    "created_at",
                                                    "website",
                                                    "buyer_name",
                                                ].includes(key)
                                        )
                                        .map(([key, value]) => (
                                            <React.Fragment key={key}>
                                                <div className="text-sm font-medium">
                                                    {key
                                                        .split("_")
                                                        .map(
                                                            (word) => word.charAt(0).toUpperCase() + word.slice(1)
                                                        )
                                                        .join(" ")}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {String(value || "N/A")}
                                                </div>
                                            </React.Fragment>
                                        ))}
                                </div>
                            ) : (
                                <div className="text-sm text-gray-500">
                                    No business details available
                                </div>
                            )}
                        </div>

                        {/* Customer Information */}
                        {customerData && (
                            <div className="space-y-4 mt-4 border-t pt-4">
                                <h3 className="text-lg font-semibold">Customer Information</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="grid grid-cols-2 gap-2">
                                        <div className="text-sm font-medium">Company Name</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.company_name || "N/A"}
                                        </div>

                                        <div className="text-sm font-medium">Contact Name</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.primary_contact_name || "N/A"}
                                        </div>

                                        <div className="text-sm font-medium">Phone</div>
                                        <div className="text-sm text-gray-500">
                                            {user.phone || "N/A"}
                                        </div>

                                        <div className="text-sm font-medium">Customer Number</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.customer_number || "N/A"}
                                        </div>

                                        <div className="text-sm font-medium">Credit Limit</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.credit_limit !== null
                                                ? `$${customerData.credit_limit.toFixed(2)}`
                                                : "N/A"}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-2">
                                        <div className="text-sm font-medium">Company Website</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.company_website ? (
                                                <a
                                                    href={customerData.company_website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:underline"
                                                >
                                                    {customerData.company_website}
                                                </a>
                                            ) : (
                                                "N/A"
                                            )}
                                        </div>

                                        <div className="text-sm font-medium">Customer Role</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.role || "N/A"}
                                        </div>

                                        <div className="text-sm font-medium">Status</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.status && (
                                                <CustomerStatusBadge
                                                    status={customerData.status as UserStatus}
                                                />
                                            )}
                                        </div>

                                        <div className="text-sm font-medium">Group</div>
                                        <div className="text-sm text-gray-500">
                                            {customerData.group_data?.data?.name || "None"}
                                        </div>
                                    </div>
                                </div>

                                {customerData.shipping_notes && (
                                    <div className="mt-4">
                                        <div className="text-sm font-medium">Shipping Notes</div>
                                        <div className="text-sm text-gray-500 mt-1 p-2 border rounded bg-gray-50">
                                            {customerData.shipping_notes}
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </>
                )
                }
            </DialogContent>
        </Dialog>
    );
}