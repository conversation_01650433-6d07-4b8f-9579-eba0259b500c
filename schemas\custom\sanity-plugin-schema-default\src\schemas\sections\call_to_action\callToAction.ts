import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { callToActionVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantFImage from "./images/variant_a.jpg";
import variantGImage from "./images/variant_g.png";
import variantHImage from "./images/variant_g.png";
import initialValue from "./initialValue";
import { callToActionSchema } from "./schema";

export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows

  {
    title: "Variant F ",
    description: "A new variant for header component",
    value: "variant_f",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    description: "A new variant for header component",
    value: "variant_g",
    image: variantGImage.src,
  },
  {
    title: "Variant H",
    description: "A new variant for header component",
    value: "variant_h",
    image: variantHImage.src,
  },
  {
    title: "Variant I",
    description: "A new variant for header component",
    value: "variant_i",
    image: variantGImage.src,
  },
  {
    title: "Variant J",
    description: "A new variant for header component",
    value: "variant_j",
    image: variantFImage.src,
  },
];

export default rootSchema(
  "callToAction",
  "Call To Action",
  MdVerticalAlignTop,
  variantsList,
  callToActionSchema,
  initialValue
);
