// SHOWS THE DEFAULT VARIANTS OF NAVIGATION
// ONLY EDIT THIS FILE IF YOU HAVE A NEW VARIANT

import type { Meta, StoryObj } from "@storybook/react";
import { Components } from "components/list";
import { Sections } from "types";
import {
  navigationSchema,
  navigationInitialValue,
} from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { filterArgsByVariant } from "components/common";
import { urlFor } from "lib/sanity";

const args = {
  ...navigationInitialValue,
  logo: {
    ...navigationInitialValue.logo,
    image: urlFor(navigationInitialValue.logo.image)
  },
};

const NavigationComponent = Components.navigation;

const meta: Meta<typeof NavigationComponent> & any = {
  title: "Components/Navigation/Defaults",
  component: NavigationComponent,
  tags: ["autodocs"],
  render: ({ variant, ...args }) => {
    const data = {
      variant,
      variants: args,
    };
    return <NavigationComponent data={data} template={args.template} />;
  },
} satisfies Meta<typeof NavigationComponent> & any;

export default meta;
type Story = StoryObj<Sections>;

const filterArgs = (variant: string) => {
  return {
    args: {
      variant: variant,
      ...filterArgsByVariant(navigationSchema, args, variant),
    },
  };
};

export const variant_a: Story = filterArgs("variant_a");
export const variant_b: Story = filterArgs("variant_b");
export const variant_c: Story = filterArgs("variant_c");
export const variant_d: Story = filterArgs("variant_d");
export const variant_e: Story = filterArgs("variant_e");
