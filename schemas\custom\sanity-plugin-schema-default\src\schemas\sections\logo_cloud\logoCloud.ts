import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdCloudCircle } from "react-icons/md";

import { logoCloudVariants as baseVariantsLists } from "@webriq-pagebuilder/sanity-plugin-schema-default";

// Images
import variantDImage from "./images/variant_d.jpg";
import initialValue from "./initialValue";
import { logoCloudSchema } from "./schema";

export const variantsList = [
  ...baseVariantsLists,

  {
    title: "Variant E",
    description: "Logo cloud with logo images only",
    value: "variant_e",
    image: variantDImage.src,
  },
];

export default rootSchema(
  "logoCloud",
  "Logo Cloud",
  MdCloudCircle,
  variantsList,
  logoCloudSchema,
  initialValue
);
