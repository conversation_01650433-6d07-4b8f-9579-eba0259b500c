import { But<PERSON> } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Input } from "@stackshift-ui/input";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState } from "react";
import { CTAProps } from ".";
import { logoLink, thankYouPageLink } from "../../../helper";
import { Logo, Form as iForm } from "../../../types";
import { useRouter } from "next/router";

export default function CallToAction_G({
  logo,
  title,
  plainText,
  form,
}: CTAProps) {
  return (
    <Section className="py-20 bg-background">
      <Container className="text-center" maxWidth={765}>
        <HeadingAndText title={title} text={plainText} />
        <CTAForm form={form} />
      </Container>
    </Section>
  );
}

function HeadingAndText({ title, text }: { title?: string; text?: string }) {
  return (
    <React.Fragment>
      {text ? <Text className=" text-left">{text}</Text> : null}
      {title ? (
        <Heading fontSize="base" className="mb-4 text-left">
          {title}
        </Heading>
      ) : null}
    </React.Fragment>
  );
}

function CTAForm({ form }: { form?: iForm }) {
  if (!form) return null;

  return (
    <Form
      id={form?.id || ""}
      name="Calltoaction-VariantB-Form"
      thankyouPage={thankYouPageLink(form?.thankYouPage)}
    >
      <Flex align="center" gap={2} className="md:justify-center flex flex-col">
        <FormFields fields={form?.fields} />
        <div>
          <div className="webriq-recaptcha" />
        </div>
        <CTABtton form={form} />
      </Flex>
    </Form>
  );
}

function FormFields({ fields }: { fields?: iForm["fields"] }) {
  if (!fields) return null;

  const [filename, setFilename] = React.useState("");
  const router = useRouter();

  return (
    <React.Fragment>
      <div className="mx-auto bg-white shadow-md rounded-md p-8">
        <div className="mb-10">
          <div className="flex items-start justify-start flex-col mb-6 md:gap-y-4 gap-y-0">
            {fields?.slice(0, 5).map((formField, index) => (
              <div key={index} className={`w-full mb-4 md:mb-0`}>
                <label
                  htmlFor={formField.name}
                  className="block text-left text-md mb-2"
                >
                  {formField?.isRequired && (
                    <span className="text-red-600">*</span>
                  )}
                  {formField.name}
                </label>

                <input
                  type={formField.type}
                  id={formField.name}
                  name={formField.name}
                  required={formField?.isRequired}
                  className="w-full p-2 border rounded"
                />
              </div>
            ))}
          </div>
        </div>

        {/* <div className="mb-10">
          <div>
            {fields?.slice(6, 7).map((formField, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox"
                  name={formField.name}
                />
                <span className="ml-2 text-sm text-gray-700">
                  {formField.name}
                </span>
              </label>
            ))}
          </div>
        </div> */}

        <div>
          {fields?.slice(5, 6).map((formField, index) => (
            <div key={index} className="mb-6 flex justify-start flex-col">
              {formField.type === "inputText" ? (
                <div className="mb-6 flex justify-start items-start flex-col">
                  <label
                    htmlFor={formField.name}
                    className="block text-left text-md mb-2"
                  >
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}
                    {formField.name}
                  </label>

                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField.type === "inputSelect" ? (
                <div className="relative">
                  <label
                    htmlFor={formField.name}
                    className="block text-left text-md mb-2"
                  >
                    {formField.label}
                  </label>
                  <select
                    aria-label={formField.placeholder ?? formField.name}
                    className="w-full p-2 border rounded"
                    name={formField.name}
                    required={formField.isRequired}
                    onFocus={(e) => e.target.classList.add("focused")}
                    onBlur={(e) => e.target.classList.remove("focused")}
                  >
                    {formField?.items?.map((item, index) => (
                      <option key={index} value={item}>
                        {item}
                      </option>
                    ))}
                  </select>
                </div>
              ) : formField.type === "inputRadio" ? (
                <>
                  <label className="block text-left text-md mb-2">
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}{" "}
                    {formField.label}
                  </label>
                  <div className="flex flex-row justify-start">
                    {formField?.items?.map((item, index) => (
                      <label
                        key={index}
                        className="inline-flex items-center mb-2 text-sm md:text-base"
                      >
                        <input
                          type="radio"
                          className="form-radio"
                          name={formField.name}
                          value={item}
                        />
                        <span className="ml-2 mr-2">{item}</span>
                      </label>
                    ))}
                  </div>
                </>
              ) : formField.type === "inputFile" ? (
                <div>
                  <label
                    htmlFor={formField.name}
                    className="block text-left text-md mb-2"
                  >
                    {formField.name}
                    <div className="flex items-center mt-1 w-full border-2 border-gray-300 h-12 p-1 overflow-hidden">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : null}
            </div>
          ))}
        </div>

        <div className="mb-10 border-b">
          <div className="flex items-start justify-start flex-col gap-0 md:gap-y-4">
            {fields?.slice(6, 11).map(
              (formField, index) => (
                // formField?.name?.includes("Date") ||
                // formField?.name?.includes("Year") ? (
                //   <div className="mb-6 cs w-full">
                //     <label
                //       htmlFor={formField.name}
                //       className="block text-md font-medium text-left mb-2"
                //     >
                //       {formField?.name?.replace(/\d/g, "")}
                //     </label>
                //     <input
                //       type="date"
                //       id={formField.name}
                //       name={formField.name}
                //       className="w-full p-2 border rounded"
                //       required={formField.isRequired}
                //     />
                //   </div>
                // ) : (
                <div key={index} className={`w-full mb-4 md:mb-0`}>
                  <label
                    htmlFor={formField.name}
                    className="block text-left text-md mb-2"
                  >
                    {" "}
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}
                    {formField.name}
                  </label>

                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              )
              // )
            )}
            <hr className="mt-10"></hr>
          </div>
        </div>

        <div className="mb-10">
          <h3 className="text-xl font-semibold mb-4">Business Make Up</h3>
          <label className="block text-md font-medium mb-2 text-left">
            <span className="text-red-600">*</span>Check one box in each row
          </label>

          {fields?.slice(11, 15).map((formField, index) => (
            <div key={index} className="mb-2 flex flex-row gap-x-4">
              <span className="">{index + 1}.</span>
              {formField.type === "inputText" ? (
                <div>
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium text-gray-700 mt-2"
                  >
                    {" "}
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}
                    {formField.name}
                  </label>
                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField.type === "inputSelect" ? (
                <div className="relative">
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium text-gray-700"
                  >
                    {formField.label}
                  </label>
                  <select
                    aria-label={formField.placeholder ?? formField.name}
                    className="w-full p-2 border rounded"
                    name={formField.name}
                    required={formField.isRequired}
                    onFocus={(e) => e.target.classList.add("focused")}
                    onBlur={(e) => e.target.classList.remove("focused")}
                  >
                    {formField?.items?.map((item, index) => (
                      <option key={index} value={item}>
                        {item}
                      </option>
                    ))}
                  </select>
                </div>
              ) : formField.type === "inputRadio" ? (
                <>
                  {/* <label className="block text-md font-medium mb-2">
                  {formField.label}
                </label> */}
                  <div className="flex flex-row flex-wrap">
                    {formField?.items?.map((item, index) => (
                      <label
                        key={index}
                        className="inline-flex items-center mb-2"
                      >
                        <input
                          type="radio"
                          className="form-radio"
                          name={formField.name}
                          value={item}
                        />
                        <span className="ml-2 mr-2">{item}</span>
                      </label>
                    ))}
                  </div>
                </>
              ) : formField.type === "inputFile" ? (
                <div>
                  <label className="block text-md font-medium text-gray-700 mb-2">
                    {formField.name}
                    <div className="flex items-center mt-1 w-full border-2 border-gray-300 h-12 p-1 overflow-hidden">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : null}
            </div>
          ))}
          <hr className="mt-10"></hr>
        </div>

        <div className="my-10">
          <h3 className="text-xl font-semibold mb-4">
            Partners or Corporate Officers
          </h3>

          <div className="flex flex-col gap-y-4">
            {fields?.slice(15, 19).map((formField, index) => (
              <div key={index} className={`w-full`}>
                <label
                  htmlFor={formField.name}
                  className="block text-md font-medium text-left mb-2"
                >
                  {formField?.isRequired && (
                    <span className="text-red-600">*</span>
                  )}
                  {formField?.name?.includes("Phone")
                    ? formField?.name?.replace(/\d/g, "")
                    : formField?.name}
                </label>
                <input
                  type={formField.type}
                  id={formField.name}
                  name={formField.name}
                  className="w-full p-2 border rounded"
                />
              </div>
            ))}
          </div>
          <hr className="mt-10"></hr>
        </div>

        <div className="mb-10">
          <h3 className="text-xl font-semibold mb-4">Bank Reference</h3>
          <div className="flex flex-col gap-y-4">
            {fields?.slice(19, 23).map((formField, index) =>
              formField.type === "inputText" ? (
                <div className="w-full" key={index}>
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium mb-2 text-left"
                  >
                    {formField?.name}
                  </label>
                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField?.name?.includes("Date") ? (
                <div className="mb-6 col-span-2 md:col-span-1 w-2/3 sm:w-1/2 md:w-full">
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium text-gray-700 mt-2"
                  >
                    {formField?.name?.replace(/\d/g, "")}
                  </label>
                  <input
                    type="date"
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                    required={formField.isRequired}
                  />
                </div>
              ) : formField.type === "inputFile" ? (
                <div className="w-full col-span-2 mb-6">
                  <label className="block text-md font-medium text-gray-700 mb-2">
                    {formField.name}
                    <div className="flex items-center mt-2 w-full border-2 border-gray-300 h-12 p-1 overflow-hidden">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : null
            )}
          </div>
          <hr className="mt-10"></hr>
        </div>

        <div className="mb-10">
          <h3 className="text-xl font-semibold mb-4">Trade References</h3>
          <div className="flex flex-col gap-2 md:gap-y-4">
            <h4 className="text-xl font-semibold text-left">References 1</h4>
            {fields?.slice(23, 28).map((formField, index) =>
              formField.type === "inputText" ? (
                <div className="w-full" key={index}>
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium mb-2 text-left "
                  >
                    {" "}
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}{" "}
                    {formField.name}
                  </label>
                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField?.name?.includes("Date") ||
                formField?.name?.includes("Year") ? (
                <div className="mb-6 w-full">
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium mt-2"
                  >
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}
                    {formField?.name?.replace(/\d/g, "")}
                  </label>
                  <input
                    type="date"
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                    required={formField.isRequired}
                  />
                </div>
              ) : formField.type === "inputFile" ? (
                <div className="w-full mb-6">
                  <label className="block text-md font-medium mb-2">
                    {formField.name}
                    <div className="">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : formField.type === "inputEmail" ? (
                <div className="w-full">
                  <label className="block text-left text-md font-medium !mb-2">
                    {formField?.isRequired && (
                      <span className="text-red-600">*</span>
                    )}{" "}
                    {formField.name}
                  </label>
                  <div className="">
                    <input
                      type="email"
                      id={formField.name}
                      name={formField.name}
                      className="w-full p-2 border rounded"
                      required={formField.isRequired}
                    />
                  </div>
                </div>
              ) : null
            )}
          </div>
        </div>

        <div className="mb-10">
          <div className="flex flex-col gap-2 md:gap-y-4">
            <h4 className="text-xl font-semibold text-left">References 2</h4>
            {fields?.slice(28, 33).map((formField, index) =>
              formField.type === "inputText" ? (
                <div className="w-full" key={index}>
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium mb-2 text-left"
                  >
                    {formField?.name}
                  </label>
                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField?.name?.includes("Date") ||
                formField?.name?.includes("Year") ? (
                <div className="mb-6 cs w-full">
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium text-gray-700 mt-2"
                  >
                    {formField?.name?.replace(/\d/g, "")}
                  </label>
                  <input
                    type="date"
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                    required={formField.isRequired}
                  />
                </div>
              ) : formField.type === "inputFile" ? (
                <div className="w-full mb-6">
                  <label className="block text-md font-medium text-gray-700 mb-2">
                    {formField.name}
                    <div className="flex items-center mt-2 w-full border-2 border-gray-300 h-12 p-1 overflow-hidden">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : formField.type === "inputEmail" ? (
                <div className="w-full">
                  <label className="block text-left text-md font-medium mb-2">
                    {formField.name}
                  </label>
                  <div className="">
                    <input
                      type="email"
                      id={formField.name}
                      name={formField.name}
                      className="w-full p-2 border rounded"
                      required={formField.isRequired}
                    />
                  </div>
                </div>
              ) : null
            )}
          </div>
        </div>

        <div className="mb-10">
          <div className="flex flex-col gap-2 md:gap-y-4">
            <h4 className="text-xl font-semibold text-left">References 3</h4>
            {fields?.slice(33, 38).map((formField, index) =>
              formField.type === "inputText" ? (
                <div className="w-full" key={index}>
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium mb-2 text-left"
                  >
                    {formField?.name}
                  </label>
                  <input
                    type={formField.type}
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                  />
                </div>
              ) : formField?.name?.includes("Date") ||
                formField?.name?.includes("Year") ? (
                <div className="mb-6 cs w-full">
                  <label
                    htmlFor={formField.name}
                    className="block text-md font-medium text-gray-700 mt-2"
                  >
                    {formField?.name?.replace(/\d/g, "")}
                  </label>
                  <input
                    type="date"
                    id={formField.name}
                    name={formField.name}
                    className="w-full p-2 border rounded"
                    required={formField.isRequired}
                  />
                </div>
              ) : formField.type === "inputFile" ? (
                <div className="w-full mb-6">
                  <label className="block text-md font-medium text-gray-700 mb-2">
                    {formField.name}
                    <div className="flex items-center mt-2 w-full border-2 border-gray-300 h-12 p-1 overflow-hidden">
                      <input
                        type="file"
                        id={formField.name}
                        name={formField.name}
                        className="w-full p-2 border rounded"
                        required={formField.isRequired}
                        onChange={(e) =>
                          setFilename(e.target?.files[0]?.name ?? "")
                        }
                      />
                    </div>
                  </label>
                </div>
              ) : formField.type === "inputEmail" ? (
                <div className="w-full">
                  <label className="block text-left text-md font-medium mb-2">
                    {formField.name}
                  </label>
                  <div className="">
                    <input
                      type="email"
                      id={formField.name}
                      name={formField.name}
                      className="w-full p-2 border rounded"
                      required={formField.isRequired}
                    />
                  </div>
                </div>
              ) : null
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}

function CTABtton({ form }: { form?: iForm }) {
  if (!form?.buttonLabel) return null;

  return (
    <Button
      as="button"
      className="w-1/4 self-start"
      ariaLabel={form?.buttonLabel}
      type="submit"
    >
      {form?.buttonLabel}
    </Button>
  );
}

export { CallToAction_G };
