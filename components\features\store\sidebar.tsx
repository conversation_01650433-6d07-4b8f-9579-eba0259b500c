import {
    <PERSON>bar,
    <PERSON>bar<PERSON><PERSON>nt,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarTrigger
} from "@/components/ui/sidebar";
import useAuthStore from "@/stores/auth-store";
import {
    ChartNoAxesColumnIncreasing,
    FileTextIcon,
    HomeIcon,
    LucideIcon,
    PackageIcon,
    PackageSearch,
    SettingsIcon,
    ShoppingCartIcon,
    UsersIcon
} from "lucide-react";
import Link from "next/link";
import { NavUser } from "./nav-user";
import { Button } from "@/components/ui/shadcn-button";
import useCartStore from "@/stores/cart-store";
import { Badge } from "@/components/ui/badge";

interface Header {
    title: string;
}

interface Nav {
    title: string;
    link?: string;
    icon?: LucideIcon;
}

interface SidebarData {
    admin?: {
        header: Header;
        nav: Nav[];
    },
    manager?: {
        header: Header;
        nav: Nav[];
    };
}

const sidebarData: SidebarData = {
    manager: {
        header: {
            title: "Maxton"
        },
        nav: [
            {
                title: "Home",
                link: "/store/dashboard",
                icon: HomeIcon
            },
            {
                title: "My Products",
                link: "/store/products",
                icon: PackageIcon
            },
            {
                title: "My Orders",
                link: "/store/orders",
                icon: ChartNoAxesColumnIncreasing
            },
        ]
    },
    admin: {
        header: {
            title: "Maxton"
        },
        nav: [
            {
                title: "Home",
                link: "/store/dashboard",
                icon: HomeIcon
            },
            {
                title: "Sales",
                link: "/store/sales",
                icon: ChartNoAxesColumnIncreasing
            },
            {
                title: "Products",
                link: "/store/products",
                icon: PackageSearch
            },
            {
                title: "Customers",
                link: "/store/customers",
                icon: UsersIcon
            },
            {
                title: "RFQ",
                link: "/store/rfq/list",
                icon: FileTextIcon
            },
            {
                title: "My Account",
                link: "/profile",
                icon: SettingsIcon
            },
        ]
    }
};

export default function StoreSidebar() {
    const userData = useAuthStore((state) => state.data);
    const role = useAuthStore((state) => state.getRole)();
    const cartItems = useCartStore((state) => state.items);

    const currentSidebar = sidebarData[role];

    return <Sidebar collapsible="icon">
        <SidebarHeader>
            <div className="w-full h-full flex items-center justify-between">
                <h1 className="truncate">{currentSidebar?.header.title}</h1>
                <SidebarTrigger size="icon" className="text-primary" />
            </div>
        </SidebarHeader>
        <SidebarContent>
            <SidebarGroup>
                <SidebarGroupLabel>Products</SidebarGroupLabel>
                <SidebarMenu>
                    {
                        currentSidebar?.nav.map((menuItem) => (
                            <SidebarMenuItem key={menuItem.title}>
                                <SidebarMenuButton asChild>
                                    {
                                        menuItem.link ? (
                                            <Link href={menuItem.link} className="text-lg font-bold py-6 data-[active=true]:bg-sidebar-primary">
                                                {menuItem.icon && <menuItem.icon size={32} className="w-12 h-12 text-primary" />}
                                                <span>{menuItem.title}</span>
                                            </Link>
                                        ) : (
                                            <Button variant="ghost" size="sm" className="text-sm flex items-center justify-start font-bold py-6 data-[active=true]:bg-sidebar-primary">
                                                {menuItem.icon && <menuItem.icon size={32} className="w-12 h-12 text-primary" />}
                                                <span>{menuItem.title}</span>
                                                {
                                                    cartItems.length > 0 && (
                                                        <Badge variant="success" className="ml-auto">
                                                            {cartItems.length}
                                                        </Badge>
                                                    )
                                                }
                                            </Button>
                                        )
                                    }
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        ))
                    }
                </SidebarMenu>
            </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
            <NavUser user={{
                name: "Customer",
                email: userData.email ?? "",
                avatar: ""
            }} />
        </SidebarFooter>
    </Sidebar>
}  