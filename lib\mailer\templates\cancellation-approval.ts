import emailTemplate from ".";
import { formatId } from "@/lib/utils/order-helpers";

interface CancellationApprovalProps {
  to: string;
  name: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  companyWebsite?: string;
  accountNumber?: string;
  shippingNotes?: string;
  orderId: string;
  invoice: string;
  requestDate: string;
  approvalDate?: string;
  // Order items
  items?: {
    name: string;
    quantity: number;
    price?: number;
    item_price?: number;
    options?: { name: string; value: string; price?: number }[];
  }[];
  totalAmount?: number;
  // Shipping and contact details
  shippingAddress?: {
    contactName?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    phone?: string;
    country?: string;
  };
  // Billing details
  billingAddress?: {
    contactName?: string;
    address?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    phone?: string;
    country?: string;
  };
  // Payment and shipping method
  paymentMethod?: string;
  deliveryMethod?: string;
  shipCollect?: boolean;
  upsAccountNumber?: string;
  phoneNumber?: string;
  // Keep refund fields but they won't be displayed for now
  refundAmount?: number;
  refundMethod?: string;
  cancellationReason?: string;
  additionalInfo?: string;
  orderDetailsUrl: string;
  poNumber?: string;
  notes?: string;
}

export default function createCancellationApprovalTemplate({
  to,
  name,
  firstName,
  lastName,
  companyName,
  companyWebsite,
  accountNumber,
  shippingNotes,
  orderId,
  invoice,
  requestDate,
  approvalDate,
  items,
  totalAmount,
  shippingAddress,
  billingAddress,
  paymentMethod,
  deliveryMethod,
  shipCollect,
  upsAccountNumber,
  phoneNumber,
  refundAmount,
  refundMethod,
  cancellationReason,
  additionalInfo,
  orderDetailsUrl,
  poNumber,
  notes,
}: CancellationApprovalProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Manufacturing Company";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

  // Determine if this is an approval or immediate cancellation
  const isApproval = !!approvalDate;
  const emailSubject = isApproval
    ? `Order Cancellation Approved - ${invoice}`
    : `Order Cancellation Confirmation - ${invoice}`;

  // Calculate totals for items if present
  let subtotal = 0;
  let optionsTotal = 0;
  let grandTotal = totalAmount || 0;

  // Format items list similar to order confirmation
  const itemsList = items
    ? items
        .map((item) => {
          // The item.price should already contain the correct discounted price from the database
          const itemPrice = item.price || 0;

          // Calculate option prices if they exist
          const itemSubtotal = itemPrice * item.quantity;
          let itemOptionsTotal = 0;

          // Build options HTML with detailed pricing
          let optionsHtml = "";
          if (item.options && item.options.length > 0) {
            optionsHtml += '<div style="margin-top: 6px; padding-left: 15px;">';
            optionsHtml +=
              '<div style="font-size: 12px; font-weight: bold; color: #555; margin-bottom: 4px;">Selected Options:</div>';

            item.options.forEach((opt) => {
              const optionPrice = opt.price || 0;
              const optionTotalPrice = optionPrice * item.quantity;
              itemOptionsTotal += optionTotalPrice;

              optionsHtml += `
              <div style="display: flex; justify-content: space-between; font-size: 12px; color: #666; margin-bottom: 2px;">
                <div>${opt.name}: ${opt.value}</div>
                ${
                  optionPrice > 0
                    ? `<div style="text-align: right; min-width: 90px;">
                    <span style="color: #28a745;">+$${optionPrice.toFixed(
                      2
                    )}</span> × ${
                      item.quantity
                    } = <span style="color: #28a745;">$${optionTotalPrice.toFixed(
                      2
                    )}</span>
                  </div>`
                    : ""
                }
              </div>`;
            });

            optionsHtml += "</div>";
          }

          // Update running totals
          subtotal += itemSubtotal;
          optionsTotal += itemOptionsTotal;
          const itemTotal = itemSubtotal + itemOptionsTotal;

          return `<tr>
          <td style="padding: 12px 8px; border-bottom: 1px solid #ddd;">
            <div style="font-weight: bold;">${item.name}</div>
            ${optionsHtml}
          </td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #ddd; text-align: center; vertical-align: top;">${
            item.quantity
          }</td>
          <td style="padding: 12px 8px; border-bottom: 1px solid #ddd; text-align: right; vertical-align: top;">
            <div>$${itemPrice.toFixed(2)}</div>
            ${
              itemOptionsTotal > 0
                ? `<div style="margin-top: 4px; font-size: 13px;">
                <div style="color: #28a745;">Options: $${(
                  itemOptionsTotal / item.quantity
                ).toFixed(2)}</div>
                <div style="font-weight: bold; margin-top: 4px; border-top: 1px solid #eee; padding-top: 4px;">
                  Item Total: $${(itemTotal / item.quantity).toFixed(2)}
                </div>
              </div>`
                : `<div style="margin-top: 4px; font-size: 13px; color: #666;">
                Item Total: $${itemTotal.toFixed(2)}
              </div>`
            }
          </td>
        </tr>`;
        })
        .join("")
    : "";

  // If totalAmount is not provided, calculate it
  if (totalAmount === undefined && items) {
    const merchandiseTotal = subtotal + optionsTotal;
    grandTotal = merchandiseTotal;
  }

  // Helper to format payment method
  const formatPaymentMethod = (method?: string) => {
    if (!method) return undefined;

    // Split by underscore and capitalize each word
    return method
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const shortAccountNumber = formatId(accountNumber);
  const shortOrderId = formatId(orderId);

  return emailTemplate({
    to,
    subject: emailSubject,
    from: from,
    bcc: "<EMAIL>",
    body: `
      <h2 style="color: #d32f2f; background-color: #ffebee; padding: 10px; border-left: 4px solid #d32f2f;">Order Cancellation ${
        isApproval ? "Approval" : "Confirmation"
      }</h2>
      <p>Dear ${name},</p>
      
      <p>${additionalInfo}</p>
      
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f5f5f5;">
        <h3 style="margin-top: 0;">Account & Order Information</h3>
        <ul style="padding-left: 20px; margin-bottom: 0;">
          <li><strong>Name:</strong> ${firstName || ""} ${lastName || name}</li>
          ${
            companyName
              ? `<li><strong>Company/Business:</strong> ${companyName}</li>`
              : ""
          }
          ${
            companyWebsite
              ? `<li><strong>Website:</strong> <a href="${companyWebsite}" style="color: #0045d8; text-decoration: none;">${companyWebsite}</a></li>`
              : ""
          }
          ${
            accountNumber
              ? `<li><strong>Account ID:</strong> ${shortAccountNumber}</li>`
              : ""
          }
          <li><strong>Email:</strong> ${to}</li>
          ${
            phoneNumber ? `<li><strong>Phone:</strong> ${phoneNumber}</li>` : ""
          }
          ${
            shippingNotes
              ? `<li><strong>Shipping Notes:</strong> ${shippingNotes}</li>`
              : ""
          }

          ${
            deliveryMethod
              ? `<li><strong>Delivery Method:</strong> ${deliveryMethod}</li>`
              : ""
          }
          ${
            // Check if shipping address is for a special country
            (() => {
              const isSpecialCountry =
                shippingAddress?.country === "Mexico" ||
                shippingAddress?.country === "Puerto Rico" ||
                shippingAddress?.state === "Puerto Rico" ||
                (shippingAddress?.state &&
                  shippingAddress.state.includes("Mexico"));

              // Only show Ship Collect UPS info if not a special country
              return !isSpecialCountry && shipCollect !== undefined
                ? `<li>
                    <strong>Ship Collect UPS:</strong> ${
                      shipCollect ? "Yes" : "No"
                    }
                    ${
                      shipCollect && upsAccountNumber
                        ? `<br><span style="padding-left: 20px;"><strong>UPS Account Number:</strong> ${upsAccountNumber}</span>`
                        : ""
                    }
                  </li>`
                : "";
            })()
          }
          ${
            paymentMethod
              ? `<li><strong>Payment Method:</strong> ${formatPaymentMethod(
                  paymentMethod
                )}</li>`
              : ""
          }
          ${
            notes
              ? `<li><strong>Order Notes:</strong> ${notes}</li>`
              : ""
          }
        </ul>
      </div>
      
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="width: 50%; padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd;">Billing Address</th>
            <th style="width: 50%; padding: 10px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd;">Shipping Address</th>
          </tr>
          <tr>
            <td style="padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              ${
                billingAddress
                  ? `
                ${billingAddress.contactName || ""}<br>
                ${billingAddress.address || ""}<br>
                ${billingAddress.city || ""}, ${billingAddress.state || ""} ${
                  billingAddress.zipCode || ""
                }
                ${billingAddress.country ? `<br>${billingAddress.country}` : ""}
                ${
                  billingAddress.phone
                    ? `<br>Phone: ${billingAddress.phone}`
                    : ""
                }
              `
                  : "Contact your account manager for billing details"
              }
            </td>
            <td style="padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              ${
                shippingAddress
                  ? `
                ${shippingAddress.contactName || ""}<br>
                ${shippingAddress.address || ""}<br>
                ${shippingAddress.city || ""}, ${shippingAddress.state || ""} ${
                  shippingAddress.zipCode || ""
                }
                ${
                  shippingAddress.country
                    ? `<br>${shippingAddress.country}`
                    : ""
                }
                ${
                  shippingAddress.phone
                    ? `<br>Phone: ${shippingAddress.phone}`
                    : ""
                }
              `
                  : "No shipping address provided"
              }
            </td>
          </tr>
        </table>
      </div>
      
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #fff8e1;">
        <h3 style="margin-top: 0;">Cancellation Details</h3>
        <ul style="padding-left: 20px; margin-bottom: 0;">
          <li><strong>Order ID:</strong> ${shortOrderId}</li>
          <li><strong>Invoice Number:</strong> ${invoice}</li>
          ${
            poNumber
              ? `<li><strong>Purchase Order Number:</strong> ${poNumber}</li>`
              : ""
          }
          <li><strong>Cancellation Date:</strong> ${requestDate}</li>
          ${
            isApproval
              ? `<li><strong>Approval Date:</strong> ${approvalDate}</li>`
              : ""
          }
          ${
            cancellationReason
              ? `<li><strong>Reason for Cancellation:</strong> ${cancellationReason}</li>`
              : ""
          }
        </ul>
      </div>
      
      ${
        items && items.length > 0
          ? `
      <div style="margin: 20px 0; padding: 15px; border: 2px solid #d32f2f; border-radius: 5px; background-color: #ffffff;">
        <h3 style="margin-top: 0; color: #d32f2f;">Cancelled Items</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>
              <th style="padding: 12px 8px; text-align: left; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Product</th>
              <th style="padding: 12px 8px; text-align: center; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Quantity</th>
              <th style="padding: 12px 8px; text-align: right; border-bottom: 2px solid #ddd; background-color: #f8f9fa;">Price</th>
            </tr>
          </thead>
          <tbody>
            ${itemsList}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="2" style="padding: 12px 8px; text-align: right; font-weight: bold; font-size: 16px; border-top: 2px solid #ddd;">Order Total:</td>
              <td style="padding: 12px 8px; text-align: right; font-weight: bold; font-size: 16px; border-top: 2px solid #ddd;">$${grandTotal.toFixed(
                2
              )}</td>
            </tr>
          </tfoot>
        </table>
      </div>
      `
          : ""
      }
      
      <p>You can view the complete details of this order by visiting your <a href="${orderDetailsUrl}" style="color: #0045d8; text-decoration: none;">order history</a>.</p>
      
      <p>If you have any questions regarding this cancellation or need further assistance, please don't hesitate to contact our customer service team.</p>
      
      <p>Thank you for your business, and we hope to serve you again in the future.</p>
    `,
  });
}
