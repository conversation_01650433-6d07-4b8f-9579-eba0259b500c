import { FormLabel } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/shadcn-button"
import { PlusCircle, Trash2 } from "lucide-react"

interface UrlLabelItem {
    url: string;
    label: string;
}

interface HelpfulHintsTabProps {
    helpfulHints: UrlLabelItem[]
    addHelpfulHint: () => void
    removeHelpfulHint: (index: number) => void
    updateHelpfulHint: (index: number, field: keyof UrlLabelItem, value: string) => void
}

export default function HelpfulHintsTab({
    helpfulHints,
    addHelpfulHint,
    removeHelpfulHint,
    updateHelpfulHint
}: HelpfulHintsTabProps) {
    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <FormLabel>Helpful Hints</FormLabel>
                <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addHelpfulHint}
                >
                    <PlusCircle className="h-4 w-4 mr-1" /> Add Hint
                </Button>
            </div>

            <div className="space-y-3">
                {helpfulHints.map((hint, index) => (
                    <div key={index} className="grid grid-cols-1 gap-2 md:grid-cols-2 items-center border p-3 rounded-md">
                        <div className="space-y-2">
                            <Label htmlFor={`hint-label-${index}`}>Hint Text</Label>
                            <Input
                                id={`hint-label-${index}`}
                                placeholder="Helpful hint description"
                                value={hint.label}
                                onChange={(e) => updateHelpfulHint(index, "label", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor={`hint-url-${index}`}>URL (Optional)</Label>
                            <div className="flex gap-2">
                                <Input
                                    id={`hint-url-${index}`}
                                    placeholder="Link for more information"
                                    value={hint.url}
                                    onChange={(e) => updateHelpfulHint(index, "url", e.target.value)}
                                    className="flex-1"
                                />
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => removeHelpfulHint(index)}
                                    disabled={helpfulHints.length === 1 && index === 0}
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <p className="text-sm text-muted-foreground mt-1">
                Add helpful hints for customers with optional URLs for more information.
            </p>
        </div>
    )
} 