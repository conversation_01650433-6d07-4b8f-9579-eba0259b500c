"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog-shadcn";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import { useDeleteUserMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Trash2 } from "lucide-react";
import { useState } from "react";

interface DeleteUserDialogProps {
  user: PublicUserWithCustomer;
}

export function DeleteUserDialog({ user }: DeleteUserDialogProps) {
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const token = useAuthStore((state) => state.token);
  const deleteUserMutation = useDeleteUserMutation(token);

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      await deleteUserMutation.mutateAsync(user.id);

      toast({
        title: "Success",
        description: `User ${user.email} has been deleted successfully.`,
        variant: "default",
      });

      // Only close the dialog after successful deletion
      setOpen(false);
    } catch (error) {
      console.error("Error deleting user:", error);

      toast({
        title: "Error",
        description: "Failed to delete user. Please try again.",
        variant: "destructive",
      });
      // Don't close the dialog on error, let user try again
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <DropdownMenuItem
          className="text-red-600 focus:text-red-700 focus:bg-red-50"
          onSelect={(e) => e.preventDefault()}
        >
          <Trash2 className="h-3 w-3" />
          Delete user
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="h-5 w-5" />
            Delete User Account
          </DialogTitle>
          <DialogDescription className="space-y-2">
            <p>
              Are you sure you want to permanently delete <strong>{user.email}</strong>?
            </p>
            <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
              <p className="font-medium mb-1">⚠️ This action cannot be undone!</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>All user data will be permanently removed</li>
                <li>User will lose access to their account immediately</li>
                {/* <li>Order history and related data will be deleted</li> */}
              </ul>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {isDeleting ? "Deleting..." : "Delete User"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}