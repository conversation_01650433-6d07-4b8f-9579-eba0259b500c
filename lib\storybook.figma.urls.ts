// Add/update with the FIGMA URL to the design for the defined Storybook components
export const config = {
  components: {
    ui: {
      Avatar: {
        primary: "",
        initials: "",
      },
      Badge: {
        primary: "",
      },
      BlockStyle: {
        primary: "",
        custom: "",
      },
      Button: {
        default:
          "https://www.figma.com/file/Q0yWNIEVjrQ11aFlROZXNt/WebriQekeke?type=design&node-id=1%3A2&mode=design&t=BI877OFsAX2wUB9v-1",
        solid:
          "https://www.figma.com/file/Q0yWNIEVjrQ11aFlROZXNt/WebriQekeke?type=design&node-id=1%3A2&mode=design&t=BI877OFsAX2wUB9v-1",
        outline: "",
        ghost: "",
        link: "",
        custom: "",
        unstyled: "",
        addToWishlist: "",
        swiperPagination: "",
        tab: "",
      },
      Card: {
        primary: "",
        custom: "",
      },
      Checkbox: {
        primary: "",
        custom: "",
      },
      CheckboxGroup: {
        primary: "",
        inline: "",
        custom: "",
      },
      File: {
        primary: "",
        outline: "",
      },
      Form: {
        primary: "",
        custom: "",
      },
      FormField: {
        primary: "",
      },
      Heading: {
        primary: "",
      },
      Input: {
        primary: "",
        secondary: "",
        noLabel: "",
        outline: "",
      },
      Radio: {
        primary: "",
      },
      RadioGroup: {
        primary: "",
        inline: "",
      },
      Select: {
        primary: "",
      },
      SocialIcons: {
        primary: "",
      },
      SwiperButton: {
        variantALeft: "",
        variantARight: "",
        variantBLeft: "",
        variantBRight: "",
      },
      SwiperPagination: {
        primary: "",
      },
      Text: {
        primary: "",
      },
      Textarea: {
        primary: "",
        outline: "",
      },
    },

    common: {
      Form: {
        primary: "",
      },
      StatsCard: {
        primary: "",
        stacked: "",
      },
      YoutubeVid: {
        primary: "",
      },
    },

    layout: {
      Container: {
        primary: "",
        withMaxW: "",
      },
      Flex: {
        primary: "",
      },
      Grid: {
        primary: "",
      },
    },
  },

  sections: {
    appPromo: {},
    blog: {},
    callToAction: {},
    contact: {},
    cookies: {},
    faqs: {},
    featuredProducts: {},
    features: {},
    footer: {},
    header: {},
    howItWorks: {},
    logoCloud: {},
    navigation: {},
    newsletter: {},
    portfolio: {},
    pricing: {},
    productInfo: {},
    signInSignUp: {},
    stats: {},
    team: {},
    testimonials: {},
    textComponent: {},
    wishlist: {},
  },

  cstudio: {
    allProducts: {},
    featuredProducts: {},
    productInfo: {},
    wishlist: {},
  },
};
