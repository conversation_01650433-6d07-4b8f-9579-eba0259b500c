import { FiPackage } from "react-icons/fi";
import { isSlugUnique } from "studio/isSlugUnique";
import { defineType, defineField } from "sanity";

export default defineType({
  name: "archiveMonth",
  title: "Archive Month",
  icon: FiPackage,
  type: "document",
  fields: [
    define<PERSON>ield({
      name: "title",
      title: "Title",
      type: "string",
    }),
    defineField({
      name: "slug",
      title: "Slug",
      type: "slug",
      
      validation: (Rule) =>
        Rule.required().custom((slug) => {
          const regex = /[!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?]+/;

          if (regex.test(slug.current)) {
            return `Slug cannot contain these special characters [!@#$%^&*()+\=\[\]{};':"\\|,.<>\/?]`;
          }

          if (slug.current !== slug.current.toLowerCase()) {
            return "Slug must be in lowercase";
          }

          if (slug.current.indexOf(" ") !== -1) {
            return "Slug cannot contain spaces";
          }

          return true;
        }),
      options: {
        source: "title",
        maxLength: 96,
        isUnique: isSlugUnique,
      },
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "text",
    }),
    // SEO fields
    defineField({
      title: "SEO Settings",
      name: "seo",
      type: "seoSettings",
      options: {
        collapsible: true,
        collapsed: true,
      },
    }),
  ],
});
