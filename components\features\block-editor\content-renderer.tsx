"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface ContentRendererProps {
    content: string
    className?: string
    truncate?: boolean
    maxLength?: number
}

export function ContentRenderer({
    content,
    className,
    truncate = false,
    maxLength = 200,
}: ContentRendererProps) {
    // If content is empty, return null
    if (!content) return null

    const processedContent = React.useMemo(() => {
        if (truncate) {
            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = content

            // Get text content
            const textContent = tempDiv.textContent || tempDiv.innerText

            // Truncate text if needed
            if (textContent.length > maxLength) {
                return `${textContent.slice(0, maxLength)}...`
            }

            return textContent
        }

        return content
    }, [content, truncate, maxLength])

    if (truncate) {
        return (
            <p className={cn("text-sm", className)}>
                {processedContent}
            </p>
        )
    }

    return (
        <div
            className={cn(
                "prose max-w-none dark:prose-invert",
                "[&_a]:text-blue-600 [&_a]:underline [&_a]:hover:text-blue-800 [&_a]:transition-colors",
                className
            )}
            dangerouslySetInnerHTML={{ __html: processedContent }}
        />
    )
}

export default ContentRenderer 