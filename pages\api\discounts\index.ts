import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { supabaseClient } from "@/supabase";
import { Discount } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
    GET: checkAdmin(getDiscountsHandler),
    POST: createDiscountHandler,
});

export interface GetDiscountsResponse {
    error?: string;
    discounts?: Discount[];
    total?: number;
    totalPages?: number;
}

async function getDiscountsHandler(req: NextApiRequest, res: NextApiResponse<GetDiscountsResponse>) {
    const query = req.query;
    const page = query.page ? parseInt(query.page as string) : 1;
    const limit = query.limit ? parseInt(query.limit as string) : 10;

    const { data, error } = await supabaseClient
        .schema("public")
        .from("discounts")
        .select("*", { count: "exact" })
        .range((page - 1) * limit, page * limit - 1);

    if (error) {
        return res.status(500).json({ error: error.message });
    }

    const total = data?.length;
    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({ discounts: data, total, totalPages });
}

const createDiscountSchema = z.object({
    code: z.string().min(1, "Code is required"),
    description: z.string().min(1, "Description is required"),
    discount_percentage: z.number().min(0, "Discount percentage is required"),
});

export type CreateDiscountRequest = z.infer<typeof createDiscountSchema>;

export interface CreateDiscountResponse {
    error?: string;
    discount?: Discount;
}

async function createDiscountHandler(req: NextApiRequest, res: NextApiResponse<CreateDiscountResponse>) {
    const { code, description, discount_percentage } = createDiscountSchema.parse(req.body);

    const { data, error } = await supabaseClient
        .schema("public")
        .from("discounts")
        .insert({ code, info: description, value: discount_percentage })
        .select("*")
        .single();

    if (error) {
        return res.status(500).json({ error: error.message });
    }

    return res.status(200).json({ discount: data });
}
