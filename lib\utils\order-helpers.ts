/**
 * Utility functions for order-related operations
 */

/**
 * Formats an order ID to a standardized display format
 * Uses the first 6 characters of the order ID for consistent display
 * 
 * @param orderId - The full order ID to format
 * @returns Formatted order ID string (first 6 characters)
 */
export function formatId(orderId: string | null | undefined): string {
  if (!orderId) {
    return "N/A";
  }

  return orderId.slice(0, 6);
}

/**
 * Formats an order ID with a hash prefix for display
 * 
 * @param orderId - The full order ID to format
 * @returns Formatted order ID string with # prefix
 */
export function formatIdWithHash(orderId: string | null | undefined): string {
  if (!orderId) {
    return "#N/A";
  }

  return `#${orderId.slice(0, 6)}`;
}