import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CheckoutFormValues } from "@/pages/store/checkout";
import {
    useGetBillingAddressesQuery,
    useGetShippingAddressesQuery
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import {
    Package
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { ShippingAddress } from "supabase/types";

export interface CheckoutDeliveryMethodProps {
    checkoutForm: UseFormReturn<CheckoutFormValues>;
}

export function CheckoutDeliveryMethod({ checkoutForm }: CheckoutDeliveryMethodProps) {
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;
    const shippingAddressId = checkoutForm.watch("shippingAddressId");
    const isShipCollect = checkoutForm.watch("ship_collect");
    const shippingAddresses = useGetShippingAddressesQuery(userId);
    const billingAddresses = useGetBillingAddressesQuery(userId);
    const [selectedAddress, setSelectedAddress] =
        useState<ShippingAddress | null>(null);
    const useBillingAddressRef = useRef(false);

    useEffect(() => {
        if (
            !shippingAddressId ||
            (!shippingAddresses.data && !billingAddresses.data)
        )
            return;

        // Check if the shipping address ID matches any billing address ID
        const billingAddress = billingAddresses.data?.find(
            (addr) => addr.id === shippingAddressId
        );

        if (billingAddress) {
            // Convert billing address to shipping address format
            const shippingAddress: ShippingAddress = {
                id: billingAddress.id,
                address: billingAddress.address || null,
                address_2: billingAddress.address_2,
                address_type: billingAddress.address_type || "commercial",
                city: billingAddress.city || null,
                country: billingAddress.country || null,
                state: billingAddress.state || null,
                zip_code: billingAddress.zip_code || null,
                contact_name: billingAddress.company_name || null,
                contact_email: null,
                contact_number: null,
                option_name: null,
                default: false,
                created_at: billingAddress.created_at,
                updated_at: billingAddress.updated_at || billingAddress.created_at,
                customer_id: userId,
            };
            setSelectedAddress(shippingAddress);
            useBillingAddressRef.current = true;
        } else {
            // Regular shipping address
            const address = shippingAddresses.data?.find(
                (addr) => addr.id === shippingAddressId
            );
            if (address) {
                setSelectedAddress(address);
                useBillingAddressRef.current = false;
            }
        }
    }, [shippingAddressId, shippingAddresses.data, billingAddresses.data]);

    // Check for all special country conditions
    const selectedCountry = selectedAddress?.country?.toLowerCase();
    const selectedState = selectedAddress?.state?.toLowerCase();
    const isSpecialCountry =
        !!selectedAddress &&
        (selectedCountry === "mexico" ||
            selectedCountry === "puerto rico" ||
            selectedState === "puerto rico" ||
            selectedState === "mexico");

    const upsOptions = [
        "UPS Ground",
        "UPS Next Day Air",
        "UPS 2nd Day Air",
        "UPS 3 Day Select",
        "UPS Next Day Air Saver",
        "UPS Next Day Air Early A.M.",
        "UPS 2nd Day Air A.M.",
    ];

    return (
        <Card className="hover:shadow-md transition-all">
            <CardHeader className="flex flex-row items-center gap-4">
                <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <Package className="h-5 w-5 text-primary" />
                </div>
                <div>
                    <CardTitle>Delivery Method</CardTitle>
                    <CardDescription>
                        Select your preferred shipping method
                    </CardDescription>
                </div>
            </CardHeader>
            <CardContent className="space-y-6">
                {!selectedAddress ? (
                    <div className="flex items-center justify-center p-6 text-muted-foreground border rounded-lg bg-muted/50">
                        Please select a shipping address first
                    </div>
                ) : isSpecialCountry ? (
                    <div className="space-y-6">
                        <div className="rounded-lg border bg-muted/50 p-4">
                            <p className="text-sm text-muted-foreground">
                                Prepaid delivery is used for shipments to{" "}
                                {selectedCountry === "mexico" ||
                                    selectedCountry === "puerto rico"
                                    ? selectedAddress?.country
                                    : selectedState === "puerto rico" ||
                                        selectedState === "mexico"
                                        ? selectedAddress?.state
                                        : ""}
                            </p>
                        </div>
                        <div className="space-y-4">
                            <h3 className="font-medium flex items-center gap-2">
                                <Package className="h-4 w-4" />
                                United Parcel Service
                            </h3>
                            <FormField
                                control={checkoutForm.control}
                                name="delivery_method"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <RadioGroup
                                                value={field.value}
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    checkoutForm.clearErrors("delivery_method");
                                                }}
                                                className="grid gap-2"
                                            >
                                                {upsOptions.map((option) => (
                                                    <Label
                                                        key={option}
                                                        className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary"
                                                    >
                                                        <RadioGroupItem value={option} id={option} />
                                                        <span>{option}</span>
                                                    </Label>
                                                ))}
                                            </RadioGroup>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
                ) : (
                    <div className="space-y-6">
                        <FormField
                            control={checkoutForm.control}
                            name="ship_collect"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>
                                        Ship Collect UPS<span className="text-destructive">*</span>
                                    </FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={(value) => {
                                                field.onChange(value === "yes");
                                                checkoutForm.clearErrors("ship_collect");
                                            }}
                                            value={
                                                field.value === true
                                                    ? "yes"
                                                    : field.value === false
                                                        ? "no"
                                                        : undefined
                                            }
                                            className="flex items-center gap-4"
                                        >
                                            <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                                                <RadioGroupItem value="yes" id="ship-collect-yes" />
                                                <span>Yes</span>
                                            </Label>
                                            <Label className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary">
                                                <RadioGroupItem value="no" id="ship-collect-no" />
                                                <span>No</span>
                                            </Label>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {isShipCollect && (
                            <FormField
                                control={checkoutForm.control}
                                name="ups_account_number"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>
                                            UPS Account Number
                                            <span className="text-destructive">*</span>
                                        </FormLabel>
                                        <FormControl>
                                            <Input
                                                placeholder="Enter your UPS account number"
                                                {...field}
                                                required={isShipCollect}
                                                onChange={(e) => {
                                                    field.onChange(e.target.value);
                                                    if (e.target.value.trim() !== "") {
                                                        checkoutForm.clearErrors("ups_account_number");
                                                    }
                                                }}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        )}

                        <div className="space-y-4">
                            <h3 className="font-medium flex items-center gap-2">
                                <Package className="h-4 w-4" />
                                United Parcel Service<span className="text-destructive">*</span>
                            </h3>
                            <FormField
                                control={checkoutForm.control}
                                name="delivery_method"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <RadioGroup
                                                value={field.value}
                                                onValueChange={(value) => {
                                                    field.onChange(value);
                                                    checkoutForm.clearErrors("delivery_method");
                                                }}
                                                className="grid gap-2"
                                            >
                                                {upsOptions.map((option) => (
                                                    <Label
                                                        key={option}
                                                        className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer [&:has([data-state=checked])]:border-primary"
                                                    >
                                                        <RadioGroupItem value={option} id={option} />
                                                        <span>{option}</span>
                                                    </Label>
                                                ))}
                                            </RadioGroup>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}