import ProductForm from "@/components/features/admin/add-new-product";
import AdminLayout from "@/components/features/admin/layout";
import { Button } from "@/components/ui/shadcn-button";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import useAuthStore from "@/stores/auth-store";
import { AlertTriangle } from "lucide-react";
import Head from "next/head";
import Link from "next/link";

export default function NewProduct() {
    const permissions = useAuthStore((state) => state.permissions);
    const hasCreatePermission = checkUserHasPermission(permissions, "create:products");

    return <AdminLayout>
        <Head>
            <title>Add New Product</title>
        </Head>
        <div className="flex flex-col gap-4">
            {
                hasCreatePermission ? (
                    <ProductForm />
                ) : (
                    <div className="flex flex-col gap-4 items-center justify-center">
                        <div className="flex flex-col gap-2 items-center justify-center">
                            <AlertTriangle className="w-10 h-10 text-red-500" />
                            <h1 className="text-2xl font-bold">You do not have permission to create products</h1>
                            <p className="text-sm text-muted-foreground">
                                Please contact your administrator to get access to this page.
                            </p>
                            <Button variant="outline" asChild>
                                <Link href="/admin/products">
                                    Go back to products
                                </Link>
                            </Button>
                        </div>
                    </div>
                )
            }
        </div>
    </AdminLayout>
}
