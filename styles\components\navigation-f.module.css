.AccordionRoot {
  border-radius: 6px;
  width: 300px;
  background-color: var(--mauve-6);
  box-shadow: 0 2px 10px var(--black-a4);
}

.AccordionItem {
  overflow: hidden;
  margin-top: 1px;
}

.AccordionItem:first-child {
  margin-top: 0;
}

.AccordionContent {
  @apply p-3;
  overflow: hidden;
}
.AccordionContent[data-state="open"] {
  animation: slideDown 300ms cubic-bezier(0.87, 0, 0.13, 1);
}
.AccordionContent[data-state="closed"] {
  animation: slideUp 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

.AccordionChevron {
  @apply flex-none ml-3;
}

.AccordionTrigger[data-state="open"] > .AccordionChevron {
  transform: rotate(180deg);
}

@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.mainNavLink {
  position: relative;
}

.mainNavLink::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 1px;
  background-color: theme("colors.primary");
  transition: all 0.3s ease;
}

.mainNavLink:hover::after {
  width: 100%;
  left: 0;
}

.dropdownItem {
  transition: all 0.3s ease;
}

.dropdownItem:hover {
  background-color: rgba(1, 84, 162, 0.1);
}

#google_translate_element {
  width: 100%;
}

#google_translate_element .goog-te-combo {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
}
