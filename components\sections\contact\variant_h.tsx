import { PortableTextComponents } from "@portabletext/react";
import { But<PERSON> } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Form } from "@stackshift-ui/form";
import { Heading } from "@stackshift-ui/heading";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { SocialIcons } from "@stackshift-ui/social-icons";
import React, { useState } from "react";
import { IoLocationSharp } from "react-icons/io5";
import { FaPhoneAlt, FaFax, FaBusinessTime } from "react-icons/fa";
import { MdEmail } from "react-icons/md";
import { ContactProps } from ".";
import { thankYouPageLink } from "../../../helper";

export default function Contact_H({
  officeInformation,
  contactEmail,
  contactNumber,
  officeHours,
  contactFaxNumber,
  socialLinks,
  form,
  title,
}: ContactProps) {
  // Add form state management
  const [formData, setFormData] = useState<Record<string, string>>({});

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  return (
    <Section className="pt-10 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Heading type="h2" className="text-center mb-10">
          {title}
        </Heading>

        <div className="flex flex-col md:flex-row bg-transparent rounded-xl overflow-hidden shadow-lg mx-auto">
          {/* Left sidebar with contact information */}
          <div className="w-full md:w-80 bg-primary p-8 flex flex-col rounded-l-xl">
            <div className="mb-10">
              {/* <h1 className="text-2xl font-medium text-white">Contact Us</h1> */}
            </div>

            <h2 className="text-xl font-medium text-white mb-8">
              Contact Information
            </h2>

            {/* Contact information */}
            <div className="space-y-6">
              {officeInformation && (
                <div className="flex items-start">
                  <IoLocationSharp className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Office</h3>
                    <p className="text-white/80">{officeInformation}</p>
                  </div>
                </div>
              )}

              {contactNumber && (
                <div className="flex items-start">
                  <FaPhoneAlt className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Phone</h3>
                    <Link
                      href={`tel:${contactNumber}`}
                      className="text-white/80 hover:text-white"
                    >
                      {contactNumber}
                    </Link>
                  </div>
                </div>
              )}

              {contactFaxNumber && (
                <div className="flex items-start">
                  <FaFax className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Fax</h3>
                    <p className="text-white/80">{contactFaxNumber}</p>
                  </div>
                </div>
              )}

              {contactEmail && (
                <div className="flex items-start">
                  <MdEmail className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">Email</h3>
                    <Link
                      href={`mailto:${contactEmail}`}
                      className="text-white/80 hover:text-white"
                    >
                      {contactEmail}
                    </Link>
                  </div>
                </div>
              )}

              {officeHours && (
                <div className="flex items-start">
                  <FaBusinessTime className="w-5 h-5 text-white mr-4 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="text-white font-medium mb-1">
                      Office Hours
                    </h3>
                    <p className="text-white/80">{officeHours}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Social links */}
            {socialLinks && socialLinks.length > 0 && (
              <div className="mt-8">
                <h3 className="text-white font-medium mb-4">Connect With Us</h3>
                <div className="flex space-x-4">
                  {socialLinks.map((social) => (
                    <Link
                      key={social?._key}
                      aria-label={
                        social?.socialMedia || social?.socialMediaPlatform || ""
                      }
                      href={social?.socialMediaLink ?? "/page-not-found"}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-white/80"
                    >
                      <SocialIcons social={social.socialMedia as any} />
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Main content area - Contact Form */}
          <div className="flex-1 p-8 md:p-12 bg-white/5 backdrop-blur-sm rounded-r-xl">
            <div className="max-w-2xl mx-auto">
              {/* Form Header */}
              {form && (
                <div className="mb-14">
                  {form.name && (
                    <h2 className="text-3xl font-medium text-gray-800 mb-2">
                      {form.name}
                    </h2>
                  )}
                  {form.subtitle && (
                    <p className="text-base text-gray-800 mb-6">
                      {form.subtitle}
                    </p>
                  )}
                </div>
              )}

              {/* Contact Form */}
              {form?.fields && (
                <Form
                  id={form?.id || ""}
                  name="Contact-Form"
                  thankyouPage={thankYouPageLink(form?.thankYouPage)}
                >
                  {/* Form fields */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    {form.fields.map((field, index) => {
                      // Full width for textarea fields
                      const isFullWidth = field.type === "textarea";
                      const fieldName = field.name || `field-${index}`;

                      return (
                        <div
                          key={index}
                          className={isFullWidth ? "sm:col-span-2" : ""}
                        >
                          <label
                            htmlFor={fieldName}
                            className="block text-left text-md mb-2"
                          >
                            {field?.isRequired && (
                              <span className="text-red-600">*</span>
                            )}{" "}
                            {field.name}
                          </label>

                          {field.type === "textarea" ? (
                            <textarea
                              id={fieldName}
                              name={fieldName}
                              placeholder={field.placeholder}
                              required={field?.isRequired}
                              rows={4}
                              value={formData[fieldName] || ""}
                              onChange={handleInputChange}
                              className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                            ></textarea>
                          ) : field.type === "inputEmail" ? (
                            <input
                              type="email"
                              id={fieldName}
                              name={fieldName}
                              placeholder={field.placeholder}
                              required={field?.isRequired}
                              value={formData[fieldName] || ""}
                              onChange={handleInputChange}
                              className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                            />
                          ) : (
                            <input
                              type="text"
                              id={fieldName}
                              name={fieldName}
                              placeholder={field.placeholder}
                              required={field?.isRequired}
                              value={formData[fieldName] || ""}
                              onChange={handleInputChange}
                              className="w-full p-2 border rounded bg-white focus:outline-2 focus:ring-1 focus:outline-primary placeholder-transparent"
                            />
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Submit Button */}
                  <div className="border-t border-gray-200 pt-8 mt-8">
                    <div className="flex justify-end">
                      <Button
                        as="button"
                        type="submit"
                        variant="maxtonPrimary"
                        ariaLabel={form?.buttonLabel || "Submit"}
                      >
                        {form?.buttonLabel || "Submit"}
                      </Button>
                    </div>
                  </div>
                  {/* reCAPTCHA - moved outside the form's content div for better visibility */}
                  <div className="mt-6">
                    <div className="webriq-recaptcha" />
                  </div>
                </Form>
              )}
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
}

// block styling as props to `components` of the PortableText component
const blockCustomization: PortableTextComponents = {
  marks: {
    internalLink: ({ children, value }) => (
      <Link
        aria-label={value.href ?? "internal link"}
        style={{ color: "red" }}
        href={value.slug.current}
      >
        {children}
      </Link>
    ),
    link: ({ children, value }) =>
      value.blank ? (
        <Link
          aria-label={value.href ?? "external link"}
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      ) : (
        <Link
          aria-label={value.href ?? "external link"}
          style={{ color: "blue" }}
          href={value.href}
        >
          {children}
        </Link>
      ),
  },
};

export { Contact_H };
