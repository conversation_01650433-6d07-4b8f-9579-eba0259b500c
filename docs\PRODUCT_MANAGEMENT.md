# Product Management

## Overview
The product management system allows administrators to create, view, edit, and delete products. The system includes role-based permissions to ensure only authorized users can perform specific actions.

## Bulk Price Updates

### Overview
The bulk price update feature allows administrators to update product group prices in bulk using a CSV file. The system uses a long format where each row represents one price for one product in one price group.

### CSV Format Requirements
The CSV file must use the following format:
- **Header**: `Part_Nbr,PC,Qty,Price,Weight`
- **Part_Nbr**: Product SKU/Code (required)
- **PC**: Price group name (required) - must match existing price groups
- **Qty**: Quantity (optional, ignored during processing)
- **Price**: Price value (required) - must be a non-negative number
- **Weight**: Weight (optional, ignored during processing)

### Format Structure
- Each row represents one price for one product in one price group
- Multiple rows per product are expected (one per price group)
- Empty price values are allowed for preview but excluded from processing

### Example CSV Format
```csv
Part_Nbr,PC,Qty,Price,Weight
00041,LIST,,30.0,
00041,FILCV,,32.5,
00041,CT1,,28.0,
00042,CUSTOM_GROUP,,45.0,
00042,NEW_TIER,,47.5,
00042,special_price,,42.0,
```

*Note: Group names are case-insensitive and can be any custom name. They will be converted to uppercase in the system.*

### Validation Rules
- Part_Nbr and PC are required fields
- PC values must match existing price groups in the system (case-insensitive)
- PC values are automatically converted to uppercase in the output
- Price values must be non-negative numbers
- Duplicate headers are not allowed
- Each row must have the correct number of columns

### Features
- File upload with drag-and-drop support
- Real-time CSV validation with detailed error reporting
- Edit mode for direct CSV text editing
- Preview mode showing parsed data
- JSON preview showing API format
- Template download with example format

### Permission Requirements
- Users must have the `update:products` permission to access bulk price update functionality
- Users without this permission will see an access denied screen

### Implementation Details
The bulk price update functionality is implemented in `pages/admin/products/bulk-price-update.tsx` and includes:

- CSV parsing functions that handle the new long format
- Validation logic for required headers and data types
- Price group validation against existing groups from the API
- Data transformation for API submission
- User interface components for file upload, editing, and preview

## Product Creation
Administrators with the appropriate permissions can create new products using the product creation form located at `/admin/products/new`. The form allows for comprehensive product information input, including:

- Basic product details
- Pricing information
- Inventory management
- Product categorization
- Product images and media

### Permission Requirements
- Users must have the `create:products` permission to access the product creation functionality
- Users without this permission will see an error message and a link to return to the products list

### Implementation Details
The product creation page is implemented in `pages/admin/products/new.tsx` and uses the following components:

- `ProductForm` from `@/components/features/admin/add-new-product`
- Permission checks via `checkUserHasPermission` from `@/middlewares/auth-middleware`
- User permissions stored in `useAuthStore`

## User Interface

### Successful Access
When a user with the appropriate permissions accesses the page, they will see:
- A product form with all necessary fields to create a new product

### Access Denied
When a user without the appropriate permissions accesses the page, they will see:
- A warning icon
- Text informing them they don't have permission
- A suggestion to contact their administrator
- A button to return to the products list

## Technical Implementation
The page checks for user permissions using the `checkUserHasPermission` function, which validates if the user has the required `create:products` permission. This permission is stored in the auth store and is typically set during user login based on their role.

The product form component encapsulates all the form functionality, including validation, submission handling, and error management.