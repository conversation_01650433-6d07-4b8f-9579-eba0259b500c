import { Card } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";
import { ButtonProps } from "../header";
import { Button, Link } from "components/ui";
import { PortableText } from "@portabletext/react";

import { MyPortableTextComponents } from "types";
import { PortableTextBlock } from "@sanity/types";
import { IoDocumentOutline } from "react-icons/io5";
import { urlFor } from "lib/sanity";
import ValveCalculator from "../../calculator/sideCalculator";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-900 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary-foreground hover:text-secondary-foreground underline text-primary"
        href={value?.href}
        target={`${
          value?.href?.includes("wwww") || value?.href?.includes("https")
            ? "_blank"
            : ""
        }`}
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Features_T({
  title,
  firstColumn,
  featuredItems,
  hasCalculator,
}: FeaturesProps) {
  return (
    <Section className="pt-16 pb-20 bg-background">
      <Container maxWidth={1280}>
        {/* TitleAndDescription first on small screens, second on larger screens */}
        <div className="relative lg:-mt-48 w-full lg:w-2/3 lg:pl-10 ml-auto mb-10 lg:mb-20">
          <CaptionAndTitleSection title={title} firstColumn={firstColumn} />
        </div>
        <Flex direction="col" justify="between" className="lg:flex-row">
          <div
            className={`relative w-full  ${
              hasCalculator && "lg:w-[70%] lg:pr-8 lg:border-r border-gray-300"
            }`}
          >
            <Flex direction="col" justify="between" className="gap-10 ">
              {/* firstColumn below on small screens, left on larger screens */}
              <Flex direction="col" gap={8} className="w-full items-center">
                <FeatureItems features={featuredItems} />
              </Flex>
            </Flex>
          </div>
          {hasCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-4 lg:pl-8">
              <ValveCalculator />
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  title,
  firstColumn,
}: {
  title?: string;
  firstColumn?: PortableTextBlock[];
}) {
  return (
    <div className="w-full lg:bg-white lg:rounded-lg lg:shadow-xl md:p-8">
      {title && (
        <Heading type="h2" className="text-xl lg:text-2xl mb-3">
          {title}
        </Heading>
      )}
      {firstColumn && (
        <div>
          <PortableText
            value={firstColumn}
            components={textComponentBlockStyling}
            onMissingComponent={false}
          />
        </div>
      )}
    </div>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <div className="  flex flex-col gap-y-4">
      <Heading type="h3" className="text-xl lg:text-4xl mb-3 uppercase mx-auto">
        Sections
      </Heading>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature) => (
          <FeatureItem feature={feature} key={feature._key} />
        ))}
      </div>
    </div>
  );
}

function FeatureItem({ feature }: { feature: FeaturedItem }) {
  const pdfUrl = feature?.pdfFile?.asset?.url;
  const zipUrl = feature?.zipFile?.asset?.url;

  const handleDownload = (event: React.MouseEvent) => {
    if (zipUrl) {
      event.preventDefault(); // Prevent default link behavior
      const link = document.createElement("a");
      link.href = zipUrl;
      link.setAttribute("download", "");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const CardWrapper = ({ children }: { children: React.ReactNode }) => {
    if (zipUrl) {
      return (
        <button onClick={handleDownload} className="w-full text-left">
          {children}
        </button>
      );
    }

    return (
      <Link
        href={pdfUrl || "#"}
        target={pdfUrl ? "_blank" : undefined}
        rel={pdfUrl ? "noopener noreferrer" : undefined}
        className="block w-full px-4 sm:px-0"
      >
        {children}
      </Link>
    );
  };

  const downloadImage = (
    <Image
      src={"/assets/elements/downloads/download_circle.png"}
      alt={"Download icon"}
      width={32}
      height={32}
    />
  );
  return (
    <CardWrapper>
      <Card
        variant="horizontalCard"
        icon={<IoDocumentOutline />}
        downloadImage={downloadImage}
        borderRadius="md"
        title={feature?.title}
        description={feature?.description}
      />
    </CardWrapper>
  );
}

export { Features_T };
