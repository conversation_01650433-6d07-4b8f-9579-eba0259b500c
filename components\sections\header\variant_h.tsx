import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React, { useState, useEffect } from "react";
import { ButtonProps, HeaderProps } from ".";

import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";
import { urlFor } from "lib/sanity";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-10 font-body text-base text-gray-500 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary-foreground hover:text-secondary-foreground"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Header_H({
  title,
  subtitle,
  description,
  firstColumn,
  images,
  imagesCopy,
}: HeaderProps): JSX.Element {
  return (
    <Section className="pt-10 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex
          direction="col"
          gap={8}
          justify="between"
          className="lg:flex-row-reverse"
        >
          {/* TitleAndDescription first on small screens, second on larger screens */}
          <div className="relative  lg:-mt-48 w-full lg:w-2/3 lg:pl-10 ">
            <TitleAndDescription
              title={title}
              subtitle={subtitle}
              description={description}
              images={images}
              imagesCopy={imagesCopy}
            />
          </div>

          {/* firstColumn below on small screens, left on larger screens */}
          <Flex direction="col" gap={8} className="w-full lg:basis-1/2">
            {firstColumn && (
              <div>
                <PortableText
                  value={firstColumn}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            )}
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function TitleAndDescription({
  title,
  subtitle,
  description,
  images,
  imagesCopy,
}: {
  title?: string;
  subtitle?: string;
  description?: string;
  images?: any[];
  imagesCopy?: any[];
}) {
  return (
    <div className="w-full lg:bg-white lg:rounded-lg lg:shadow-xl md:p-8">
      {title && (
        <Heading type="h2" className="text-2xl lg:text-3xl">
          {title}
        </Heading>
      )}
      {description && (
        <Text muted className="mt-3 !text-base leading-loose">
          {description}
        </Text>
      )}

      {/* Images Section */}
      {images && images.length > 0 && (
        <Flex
          gap={4}
          className="my-6 grid grid-cols-2 gap-4 md:grid-cols-4 md:flex md:flex-row md:justify-center md:items-center"
        >
          {images.map((image, index) => (
            <Image
              key={index}
              src={image?.image}
              width={150}
              height={150}
              alt={image.alt || "image"}
              className="object-cover w-[120px] md:w-[150px] h-auto"
              quality={100}
            />
          ))}
        </Flex>
      )}

      {subtitle && (
        <div>
          <Heading type="h3" className="text-2xl lg:text-2xl">
            {subtitle}
          </Heading>
          {imagesCopy && imagesCopy.length > 0 && (
            <div className="mt-6 grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4 md:flex md:flex-row md:justify-center md:items-center">
              {imagesCopy.map((image, index) => (
                <Image
                  key={index}
                  src={image?.image}
                  width={100}
                  height={100}
                  alt={image.alt || "image"}
                  className="object-cover w-[120px] md:w-[150px] h-auto"
                  quality={100}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export { Header_H };
