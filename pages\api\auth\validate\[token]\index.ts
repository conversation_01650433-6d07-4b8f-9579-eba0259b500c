import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: handler,
});

export interface ValidateTokenResponse {
  valid?: boolean;
  error?: string;
}

async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ValidateTokenResponse>
) {
  const token = req.query.token as string;

  if (!token) {
    return res.status(400).json({
      error: "Missing token",
    });
  }

  if (z.string().jwt({ alg: "HS256" }).safeParse(token).error) {
    return res.status(400).json({
      error: "Invalid token",
    });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { error } = await supabaseAdminClient.auth.getUser(token);

  if (error) {
    return res.status(400).json({
      error: "Invalid token",
    });
  }

  return res.status(200).json({
    valid: true,
  });
}
