import { <PERSON><PERSON> } from "@stackshift-ui/button";
import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Input } from "@stackshift-ui/input";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { format } from "date-fns";
import React, { useEffect } from "react";
import { BlogProps } from "@stackshift-ui/blog";
import { SanityBody, SanityImage } from "types";
import { AiFillCaretRight } from "react-icons/ai";
import { allPostsQuery } from "pages/api/query";
import { sanityClient } from "lib/sanity.client";
import { urlFor } from "@/lib/sanity";
import { getImageDimensions } from "@sanity/asset-utils";

// Define more accurate types for blog post data
interface Author {
  _id: string;
  name?: string;
  slug?: {
    current?: string;
  };
}

interface Category {
  _id: string;
  title?: string;
  slug?: {
    current?: string;
  };
}

interface SanityPost extends SanityBody {
  title?: string;
  slug?: {
    _type: string;
    current: string;
  };
  excerpt?: string;
  publishedAt?: string;
  mainImage?: SanityImage;
  authors?: Author[];
  categories?: Category[];
  link?: string;
}

export default function Blog_E({ subtitle, title }: BlogProps) {
  const [allPosts, setAllPosts] = React.useState<SanityPost[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const blogsPerPage = 10;

  // Fetch all posts from Sanity
  useEffect(() => {
    async function fetchPosts() {
      setLoading(true);
      try {
        const posts = await sanityClient.fetch(allPostsQuery);
        setAllPosts(posts);
      } catch (err) {
        console.error("Error fetching posts:", err);
      } finally {
        setLoading(false);
      }
    }

    fetchPosts();
  }, []);

  // Sort posts by publishedAt date in descending order
  const sortedPosts = React.useMemo(() => {
    return [...(allPosts ?? [])].sort((a, b) => {
      return (
        new Date(b?.publishedAt ?? 0).getTime() -
        new Date(a?.publishedAt ?? 0).getTime()
      );
    });
  }, [allPosts]);

  //Pagination
  const indexOfLastPost = currentPage * blogsPerPage;
  const indexOfFirstPost = indexOfLastPost - blogsPerPage;
  const currentPosts = sortedPosts?.slice(indexOfFirstPost, indexOfLastPost);

  //Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  return (
    <Section className="py-20 bg-background">
      <Container maxWidth={1280}>
        <SubtitleAndTitleText subtitle={subtitle} title={title} />
        <Flex wrap>
          {loading ? (
            <LoadingState />
          ) : !currentPosts?.length ? (
            <NoPostsMessage />
          ) : (
            <PostItems currentPosts={currentPosts} />
          )}
        </Flex>
        <Pagination
          blogsPerPage={blogsPerPage}
          totalBlogs={sortedPosts?.length}
          paginate={paginate}
          currentPage={currentPage}
        />
      </Container>
    </Section>
  );
}

function LoadingState() {
  return (
    <div className="w-full px-3 py-10 text-center">
      <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
      <p className="mt-4 text-gray-600">Loading blog posts...</p>
    </div>
  );
}

function NoPostsMessage({ message = "No post available." }) {
  return (
    <div className="w-full px-3 lg:w-3/4 font-medium text-lg">{message}</div>
  );
}

function SubtitleAndTitleText({
  subtitle,
  title,
}: {
  subtitle?: string;
  title?: string;
}) {
  return (
    <div className="w-full mb-16">
      {subtitle ? (
        <Text weight={"bold"} className="text-secondary">
          {subtitle}
        </Text>
      ) : null}
      {title ? <Heading fontSize="3xl">{title}</Heading> : null}
    </div>
  );
}

function PostItems({ currentPosts }: { currentPosts: SanityPost[] }) {
  if (!currentPosts) return null;

  return (
    <div className="w-full px-3 grid grid-cols-1 gap-8">
      {currentPosts?.map((post, index) => <PostItem post={post} key={index} />)}
    </div>
  );
}

function PostItem({ post }: { post: SanityPost }) {
  if (!post) return null;

  return (
    <Flex
      wrap
      className="mb-8 lg:mb-6 bg-white shadow overflow-hidden group h-full"
    >
      {post?.mainImage && (
        <div className="w-full lg:w-1/2 lg:h-full relative overflow-hidden">
          <Link
            href={`/${post?.slug?.current ?? "page-not-added"}`}
            className="block h-full"
          >
            <div className="lg:absolute lg:inset-0 h-64 lg:h-full">
              <Image
                className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                src={`${urlFor(post.mainImage)}`}
                sizes="(max-width: 768px) 100vw, 50vw"
                width={getImageDimensions(urlFor(post.mainImage))?.width}
                height={getImageDimensions(urlFor(post.mainImage))?.height}
                alt={`blog-image-${post?.title}`}
                style={{ objectFit: "cover" }}
              />
            </div>
          </Link>
        </div>
      )}
      <div
        className={`w-full px-7 py-6 ${
          post?.mainImage ? "lg:w-1/2" : ""
        } flex flex-col h-full`}
      >
        {post?.title && (
          <Link
            aria-label={post?.title}
            className="block mb-3 text-[22px] font-bold hover:text-primary font-heading"
            href={`/${post?.slug?.current ?? "page-not-added"}`}
          >
            {/* {post?.title.length > 120
              ? post?.title?.substring(0, 120) + "..."
              : post?.title} */}
            {post?.title}
          </Link>
        )}
        <Flex wrap align="center" gap={2} className="mb-4">
          {/* Category with icon */}
          {post?.categories && post.categories.length > 0 && (
            <div className="flex items-center mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                />
              </svg>
              <div className="flex flex-wrap items-center">
                {post.categories.map((category, index, { length }) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span className="mx-1 text-gray-500">,</span>}
                    <Link
                      href={`/category/${category.slug?.current || ""}`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Text className="text-gray-600 text-sm hover:text-primary !font-medium transition-colors duration-200">
                        {category?.title}
                      </Text>
                    </Link>
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}

          {/* Author with icon */}
          {post?.authors && post.authors.length > 0 && (
            <div className="flex items-center mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
              <div className="flex flex-wrap items-center">
                {post.authors.map((author, index, { length }) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span className="mx-1 text-gray-500">,</span>}
                    <Link href={`/author/${author.slug?.current}`}>
                      <Text className="text-gray-600 text-sm hover:text-primary !font-medium transition-colors duration-200">
                        {author.name}
                      </Text>
                    </Link>
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}

          {/* Published date with icon */}
          {post?.publishedAt && (
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
              <Link
                href={`/archive/${format(
                  new Date(post.publishedAt),
                  "yyyy"
                )}/${format(new Date(post.publishedAt), "MM")}`}
                onClick={(e) => e.stopPropagation()}
              >
                <Text className="text-gray-600 text-sm hover:text-primary !font-medium transition-colors duration-200">
                  {format(new Date(post?.publishedAt), "MMMM dd, yyyy")}
                </Text>
              </Link>
            </div>
          )}
        </Flex>

        <div className="flex-grow">
          {post?.excerpt ? (
            <Text className="text-gray-800 mb-4 text-base">
              {post?.excerpt.length > 400
                ? post?.excerpt.substring(0, 400) + "..."
                : post?.excerpt}
            </Text>
          ) : null}
        </div>

        <Link
          aria-label={post?.title}
          className="w-[100px] relative text-sm font-bold text-black font-heading flex flex-row items-center gap-2 pt-3 group mt-auto"
          href={`/${post?.slug?.current ?? "page-not-added"}`}
        >
          <div className="flex items-center gap-2">
            Read more
            <AiFillCaretRight />
          </div>
          <span className="absolute bottom-[-4px] left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
        </Link>
      </div>
    </Flex>
  );
}

interface PaginationProps {
  blogsPerPage: number;
  totalBlogs: number;
  paginate: (pageNumber: number) => void;
  currentPage: number;
}

function Pagination({
  blogsPerPage,
  totalBlogs,
  paginate,
  currentPage,
}: PaginationProps) {
  if (!blogsPerPage || totalBlogs === 0) return null;

  const totalPages = Math.ceil(totalBlogs / blogsPerPage);

  // Logic for which page numbers to display
  const getPageNumbers = () => {
    // Always show first and last page
    const firstPage = 1;
    const lastPage = totalPages;

    // Number of pages to show on each side of current page
    const siblingsCount = 1;

    // Calculate range of pages to show
    let startPage = Math.max(firstPage, currentPage - siblingsCount);
    let endPage = Math.min(lastPage, currentPage + siblingsCount);

    // Adjust if we're near the beginning or end
    if (currentPage <= siblingsCount + 1) {
      endPage = Math.min(firstPage + siblingsCount * 2, lastPage);
    }

    if (currentPage >= lastPage - siblingsCount) {
      startPage = Math.max(lastPage - siblingsCount * 2, firstPage);
    }

    // Build the array of page numbers to show
    const pageNumbers: (number | string)[] = [];

    // Add first page
    if (startPage > firstPage) {
      pageNumbers.push(firstPage);
      // Add ellipsis if there's a gap
      if (startPage > firstPage + 1) {
        pageNumbers.push("...");
      }
    }

    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Add last page
    if (endPage < lastPage) {
      // Add ellipsis if there's a gap
      if (endPage < lastPage - 1) {
        pageNumbers.push("...");
      }
      pageNumbers.push(lastPage);
    }

    return pageNumbers;
  };

  const pageNumbers = getPageNumbers();

  return (
    <nav className="mt-8" aria-label="Pagination">
      <div className="flex flex-wrap gap-2 justify-center items-center">
        {/* Previous button */}
        {currentPage > 1 && (
          <Button
            variant="unstyled"
            as="button"
            ariaLabel="Previous page"
            className="bg-white hover:bg-secondary-foreground hover:text-gray-500 text-primary font-medium py-2 px-4 border border-primary rounded-global focus:outline-none"
            onClick={() => paginate(currentPage - 1)}
          >
            &larr;
          </Button>
        )}

        {/* Page numbers with ellipsis */}
        {pageNumbers.map((item, index) =>
          typeof item === "number" ? (
            <Button
              variant="unstyled"
              as="button"
              ariaLabel={`Page ${item}`}
              key={index}
              className={`${
                currentPage === item
                  ? "bg-primary text-white"
                  : "bg-white hover:bg-secondary-foreground hover:text-gray-500 text-primary"
              } font-medium py-2 px-4 border border-primary rounded-global focus:outline-none transition-colors duration-200`}
              onClick={() => paginate(item)}
            >
              {item}
            </Button>
          ) : (
            <span key={index} className="px-2 text-gray-500">
              {item}
            </span>
          )
        )}

        {/* Next button */}
        {currentPage < totalPages && (
          <Button
            variant="unstyled"
            as="button"
            ariaLabel="Next page"
            className="bg-white hover:bg-secondary-foreground hover:text-gray-500 text-primary font-medium py-2 px-4 border border-primary rounded-global focus:outline-none"
            onClick={() => paginate(currentPage + 1)}
          >
            &rarr;
          </Button>
        )}
      </div>
    </nav>
  );
}

export { Blog_E };
