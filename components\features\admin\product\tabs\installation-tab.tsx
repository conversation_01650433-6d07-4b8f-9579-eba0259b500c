import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/shadcn-button"
import { FileIcon, X } from "lucide-react"

interface InstallationTabProps {
    installationFile: File | null
    handleInstallationFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    setInstallationFile: React.Dispatch<React.SetStateAction<File | null>>
}

export default function InstallationTab({
    installationFile,
    handleInstallationFileChange,
    setInstallationFile
}: InstallationTabProps) {
    return (
        <div className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="installation-file">Installation Instructions (PDF)</Label>
                <Input
                    id="installation-file"
                    type="file"
                    accept=".pdf,application/pdf"
                    onChange={handleInstallationFileChange}
                />
                {installationFile && (
                    <div className="mt-2 flex items-center gap-2">
                        <FileIcon className="h-4 w-4" />
                        <span className="text-sm">{installationFile.name}</span>
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => setInstallationFile(null)}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                )}
            </div>

            <p className="text-sm text-muted-foreground mt-1">
                Upload installation instructions as a PDF file. Maximum size: 5MB.
            </p>
        </div>
    )
} 