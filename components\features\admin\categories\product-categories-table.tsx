import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable, DataTableSkeleton } from "@/components/ui/data-table";
import { useGetAllProductsQuery } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { ColumnDef, ColumnFiltersState, SortingState, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, getSortedRowModel, useReactTable } from "@tanstack/react-table";
import { useState } from "react";
import { ConfigureProductCategoriesDialog } from "./configure-product-categories-dialog";

const productCategoryColumns: ColumnDef<any>[] = [
    {
        header: "Product Name",
        accessorKey: "name",
    },
    {
        header: "Categories",
        accessorKey: "product_categories",
        cell: ({ row }) => {
            const categories = row.original.product_categories;
            return categories?.map((cat: any) => cat.category_data.name).join(", ") || "No categories";
        }
    },
    {
        header: "SKU",
        accessorKey: "sku",
    },
    {
        header: "Created",
        accessorKey: "created_at",
        cell: ({ row }) => {
            const date = new Date(row.original.created_at);
            return <div>{date.toISOString().split('T')[0]}</div>;
        }
    },
    {
        id: "actions",
        cell: ({ row }) => {
            return (
                <div className="flex items-center gap-2">
                    <ConfigureProductCategoriesDialog product={row.original} />
                </div>
            );
        },
    },
];

export function ProductCategoriesDataTable() {
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

    const token = useAuthStore((state) => state.token);
    const { data, isLoading } = useGetAllProductsQuery(page, limit, token);

    const table = useReactTable({
        data: data?.products ?? [],
        columns: productCategoryColumns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        manualPagination: true,
        pageCount: Math.ceil((data?.total ?? 0) / limit),
        onPaginationChange: updater => {
            if (typeof updater === 'function') {
                const newPagination = updater({
                    pageIndex: page - 1,
                    pageSize: limit,
                });
                setPage(newPagination.pageIndex + 1);
            } else {
                setPage(updater.pageIndex + 1);
            }
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        state: {
            sorting,
            columnFilters,
            pagination: {
                pageIndex: page - 1,
                pageSize: limit,
            },
        },
    });

    if (isLoading) return <DataTableSkeleton />;

    return (
        <Card className="container mx-auto">
            <CardHeader>
                <div>
                    <h3 className="text-lg font-bold">Product Categories</h3>
                    <p className="text-sm text-muted-foreground">
                        View all products and their category associations.
                    </p>
                </div>
            </CardHeader>
            <CardContent>
                <DataTable
                    columns={productCategoryColumns}
                    data={data?.products ?? []}
                    filterColumn="name"
                    filterPlaceholder="Search products..."
                    table={table}
                />
            </CardContent>
        </Card>
    );
} 