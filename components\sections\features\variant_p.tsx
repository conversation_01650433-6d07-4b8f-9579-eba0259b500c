import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { PortableText } from "@portabletext/react";
import React from "react";
import { FeaturesProps } from ".";
import { MyPortableTextComponents } from "@/types";
import { getImageDimensions } from "@sanity/asset-utils";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 font-bold text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return (
        <h2 className="mb-4 text-2xl font-semibold text-primary">{children}</h2>
      );
    },
    h3: ({ children }) => {
      return (
        <h3 className="mb-4 text-lg font-medium text-gray-900">{children}</h3>
      );
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-lg font-medium leading-loose text-gray-900">
          {children}
        </h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-700 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-secondary px-14 border-l-4 border-primary pl-4">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre
        className="bg-gray-100 p-4 rounded-lg overflow-x-auto"
        data-language={value.language}
      >
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-700 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="pl-10 mb-6 leading-loose text-gray-700 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-700">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => (
      <strong className="font-semibold">{children}</strong>
    ),
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => (
      <code className="px-2 py-1 bg-gray-100 rounded text-primary">
        {children}
      </code>
    ),
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/80 underline transition-colors"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);
      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex
          direction="col"
          gap={4}
          className="mt-6 justify-center flex-wrap md:flex-row"
        >
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full md:w-[300px] h-auto mb-6 md:mb-10 rounded-lg shadow-md transition-transform hover:scale-105"
              width={300}
              height={300}
              src={image?.image}
              alt={image?.alt ?? image?.image?.asset?._ref ?? "Feature image"}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Features_P({
  title,
  featuredItems,
  firstColumn,
}: FeaturesProps) {
  return (
    <Section className="py-12 md:py-20 bg-background">
      <Container maxWidth={1280} className="px-4 md:px-6">
        <Flex
          direction="col"
          gap={6}
          className="items-start lg:flex-row md:gap-8 lg:gap-12"
        >
          {/* Images Column */}
          <div className="w-full lg:w-1/2 space-y-6 md:space-y-8">
            {featuredItems?.map((item, index) => (
              <div
                key={index}
                className={`
                  ${index === 0 ? "text-center" : ""}
                  transition-all duration-300 hover:transform hover:scale-105
                `}
              >
                {item.mainImage?.image && (
                  <Image
                    src={item?.mainImage?.image}
                    alt={item.mainImage?.alt || `Feature ${index + 1}`}
                    className={`
                      ${
                        index === 0
                          ? "w-2/3 md:w-1/2 lg:w-1/3 mx-auto"
                          : "w-full"
                      } 
                      object-contain rounded-lg shadow-lg
                    `}
                    width={getImageDimensions(item?.mainImage?.image)?.width}
                    height={getImageDimensions(item?.mainImage?.image)?.height}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Content Column */}
          <div className="w-full lg:w-1/2 bg-white p-6 rounded-lg shadow-sm">
            {firstColumn && (
              <div className="prose max-w-none prose-lg prose-headings:text-primary prose-p:text-gray-700">
                <PortableText
                  value={firstColumn}
                  components={textComponentBlockStyling}
                  onMissingComponent={false}
                />
              </div>
            )}
          </div>
        </Flex>
      </Container>
    </Section>
  );
}

export { Features_P };
