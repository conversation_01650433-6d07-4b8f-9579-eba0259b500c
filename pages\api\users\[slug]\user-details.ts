import { NextApiRequestWithUserContext } from '@/middlewares';
import { checkAdmin } from '@/middlewares/auth-middleware';
import { matchRoute } from '@/middlewares/match-route';
import { createSupabaseAdminClient } from '@/supabase/index';
import { NextApiResponse } from 'next';
import { z } from 'zod';

export default checkAdmin(
  matchRoute({
    PUT: editUserDetailsHandler
  })
);

export const editUserDetailsSchema = z.object({
  // user details
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  notes: z.string().optional(),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  company_name: z.string().optional(),
  phone: z.string().optional().or(z.literal('')),
  primary_contact_name: z.string().optional(),
  shipping_notes: z.string().optional(),
  // business details
  buyer_name: z.string().optional(),
  business_type: z.string().optional(),
  account_payable_contact: z.string().optional(),
  account_payable_phone: z.string().optional().or(z.literal('')),
  authorized_contact_name: z.string().optional(),
  business_nature: z.string().optional(),
  has_mechanic: z.string().optional(),
  maxton_account: z.string().optional(),
  number_of_elevators: z.number().int().min(0).optional(),
  technical_contact: z.string().optional(),
  technical_contact_phone: z.string().optional().or(z.literal('')),
  technician: z.string().optional(),
});

export type EditUserDetails = z.infer<typeof editUserDetailsSchema>;

export interface EditUserDetailsResponse {
  message?: string;
  error?: string;
}

async function editUserDetailsHandler(req: NextApiRequestWithUserContext, res: NextApiResponse<EditUserDetailsResponse>) {
  const { slug } = req.query;

  const userId = slug?.toString();

  if (!userId) {
    return res.status(400).json({ error: 'Invalid user ID' });
  }

  const parsedBody = editUserDetailsSchema.safeParse(req.body);

  if (parsedBody.error) {
    return res.status(400).json({ error: parsedBody.error.issues[0].message });
  }

  const data = parsedBody.data;

  const {
    first_name,
    last_name,
    notes,
    website,
    buyer_name,
    business_type,
    account_payable_contact,
    account_payable_phone,
    authorized_contact_name,
    business_nature,
    has_mechanic,
    maxton_account,
    number_of_elevators,
    technical_contact,
    technical_contact_phone,
    technician,
    company_name,
    phone,
    primary_contact_name,
    shipping_notes,
  } = data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const now = new Date();
  const updatedAt = now.toISOString();

  const updateUserDetails = await supabaseAdminClient
    .from("users")
    .update({
      first_name,
      last_name,
      notes,
      phone,
      updated_at: updatedAt,
    })
    .eq("id", userId);

  if (updateUserDetails.error) {
    return res.status(400).json({ error: updateUserDetails.error.message });
  }

  const updateCustomerDetails = await supabaseAdminClient
    .from("customers")
    .update({
      company_name,
      phone,
      primary_contact_name,
      shipping_notes,
      updated_at: updatedAt,
      company_website: website,
    })
    .eq("user_id", userId);

  if (updateCustomerDetails.error) {
    return res.status(400).json({ error: updateCustomerDetails.error.message });
  }

  const updateBusinessDetails = await supabaseAdminClient
    .from("business_details")
    .update({
      buyer_name,
      business_type,
      account_payable_contact,
      account_payable_phone,
      authorized_contact_name,
      business_nature,
      has_mechanic,
      maxton_account,
      number_of_elevators,
      technical_contact,
      technical_contact_phone,
      technician,
      updated_at: updatedAt,
    })
    .eq("user_id", userId);

  if (updateBusinessDetails.error) {
    return res.status(400).json({ error: updateBusinessDetails.error.message });
  }

  res.status(200).json({ message: 'User details updated successfully.' });
}
