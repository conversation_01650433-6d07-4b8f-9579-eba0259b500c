import { SvgSpinners90Ring } from "@/components/common/icons";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { supabaseClient } from "@/supabase";
import { Download } from "lucide-react";
import { useState } from "react";

interface DownloadAttachmentButtonProps {
    filePath: string;
}

export default function DownloadAttachmentButton({
    filePath,
}: DownloadAttachmentButtonProps) {
    const [isLoading, setIsLoading] = useState(false);

    const handleDownload = async () => {
        setIsLoading(true);
        try {
            const { data, error } = await supabaseClient.storage
                .from("maxton-bucket")
                .download(filePath);

            if (error) {
                toast({
                    title: "Error downloading file",
                    description: "An error occurred while downloading the file.",
                    variant: "destructive",
                });
                return;
            }

            if (data) {
                // Create a blob URL for the downloaded data
                const url = URL.createObjectURL(data);

                // Create a temporary anchor element to trigger the download
                const a = document.createElement("a");
                a.href = url;
                a.download = filePath.split("/").pop() || "attachment"; // Extract filename or use default
                document.body.appendChild(a);
                a.click();

                // Clean up
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }

            toast({
                title: "Download started",
                description: "Your download has started.",
                variant: "default",
            });
        } catch (error: any) {
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Button
            type="button"
            variant="default"
            className=""
            onClick={handleDownload}
            disabled={isLoading}
        >
            {isLoading ? (
                <>
                    <SvgSpinners90Ring className="mr-2" />
                    Downloading...
                </>
            ) : (
                <>
                    <Download />
                    Download Attachment
                </>
            )}
        </Button>
    );
}