import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface UrlLabelItem {
    url: string;
    label: string;
}

interface ShippingTabProps {
    shipping: UrlLabelItem
    setShipping: React.Dispatch<React.SetStateAction<UrlLabelItem>>
}

export default function ShippingTab({
    shipping,
    setShipping
}: ShippingTabProps) {
    return (
        <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                    <Label htmlFor="shipping-label">Shipping Information</Label>
                    <Input
                        id="shipping-label"
                        placeholder="Shipping information"
                        value={shipping.label}
                        onChange={(e) => setShipping({ ...shipping, label: e.target.value })}
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="shipping-url">Shipping Policy URL (Optional)</Label>
                    <Input
                        id="shipping-url"
                        placeholder="Link to shipping policy"
                        value={shipping.url}
                        onChange={(e) => setShipping({ ...shipping, url: e.target.value })}
                    />
                </div>
            </div>

            <p className="text-sm text-muted-foreground mt-1">
                Add shipping information and an optional link to shipping policy.
            </p>
        </div>
    )
} 