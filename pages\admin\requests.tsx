import AdminLayout from "@/components/features/admin/layout";
import { CancellationRequestsDataTable } from "@/components/features/admin/requests/cancelation-request";
import { PendingBillingAddressesDataTable } from "@/components/features/admin/requests/pending-billing-addresses";
import Head from "next/head";


export default function Requests() {
    return (
        <AdminLayout>
            <Head>
                <title>Requests</title>
            </Head>
            <div className="space-y-4">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <h2 className="text-4xl font-bold tracking-tight">Requests</h2>
                </div>
                <div className="relative w-full h-full flex flex-col gap-12 pt-5">
                    <CancellationRequestsDataTable />
                    <PendingBillingAddressesDataTable />
                </div>
            </div>
        </AdminLayout>
    )
}
