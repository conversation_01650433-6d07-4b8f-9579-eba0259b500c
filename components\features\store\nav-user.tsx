"use client";

import { LogOut, UserIcon, ChartLine } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/shadcn-button";
import { toast } from "@/hooks/use-toast";
import { useSignOutMutation } from "@/queries/user-queries";
import useAuthStore from "@/stores/auth-store";
import Link from "next/link";
import { useRouter } from "next/router";
import { navData } from "./nav";
import { useRandomProfile } from "@/hooks/use-random-profile";

export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.token);
  const signOutMutation = useSignOutMutation();
  const role = useAuthStore((state) => state.role);
  const currentNav = navData[role];
  const isAdmin = useAuthStore((state) => state.isAdmin)();
  const isStaff = useAuthStore((state) => state.isStaff)();

  const profile = useRandomProfile({
    value: user.name,
    size: 100,
  });

  const signout = () => {
    signOutMutation
      .mutateAsync(accessToken)
      .then(() => {
        toast({
          title: "Sign out",
          description: "You have successfully signed out.",
          duration: 3000,
        });
        router.push("/");
      })
      .catch(() => { });
  };

  // Get initials from the user name for avatar fallback
  const getInitials = (name: string) => {
    if (!name) return "U";
    const parts = name.split(" ");
    if (parts.length === 1) return name.charAt(0).toUpperCase();
    return (
      parts[0].charAt(0) + parts[parts.length - 1].charAt(0)
    ).toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative p-0 h-12 w-12 rounded-full hover:bg-transparent"
        >
          <Avatar className="h-10 w-10 rounded-full bg-gray-300 p-1">
            <AvatarImage
              src={profile.url}
              alt={user.name}
              className="object-cover"
            />
            <AvatarFallback className="rounded-full bg-primary text-white">
              {getInitials(user.name)}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="min-w-56 rounded-lg"
        align="end"
        sideOffset={4}
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="h-8 w-8 rounded-lg">
              <AvatarImage
                src={profile.url}
                alt={user.name}
                className="object-cover"
              />
              <AvatarFallback className="rounded-lg">
                <UserIcon className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <div className="flex items-center gap-2">
                <span className="truncate font-semibold">{user.name}</span>
                {/* {isAdmin && (
                                    <Badge className="px-1.5 py-0.5 text-xs bg-red-600 text-white font-medium uppercase">
                                        Admin
                                    </Badge>
                                )} */}
              </div>
              <span className="truncate text-xs">{user.email}</span>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {!isAdmin && !isStaff &&
          currentNav?.nav.map((item) => (
            <DropdownMenuItem key={item.title}>
              <Link
                href={item.link || "#"}
                className="w-full justify-start px-0 flex items-center gap-2"
              >
                {item.icon && (
                  <item.icon className="mr-2 h-4 w-4 text-primary" />
                )}
                <span>{item.title}</span>
              </Link>
            </DropdownMenuItem>
          ))}

        {isAdmin || isStaff ? (
          <DropdownMenuItem key="dashboard-link">
            <Link
              href="/admin/dashboard"
              className="w-full justify-start px-0 flex items-center gap-2"
            >
              <ChartLine className="mr-2 h-4 w-4 text-primary" />
              <span>Dashboard</span>
            </Link>
          </DropdownMenuItem>
        ) : null}

        <DropdownMenuItem>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start px-0 text-red-500"
            onClick={signout}
            disabled={signOutMutation.isPending}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Log out
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
