// import { defaultLogo } from "../../../common/defaults";
import {
  blockContentNormalStyle,
  formLinks,
  logo,
  plainText,
  primaryButton,
  signInLink,
  tags,
  title,
  webriqForms,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const callToActionSchema = [
  logo(
    hideIfVariantIn([
      "variant_c",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
    ])
  ),
  title(hideIfVariantIn(["variant_e"])),
  plainText(hideIfVariantIn(["variant_e", "variant_f"])),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),

  primaryButton(
    hideIfVariantIn([
      "variant_b",
      "variant_c",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
    ])
  ),
  webriqForms(
    hideIfVariantIn(["variant_a", "variant_f", "variant_h", "variant_j"])
  ),

  signInLink(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_f",
      "variant_h",
      "variant_j",
    ])
  ),
  formLinks(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
    ])
  ),
  tags(
    "Add Tags",
    "",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
      "variant_i",
      "variant_j",
    ])
  ),
];
