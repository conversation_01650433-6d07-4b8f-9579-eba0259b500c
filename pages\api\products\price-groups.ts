import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { ProductGroupPrice } from "@/supabase/types";
import { nanoid } from "nanoid";
import { NextApiResponse } from "next";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

export default matchRoute({
  PATCH: checkAdmin(bulkUpdateProductGroupPricesHandler),
});

export interface BulkUpdateProductGroupPricesResponse {
  error?: string;
  message?: string;
}

const bulkUpdateProductGroupPricesSchema = z.object({
  data: z.array(
    z.object({
      product_code: z.string().min(1, "Product code is required"),
      group_prices: z.array(
        z.object({
          group_name: z.string().min(1, "Group name is required"),
          custom_price: z.number().min(0, "Price must be non-negative"),
        })
      ),
    })
  ),
});

export type BulkUpdateProductGroupPricesRequest = z.infer<
  typeof bulkUpdateProductGroupPricesSchema
>;

async function bulkUpdateProductGroupPricesHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<BulkUpdateProductGroupPricesResponse>
) {
  const parsedData = bulkUpdateProductGroupPricesSchema.safeParse(req.body);

  if (!parsedData.success) {
    return res.status(400).json({ error: parsedData.error.message });
  }

  const { data } = parsedData.data;

  const productCodes = data.map((item) => item.product_code);
  const groupNames = data
    .flatMap((item) => item.group_prices)
    .map((item) => item.group_name);

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data: products, error: productError } = await supabaseAdminClient
    .from("products")
    .select("id, sku, name")
    .in("sku", productCodes);

  if (productError) {
    return res.status(400).json({ error: productError.message });
  }

  const { data: groups, error: groupError } = await supabaseAdminClient
    .from("groups")
    .select("id, name")
    .in("name", groupNames);

  if (groupError) {
    return res.status(400).json({ error: groupError.message });
  }

  const productMap = new Map(
    products.map((item) => [item.sku, { id: item.id, name: item.name }])
  );
  const groupMap = new Map(groups.map((item) => [item.name, item.id]));

  const productGroupPrices = await supabaseAdminClient
    .from("product_group_prices")
    .select(
      "id, hash, product_id, custom_price, group_id, products(sku), groups(name)"
    )
    .in(
      "product_id",
      products.map((p) => p.id)
    )
    .in(
      "group_id",
      groups.map((g) => g.id)
    );

  const updates = data
    .map((item) => {
      const product = productMap.get(item.product_code);
      const group_prices = item.group_prices
        .map((price) => {
          const group = groupMap.get(price.group_name);
          const hash = `${product?.name}-${group}`
            .replace(/\s+/g, "-")
            .toLowerCase();
          const newHash = `${hash}-${nanoid(6)}`;

          const existing = productGroupPrices.data?.find(
            (p) =>
              p.products?.sku === item.product_code &&
              p.groups?.name === price.group_name
          );

          // If the price hasn't changed, skip it
          if (existing && existing?.custom_price === price.custom_price) {
            return null;
          }

          const id = existing?.id || uuidv4();

          return {
            id,
            product_id: product?.id,
            group_id: group,
            custom_price: price.custom_price,
            hash: newHash,
          };
        })
        .filter(Boolean) as ProductGroupPrice[];

      return {
        group_prices,
      };
    })
    .flatMap((item) => item.group_prices);

  console.log("Total updates:", updates.length);

  // If a group price doesn't exist, create it. If it does, update it.
  const { error: updateError } = await supabaseAdminClient
    .from("product_group_prices")
    .upsert(updates);

  if (updateError) {
    console.error("Error updating product group prices:", updateError);
    return res.status(400).json({ error: updateError.message });
  }

  return res
    .status(200)
    .json({ message: "Successfully updated product group prices" });
}
