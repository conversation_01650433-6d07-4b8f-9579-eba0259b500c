import { rootSchema } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { portfolioVariants as baseVariantsList } from "@webriq-pagebuilder/sanity-plugin-schema-default";
import { MdVerticalAlignTop } from "react-icons/md";

import variantEImage from "./images/variant_e.png";
import variantFImage from "./images/variant_f.png";
import variantGImage from "./images/variant_g.png";

import initialValue from "./initialValue";
import { portfolioSchema } from "./schema";

export const variantsList = [
  ...baseVariantsList, // adds all the existing variants for header component and insert the new variants as follows

  {
    title: "Variant E",
    description: "A new variant for header component",
    value: "variant_e",
    image: variantEImage.src,
  },
  {
    title: "Variant F",
    description: "A new variant for header component",
    value: "variant_f",
    image: variantFImage.src,
  },
  {
    title: "Variant G",
    description: "A variant for Adjustment Procedures",
    value: "variant_g",
    image: variantGImage.src,
  },
  {
    title: "Variant H",
    description: "A variant for Adjustment Procedures",
    value: "variant_h",
    image: variantGImage.src,
  },
];

export default rootSchema(
  "portfolio",
  "Portfolio",
  MdVerticalAlignTop,
  variantsList,
  portfolioSchema,
  initialValue
);
