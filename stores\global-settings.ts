import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

export interface GlobalSettings {
    collapseSidebar: boolean;
    setCollapseSidebar: (collapseSidebar: boolean) => void;
}

export const useGlobalSettings = create<GlobalSettings>()(
    persist(
        (set, get) => ({
            collapseSidebar: false,
            setCollapseSidebar: (collapseSidebar: boolean) => set({ collapseSidebar }),
        }),
        {
            name: "global-settings",
            storage: createJSONStorage(() => localStorage),
        }
    )
);
