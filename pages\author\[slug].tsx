import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { groq } from "next-sanity";
import { PreviewSuspense } from "next-sanity/preview";
import { sanityClient, getClient } from "lib/sanity.client";
import {
  allCategories,
  authorQuery,
  postsByAuthor,
  globalSEOQuery,
  allArchives,
} from "pages/api/query";
import { usePreviewSubscription } from "lib/sanity";
import { PageSections } from "components/page";
import AuthorSection from "components/author";
import { PreviewBanner } from "components/PreviewBanner";
import { PreviewNoContent } from "components/PreviewNoContent";
import { filterDataToSingleItem } from "components/list";
import PageNotFound from "pages/404";
import InlineEditorContextProvider from "context/InlineEditorContext";
import { GetStaticPaths, GetStaticProps } from "next";
import {
  CommonPageData,
  BlogsData,
  DefaultSeoData,
  Categories,
  AuthorPageProps,
} from "types";
import { ArchivePageData } from "pages/archive/[year]/[month]";

interface PageBySlugProps {
  data: AuthorPageData | null;
  preview: boolean;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

interface DocumentWithPreviewProps {
  data: AuthorPageData | null;
  slug: string | string[] | undefined;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

interface ArchiveCombination {
  year: {
    _id: string;
    title: string;
    slug: string;
  };
  month: {
    _id: string;
    title: string;
    slug: string;
  };
}

export interface AuthorPageData {
  author: AuthorPageProps | null;
  posts: BlogsData[] | null;
  categories: Categories[] | null;
  hasUnpublishedEdits?: boolean | null;
  archive: ArchivePageData["archive"];
  allArchives?: {
    years: any[];
    months: any[];
    validCombinations: ArchiveCombination[];
  };
  title: string;
  hasNeverPublished?: boolean;
  seo?: {
    seoTitle?: string;
  };
  _type?: string;
}

export interface PageData extends CommonPageData {
  collections: any;
  slug: string | string[];
  title: string;
}

export function AuthorPage({
  data,
  preview,
  token,
  source,
  defaultSeo,
}: PageBySlugProps) {
  const router = useRouter();
  const slug = router.query.slug;
  const showInlineEditor = source === "studio";

  if (preview) {
    return (
      <>
        <PreviewBanner />
        <PreviewSuspense fallback="Loading...">
          <InlineEditorContextProvider showInlineEditor={showInlineEditor}>
            <DocumentWithPreview
              {...{ data, token, slug, source, defaultSeo }}
            />
          </InlineEditorContextProvider>
        </PreviewSuspense>
      </>
    );
  }

  return <Document {...{ data, defaultSeo }} />;
}

/**
 *
 * @param {data} Data from getStaticProps based on current slug value
 *
 * @returns Document with published data
 */
function Document({
  data,
  defaultSeo,
}: {
  data: AuthorPageData | null;
  defaultSeo: DefaultSeoData;
}) {
  const publishedData = data?.author; // latest published data in Sanity

  // General safeguard against empty data
  if (!publishedData) {
    return null;
  }

  if (publishedData?.hasNeverPublished) {
    return <PageNotFound />;
  }

  const { title, seo, _type } = publishedData;

  return (
    <>
      <Head>
        <title>
          {publishedData.name ?? "WebriQ Studio"}, Author at Maxton
          Manufacturing Company
        </title>
      </Head>

      {/*  Show page sections */}
      {data?.author && (
        <AuthorSection
          author={data?.author}
          posts={data?.posts}
          categories={data?.categories}
          archive={data.archive}
          allArchives={data.allArchives}
        />
      )}
    </>
  );
}

/**
 *
 * @param data Data from getStaticProps based on current slug value
 * @param slug Slug value from getStaticProps
 * @param token Token value supplied via `/api/preview` route
 * @param source Source value supplied via `/api/preview` route
 *
 * @returns Document with preview data
 */
function DocumentWithPreview({
  data,
  slug,
  token,
  defaultSeo,
}: DocumentWithPreviewProps) {
  const { data: previewData } = usePreviewSubscription(authorQuery, {
    initialData: data?.author,
    enabled: Boolean(token) && Boolean(slug),
    params: { slug: typeof slug === "string" ? slug : slug?.[0] },
  });

  if (!previewData) {
    return null;
  }

  const { seo, _type } = previewData;

  const title = `${previewData.title} `;

  return (
    <>
      <Head>
        <title>Author | {previewData.name ?? "WebriQ Studio"}</title>
      </Head>

      {/* if page has no sections, show no sections only in preview */}
      {_type === "page" &&
        "sections" in previewData &&
        (!previewData ||
          !previewData?.sections ||
          previewData?.sections?.length === 0) && <PreviewNoContent />}

      {/*  Show page sections */}
      {data?.author && (
        <AuthorSection
          author={previewData}
          posts={data?.posts}
          categories={data.categories}
          archive={data.archive}
          allArchives={data.allArchives}
        />
      )}
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData = {},
}: any): Promise<{ props: PageBySlugProps; revalidate?: number }> => {
  const client =
    preview && previewData?.token
      ? getClient(false).withConfig({ token: previewData.token })
      : getClient(preview);

  const [author, posts, categories, globalSEO, archives] = await Promise.all([
    client.fetch(authorQuery, { slug: params.slug }),
    client.fetch(postsByAuthor, { slug: params.slug }),
    client.fetch(allCategories),
    client.fetch(globalSEOQuery),
    client.fetch(allArchives),
  ]);

  const singleAuthorData = filterDataToSingleItem(author, preview);

  if (!singleAuthorData) {
    return {
      props: {
        preview,
        token: (preview && previewData.token) || "",
        source: (preview && previewData?.source) || "",
        data: null,
        defaultSeo: globalSEO || null,
      },
    };
  }

  return {
    props: {
      preview,
      token: (preview && previewData.token) || "",
      source: (preview && previewData?.source) || "",
      data: {
        author: singleAuthorData || null,
        posts: posts || null,
        categories: categories || null,
        allArchives: archives || null,
        archive: null,
        title: singleAuthorData?.name || "",
      },
      defaultSeo: globalSEO || null,
    },
    revalidate: process.env.SANITY_REVALIDATE_SECRET ? undefined : 60,
  };
};

export const getStaticPaths: GetStaticPaths = async () => {
  const paths = await sanityClient.fetch(
    groq`*[_type == "author" && defined(slug.current)][].slug.current`
  );

  return {
    paths: paths.map((slug) => ({ params: { slug } })),
    fallback: true,
  };
};

export default React.memo(AuthorPage);
