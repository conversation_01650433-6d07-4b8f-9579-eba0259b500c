import { SvgSpinners90Ring } from "@/components/common/icons";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { useChangePasswordMutation } from "@/queries/user-queries";
import useAuthStore from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeClosed } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

export function ChangePassword() {
    const userData = useAuthStore((state) => state.data);
    const logout = useAuthStore((state) => state.logout);
    const token = useAuthStore((state) => state.token);
    const changePasswordMutation = useChangePasswordMutation(userData.id, token);
    const { toast } = useToast();
    const [viewPassword, setViewPassword] = useState(false);

    const toggleViewPassword = () => {
        setViewPassword(!viewPassword);
    }

    const changePasswordSchema = z.object({
        new_password: z.string().min(8, "New password is required"),
        confirm_password: z.string().min(8, "Confirm password is required"),
    }).refine((data) => data.new_password === data.confirm_password, {
        path: ["confirm_password"],
        message: "Passwords do not match",
    });

    type ChangePasswordForm = z.infer<typeof changePasswordSchema>;

    const form = useForm<ChangePasswordForm>({
        resolver: zodResolver(changePasswordSchema),
        defaultValues: {
            new_password: "",
            confirm_password: "",
        },
    });

    const onChangePasswordSubmit = async (data: ChangePasswordForm) => {
        try {
            await changePasswordMutation.mutateAsync(data);
        } catch (error) {
        }
    }

    useEffect(() => {
        if (changePasswordMutation.isSuccess) {
            const t = toast({
                title: "Password updated",
                description: "Your password has been updated successfully, you will be logged out shortly.",
                variant: "success",
            });

            const timeout = setTimeout(() => {
                logout();
                t.dismiss();
            }, 3000);

            return () => clearTimeout(timeout);
        }
    }, [changePasswordMutation.isSuccess]);

    return (
        <div className="flex flex-col gap-4">
            <div>
                <h2 className="text-2xl font-bold">Change Your Password</h2>
                <p className="text-sm text-gray-500">
                    Change your password at any time
                </p>
            </div>
            <div className="w-full h-full flex flex-wrap items-center justify-start gap-4 pt-6">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onChangePasswordSubmit)} className="w-full h-full flex flex-col gap-4 max-w-sm">

                        <FormField
                            control={form.control}
                            name="new_password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel
                                        className="text-sm font-medium uppercase">
                                        New Password <span className="text-red-500">*</span>
                                    </FormLabel>
                                    <FormControl>
                                        <div className="relative">
                                            <Input
                                                autoComplete="new-password"
                                                type={viewPassword ? "text" : "password"}
                                                placeholder="New Password"
                                                className="border-secondary bg-black/10 rounded-none"
                                                {...field}
                                            />
                                            <Button
                                                variant="ghost"
                                                type="button"
                                                size="icon"
                                                onClick={toggleViewPassword}
                                                className="absolute right-0 top-0 hover:bg-transparent"
                                            >
                                                {
                                                    viewPassword ? (
                                                        <EyeClosed className="w-4 h-4" />
                                                    ) : (
                                                        <Eye className="w-4 h-4" />
                                                    )
                                                }
                                            </Button>
                                        </div>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="confirm_password"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel
                                        className="text-sm font-medium uppercase">
                                        Confirm Password <span className="text-red-500">*</span>
                                    </FormLabel>
                                    <FormControl>
                                        <Input
                                            type={viewPassword ? "text" : "password"}
                                            placeholder="Confirm Password"
                                            autoComplete="new-password"
                                            {...field}
                                            className="border-secondary bg-black/10 rounded-none"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="flex flex-col gap-2 pt-6">
                            <Button
                                type="submit"
                                variant="default"
                                className="w-full h-12 bg-primary text-white rounded-none flex items-center justify-center gap-2"
                                disabled={changePasswordMutation.isPending}
                            >
                                {
                                    changePasswordMutation.isPending ? (
                                        "Saving new password..."
                                    ) : (
                                        "Save Changes"
                                    )
                                }

                                {
                                    changePasswordMutation.isPending && (
                                        <SvgSpinners90Ring className="w-4 h-4 animate-spin" />
                                    )
                                }
                            </Button>
                        </div>
                    </form>
                </Form>
            </div>
        </div>
    )
}