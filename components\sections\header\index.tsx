import dynamic from "next/dynamic";

import {
  SectionsProps,
  Images,
  MainImage,
  LabeledRouteWithKey,
  Form,
  Template,
  LabeledRoute,
} from "../../../types";
import * as HeaderVariant from "@stackshift-ui/header";
import { PortableTextBlock } from "@sanity/types";

const Variants = {
  variant_a: HeaderVariant.Header_A,
  variant_b: HeaderVariant.Header_B,
  variant_c: HeaderVariant.Header_C,
  variant_d: HeaderVariant.Header_D,
  variant_e: HeaderVariant.Header_E,
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
  variant_i: dynamic(() => import("./variant_i")),
  variant_j: dynamic(() => import("./variant_j")),
  variant_k: dynamic(() => import("./variant_k")),
  variant_l: dynamic(() => import("./variant_l")),
  variant_m: dynamic(() => import("./variant_m")),
  variant_n: dynamic(() => import("./variant_n")),
  variant_o: dynamic(() => import("./variant_o")),
  variant_p: dynamic(() => import("./variant_p")),
  variant_q: dynamic(() => import("./variant_q")),
};

export interface ButtonProps {
  as?: string;
  label?: string;
  link?: {
    target?: string;
    route?: string;
  };
  ariaLabel?: string;
  variant?: string;
}

export interface HeaderProps {
  template?: Template;
  mainImage?: MainImage;
  images?: Images[];
  imagesCopy?: Images[];
  title?: string;
  subtitle?: string;
  description?: string;
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
  videoLink?: string;
  formLinks?: LabeledRouteWithKey[];
  form?: Form;
  firstColumn?: PortableTextBlock[];
  secondColumn?: PortableTextBlock[];
  content?: PortableTextBlock[];
  notesSection?: {
    title: string;
    description?: string;
    primaryButton: LabeledRoute;
    backgroundColor?: string;
    hasLink: boolean;
  }[];
  repairParts?: any;
}

const displayName = "Header";

export const Header: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants[variant as keyof typeof Variants];

  const props = {
    mainImage: data?.variants?.mainImage ?? undefined,
    images: data?.variants?.images ?? undefined,
    imagesCopy: data?.variants?.imagesCopy ?? undefined,
    title: data?.variants?.title ?? undefined,
    subtitle: data?.variants?.subtitle ?? undefined,
    description: data?.variants?.description ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
    secondaryButton: data?.variants?.secondaryButton ?? undefined,
    videoLink: data?.variants?.youtubeLink ?? undefined,
    formLinks: data?.variants?.formLinks ?? undefined,
    form: data?.variants?.form ?? undefined,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    secondColumn: data?.variants?.secondColumn ?? undefined,
    content: data?.variants?.content ?? undefined,
    notesSection: data?.variants?.notesSection ?? undefined,
    repairParts: data?.variants?.repairParts ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Header.displayName = displayName;
