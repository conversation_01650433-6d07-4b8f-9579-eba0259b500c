import emailTemplate from ".";

interface AccountStatusUpdateProps {
  to: string;
  name: string;
  email: string;
  accountId?: string;
  accountType: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  companyName?: string;
  businessType?: string;
  businessNature?: string;
  website?: string;
  maxtonAccount?: string;
  numberOfElevators?: number;
  status: "approved" | "rejected" | "banned" | "pending";
  statusDate: string;
  loginUrl: string;
  rejectionReason?: string;
  additionalNotes?: string;
}

export default function createAccountStatusTemplate({
  to,
  name,
  email,
  accountId,
  accountType,
  address,
  city,
  state,
  zip_code,
  country,
  companyName,
  businessType,
  businessNature,
  website,
  maxtonAccount,
  numberOfElevators,
  status,
  statusDate,
  loginUrl,
  rejectionReason,
  additionalNotes,
}: AccountStatusUpdateProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Manufacturing Company";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;

  const isApproved = status === "approved";
  const isRejected = status === "rejected" || status === "banned";

  // Determine header color based on status
  const headerColor = isApproved
    ? "#28a745"
    : isRejected
    ? "#dc3545"
    : "#0045d8";
  const headerBgColor = isApproved
    ? "#e6ffe6"
    : isRejected
    ? "#ffebee"
    : "#e6f0ff";

  const subject = isApproved
    ? `Your Account Has Been Approved - ${APP_NAME}`
    : `Important Update Regarding Your Account - ${APP_NAME}`;

  // Format date to be more user-friendly
  const formattedDate = new Date(statusDate).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Full address in a single line
  const fullAddress =
    address && city && state && zip_code && country
      ? `${address}, ${city}, ${state} ${zip_code}, ${country}`
      : "";

  return emailTemplate({
    to,
    subject,
    from: from,
    bcc: "<EMAIL>",
    body: `
      <h2 style="color: ${headerColor}; background-color: ${headerBgColor}; padding: 10px; border-left: 4px solid ${headerColor};">${
        isApproved ? "Account Approval Confirmation" : "Account Status Update"
      }</h2>
      <p>Dear ${name},</p>
      
      ${
        isApproved
          ? `
      <p>We are pleased to inform you that your account has been approved. You can now access all features and services of the ${APP_NAME} platform. Please login to your account using your email address and password by clicking the button below.</p>
      `
          : `
      <p>We regret to inform you that your account application has been ${status}. ${
        rejectionReason || ""
      }</p>
      `
      }
      
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="padding: 12px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd; font-size: 18px; font-weight: 600;" colspan="2">Account Information</th>
          </tr>
          <tr>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                <li style="margin-bottom: 4px;"><strong>Name:</strong> ${name}</li>
                <li style="margin-bottom: 4px;"><strong>Email:</strong> ${email}</li>
                ${
                  isApproved && accountId
                    ? `<li style="margin-bottom: 4px;"><strong>Account ID:</strong> ${accountId.slice(
                        0,
                        6
                      )}</li>`
                    : ""
                }
                <li style="margin-bottom: 4px;"><strong>Approval Date:</strong> ${formattedDate}</li>
                ${
                  isRejected && rejectionReason
                    ? `<li style="margin-bottom: 4px;"><strong>Reason:</strong> ${rejectionReason}</li>`
                    : ""
                }
              </ul>
            </td>
            <td style="width: 50%; padding: 15px; vertical-align: top; border: 1px solid #ddd;">
              <ul style="list-style-type: none; padding-left: 0; margin: 0;">
                ${
                  companyName
                    ? `<li style="margin-bottom: 4px;"><strong>Company Name:</strong> ${companyName}</li>`
                    : ""
                }
                ${
                  businessType
                    ? `<li style="margin-bottom: 4px;"><strong>Business Type:</strong> ${businessType}</li>`
                    : ""
                }
                ${
                  businessNature
                    ? `<li style="margin-bottom: 4px;"><strong>Nature of Business:</strong> ${businessNature}</li>`
                    : ""
                }
                ${
                  website
                    ? `<li style="margin-bottom: 4px;"><strong>Website:</strong> <a href="${
                        website.startsWith("http")
                          ? website
                          : "https://" + website
                      }" style="color: ${headerColor}; text-decoration: none;">${website}</a></li>`
                    : ""
                }
                ${
                  maxtonAccount
                    ? `<li style="margin-bottom: 4px;"><strong>Maxton Account:</strong> ${maxtonAccount}</li>`
                    : ""
                }
                ${
                  numberOfElevators !== undefined
                    ? `<li style="margin-bottom: 4px;"><strong>Number of Elevators:</strong> ${numberOfElevators}</li>`
                    : ""
                }
              </ul>
            </td>
          </tr>
        </table>
      </div>
      
      ${
        fullAddress
          ? `
      <div style="margin: 20px 0;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
          <tr>
            <th style="padding: 12px; text-align: left; background-color: #f0f0f0; border: 1px solid #ddd; font-size: 18px; font-weight: 600;">Address</th>
          </tr>
          <tr>
            <td style="padding: 15px; border: 1px solid #ddd;">${fullAddress}</td>
          </tr>
        </table>
      </div>
      `
          : ""
      }
      
      ${
        additionalNotes
          ? `
      <div style="margin: 20px 0;">
        <h3 style="font-size: 18px; margin-bottom: 8px;">Additional Information</h3>
        <p>${additionalNotes}</p>
      </div>
      `
          : ""
      }
      
      ${
        isApproved
          ? `
      <div style="margin: 25px 0; text-align: center;">
        <a href="${loginUrl}" 
           style="display: inline-block; padding: 12px 24px; background-color: ${headerColor}; color: white; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Login to Your Account
        </a>
      </div>
      
      <p>With your approved account, you can now:</p>
      <ul>
        <li>Place orders directly through our online portal</li>
        <li>Access your order history and track shipments</li>
        <li>Download product documentation and technical resources</li>
      </ul>
      `
          : `
      <p>If you have any questions regarding this decision or would like to understand more about our requirements, please contact our customer service team.</p>
      
      <p>You may reapply after addressing the issues mentioned above or explore alternative options with our team.</p>
      `
      }
      
      <p>If you have any questions or need assistance, please don't hesitate to contact our customer service team.</p>
      
      <p>Thank you for your interest in ${APP_NAME}.</p>
    `,
  });
}
