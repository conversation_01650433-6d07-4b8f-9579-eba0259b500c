import { Skeleton } from "@/components/ui/skeleton";

export function EditProductFormSkeleton() {
    return (
        <div className="w-full border-0 bg-transparent max-w-5xl mx-auto space-y-8">
            {/* Header skeleton */}
            <div className="flex items-center justify-between">
                <Skeleton className="h-10 w-48" />
                <div className="flex gap-4">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                </div>
            </div>
            
            {/* First row: Product Details and Pricing skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Product Details Column */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <Skeleton className="h-6 w-36 mb-4" />
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-24 w-full" />
                    </div>
                </div>

                {/* Pricing Column */}
                <div className="space-y-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <Skeleton className="h-6 w-24 mb-4" />
                        <div className="space-y-4">
                            <Skeleton className="h-10 w-full" />
                            <Skeleton className="h-10 w-full" />
                            <Skeleton className="h-6 w-32" />
                        </div>
                    </div>
                    
                    {/* Group Pricing Card */}
                    <div className="bg-white p-6 rounded-lg shadow">
                        <Skeleton className="h-6 w-36 mb-4" />
                        <div className="space-y-4">
                            <Skeleton className="h-24 w-full" />
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Second row: Images and Categories skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Images Column */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <Skeleton className="h-6 w-24 mb-4" />
                    <div className="grid grid-cols-2 gap-4">
                        <Skeleton className="h-10 w-full" />
                        <Skeleton className="h-32 w-32 mx-auto" />
                    </div>
                </div>

                {/* Categories Column */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <Skeleton className="h-6 w-32 mb-4" />
                    <Skeleton className="h-10 w-full" />
                </div>
            </div>
            
            {/* Third row: Options and Additional Info skeleton */}
            <div className="space-y-6">
                {/* Product Options */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <Skeleton className="h-6 w-40 mb-4" />
                    <div className="space-y-4">
                        <Skeleton className="h-32 w-full" />
                    </div>
                </div>
                
                {/* Additional Information */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <Skeleton className="h-6 w-48 mb-4" />
                    <div className="space-y-4">
                        <Skeleton className="h-32 w-full" />
                        <Skeleton className="h-32 w-full" />
                    </div>
                </div>
            </div>
        </div>
    );
} 