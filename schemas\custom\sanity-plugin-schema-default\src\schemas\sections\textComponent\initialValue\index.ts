import { defaultBlockContent }  from "@webriq-pagebuilder/sanity-plugin-schema-default";

export default {
  title: "Great quality",
  firstColumn: defaultBlockContent([
    // default - single block element, add more elements to block array to create multiple blocks
    {
      // default - single children array with one element
      // add more elements when markDefs (link) or marks/decorators (i.e. strong, em/italic, underline) is applied to text element
      children: [
        {
          text: `Etiam facilisis mauris leo, eu aliquet est iaculis eu. Mauris vitae pellentesque augue, quis efficitur elit. Suspendisse potenti. Vivamus et sem eget ligula bibendum pulvinar. Nullam libero velit, efficitur ut dui eget, tempus ultricies felis. Pellentesque ut lorem id velit aliquam pharetra id placerat purus. Aliquam erat mauris, cursus eget cursus in, rutrum et nisi. Phasellus consequat vehicula metus non sagittis. Sed quis ipsum non velit tempus consequat sit amet eget augue. Donec feugiat ultricies ultrices.`,
          marks: [], // leave empty if no marks/decorators (i.e. strong, em, underline) are applied to text element, else add marks/decorators strings here
        },
      ],
    },
  ]),
  secondColumn: defaultBlockContent([
    // default - single block element, add more elements to block array to create multiple blocks
    {
      // default - single children array with one element
      // add more elements when markDefs (link) or marks/decorators (i.e. strong, em/italic, underline) is applied to text element
      children: [
        {
          text: `Etiam facilisis mauris leo, eu aliquet est iaculis eu. Mauris vitae pellentesque augue, quis efficitur elit. Suspendisse potenti. Vivamus et sem eget ligula bibendum pulvinar. Nullam libero velit, efficitur ut dui eget, tempus ultricies felis. Pellentesque ut lorem id velit aliquam pharetra id placerat purus. Aliquam erat mauris, cursus eget cursus in, rutrum et nisi. Phasellus consequat vehicula metus non sagittis. Sed quis ipsum non velit tempus consequat sit amet eget augue. Donec feugiat ultricies ultrices.`,
          marks: [], // leave empty if no marks/decorators (i.e. strong, em, underline) are applied to text element, else add marks/decorators strings here
        },
      ],
    },
  ]),
  thirdColumn: defaultBlockContent([
    // default - single block element, add more elements to block array to create multiple blocks
    {
      // default - single children array with one element
      // add more elements when markDefs (link) or marks/decorators (i.e. strong, em/italic, underline) is applied to text element
      children: [
        {
          text: `Etiam facilisis mauris leo, eu aliquet est iaculis eu. Mauris vitae pellentesque augue, quis efficitur elit. Suspendisse potenti. Vivamus et sem eget ligula bibendum pulvinar. Nullam libero velit, efficitur ut dui eget, tempus ultricies felis. Pellentesque ut lorem id velit aliquam pharetra id placerat purus. Aliquam erat mauris, cursus eget cursus in, rutrum et nisi. Phasellus consequat vehicula metus non sagittis. Sed quis ipsum non velit tempus consequat sit amet eget augue. Donec feugiat ultricies ultrices.`,
          marks: [], // leave empty if no marks/decorators (i.e. strong, em, underline) are applied to text element, else add marks/decorators strings here
        },
      ],
    },
  ]),
};
