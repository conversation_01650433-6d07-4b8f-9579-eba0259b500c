import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatPrice } from "@/lib/utils";
import { ProductOptionType } from "@/supabase/types";
import React, { forwardRef } from "react";

type OnSelectFn = (param: {
  value: string;
  name: string;
  price?: number | undefined;
}) => void;

export interface ProductOptionProps {
  name: string;
  type: ProductOptionType;
  value: string | number;
  options?: { value: string; name: string; price: number }[];
  min?: number;
  max?: number;
  onSelect?: OnSelectFn;
  error?: boolean;
}

export const ProductOptionComponent = forwardRef<
  HTMLDivElement,
  ProductOptionProps
>((props, ref) => {
  const onInputChangeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.name;
    const value = e.target.value;
    props.onSelect?.({ name, value });
  };

  const onSelectHandler = (value: string) => {
    const name = props.name ?? "";
    const price =
      props.options?.find((option) => option.name === value)?.price ?? 0;

    props.onSelect?.({ name, value, price });
  };

  return (
    <div
      ref={ref}
      className="relative w-full max-w-96 h-fit flex flex-col gap-2"
    >
      <Label className="text-sm font-medium">
        {props.name} <span className="text-red-500">*</span>
      </Label>
      {props.type === "text" && (
        <>
          <Input
            aria-required
            required
            className={`h-8 text-xs ${props.error ? "border-red-500" : ""}`}
            id={props.name}
            name={props.name}
            type="text"
            value={props.value}
            onChange={onInputChangeHandler}
          />
          {props.error && (
            <p className="text-red-500 text-xs mt-1">This field is required</p>
          )}
        </>
      )}

      {props.type === "select" && (
        <>
          <Select
            onValueChange={onSelectHandler}
            // defaultValue={props.options?.[0]?.name}
            required
          >
            <SelectTrigger
              id={props.name}
              name={props.name}
              className={`h-8 text-xs ${
                props.error
                  ? "border-red-500 focus:ring-0 focus:ring-transparent focus:ring-offset-0 data-[state=open]:!ring-red-500  data-[state=open]:!ring-1"
                  : ""
              }`}
            >
              <SelectValue placeholder="Please Select" />
            </SelectTrigger>

            <SelectContent className="text-xs">
              {props.options?.map((option) => {
                const price = option.price
                  ? ` (+${formatPrice(option.price)})`
                  : "";

                return (
                  <SelectItem
                    key={`product-option-select-${option.name}-${option.value}`}
                    className="text-black text-xs"
                    value={option.name}
                  >
                    {option.name}{" "}
                    <span className="text-green-500 text-xs">{price}</span>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
          {props.error && (
            <p className="text-red-500 text-xs mt-1">This field is required</p>
          )}
        </>
      )}

      {props.type === "number" && (
        <>
          <Input
            aria-required
            required
            className={`h-8 text-xs ${props.error ? "border-red-500" : ""}`}
            id={props.name}
            name={props.name}
            type="number"
            value={props.value}
            onChange={onInputChangeHandler}
            onWheel={(e) => e.currentTarget.blur()}
          />
          {props.error && (
            <p className="text-red-500 text-xs mt-1">This field is required</p>
          )}
        </>
      )}
    </div>
  );
});

ProductOptionComponent.displayName = "ProductOptionComponent";

interface ProductOptionListProps extends React.HTMLAttributes<HTMLDivElement> {}

export const ProductOptionList = forwardRef<
  HTMLDivElement,
  ProductOptionListProps
>(({ children }, ref) => {
  return (
    <div ref={ref} className="flex flex-col gap-2">
      {children}
    </div>
  );
});

ProductOptionList.displayName = "ProductOptionList";
