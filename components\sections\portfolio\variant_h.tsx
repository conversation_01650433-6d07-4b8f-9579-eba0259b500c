import React from "react";
import { Button } from "components/ui";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { PortableText } from "@portabletext/react";
import { PortfolioProps } from "."; // Import PortfolioProps from index.tsx
import ValveCalculator from "../../calculator/sideCalculator";
import Link from "next/link"; // Use Next.js Link for internal routing if needed

// Define a proper type for props received by custom block components
interface CustomComponentProps {
  children?: React.ReactNode;
  value?: any;
  isInline?: boolean;
  [key: string]: any;
}

// Simplified PortableText components based on variant_g
const customComponents = {
  list: {
    bullet: ({ children }: CustomComponentProps) => (
      <ul className="list-disc pl-5 mb-4 text-lg text-gray-800 leading-loose">
        {children}
      </ul>
    ),
    number: ({ children }: CustomComponentProps) => (
      <ol className="list-decimal pl-5 mb-4 text-lg text-gray-800 leading-loose">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }: CustomComponentProps) => <li>{children}</li>,
    number: ({ children }: CustomComponentProps) => <li>{children}</li>,
  },
  block: {
    h1: ({ children }: CustomComponentProps) => (
      <h1 className="mb-6 text-5xl font-bold text-primary">{children}</h1>
    ),
    h2: ({ children }: CustomComponentProps) => (
      <h2 className="mb-5 text-4xl font-semibold text-primary">{children}</h2>
    ),
    h3: ({ children }: CustomComponentProps) => (
      <h3 className="mb-4 text-3xl font-medium text-gray-900">{children}</h3>
    ),
    h4: ({ children }: CustomComponentProps) => (
      <h4 className="mb-4 text-2xl font-medium text-gray-800">{children}</h4>
    ),
    h5: ({ children }: CustomComponentProps) => (
      <h5 className="mb-3 text-xl font-medium text-gray-700">{children}</h5>
    ),
    h6: ({ children }: CustomComponentProps) => (
      <h6 className="mb-3 text-lg font-medium text-gray-600">{children}</h6>
    ),
    normal: ({ children }: CustomComponentProps) => (
      <p className="mb-5 font-body text-lg text-gray-800 leading-loose">
        {children}
      </p>
    ),
    blockquote: ({ children }: CustomComponentProps) => (
      <blockquote className="mb-6 italic leading-loose text-gray-500 border-l-4 border-gray-300 pl-4">
        {children}
      </blockquote>
    ),
  },
  marks: {
    strong: ({ children }: CustomComponentProps) => (
      <strong className="font-semibold">{children}</strong>
    ),
    em: ({ children }: CustomComponentProps) => (
      <em className="italic">{children}</em>
    ),
    underline: ({ children }: CustomComponentProps) => (
      <span className="underline">{children}</span>
    ),
    link: ({ children, value }: CustomComponentProps) => {
      const href = value?.href || "";
      const isExternal = href.startsWith("http");
      const target = isExternal ? "_blank" : undefined;
      const rel = target === "_blank" ? "noopener noreferrer" : undefined;

      if (isExternal) {
        return (
          <a
            href={href}
            target={target}
            rel={rel}
            className="text-primary hover:text-primary/70 underline"
          >
            {children}
          </a>
        );
      }

      // Use Next.js Link for internal links if needed, otherwise standard <a>
      return (
        <Link href={href} passHref legacyBehavior>
          <a className="text-primary hover:text-primary/70 underline">
            {children}
          </a>
        </Link>
      );
    },
  },
  // Add other types if needed, e.g., for tables or images, simplified
};

export default function Portfolio_H({
  title,
  firstColumn,
  portfoliosWithCategory, // Renamed from portfoliosWithCategories for clarity based on data structure
  hasCalculator,
}: PortfolioProps): React.JSX.Element {
  // Use the actual data structure provided
  const categories = portfoliosWithCategory || [];

  return (
    <Section className="py-16 md:py-20 bg-background">
      <Container maxWidth={1536}>
        <Flex direction="col" justify="between" className="lg:flex-row gap-8">
          {/* Main Content Area */}
          <div
            className={`w-full ${
              hasCalculator ? "lg:w-[70%] lg:pr-8" : "lg:w-full"
            }`}
          >
            <Flex className="flex-col md:flex-row gap-8 md:gap-12">
              {/* Left Sidebar: Links */}
              <div className="w-full md:w-1/4 lg:w-1/3 md:max-w-[300px] md:sticky md:top-24 md:self-start">
                <div className="bg-gray-100 p-6 rounded-lg shadow">
                  <Heading
                    type="h2"
                    fontSize="lg"
                    weight="bold"
                    className="mb-5 !text-gray-900  pb-3"
                  >
                    Choose {title} Categories:
                  </Heading>
                  <div className="space-y-3 flex flex-col">
                    {categories.map((item) => {
                      const link = item?.primaryButton;
                      const label = item?.category;
                      const target = item?.primaryButton?.linkTarget || "_self";

                      if (!link || !label) return null; // Skip if no link or label

                      return (
                        <Button
                          key={item._key}
                          as="link" // Use as="link" for Button component
                          link={link}
                          target={target}
                          variant="ghost" // Use a subtle variant
                          ariaLabel={label}
                          className="w-full text-left !justify-start px-3 py-2 !font-medium text-gray-700 hover:bg-primary/10 hover:text-primary rounded transition-colors duration-200"
                        >
                          {label}
                        </Button>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Right Content: Title and Text Block */}
              <div className="w-full md:w-3/4 lg:w-2/3">
                {title && (
                  <Heading
                    fontSize="3xl"
                    type="h2"
                    className="!text-primary uppercase mb-6"
                  >
                    {title}s
                  </Heading>
                )}
                <div className="w-full border-b border-primary h-1 mb-6">
                  <div className="h-1 w-10 bg-primary mb-6"></div>
                </div>
                {firstColumn && (
                  <div className="prose max-w-none">
                    <PortableText
                      value={firstColumn}
                      components={customComponents}
                      onMissingComponent={false} // Set to false to avoid errors for missing components
                    />
                  </div>
                )}
              </div>
            </Flex>
          </div>

          {/* Calculator Area (Conditional) */}
          {hasCalculator && (
            <div className="w-full lg:w-[30%] mt-8 lg:mt-0 lg:pl-8 lg:border-l border-gray-300">
              {/* Sticky container for the calculator */}
              <div className="lg:sticky lg:top-24">
                <ValveCalculator />
              </div>
            </div>
          )}
        </Flex>
      </Container>
    </Section>
  );
}

// Export the component for dynamic import
export { Portfolio_H };
