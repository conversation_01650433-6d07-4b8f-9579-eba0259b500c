import {
  dateAdded,
  description,
  mainImage,
  portfolios,
  portfoliosWithCategories,
  primaryButton,
  subtitle,
  title,
  blockContentNormalStyle,
  arrayOfTitleAndDescription,
  hasCalculator,
} from "../../../common/fields";
import { BsCollection } from "react-icons/bs";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const portfolioSchema = [
  subtitle(hideIfVariantIn(["variant_g", "variant_h"])),
  title(),
  blockContentNormalStyle(
    "firstColumn",
    "Content",
    "Add first text content in a single column.",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
    ])
  ),
  portfoliosWithCategories(
    "portfoliosWithCategories",
    "Categories",
    "Click the 'Add item' button to add portfolio categories. If you want to edit what is added, click this ⋮ icon found on its right.",
    {
      name: "content",
      title: "Content",
      description:
        "Click the 'Add item' button to add portfolios. If you want to edit what is added, click this ⋮ icon found on its right.",
      type: "array",
      hidden: hideIfVariantIn(["variant_h"]),
      of: [
        {
          type: "object",
          name: "portfolioItems",
          title: "Portfolio Items",
          icon: BsCollection,
          fields: [
            dateAdded(
              hideIfVariantIn([
                "variant_a",
                "variant_e",
                "variant_f",
                "variant_g",
                "variant_h",
              ])
            ),
            mainImage(hideIfVariantIn(["variant_g", "variant_h"])),
            subtitle(
              hideIfVariantIn([
                "variant_a",
                "variant_e",
                "variant_f",
                "variant_g",
              ])
            ),
            title(hideIfVariantIn(["variant_a"])),
            description(
              hideIfVariantIn([
                "variant_a",
                "variant_e",
                "variant_f",
                "variant_g",
                "variant_h",
              ])
            ),
            primaryButton(
              hideIfVariantIn(["variant_e", "variant_f", "variant_g"])
            ),
            blockContentNormalStyle(
              "firstColumn",
              "Content",
              "Add text content in a single column.",
              hideIfVariantIn([
                "variant_a",
                "variant_b",
                "variant_c",
                "variant_d",
                "variant_h",
              ])
            ),

            //   Variant B
            blockContentNormalStyle(
              "secondColumn",
              "Second Column",
              "Add content in the second column.",
              hideIfVariantIn([
                "variant_a",
                "variant_b",
                "variant_c",
                "variant_d",
                "variant_g",
                "variant_h",
              ])
            ),
            arrayOfTitleAndDescription(
              "Title",
              "Description",
              hideIfVariantIn([
                "variant_a",
                "variant_b",
                "variant_c",
                "variant_d",
                "variant_g",
                "variant_h",
              ])
            ),
          ],
        },
      ],
    },
    hideIfVariantIn(["variant_b", "variant_c"])
  ),
  portfolios(
    hideIfVariantIn([
      "variant_a",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
    ])
  ),
  primaryButton(
    hideIfVariantIn([
      "variant_a",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
    ])
  ),
  hasCalculator(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
    ])
  ),
];
