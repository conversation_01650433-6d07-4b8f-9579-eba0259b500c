import {
    defaultArrayOfObjects,
    defaultButton,
    defaultImageArray,
    defaultImage,
  } from "@webriq-pagebuilder/sanity-plugin-schema-default"
  
  export default {
    subtitle: "Dolor sit amet consectutar",
    title: "Build & Launch without problems",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nisl sodales egestas lobortis.",
    tags: [
      "Vestibulum viverra ante non libero",
      "Morbi mollis metus pretium ultrices tincidunt",
      "Etiam lectus nunc, commodo et risus in",
    ],
    primaryButton: defaultButton("Get Started"),
    images: defaultImageArray([
      "image-1d0655534230a5cb4e08d8b7bd14b271c5317d82-634x951-jpg",
      "image-0dcfa20067460d48780f59c2c4a7a57e7c507969-1050x701-jpg",
      "image-881fdf41f1db63ed80d886037220b4fecc0c39b6-701x876-jpg",
      "image-04732685ec70933bc8c49683b2930032929b1fbe-1050x701-jpg",
    ]),
    featuredItems: defaultArrayOfObjects([
      {
        _type: "items",
        subtitle: "Dolor sit amet consectutar",
        title: "Build & Launch without problems",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nisl sodales egestas lobortis.",
        mainImage: defaultImage(
          "image-3a1ef9b3424fabd086e3d7bdea95583ba9ea6363-1048x701-jpg"
        ),
      },
      {
        _type: "items",
        subtitle: "Lorem ipsum dolor sit amet",
        title: "Learn how We will help you to reach your goals",
        description: "Pellentesque efficitur nisl sodales egestas lobortis.",
        mainImage: defaultImage(
          "image-2d9f87020c05fba5986084e4744c829844bf21b6-1050x700-jpg"
        ),
      },
    ]),
    arrayOfImageTitleAndText: defaultArrayOfObjects([
      {
        mainImage: defaultImage(
          "image-97b6696849c90facc200537fd780b03d373e5212-980x830-png"
        ),
        title: "Lorem ipsum dolor sit amet consectutar",
        plainText:
          "Fusce quam tellus, placerat eu metus ut, viverra aliquet purus. Suspendisse potenti. Nulla non nibh feugiat.",
      },
      {
        mainImage: defaultImage(
          "image-d52b2d79a8c0ff7df5bac2ab9b31e4f8b35b2d49-515x485-png"
        ),
        title: "Ut congue nec leo eget aliquam",
        plainText:
          "Ut tempus tellus ac nisi vestibulum tempus. Nunc tincidunt lectus libero, ac ultricies augue elementum at.",
      },
      {
        mainImage: defaultImage(
          "image-b362a413487c075bc56646b996ffaf5b888b8fd1-1200x1063-png"
        ),
        title: "Proin fringilla eleifend justo pellentesque",
        plainText:
          "Donec ut ligula nunc. Mauris blandit vel est et facilisis. Integer sapien felis, aliquet at posuere et, porttitor quis ligula.",
      },
      {
        mainImage: defaultImage(
          "image-46503b9f97de2e422010bcde0a1e33e954bca584-981x860-png"
        ),
        title: "Morbi sagittis ligula sit amet elit maximus",
        plainText:
          "Duis ut facilisis orci. Morbi lacinia nunc a augue eleifend, sed placerat ex faucibus. Duis ante arcu, pretium ac luctus vulputate.",
      },
    ]),
  }
  