import { Package, Package2, <PERSON><PERSON>art, Truck } from "lucide-react"
import Link from "next/link"

import StoreLayout from "@/components/features/store/layout"
import { OrderStatusBadge } from "@/components/features/store/orders-table"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/shadcn-button"
import { Skeleton } from "@/components/ui/skeleton"
import { formatPrice } from "@/lib/utils"
import { OrderItemOption, OrderWithItems, useGetOrderByIdQuery } from "@/queries/customer-queries"
import useAuthStore from "@/stores/auth-store"
import { useRouter } from "next/router"
import { useEffect, useState } from "react"
import Head from "next/head";

export default function OrderConfirmation() {
  const router = useRouter();
  const query = router.query;
  const { orderId } = query;

  const [isLoading, setIsLoading] = useState(true);

  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const order = useGetOrderByIdQuery(userId, orderId as string);

  useEffect(function hideLoading() {
    if (order.isSuccess) {
      setIsLoading(false);
    }
  }, [order.isSuccess]);

  return (
    <StoreLayout>
      <Head>
        <title>Order Confirmation</title>
      </Head>
      <main className="relative w-full h-full">
        <div className="min-h-screen w-full bg-muted/40 p-4 md:p-8 lg:p-12">
          <div className="mx-auto max-w-2xl flex flex-col gap-8">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-2">
                <Package2 className="h-6 w-6" />
                <h1 className="text-2xl font-bold tracking-tight">Order Confirmation</h1>
              </div>
              <p className="text-muted-foreground">
                Thank you for your order! We&apos;ve received it and will send you updates as it is processed.
              </p>
            </div>
            {
              isLoading ? (
                <OrderDetailsSkeleton />
              ) : <OrderDetails
                order={order.data ?? {} as OrderWithItems}
              />
            }
          </div>
        </div>
      </main>
    </StoreLayout>
  )
}

interface OrderDetailsProps {
  order: OrderWithItems;
}

function OrderDetails({ order }: OrderDetailsProps) {
  const orderId = order.id;
  const items = order.order_items ?? [];
  const shippingAddress = order.shipping_addresses ?? [];

  const date = order.created_at;
  const dateFormat = new Intl.DateTimeFormat("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });

  const dateString = date ? dateFormat.format(new Date(date)) : new Date(date).toString();

  const orderStatus = order.order_statuses?.at(0);
  const totalAmount = order.total_amount ?? 0;

  return <Card>
    <CardHeader className="flex flex-row items-center justify-between">
      <CardTitle className="text-base font-medium">Order #{orderId}</CardTitle>
      <OrderStatusBadge status={orderStatus?.status ?? "pending"} />
    </CardHeader>
    <CardContent className="grid gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <ShoppingCart className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Order Details</p>
            <p className="text-sm text-muted-foreground">{dateString}</p>
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <p className="text-sm font-medium">Total: {formatPrice(totalAmount)}</p>
        </div>
      </div>
      <Separator />
      <div className="flex flex-col gap-4">
        <h3 className="text-sm font-medium">Items</h3>
        <div className="grid gap-4">
          {
            items && items.length > 0 ? items.map((item) => {
              const options = item.options as unknown as OrderItemOption[] ?? [];
              const optionString = options.map((option) => `${option.name}: ${option.value}`).join(", ");

              // Use the stored item_price if available, otherwise fall back to base calculation
              let itemTotal = 0;
              if (item.item_price) {
                // item_price already includes options, just multiply by quantity
                itemTotal = item.item_price * item.quantity;
              } else {
                // fallback to product price * quantity
                itemTotal = item.products.price * item.quantity;
              }

              return <div
                key={`${item.products.id}-${item.quantity}-${item.products.name}-${optionString}`}
                className="flex items-center justify-between"
              >
                <div className="flex items-start gap-4">
                  <div className="aspect-square h-16 w-16 rounded-lg">
                    <img
                      src={item.products.image ?? ""}
                      alt={item.products.name}
                      className="h-full w-full rounded-lg object-cover"
                    />
                  </div>
                  <div className="flex flex-col gap-2">
                    <p className="text-sm font-medium">{item.products.name}</p>
                    <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                    {
                      options && options.length > 0 && options.map((itemOption, index) => {
                        const option = itemOption as unknown as OrderItemOption;
                        const optionPrice = option.price ? formatPrice(option.price) : '';

                        return <p
                          key={`${item.products.id}-${option.name}-${option.name}-${option.value}-${index}`}
                          className="text-sm text-muted-foreground"
                        >
                          {option.name}: {option.value}
                          <span className="text-green-500">
                            {optionPrice ? ` (+${optionPrice})` : ''}
                          </span>
                        </p>
                      })
                    }
                  </div>
                </div>
                <div className="flex items-end">
                  <p className="text-sm font-medium">
                    {formatPrice(itemTotal)}
                  </p>
                </div>
              </div>
            }) : (
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">No items in order</p>
              </div>
            )
          }
        </div>
      </div>
      <Separator />
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Truck className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Shipping Address</p>
            <p className="text-sm text-muted-foreground" key={shippingAddress.id}>
              {shippingAddress.contact_name}
              <br />
              {shippingAddress.address}
              <br />
              {shippingAddress.city}, {shippingAddress.state} {shippingAddress.zip_code}
            </p>
          </div>
        </div>
      </div>
      <Separator />
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Package className="h-5 w-5 text-muted-foreground" />
          <div className="flex flex-col gap-2">
            <p className="text-sm font-medium">Delivery Method</p>
            <div className="text-sm text-muted-foreground">
              {order.delivery_method}
              {order.ship_collect && (
                <>
                  <br />
                  Ship Collect UPS
                  {order.ups_account_number && (
                    <>
                      <br />
                      Account Number: {order.ups_account_number}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <Separator />
      <div className="flex flex-col gap-4">
        <div className="flex justify-between font-medium">
          <p className="text-sm">Total</p>
          <p className="text-sm font-medium">
            {formatPrice(totalAmount)}
          </p>
        </div>
      </div>
    </CardContent>
    <CardFooter>
      <Button asChild className="w-full">
        <Link href="/store/orders">Return to Orders</Link>
      </Button>
    </CardFooter>
  </Card>
}

function OrderDetailsSkeleton() {
  return <Card>
    <CardHeader className="flex flex-row items-center justify-between">
      <CardTitle className="text-base font-medium">
        <Skeleton className="h-6 w-24" />
      </CardTitle>
      <Skeleton className="h-6 w-24" />
    </CardHeader>
    <CardContent className="grid gap-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-12 w-12" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
        <Skeleton className="h-5 w-5" />
      </div>
      <Separator />
      <div className="flex flex-col gap-4">
        <Skeleton className="h-6 w-24" />
        <div className="grid gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-start gap-4">
              <Skeleton className="h-16 w-16" />
              <div className="flex flex-col gap-2">
                <Skeleton className="h-6 w-96" />
                <Skeleton className="h-6 w-96" />
              </div>
            </div>
            <Skeleton className="h-6 w-12" />
          </div>
        </div>
      </div>
      <Separator />
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-12 w-12" />
          <div className="flex flex-col gap-2">
            <Skeleton className="h-6 w-96" />
            <Skeleton className="h-24 w-24" />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
}