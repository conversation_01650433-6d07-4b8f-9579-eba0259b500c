import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import type React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z } from "zod";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { CategoryWithParent } from "@/pages/api/categories";
import {
  createProductMutation,
  useGetAllCategoriesQuery,
} from "@/queries/admin-queries";
import {
  useGetAllImages,
  useGetImage,
  useGetImages,
  useUploadImage,
} from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { useRouter } from "next/router";
import AdditionalInformation from "./product/additional-information";
import BasicInformation from "./product/basic-information";
import GroupPricing from "./product/group-pricing";
import { ImageSelector, SelectedImagesPreview } from "./product/image-selector";
import ProductOptions from "./product/product-options";

export const ACCEPTED_IMAGE_TYPES = [
  "image/png",
  "image/jpeg",
  "image/jpg",
  "image/gif",
  "image/webp",
];
type OptionType = "select" | "text" | "number";

// Define as Record to solve the index signature issue
interface OptionValue {
  name: string;
  value: string;
  price?: number;
}

interface ProductOption {
  name: string;
  type: OptionType;
  options?: OptionValue[];
}

// Specification type for table interface
interface Specification {
  key: string;
  value: string;
}

interface UrlLabelItem {
  url: string;
  label: string;
}

// Form schema - used for form validation
const formSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().optional(),
  description: z.string().optional(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  sku: z.string().optional(),
  available: z.boolean().default(true),
  variant: z.string().optional(),
  options: z.array(z.record(z.any())).optional(),
  features: z.array(z.string()).optional(),
  finish_and_material: z.string().optional(),
  image: z.string().optional(),
  additional_images: z.array(z.string()).optional(),
  // Extra fields not in the form schema but will be handled manually
  // These fields don't need to be part of the form schema as they're managed with React state
  category_id: z.string().optional(),
  draft: z.boolean().default(false),
  is_quote: z.boolean().default(false),
  group_prices: z
    .array(
      z.object({
        group_id: z.string(),
        custom_price: z.number(),
      })
    )
    .nullable()
    .optional(),
  tags: z.array(z.string()).optional(),
});

// Separate schema for product API - no File type, includes imageUrl as string
type ProductFormValues = z.infer<typeof formSchema>;

// API schema (what gets sent to the API)
type ProductApiData = ProductFormValues & {
  image?: string | null;
  helpful_hints?: UrlLabelItem[] | null;
  installation_instructions?: string | null;
  delivery_and_shipping?: { shipping: UrlLabelItem } | null;
  warranty?: UrlLabelItem | null;
  user_id: string;
};

export default function ProductForm() {
  const router = useRouter();
  const { toast } = useToast();
  const token = useAuthStore((state) => state.token);
  const userId = useAuthStore((state) => state.data?.id || "");
  const [file, setFile] = useState<File | null>(null);
  const [additionalFiles, setAdditionalFiles] = useState<File[]>([]);
  const [installationFile, setInstallationFile] = useState<File | null>(null);
  const [isDiscardDialogOpen, setIsDiscardDialogOpen] = useState(false);
  const allImagesQuery = useGetAllImages("product-images");
  const allImages = useMemo(
    () => allImagesQuery.data?.map((image) => `product-images/${image}`) ?? [],
    [allImagesQuery.data]
  );

  // For stored images from existing library
  const [selectedMainImage, setSelectedMainImage] = useState<string | null>(
    null
  );
  const [selectedAdditionalImages, setSelectedAdditionalImages] = useState<
    string[]
  >([]);
  const [mainImageUrl, setMainImageUrl] = useState<string | null>(null);
  const [additionalImageUrls, setAdditionalImageUrls] = useState<string[]>([]);

  // For specification table rows
  const [specs, setSpecs] = useState<Specification[]>([{ key: "", value: "" }]);

  // For features list
  const [features, setFeatures] = useState<string[]>([""]);

  // For helpful hints
  const [helpfulHints, setHelpfulHints] = useState<UrlLabelItem[]>([
    { url: "", label: "" },
  ]);

  // For shipping
  const [shipping, setShipping] = useState<UrlLabelItem>({
    url: "",
    label: "Shipping calculated at checkout.",
  });

  // For warranty
  const [warranty, setWarranty] = useState<UrlLabelItem>({
    url: "",
    label: "Subject to the terms and conditions of this Warranty.",
  });

  // For group pricing
  const [groupPrices, setGroupPrices] = useState<Record<string, number>>({});

  // For option editing UI state (not part of form data directly)
  const [currentOption, setCurrentOption] = useState<ProductOption>({
    name: "",
    type: "select",
    options: [{ name: "", value: "" }],
  });

  const uploadImageMutation = useUploadImage("product-images");
  const addProductMutation = createProductMutation(token);
  const categoriesQuery = useGetAllCategoriesQuery(1, 100, token);
  const isLoading =
    uploadImageMutation.isPending || addProductMutation.isPending;

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      brand: "",
      sku: "",
      price: 0,
      description: "",
      available: true,
      variant: "",
      options: [],
      features: [],
      finish_and_material: "",
      category_id: "",
      draft: false,
      group_prices: [],
      tags: [],
      image: "",
      additional_images: [],
      is_quote: false,
    },
  });

  // Memoize the current price value to prevent unnecessary rerenders
  const currentPrice = useMemo(
    () => form.watch("price"),
    [form.watch("price")]
  );

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const selectedFile = e.target.files[0];
        setFile(selectedFile);
      }
    },
    []
  );

  const handleAdditionalFilesChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        const selectedFiles = Array.from(e.target.files);
        setAdditionalFiles((prevFiles) => [...prevFiles, ...selectedFiles]);
      }
    },
    []
  );

  const removeAdditionalFile = useCallback((index: number) => {
    setAdditionalFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  }, []);

  const handleInstallationFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files[0]) {
        const selectedFile = e.target.files[0];
        // Check if file is PDF
        if (selectedFile.type !== "application/pdf") {
          toast({
            title: "Invalid file",
            description:
              "Please upload a PDF file for installation instructions",
            variant: "destructive",
          });
          return;
        }
        setInstallationFile(selectedFile);
      }
    },
    [toast]
  );

  const addSpecification = useCallback(() => {
    setSpecs((prev) => [...prev, { key: "", value: "" }]);
  }, []);

  const removeSpecification = useCallback((index: number) => {
    setSpecs((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateSpecification = useCallback(
    (index: number, field: keyof Specification, value: string) => {
      setSpecs((prev) => {
        const newSpecs = [...prev];
        newSpecs[index] = {
          ...newSpecs[index],
          [field]: value,
        };
        return newSpecs;
      });
    },
    []
  );

  const addFeature = useCallback(() => {
    setFeatures((prev) => [...prev, ""]);
  }, []);

  const removeFeature = useCallback((index: number) => {
    setFeatures((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateFeature = useCallback((index: number, value: string) => {
    setFeatures((prev) => {
      const newFeatures = [...prev];
      newFeatures[index] = value;
      return newFeatures;
    });
  }, []);

  const addHelpfulHint = useCallback(() => {
    setHelpfulHints((prev) => [...prev, { url: "", label: "" }]);
  }, []);

  const removeHelpfulHint = useCallback((index: number) => {
    setHelpfulHints((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const updateHelpfulHint = useCallback(
    (index: number, field: keyof UrlLabelItem, value: string) => {
      setHelpfulHints((prev) => {
        const newHints = [...prev];
        newHints[index] = {
          ...newHints[index],
          [field]: value,
        };
        return newHints;
      });
    },
    []
  );

  // Handler for selecting main image from library
  const handleMainImageSelect = useCallback((imagePath: string) => {
    setSelectedMainImage(imagePath);
    setFile(null); // Clear any uploaded file as we're using an existing image
  }, []);

  // Handler for selecting additional images from library
  const handleAdditionalImagesSelect = useCallback((imagePaths: string[]) => {
    setSelectedAdditionalImages(imagePaths);
  }, []);

  // Use useGetImage hook for the main image
  const mainImageQuery = useGetImage(selectedMainImage || "");

  // Set the mainImageUrl when the query data changes - optimize with condition check first
  useEffect(() => {
    if (
      mainImageQuery.data &&
      selectedMainImage &&
      mainImageQuery.data !== mainImageUrl
    ) {
      setMainImageUrl(mainImageQuery.data);
    }
  }, [mainImageQuery.data, selectedMainImage, mainImageUrl]);

  // Use useGetImages for additional images - make sure it's an array
  const additionalImageQueries = useGetImages(
    selectedAdditionalImages.length > 0 ? selectedAdditionalImages : []
  );

  // Optimize the effect to prevent unnecessary state updates
  useEffect(() => {
    if (!additionalImageQueries || additionalImageQueries.length === 0) return;

    const urls: string[] = [];
    let hasChanged = false;

    additionalImageQueries.forEach((query, index) => {
      if (query.data) {
        urls.push(query.data as string);
        // Check if this URL is different from the existing one
        if (
          index >= additionalImageUrls.length ||
          query.data !== additionalImageUrls[index]
        ) {
          hasChanged = true;
        }
      }
    });

    // Only update state if something has changed
    if (hasChanged || urls.length !== additionalImageUrls.length) {
      setAdditionalImageUrls(urls);
    }
  }, [additionalImageQueries, additionalImageUrls]);

  const onSubmit = async (values: ProductFormValues) => {
    const groupPricesArray = Object.entries(groupPrices).map(
      ([groupId, price]) => ({
        group_id: groupId,
        custom_price: price,
      })
    );

    form.setValue("group_prices", groupPricesArray);

    try {
      let imagePath: string | null = null;

      // Handle file upload for main image (if any)
      if (file) {
        const uploadResult = await uploadImageMutation.mutateAsync(file);
        if (uploadResult) {
          imagePath = uploadResult.path;
        }
      } else if (selectedMainImage) {
        // Use existing image from library
        imagePath = selectedMainImage;
      }

      // Upload additional images (if any)
      const additionalImagePaths: string[] = [...selectedAdditionalImages]; // Start with selected existing images

      if (additionalFiles.length > 0) {
        for (const additionalFile of additionalFiles) {
          const uploadResult =
            await uploadImageMutation.mutateAsync(additionalFile);
          if (uploadResult) {
            additionalImagePaths.push(uploadResult.path);
          }
        }
      }

      let installationPdfPath: string | null = null;
      if (installationFile) {
        // Upload installation PDF file
        const uploadResult =
          await uploadImageMutation.mutateAsync(installationFile);
        if (uploadResult) {
          installationPdfPath = uploadResult.path;
        }
      }

      const processedOptions = values.options?.map((option) => {
        const plainOption: Record<string, any> = {
          name: option.name,
          type: option.type,
        };

        if (option.options) {
          plainOption.options = option.options.map((opt) => ({
            name: opt.name,
            value: opt.value,
            price: opt.price,
          }));
        }

        return plainOption;
      });

      // Convert specifications table to JSON format
      const specificationsJson: Record<string, any> = {};

      // Only add valid specifications (non-empty key and value)
      const specsObj = specs.reduce(
        (acc, spec) => {
          if (spec.key && spec.value) {
            acc[spec.key] = spec.value;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      // Only add valid features (non-empty)
      const validFeatures = features.filter((f) => f.trim() !== "");

      // Build the complete specifications object
      if (Object.keys(specsObj).length > 0) {
        specificationsJson.specifications = specsObj;
      }

      if (validFeatures.length > 0) {
        specificationsJson.features = validFeatures;
      }

      if (values.finish_and_material) {
        specificationsJson.finish_and_material = values.finish_and_material;
      }

      // Process group prices
      const productGroupPrices = Object.entries(groupPrices)
        .filter(([_, price]) => price > 0)
        .map(([groupId, price]) => ({
          group_id: groupId,
          custom_price: price,
          hash: `${values.name}-${groupId}`.replace(/\s+/g, "-").toLowerCase(),
        }));

      console.log("🚀 ~ values", values);
      // Create the API payload with proper typing
      const productData = {
        ...values,
        image: imagePath,
        additional_images:
          additionalImagePaths.length > 0 ? additionalImagePaths : undefined,
        options: processedOptions?.length ? processedOptions : undefined,
        specifications:
          Object.keys(specificationsJson).length > 0
            ? specificationsJson
            : undefined,
        helpful_hints:
          helpfulHints.filter((hint) => hint.label.trim() !== "").length > 0
            ? helpfulHints
                .filter((hint) => hint.label.trim() !== "")
                .map((hint) => ({ ...hint }))
            : undefined,
        installation_instructions: installationPdfPath,
        delivery_and_shipping: {
          shipping: {
            url: shipping.url,
            label: shipping.label,
          },
        },
        warranty: {
          url: warranty.url,
          label: warranty.label,
        },
        group_prices:
          productGroupPrices.length > 0 ? productGroupPrices : undefined,
        user_id: userId,
      };

      await addProductMutation.mutateAsync(productData);

      toast({
        title: "Success",
        description: "Product has been created successfully",
        variant: "success",
      });

      resetForm();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create product",
        variant: "destructive",
      });
    }
  };

  const resetForm = useCallback(() => {
    form.reset();
    setFile(null);
    setAdditionalFiles([]);
    setInstallationFile(null);
    setSelectedMainImage(null);
    setSelectedAdditionalImages([]);
    setMainImageUrl(null);
    setAdditionalImageUrls([]);
    setSpecs([{ key: "", value: "" }]);
    setFeatures([""]);
    setHelpfulHints([{ url: "", label: "" }]);
    setShipping({ url: "", label: "Shipping calculated at checkout." });
    setWarranty({
      url: "",
      label: "Subject to the terms and conditions of this Warranty.",
    });
    setGroupPrices({});
    setCurrentOption({
      name: "",
      type: "select",
      options: [{ name: "", value: "" }],
    });
  }, [form]);

  const addOption = useCallback(() => {
    if (!currentOption.name) return;

    const optionToAdd: Record<string, any> = {
      name: currentOption.name,
      type: currentOption.type,
    };

    if (currentOption.type === "select" && currentOption.options) {
      const validOptions = currentOption.options.filter(
        (opt) => opt.name && opt.value
      );

      if (validOptions.length === 0) return;

      optionToAdd.options = validOptions;
    }

    const currentOptions = form.getValues("options") || [];
    form.setValue("options", [...currentOptions, optionToAdd], {
      shouldValidate: true,
    });

    setCurrentOption({
      name: "",
      type: "select",
      options: [{ name: "", value: "" }],
    });
  }, [currentOption, form]);

  const removeOption = useCallback(
    (index: number) => {
      const currentOptions = form.getValues("options") || [];
      form.setValue(
        "options",
        currentOptions.filter((_, i) => i !== index),
        { shouldValidate: true }
      );
    },
    [form]
  );

  const addOptionValue = useCallback(() => {
    if (!currentOption.options) return;

    setCurrentOption((prev) => ({
      ...prev,
      options: [...(prev.options || []), { name: "", value: "" }],
    }));
  }, [currentOption]);

  const removeOptionValue = useCallback(
    (index: number) => {
      if (!currentOption.options) return;

      setCurrentOption((prev) => ({
        ...prev,
        options: prev.options?.filter((_, i) => i !== index),
      }));
    },
    [currentOption]
  );

  const updateOptionValue = useCallback(
    (index: number, field: keyof OptionValue, value: string | number) => {
      if (!currentOption.options) return;

      setCurrentOption((prev) => {
        const newOptions = [...(prev.options || [])];
        newOptions[index] = {
          ...newOptions[index],
          [field]: value,
        };

        return {
          ...prev,
          options: newOptions,
        };
      });
    },
    [currentOption]
  );

  const handleDiscard = useCallback(() => {
    resetForm();
    router.push("/admin/products");
  }, [resetForm, router]);

  const handleSaveDraft = useCallback(() => {
    form.setValue("draft", true);
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  const handlePublish = useCallback(() => {
    form.setValue("draft", false);
    form.handleSubmit(onSubmit)();
  }, [form, onSubmit]);

  return (
    <Card className="w-full border-0 bg-transparent max-w-5xl mx-auto">
      <CardContent className="pt-6 bg-transparent px-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center justify-between">
                <h1 className="text-4xl font-bold tracking-tight">
                  Add New Product
                </h1>
              </div>
              <div className="flex justify-end gap-4">
                <AlertDialog
                  open={isDiscardDialogOpen}
                  onOpenChange={setIsDiscardDialogOpen}
                >
                  <AlertDialogTrigger asChild>
                    <Button
                      data-active={form.formState.isDirty}
                      type="button"
                      variant="secondary"
                      disabled={isLoading}
                      className="w-fit data-[active=true]:block hidden"
                    >
                      Discard
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will discard all changes made to this product. This
                        action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel type="button" className="">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction
                        type="button"
                        onClick={handleDiscard}
                        className=" bg-red-500"
                      >
                        Discard
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
                <Button
                  type="button"
                  variant="outline"
                  disabled={isLoading}
                  className="w-fit "
                  onClick={handleSaveDraft}
                >
                  {isLoading ? "Saving as Draft..." : "Save Draft"}
                </Button>
                <Button
                  type="button"
                  variant="primary"
                  disabled={isLoading}
                  className="w-fit "
                  onClick={handlePublish}
                >
                  {isLoading ? "Publishing..." : "Publish"}
                </Button>
              </div>
            </div>
            {/* First row: Product Details and Pricing */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Product Details Column */}
              <Card className="h-full">
                <CardContent className="pt-6 space-y-4">
                  <h3 className="text-lg font-semibold">Product Details</h3>
                  <div className="space-y-4">
                    <BasicInformation.NameField form={form} />
                    <BasicInformation.BrandField form={form} />
                    <BasicInformation.VariantField form={form} />
                    <BasicInformation.DescriptionField form={form} />
                    <BasicInformation.TagsField form={form} />
                  </div>
                </CardContent>
              </Card>

              {/* Group Pricing row */}
              <div className="grid grid-cols-1 gap-6">
                {/* Pricing Column */}
                <PricingSection form={form} />

                {/* Group Pricing Card - Pass memoized price value */}
                <GroupPricing
                  groupPrices={groupPrices}
                  setGroupPrices={setGroupPrices}
                  basePrice={currentPrice}
                />
              </div>
            </div>

            {/* Second row: Images and Categories */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Images Column */}
              <Card className="h-full">
                <CardContent className="pt-6 space-y-4">
                  <h3 className="text-lg font-semibold">Images</h3>
                  <div className="space-y-6">
                    {/* Image Selector Component */}
                    {allImages && allImages.length > 0 && (
                      <ImageSelector
                        allImages={allImages}
                        file={file}
                        additionalFiles={additionalFiles}
                        handleFileChange={handleFileChange}
                        handleAdditionalFilesChange={
                          handleAdditionalFilesChange
                        }
                        removeAdditionalFile={removeAdditionalFile}
                        onMainImageSelect={handleMainImageSelect}
                        onAdditionalImagesSelect={handleAdditionalImagesSelect}
                        selectedMainImage={selectedMainImage}
                        selectedAdditionalImages={selectedAdditionalImages}
                      />
                    )}

                    {/* Preview of selected images */}
                    <SelectedImagesPreview
                      mainImageUrl={mainImageUrl}
                      additionalImageUrls={additionalImageUrls}
                      file={file}
                      additionalFiles={additionalFiles}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Categories Column */}
              <CategoriesSection
                form={form}
                categories={categoriesQuery.data?.categories || []}
              />
            </div>

            {/* Third row: Others (Options and Additional Info) */}
            <div className="grid grid-cols-1 gap-6">
              {/* Product Options */}
              <ProductOptions
                form={form}
                currentOption={currentOption}
                setCurrentOption={setCurrentOption}
                addOption={addOption}
                removeOption={removeOption}
                addOptionValue={addOptionValue}
                removeOptionValue={removeOptionValue}
                updateOptionValue={updateOptionValue}
              />

              {/* Additional Information */}
              <AdditionalInformation
                form={form}
                specs={specs}
                addSpecification={addSpecification}
                removeSpecification={removeSpecification}
                updateSpecification={updateSpecification}
                features={features}
                addFeature={addFeature}
                removeFeature={removeFeature}
                updateFeature={updateFeature}
                helpfulHints={helpfulHints}
                addHelpfulHint={addHelpfulHint}
                removeHelpfulHint={removeHelpfulHint}
                updateHelpfulHint={updateHelpfulHint}
                installationFile={installationFile}
                handleInstallationFileChange={handleInstallationFileChange}
                setInstallationFile={setInstallationFile}
                shipping={shipping}
                setShipping={setShipping}
                warranty={warranty}
                setWarranty={setWarranty}
              />
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

interface PricingSectionProps {
  form: UseFormReturn<ProductFormValues>;
}

// Extract pricing-related fields from BasicInformation
const PricingSection = ({ form }: PricingSectionProps) => (
  <Card className="h-fit">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Pricing</h3>
      <div className="space-y-4">
        {/* Price Field */}
        <BasicInformation.PriceField form={form} />

        {/* SKU Field */}
        <BasicInformation.SkuField form={form} />

        {/* Available Switch */}
        <BasicInformation.AvailableField form={form} />

        {/* Quote Switch */}
        <BasicInformation.QuoteField form={form} />
      </div>
    </CardContent>
  </Card>
);

interface ImagesSectionProps {
  file: File | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  additionalFiles: File[];
  handleAdditionalFilesChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeAdditionalFile: (index: number) => void;
}

// Extract image-related fields
const ImagesSection = ({
  file,
  handleFileChange,
  additionalFiles,
  handleAdditionalFilesChange,
  removeAdditionalFile,
}: ImagesSectionProps) => (
  <Card className="h-full">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Images</h3>
      <div className="space-y-4">
        <BasicInformation.ImageField
          file={file}
          handleFileChange={handleFileChange}
        />

        <div className="space-y-2">
          <Label htmlFor="additional-images">Additional Images</Label>
          <div className="space-y-4">
            <Input
              type="file"
              id="additional-images"
              accept={ACCEPTED_IMAGE_TYPES.join(",")}
              onChange={handleAdditionalFilesChange}
              className="cursor-pointer"
              multiple
            />
            <p className="text-sm text-gray-500">
              Upload additional product images (PNG, JPG, GIF up to 10MB each)
            </p>

            {additionalFiles.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mt-4">
                {additionalFiles.map((file, index) => (
                  <div key={index} className="relative">
                    <div className="relative h-24 w-full overflow-hidden rounded-md border">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Additional image ${index + 1}`}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                      onClick={() => removeAdditionalFile(index)}
                    >
                      <span className="sr-only">Remove image</span>✕
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
);

interface CategoriesSectionProps {
  form: UseFormReturn<ProductFormValues>;
  categories: CategoryWithParent[];
}

// Extract category field
const CategoriesSection = ({ form, categories }: CategoriesSectionProps) => (
  <Card className="h-full">
    <CardContent className="pt-6 space-y-4">
      <h3 className="text-lg font-semibold">Categories</h3>
      <div className="space-y-4">
        <BasicInformation.CategoryField form={form} categories={categories} />
      </div>
    </CardContent>
  </Card>
);
