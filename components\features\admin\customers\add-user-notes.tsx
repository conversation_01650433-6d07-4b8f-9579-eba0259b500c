import { SvgSpinners90Ring } from "@/components/common/icons";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    <PERSON><PERSON>Header,
    <PERSON><PERSON><PERSON><PERSON>le,
    DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
    DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/shadcn-button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
    useUpdateUserNotesMutation
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import {
    StickyNote
} from "lucide-react";
import React, { useEffect, useState } from "react";

export function AddUserNotes({ user }: { user: PublicUserWithCustomer }) {
    const [open, setOpen] = useState(false);
    const [notes, setNotes] = useState(user.notes || "");

    const token = useAuthStore((state) => state.token);
    const updateNotesMutation = useUpdateUserNotesMutation(user.id, token);

    useEffect(() => {
        if (open) {
            setNotes(user.notes || "");
        }
    }, [open, user.notes]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        updateNotesMutation.mutate(
            { notes },
            {
                onSuccess: () => {
                    toast({
                        title: "Success",
                        description: "User notes updated successfully",
                        variant: "success",
                    });
                    setOpen(false);
                },
                onError: (error) => {
                    toast({
                        title: "Error",
                        description: error.toString(),
                        variant: "destructive",
                    });
                },
            }
        );
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <StickyNote className="mr-2 h-4 w-4" />
                    <span>{user.notes ? "Update" : "Add"} User Notes</span>
                </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>User Notes</DialogTitle>
                    <DialogDescription>
                        {user.notes ? "Update" : "Add"} notes for {user.first_name}{" "}
                        {user.last_name}
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="notes">Notes</Label>
                            <Textarea
                                id="notes"
                                className="min-h-[200px]"
                                placeholder="Enter notes about this user..."
                                value={notes}
                                onChange={(e) => setNotes(e.target.value)}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setOpen(false)}
                        >
                            Cancel
                        </Button>
                        <Button type="submit" disabled={updateNotesMutation.isPending}>
                            {updateNotesMutation.isPending ? (
                                <>
                                    <SvgSpinners90Ring className="mr-2 h-4 w-4" />
                                    Saving...
                                </>
                            ) : (
                                "Save Notes"
                            )}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
