import { useState, useMemo, useEffect, useCallback } from "react";
import { GetCountries, GetState, GetCity } from "react-country-state-city";
import { ShippingAddress } from "@/supabase/types";

const defaultAddress: Partial<ShippingAddress> = {
  country: "United States",
};

/**
 * Custom hook for handling location data (country, state, city)
 * Initializes location selectors based on address data
 */
export function useLocationData(address?: Partial<ShippingAddress>) {
  const [countryId, setCountryId] = useState<number>(0);
  const [stateId, setStateId] = useState<number>(0);
  const [selectedCountry, setSelectedCountry] = useState<any>(null);
  const [selectedState, setSelectedState] = useState<any>(null);
  const [selectedCity, setSelectedCity] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Memoize address values to prevent unnecessary re-runs
  const addressValues = useMemo(
    () => ({
      country: address?.country || defaultAddress.country,
      state: address?.state,
      city: address?.city,
    }),
    [address?.country, address?.state, address?.city],
  );

  useEffect(() => {
    const initializeLocationData = async () => {
      setIsLoading(true);
      try {
        // Get countries
        const countries = await GetCountries();

        if (!addressValues.country) {
          setIsLoading(false);
          return;
        }

        const matchedCountry = countries.find(
          (c: any) =>
            c.name.toLowerCase() === addressValues.country?.toLowerCase(),
        );

        if (!matchedCountry) {
          setIsLoading(false);
          return;
        }

        setCountryId(matchedCountry.id);
        setSelectedCountry(matchedCountry);

        const states = await GetState(matchedCountry.id);

        if (!addressValues.state) {
          setIsLoading(false);
          return;
        }

        const matchedState = states.find(
          (s: any) =>
            s.name.toLowerCase() === addressValues.state?.toLowerCase(),
        );

        if (!matchedState) {
          setIsLoading(false);
          return;
        }

        setStateId(matchedState.id);
        setSelectedState(matchedState);

        const cities = await GetCity(matchedCountry.id, matchedState.id);

        if (!addressValues.city) {
          setIsLoading(false);
          return;
        }

        const matchedCity = cities.find(
          (c: any) =>
            c.name.toLowerCase() === addressValues.city?.toLowerCase(),
        );

        if (!matchedCity) {
          setIsLoading(false);
          return;
        }

        setSelectedCity(matchedCity);
      } catch (error) {
        console.error("Error initializing location data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLocationData();
  }, [addressValues.country, addressValues.state, addressValues.city]);

  const updateCountry = useCallback((country: any) => {
    setCountryId(country.id);
    setSelectedCountry(country);
    setStateId(0);
    setSelectedState(null);
    setSelectedCity(null);
  }, []);

  const updateState = useCallback((state: any) => {
    setStateId(state.id);
    setSelectedState(state);
    setSelectedCity(null);
  }, []);

  const updateCity = useCallback((city: any) => {
    setSelectedCity(city);
  }, []);

  return useMemo(
    () => ({
      countryId,
      stateId,
      selectedCountry,
      selectedState,
      selectedCity,
      updateCountry,
      updateState,
      updateCity,
      isLoading,
    }),
    [
      countryId,
      stateId,
      selectedCountry,
      selectedState,
      selectedCity,
      updateCountry,
      updateState,
      updateCity,
      isLoading,
    ],
  );
}
