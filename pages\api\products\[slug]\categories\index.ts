import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
    matchRoute({
        POST: checkPermission("create:product_categories", createProductCategoryHandler),
    })
);

export interface CreateProductCategoryResponse {
    error?: string;
    message?: string;
}

export const createProductCategorySchema = z.object({
    category_ids: z.array(
        z.string().min(1, "Category ID is required")
    ),
});

export type CreateProductCategoryRequest = z.infer<typeof createProductCategorySchema>;

async function createProductCategoryHandler(req: NextApiRequest, res: NextApiResponse<CreateProductCategoryResponse>) {
    const { slug } = req.query;

    const parsedData = createProductCategorySchema.safeParse(req.body);

    if (parsedData.error) {
        return res.status(400).json({ error: parsedData.error.issues[0].message });
    }

    const { category_ids } = parsedData.data;

    const supabaseAdminClient = createSupabaseAdminClient();

    const bulkInsertData = category_ids.map((id) => ({ product_id: slug as string, category_id: id }));

    const { data, error } = await supabaseAdminClient
        .schema("public")
        .from("product_categories")
        .insert(bulkInsertData);

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    return res.status(200).json({ message: "Product category created successfully" });
}