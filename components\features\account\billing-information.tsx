import { Skeleton } from "@/components/ui/skeleton";
import { useGetBillingAddressesQuery } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { useMemo, useState } from "react";
import { AddBillingAddressDialog } from "../store/customers/add-billing-address";
import { BillingAddressCard, BillingAddressesSkeleton } from "../store/customers/billing-address";

export function BillingInformation() {
    const userData = useAuthStore((state) => state.data);
    const userId = userData.id;
    const [selectedAddressId, setSelectedAddressId] = useState<string | undefined>(undefined);
    const billingAddresses = useGetBillingAddressesQuery(userId);

    const sortByDefaultAddress = useMemo(() => billingAddresses?.data?.sort((a, b) => {
        if (a.default) return -1;
        if (b.default) return 1;
        return 0;
    }), [billingAddresses.data]);

    // put the pending billing address at the bottom
    const pendingBillingAddress = billingAddresses.data?.find((address) => !address.approved);
    const sortedBillingAddresses = sortByDefaultAddress?.filter((address) => address.id !== pendingBillingAddress?.id) ?? [];
    const sortedBillingAddressesWithPending = [...sortedBillingAddresses, pendingBillingAddress];

    if (billingAddresses.isLoading) {
        return <BillingInformationSkeleton />
    }

    return (
        <div className="flex flex-col gap-4">
            <div>
                <h2 className="text-2xl font-bold">Billing Addresses</h2>
                <p className="text-sm text-gray-500">
                    Update your billing address if needed.
                </p>
            </div>
            <div className="w-full h-full flex flex-wrap items-center justify-start gap-4 pt-6">
                {billingAddresses.data
                    && billingAddresses.data?.length > 0
                    && sortedBillingAddressesWithPending?.map((address, index) => {
                        if (!address) return;

                        return <BillingAddressCard
                            key={`billing-address-${address.id}-${index}`}
                            address={address}
                            isSelected={address.default}
                            onClick={() => {
                                if (address.id) {
                                    setSelectedAddressId(address.id);
                                }
                            }}
                        />
                    })}

                <div className="w-full h-full flex items-center justify-end relative max-w-[240px] max-h-[240px]">
                    <AddBillingAddressDialog />
                </div>
            </div>
        </div>
    )
}

function BillingInformationSkeleton() {
    return (
        <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Billing Addresses</h1>
                <p className="text-sm text-gray-500">
                    Update your billing address if needed.
                </p>
            </div>
            <div className="w-full h-full flex flex-wrap items-center justify-start gap-4 pt-6">
                <BillingAddressesSkeleton />
            </div>
        </div>
    )
}