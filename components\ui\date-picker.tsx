"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/shadcn-button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

function formatDate(date: Date | undefined) {
  if (!date) {
    return ""
  }
  return date.toLocaleDateString("en-US", {
    day: "2-digit",
    month: "long",
    year: "numeric",
  })
}

function isValidDate(date: Date | undefined) {
  if (!date) {
    return false
  }
  return !isNaN(date.getTime())
}

export interface DatePickerProps {
  value?: Date
  onChange: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  minDate?: Date
}

export function DatePicker({
  value,
  onChange,
  placeholder = "Select date",
  disabled = false,
  minDate,
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)
  const [month, setMonth] = React.useState<Date | undefined>(value || new Date())
  const [inputValue, setInputValue] = React.useState(formatDate(value))

  // Update input value when value prop changes
  React.useEffect(() => {
    setInputValue(formatDate(value))
  }, [value])

  return (
    <div className="relative flex">
      <Input
        value={inputValue}
        placeholder={placeholder}
        className="bg-background pr-10"
        disabled={disabled}
        onChange={(e) => {
          const inputDate = new Date(e.target.value)
          setInputValue(e.target.value)
          if (isValidDate(inputDate)) {
            // Check minDate constraint
            if (minDate && inputDate < minDate) {
              return
            }
            onChange(inputDate)
            setMonth(inputDate)
          }
        }}
        onKeyDown={(e) => {
          if (e.key === "ArrowDown" && !disabled) {
            e.preventDefault()
            setOpen(true)
          }
        }}
        aria-label={value ? `Selected date: ${formatDate(value)}` : placeholder}
      />
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            className="absolute top-1/2 right-2 size-6 -translate-y-1/2"
            disabled={disabled}
            aria-label="Open calendar"
          >
            <CalendarIcon className="size-3.5" />
            <span className="sr-only">Select date</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto overflow-hidden p-0"
          align="end"
          alignOffset={-8}
          sideOffset={10}
        >
          <Calendar
            mode="single"
            selected={value}
            captionLayout="dropdown"
            month={month}
            onMonthChange={setMonth}
            onSelect={(date) => {
              onChange(date)
              setInputValue(formatDate(date))
              setOpen(false)
            }}
            disabled={(date) => {
              if (minDate) {
                return date < minDate
              }
              return false
            }}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}