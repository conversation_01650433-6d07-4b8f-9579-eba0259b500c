import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";

export default checkAdmin(
    matchRoute({
        DELETE: checkPermission("delete:product_categories", deleteProductCategoryHandler),
    })
);

export interface DeleteProductCategoryResponse {
    error?: string;
    message?: string;
}

async function deleteProductCategoryHandler(req: NextApiRequest, res: NextApiResponse<DeleteProductCategoryResponse>) {
    const { slug, category_id } = req.query;

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
        .schema("public")
        .from("product_categories")
        .delete()
        .eq("product_id", slug as string)
        .eq("category_id", category_id as string);

    if (error) {
        return res.status(400).json({ error: error.message });
    }

    return res.status(200).json({ message: "Product category deleted" });
}
