import { ContentRenderer } from "@/components/features/block-editor/content-renderer";
import StoreLayout from "@/components/features/store/layout";
import { ProductImageCarousel } from "@/components/features/store/product-carousel";
import { ProductDetails } from "@/components/features/store/product-details";
import { ProductOptionComponent } from "@/components/features/store/product-option";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { QuantitySelector } from "@/components/ui/quantity-selector";
import { Button, buttonVariants } from "@/components/ui/shadcn-button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { formatPrice, parseProductOptions } from "@/lib/utils";
import { ProductWithGrouPricing } from "@/pages/api/products/[slug]";
import { useGetCustomerQuery, useGetImage } from "@/queries/customer-queries";
import { useGetProductByIdQuery } from "@/queries/product-queries";
import useAuthStore from "@/stores/auth-store";
import useCartStore, { SelectedOption } from "@/stores/cart-store";
import { OptionSelectProps } from "@/supabase/types";
import {
  ChevronLeft,
  FolderIcon,
  Loader2,
  LogIn,
  ShoppingCart,
  Tag,
  TextQuote,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import {
  Dialog,
  DialogPortal,
  DialogOverlay,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog-shadcn";
import { Textarea } from "@/components/ui/textarea";

// Extend the ProductWithGrouPricing interface to include is_quote if needed
declare module "@/pages/api/products/[slug]" {
  interface ProductWithGrouPricing {
    is_quote?: boolean;
  }
}

export default function ProductPage() {
  const router = useRouter();
  const { slug } = router.query;
  const { data, isLoading } = useGetProductByIdQuery(slug as string);
  const { product, related_products } = data ?? {};

  const params = new URLSearchParams(window.location.search);
  const calculator = JSON.parse(
    decodeURIComponent(params.get("calculator") || "{}")
  );

  const userData = useAuthStore((state) => state.data);
  const userId = userData?.id;
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)();
  const { data: customerData } = useGetCustomerQuery(userId);
  const { toast } = useToast();
  const addItem = useCartStore((state) => state.addItem);
  const [addingToCart, setAddingToCart] = useState(false);

  const [quantity, setQuantity] = useState(1);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOption[]>([]);
  const [optionErrors, setOptionErrors] = useState<{ [key: string]: boolean }>(
    {}
  );

  const customerCategories = useMemo(() => {
    return customerData?.customer?.categories ?? [];
  }, [customerData]);

  const customerCategoriesId = useMemo(() => {
    return customerCategories.map((category) => category.id);
  }, [customerCategories]);

  const productCategories = useMemo(() => {
    return product?.product_categories ?? [];
  }, [product]);

  const hasCategory = useMemo(() => {
    return productCategories.some((category) =>
      customerCategoriesId.includes(category.category_id?.id ?? "")
    );
  }, [productCategories, customerCategories]);

  const productGroupPrice = product?.product_group_prices?.find(
    (price) => price.group_id === customerData?.customer?.group?.id
  );
  const customerHasDiscount = isAuthenticated && productGroupPrice !== null;
  const isCustomerVisitor = customerData?.customer?.group?.name
    ?.toLowerCase()
    ?.includes("visitor");

  const discountedPrice =
    product?.price === -1
      ? -1
      : (customerHasDiscount
          ? productGroupPrice?.custom_price ?? product?.price
          : product?.price) ?? 0;

  const parsedOptions = useMemo(() => {
    if (!product?.options) return [];
    return parseProductOptions(product.options as any);
  }, [product?.options]);

  // Check if product has additional images
  const hasAdditionalImages =
    product?.additional_images &&
    Array.isArray(product.additional_images) &&
    product.additional_images.length > 0;

  // Process main image - use the useGetImage hook for Supabase storage images
  const mainImagePath = product?.image || "";
  const { data: mainImageUrl, isLoading: mainImageLoading } =
    useGetImage(mainImagePath);
  const displayMainImage = mainImageUrl || mainImagePath;

  // Track additional image loading state
  const [additionalImagesLoading, setAdditionalImagesLoading] = useState(false);

  // Store processed additional images
  const [processedAdditionalImages, setProcessedAdditionalImages] = useState<
    string[]
  >([]);

  // Process additional images
  useEffect(() => {
    // If there are no additional images, exit early
    if (!hasAdditionalImages) {
      setProcessedAdditionalImages([]);
      return;
    }

    // Start loading
    setAdditionalImagesLoading(true);

    // Function to process each additional image
    const processImages = async () => {
      try {
        // Get the array of additional images
        const additionalImages = product?.additional_images || [];
        setProcessedAdditionalImages(additionalImages);
        // We're letting the useGetImage hook handle the loading when needed in the carousel component
      } catch (error) {
        console.error("Error processing additional images:", error);
      } finally {
        setAdditionalImagesLoading(false);
      }
    };

    processImages();
  }, [hasAdditionalImages, product?.additional_images]);

  const handleOptionChange = (option: OptionSelectProps) => {
    setSelectedOptions((prev) => {
      // Find if this option exists already
      const existingIndex = prev.findIndex((opt) => opt.name === option.name);

      if (existingIndex >= 0) {
        // Update existing option
        const updated = [...prev];
        updated[existingIndex] = option;
        return updated;
      } else {
        // Add new option
        return [...prev, option];
      }
    });

    // Clear error for this option if it was previously marked as error
    if (optionErrors[option.name]) {
      setOptionErrors((prev) => ({
        ...prev,
        [option.name]: false,
      }));
    }
  };

  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const [quoteInformation, setQuoteInformation] = useState("");

  const handleAddToCart = async () => {
    if (product?.draft || !hasCategory) {
      return;
    }

    if (!product || addingToCart) return;
    // Moved setAddingToCart(true) after validation check

    // Check for unselected options and set errors
    let hasErrors = false;
    const newErrors: { [key: string]: boolean } = {};

    parsedOptions.forEach((option) => {
      const isSelected = selectedOptions.some(
        (opt) => opt.name === option.name && opt.value !== ""
      );

      if (!isSelected) {
        newErrors[option.name] = true;
        hasErrors = true;
      }
    });

    setOptionErrors(newErrors);

    if (hasErrors) {
      // If errors, stop here before setting loading state
      return;
    }

    // Set loading state only if validation passes
    setAddingToCart(true);

    // Only allow adding to cart if price is available
    if (discountedPrice >= 0) {
      addItem({
        id: product.id ?? "",
        name: product.name ?? "",
        price: discountedPrice ?? 0,
        quantity: quantity,
        sku: product.sku ?? "",
        image: product.image ?? "",
        selected: false,
        discount: discountedPrice,
        calculatorData: calculator ?? {},
        selectedOptions,
      });
    }

    await Promise.resolve(
      setTimeout(() => {
        toast({
          title: "Added to cart",
          description: `${product.name} - ${product.sku} added to your cart.`,
          variant: "success",
        });
        setAddingToCart(false);
      }, 500)
    );
  };

  const handleOpenQuoteModal = () => {
    setIsQuoteModalOpen(true);
  };

  const handleQuoteSubmit = async () => {
    if (!product) return;

    try {
      setAddingToCart(true);

      // Prepare quote data for API
      const quoteData = {
        product_id: product.id,
        user_id: userId,
        quote_information: quoteInformation,
        quantity: quantity,
        options: selectedOptions
          .filter((opt) => opt.value)
          .map((opt) => ({
            name: opt.name,
            value: opt.value,
          })),
      };

      const response = await fetch("/api/quotes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(quoteData),
      });

      if (!response.ok) {
        throw new Error("Failed to submit quote request");
      }

      toast({
        title: "Quote Request Submitted",
        description: `Your quote request for ${product.name} has been submitted successfully.`,
        variant: "success",
      });

      setQuoteInformation("");
      setIsQuoteModalOpen(false);
    } catch (error) {
      console.error("Quote submission error:", error);
      toast({
        title: "Error",
        description:
          "There was a problem submitting your quote request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingToCart(false);
    }
  };

  useEffect(() => {
    if (parsedOptions && parsedOptions.length > 0) {
      const initialOptions = parsedOptions.map((option) => {
        // Initialize value based on type. Select starts empty.
        let initialValue = "";
        if (option.type !== "select") {
          // For non-select types, you might want a default or leave empty
          // Depending on requirements, currently leaving empty like select
          initialValue = ""; // Or handle text/number defaults if needed
        }

        // Price should be 0 initially if no value is selected
        const initialPrice = 0;

        return {
          name: option.name,
          value: initialValue,
          price: initialPrice,
        };
      });
      setSelectedOptions(initialOptions);
    }
  }, [parsedOptions]); // Dependency array remains the same

  const backToProducts = () => {
    // Check for "from" parameter to determine where to redirect
    const from = router.query.from as string | undefined;

    if (from) {
      if (from === "all") {
        // User came from "All Products", redirect back to all products
        router.push("/store/products");
        return;
      } else {
        // User came from a specific category, redirect back to that category
        const encodedCategory = encodeURIComponent(from);
        router.push(`/store/products?category=${encodedCategory}&page=1`);
        return;
      }
    }

    // Fallback to old behavior if no "from" parameter (for backward compatibility)
    const category = product?.product_categories?.at(0)?.category_id?.name;

    if (category) {
      const url = encodeURIComponent(category);
      console.log({ url });
      router.push(`/store/products?category=${url}&page=1`);
      return;
    }

    router.push("/store/products");
  };

  if (isLoading || !product) {
    return (
      <StoreLayout>
        <Head>
          <title>Product | {product?.name}</title>
        </Head>
        <div className="container mx-auto py-8">
          <Button variant="ghost" onClick={backToProducts} className="mb-4">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Button>

          <div className="grid md:grid-cols-2 gap-8">
            <Skeleton className="aspect-square rounded-xl" />
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <div className="flex gap-4 pt-8">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-48" />
              </div>
            </div>
          </div>
        </div>
      </StoreLayout>
    );
  }

  return (
    <StoreLayout>
      <Head>
        <title>Product | {product.name}</title>
      </Head>
      <div className="container mx-auto py-8">
        <Button variant="ghost" onClick={backToProducts} className="mb-6">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Products
        </Button>

        <div className="grid md:grid-cols-2 gap-10">
          {/* Product Image Carousel */}
          {mainImageLoading || additionalImagesLoading ? (
            <Skeleton className="aspect-square rounded-xl" />
          ) : (
            <div className="flex flex-col space-y-6 md:sticky md:top-24 self-start">
              <ProductImageCarousel
                mainImage={displayMainImage}
                additionalImages={processedAdditionalImages}
              />
            </div>
          )}

          {/* Product Details */}
          <div className="space-y-6">
            <h1 className="text-3xl font-normal tracking-tight flex items-center gap-2">
              {product.name}
              {product.draft && (
                <Badge
                  variant="primary"
                  className="ml-2 uppercase font-semibold tracking-wider "
                >
                  Draft
                </Badge>
              )}
            </h1>

            <div className="space-y-3">
              {product?.sku === "EMV10T" && isAuthenticated ? (
                <div className="flex items-center gap-4">
                  <span className="text-lg text-gray-500 italic">
                    Please contact Maxton at{" "}
                    <a
                      href="tel:**************"
                      className="underline text-primary"
                    >
                      **************
                    </a>{" "}
                    or email{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="underline text-primary"
                    >
                      <EMAIL>
                    </a>
                  </span>
                </div>
              ) : isAuthenticated && hasCategory ? (
                <div className="flex items-center gap-4">
                  {!isCustomerVisitor &&
                  discountedPrice >= 0 &&
                  !product.is_quote ? (
                    <span className="text-2xl font-bold">
                      ${discountedPrice.toFixed(2)}
                    </span>
                  ) : (
                    <span className="text-xl text-gray-500 italic">
                      Contact for pricing
                    </span>
                  )}
                </div>
              ) : isAuthenticated && !hasCategory ? (
                <div className="flex items-center gap-4">
                  <span className="text-xl text-gray-500 italic">
                    Contact for pricing
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-4">
                  <span className="text-xl font-semibold">
                    To see pricing, please{" "}
                    <Link
                      href={`/log-in?redirect=/store/products/${slug}`}
                      className="text-primary underline"
                    >
                      login
                    </Link>{" "}
                    or{" "}
                    <Link
                      href={`/sign-up?redirect=/store/products/${slug}`}
                      className="text-primary underline"
                    >
                      register
                    </Link>
                    .
                  </span>
                </div>
              )}

              <div className="flex flex-wrap gap-2 pt-2">
                {product.sku && (
                  <Badge
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    <Tag className="h-3 w-3" />
                    <span>Product Code: {product.sku}</span>
                  </Badge>
                )}
                {(product?.product_categories?.length ?? 0) > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {product?.product_categories?.map((category) => {
                      const categoryName = category.category_id?.name;
                      if (!categoryName) return null; // Skip if category name is missing

                      // Encode category name and remove trailing slash
                      const link = `/store/products?category=${encodeURIComponent(
                        categoryName
                      )}&page=1`;

                      return (
                        <Badge
                          variant="outline"
                          className="flex items-center gap-1 hover:bg-primary hover:text-white"
                          key={category.category_id?.id}
                        >
                          <FolderIcon className="h-3 w-3" />
                          {/* Use the constructed relative link directly */}
                          <Link
                            target="_self"
                            rel="noreferrer noopener"
                            href={link}
                          >
                            <span>{categoryName}</span>{" "}
                            {/* Use the validated categoryName */}
                          </Link>
                        </Badge>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="text-muted-foreground tracking-wide leading-7">
                <ContentRenderer
                  content={(product && product.description) || ""}
                  className="text-zinc-700"
                />
              </div>
            </div>

            {/* Product Options */}
            {parsedOptions && parsedOptions.length > 0 && (
              <div className="space-y-4 pt-4 border-t">
                <h3 className="font-bold">Available Options</h3>
                <div className="grid gap-4">
                  {parsedOptions.map((option) => (
                    <div key={option.name} className="space-y-2">
                      <ProductOptionComponent
                        {...option}
                        onSelect={handleOptionChange}
                        error={optionErrors[option.name]}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Temporarily hide quantity selector and button for EMV10T */}
            {product?.sku === "EMV10T" ? (
              <div className="flex items-center gap-4">
                <span className="text-lg text-gray-500 italic">
                  Please contact Maxton at{" "}
                  <a
                    href="tel:**************"
                    className="underline text-primary"
                  >
                    **************
                  </a>{" "}
                  or email{" "}
                  <a
                    href="mailto:<EMAIL>"
                    className="underline text-primary"
                  >
                    <EMAIL>
                  </a>
                </span>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <QuantitySelector
                  quantity={quantity}
                  onDecrement={() => setQuantity((q) => Math.max(1, q - 1))}
                  onIncrement={() => setQuantity((q) => q + 1)}
                />
                {isAuthenticated ? (
                  <Button
                    className={`gap-2 flex-1  ${buttonVariants({
                      variant: "primary",
                    })}`}
                    onClick={
                      product.draft || !hasCategory
                        ? () => {}
                        : product.is_quote
                        ? handleOpenQuoteModal
                        : handleAddToCart
                    }
                    size="lg"
                    disabled={
                      addingToCart ||
                      (product.draft ?? false) ||
                      !hasCategory ||
                      isCustomerVisitor
                    }
                  >
                    {addingToCart && !product.is_quote ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Adding to Cart</span>
                      </>
                    ) : product.is_quote ? (
                      <>
                        <TextQuote className="h-4 w-4" />
                        <span>Request a Quote</span>
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="h-4 w-4" />
                        <span>Add to Cart</span>
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    className={`gap-2 flex-1  ${buttonVariants({
                      variant: "primary",
                    })} `}
                    size="lg"
                    asChild
                  >
                    <Link href={`/log-in?redirect=/store/products/${slug}`}>
                      <LogIn className="h-4 w-4" />
                      Login to Purchase
                    </Link>
                  </Button>
                )}
              </div>
            )}

            {/* Create a card to display all related products */}
            {related_products && related_products.length > 0 ? (
              <div className="pt-8 border-t w-full">
                <h2 className="text-2xl font-semibold mb-4">
                  Related Products
                </h2>
                <div className="flex flex-col gap-2 items-start justify-start w-full">
                  {related_products.map((relatedProduct) => {
                    return (
                      <RelatedProductCard
                        key={`related-product-${relatedProduct.id}`}
                        product={relatedProduct}
                        isAuthenticated={isAuthenticated}
                        hasCategory={hasCategory}
                        customerData={customerData}
                      />
                    );
                  })}
                </div>
              </div>
            ) : null}

            {/* Product Specifications and Additional Details */}
            <ProductDetails
              specifications={{
                specification:
                  (product.specifications as any)?.specifications || {},
                finish_and_material:
                  (product.specifications as any)?.finish_and_material || "",
                features: (product.specifications as any)?.features || [],
              }}
              installation={product.installation_instructions as any}
              helpfulHints={product.helpful_hints as any}
              deliveryAndShipping={product.delivery_and_shipping as any}
              warranty={product.warranty as any}
            />
          </div>
        </div>
      </div>
      {/* Quote Request Modal */}
      <RequestQuoteModal
        isQuoteModalOpen={isQuoteModalOpen}
        setIsQuoteModalOpen={setIsQuoteModalOpen}
        product={product}
        quantity={quantity}
        selectedOptions={selectedOptions}
        quoteInformation={quoteInformation}
        setQuoteInformation={setQuoteInformation}
        handleQuoteSubmit={handleQuoteSubmit}
        addingToCart={addingToCart}
      />
    </StoreLayout>
  );
}

interface RelatedProductCardProps {
  product: Partial<ProductWithGrouPricing>;
  isAuthenticated: boolean;
  hasCategory: boolean;
  customerData: any;
}

function RelatedProductCard({
  product,
  isAuthenticated,
  hasCategory,
  customerData,
}: RelatedProductCardProps) {
  const router = useRouter();
  const relatedProductUrl = useGetImage(product?.image ?? "");
  const relatedProductImage = relatedProductUrl.data ?? product?.image ?? "";

  // Calculate discounted price using the same logic as ProductCard
  const productGroupPrice = product?.product_group_prices?.find(
    (price) => price.group_id === customerData?.customer?.group?.id
  );
  const customerHasDiscount = isAuthenticated && productGroupPrice !== null;
  const isCustomerVisitor = customerData?.customer?.group?.name
    ?.toLowerCase()
    ?.includes("visitor");

  // Use the exact same pricing logic as ProductCard
  const discountedPrice = customerHasDiscount
    ? productGroupPrice?.custom_price ?? product?.price
    : product?.price ?? 0;

  // Get current "from" parameter to pass it along for related products
  const from = router.query.from as string | undefined;
  const relatedProductHref = from
    ? `/store/products/${product.sku}?from=${encodeURIComponent(from)}`
    : `/store/products/${product.sku}`;

  return (
    <Link
      key={product?.id}
      href={relatedProductHref}
      passHref
      className="w-full"
    >
      <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-200 h-48 flex flex-row items-stretch">
        <CardHeader className="p-0 w-48 h-48 flex-shrink-0">
          <div className="relative w-full h-full p-4">
            <img
              src={relatedProductImage}
              alt={product?.name}
              className="w-full h-full object-contain object-center"
            />
          </div>
        </CardHeader>
        <CardContent className="p-4 flex flex-col flex-grow overflow-hidden">
          <CardTitle className="text-lg font-medium mb-2">
            {product?.name}
          </CardTitle>
          <p className="text-sm font-normal flex-grow overflow-hidden">
            <ContentRenderer
              content={(product && product.description) || ""}
              truncate={true}
              maxLength={150}
              className="text-zinc-700 line-clamp-3"
            />
          </p>
          {isAuthenticated && hasCategory ? (
            <>
              {!isCustomerVisitor &&
              discountedPrice !== undefined &&
              discountedPrice >= 0 ? (
                <p className="text-lg font-semibold text-primary mt-2">
                  ${discountedPrice.toFixed(2)}
                </p>
              ) : (
                <p className="text-base text-gray-500 italic mt-2">
                  Contact for pricing
                </p>
              )}
            </>
          ) : isAuthenticated && !hasCategory ? (
            <p className="text-base text-gray-500 italic mt-2">
              Contact for pricing
            </p>
          ) : (
            <p className="text-base text-gray-500 italic mt-2">
              Login to see pricing
            </p>
          )}
        </CardContent>
      </Card>
    </Link>
  );
}

function RequestQuoteModal({
  isQuoteModalOpen,
  setIsQuoteModalOpen,
  product,
  quantity,
  selectedOptions,
  quoteInformation,
  setQuoteInformation,
  handleQuoteSubmit,
  addingToCart,
}) {
  return (
    <Dialog open={isQuoteModalOpen} onOpenChange={setIsQuoteModalOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Request a Quote</DialogTitle>
          <DialogDescription>
            Please provide any additional information for your quote request for{" "}
            {product?.name}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h3 className="font-medium">Product</h3>
            <p>{product?.name}</p>
            {product?.sku && (
              <p className="text-sm text-muted-foreground">
                SKU: {product.sku}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">Quantity</h3>
            <p>{quantity}</p>
          </div>

          {selectedOptions.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">Selected Options</h3>
              <div className="text-sm">
                {selectedOptions.map(
                  (option, index) =>
                    option.value && (
                      <div
                        key={index}
                        className="flex justify-between py-1 border-b last:border-b-0"
                      >
                        <span>{option.name}:</span>
                        <span className="font-medium">{option.value}</span>
                      </div>
                    )
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <h3 className="font-medium">Quote Information</h3>
            <Textarea
              placeholder="Please specify any requirements or questions you have about this product..."
              value={quoteInformation}
              onChange={(e) => setQuoteInformation(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button
            type="button"
            variant="primary"
            disabled={addingToCart || !product.is_quote}
            onClick={handleQuoteSubmit}
          >
            {addingToCart ? "Submitting Quote..." : "Submit Quote Request"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
