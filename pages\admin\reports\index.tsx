import AdminLayout from "@/components/features/admin/layout";
import { ChevronRight } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useRouter } from "next/router";

// Define interface for report items
interface ReportItem {
    title: string;
    description: string;
    link: string;
    disabled?: boolean;
}

export default function ReportsPage() {
    const router = useRouter();

    const reports: ReportItem[] = [
        {
            title: "Sales by day",
            description: "View detailed reports on your daily sales",
            link: "/admin/reports/sales?period=day",
        },
        {
            title: "Sales by week",
            description: "View detailed reports on your weekly sales",
            link: "/admin/reports/sales-by-week",
        },
        {
            title: "Sales by month",
            description: "View detailed reports on your monthly sales",
            link: "/admin/reports/sales-by-month",
        },
        // {
        //     title: "Sales by product",
        //     description: "View detailed reports on sales by product",
        //     link: "#",
        //     disabled: true,
        // },
        // {
        //     title: "Sales by SKU",
        //     description: "View detailed reports on sales by SKU",
        //     link: "#",
        //     disabled: true,
        // },
        // {
        //     title: "Sales by shipping country",
        //     description: "View detailed reports on sales by shipping country",
        //     link: "#",
        //     disabled: true,
        // },
        // {
        //     title: "Sales by customer",
        //     description: "View detailed reports on sales by customer",
        //     link: "#",
        //     disabled: true,
        // },
    ];

    return (
        <AdminLayout>
            <Head>
                <title>Reports</title>
            </Head>
            <div className="p-6">
                <h1 className="text-3xl font-bold mb-2">Sales reports</h1>
                <p className="text-gray-500 mb-8">View detailed reports on your sales</p>

                <div className="border rounded-lg divide-y">
                    {reports.map((report, index) => (
                        <div
                            key={index}
                            className={`p-4 hover:bg-gray-50 transition-colors ${report.disabled ? 'opacity-60' : ''}`}
                        >
                            {report.disabled ? (
                                <div className="flex justify-between items-center">
                                    <div>
                                        <h2 className="text-lg font-medium">{report.title}</h2>
                                        <p className="text-gray-500">{report.description}</p>
                                    </div>
                                    <span className="text-sm bg-gray-100 px-2 py-1 rounded">Coming Soon</span>
                                </div>
                            ) : (
                                <Link href={report.link} className="flex justify-between items-center">
                                    <div>
                                        <h2 className="text-lg font-medium">{report.title}</h2>
                                        <p className="text-gray-500">{report.description}</p>
                                    </div>
                                    <ChevronRight className="h-5 w-5 text-gray-400" />
                                </Link>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </AdminLayout>
    );
}