{"image": "mcr.microsoft.com/devcontainers/universal:2", "appPort": 3000, "forwardPorts": [3000], "features": {"ghcr.io/devcontainers/features/node:1": {"version": "18", "nvmVersion": "0.39"}}, "postCreateCommand": "nvm use 18 && nvm alias default 18", "customizations": {"vscode": {"extensions": ["saoudrizwan.claude-dev", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "VisualStudioExptTeam.vscodeintellicode", "eamodio.gitlens"]}}}