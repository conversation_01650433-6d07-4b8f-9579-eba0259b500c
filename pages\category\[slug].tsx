import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { groq } from "next-sanity";
import { PreviewSuspense } from "next-sanity/preview";
import { sanityClient, getClient } from "lib/sanity.client";
import {
  allCategories,
  categoryQuery,
  postsByCategory,
  globalSEOQuery,
  allArchives,
} from "pages/api/query";
import { usePreview } from "lib/sanity.preview";
import { PageSections } from "components/page";
import CategorySection from "components/category";
import { PreviewBanner } from "components/PreviewBanner";
import { PreviewNoContent } from "components/PreviewNoContent";
import { filterDataToSingleItem } from "components/list";
import PageNotFound from "pages/404";
import InlineEditorContextProvider from "context/InlineEditorContext";
import { GetStaticPaths, GetStaticProps } from "next";
import { CommonPageData, BlogsData, PostCategory, DefaultSeoData } from "types";
import { ArchivePageData } from "@/pages/archive/[year]/[month]";

interface PageBySlugProps {
  data: CategoryPageData | null;
  preview: boolean;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

interface DocumentWithPreviewProps {
  data: CategoryPageData | null;
  slug: string | string[] | undefined;
  token: string;
  source: string;
  defaultSeo: DefaultSeoData;
}

export interface CategoryPageData {
  category: PostCategory | null;
  posts: BlogsData[] | null;
  categories: PostCategory[] | null;
  allArchives?: {
    years: any[];
    months: any[];
    validCombinations: Array<{
      year: {
        _id: string;
        title: string;
        slug: string;
      };
      month: {
        _id: string;
        title: string;
        slug: string;
      };
    }>;
  };
  archive: ArchivePageData["archive"];
}

export interface PageData extends CommonPageData {
  collections: any;
  slug: string | string[];
  title: string;
}

export function CategoryPage({
  data,
  preview,
  token,
  source,
  defaultSeo,
}: PageBySlugProps) {
  const router = useRouter();
  const slug = router.query.slug;
  const showInlineEditor = source === "studio";

  if (preview) {
    return (
      <>
        <PreviewBanner />
        <PreviewSuspense fallback="Loading...">
          <InlineEditorContextProvider showInlineEditor={showInlineEditor}>
            <DocumentWithPreview
              {...{ data, token, slug, source, defaultSeo }}
            />
          </InlineEditorContextProvider>
        </PreviewSuspense>
      </>
    );
  }

  return <Document {...{ data, defaultSeo }} />;
}

/**
 *
 * @param {data} Data from getStaticProps based on current slug value
 *
 * @returns Document with published data
 */
function Document({
  data,
  defaultSeo,
}: {
  data: CategoryPageData | null;
  defaultSeo: DefaultSeoData;
}) {
  const publishedData = data?.category; // latest published data in Sanity

  // General safeguard against empty data
  if (!publishedData) {
    return null;
  }

  if (publishedData?.hasNeverPublished) {
    return <PageNotFound />;
  }

  const { title, seo, _type } = publishedData;

  return (
    <>
      <Head>
        <title>
          {seo?.seoTitle ?? title ?? "WebriQ Studio"} - Maxton Manufacturing
          Company
        </title>
      </Head>

      {/*  Show page sections */}
      {data?.category && (
        <CategorySection
          category={data?.category}
          posts={data?.posts}
          categories={data?.categories}
          allArchives={data?.allArchives}
          archive={data?.archive}
        />
      )}
    </>
  );
}

/**
 *
 * @param data Data from getStaticProps based on current slug value
 * @param slug Slug value from getStaticProps
 * @param token Token value supplied via `/api/preview` route
 * @param source Source value supplied via `/api/preview` route
 *
 * @returns Document with preview data
 */
function DocumentWithPreview({
  data,
  slug,
  token,
  defaultSeo,
}: DocumentWithPreviewProps) {
  const previewDataEventSource = usePreview(token, categoryQuery, {
    slug: typeof slug === "string" ? slug : slug?.[0],
  });
  const previewData = previewDataEventSource?.[0] || previewDataEventSource;

  if (!previewData) {
    return null;
  }

  const { title, seo, _type } = previewData;
  return (
    <>
      <Head>
        <title>{seo?.seoTitle ?? title ?? "WebriQ Studio"}</title>
      </Head>

      {/* if page has no sections, show no sections only in preview */}
      {_type === "page" &&
        "sections" in previewData &&
        (!previewData ||
          !previewData?.sections ||
          previewData?.sections?.length === 0) && <PreviewNoContent />}

      {/*  Show page sections */}
      {data?.category && (
        <CategorySection
          category={previewData}
          posts={data?.posts}
          categories={data.categories}
          allArchives={data.allArchives}
          archive={data?.archive}
        />
      )}
    </>
  );
}

export const getStaticProps: GetStaticProps = async ({
  params,
  preview = false,
  previewData = {},
}: any): Promise<{ props: PageBySlugProps; revalidate?: number }> => {
  const client =
    preview && previewData?.token
      ? getClient(false).withConfig({ token: previewData.token })
      : getClient(preview);

  const [category, posts, categories, globalSEO, archives] = await Promise.all([
    client.fetch(categoryQuery, { slug: params.slug }),
    client.fetch(postsByCategory, { slug: params.slug }),
    client.fetch(allCategories),
    client.fetch(globalSEOQuery),
    client.fetch(allArchives),
  ]);

  const singleCategoryData = filterDataToSingleItem(category, preview);

  if (!singleCategoryData) {
    return {
      props: {
        preview,
        token: (preview && previewData.token) || "",
        source: (preview && previewData?.source) || "",
        data: null,
        defaultSeo: globalSEO || null,
      },
    };
  }

  return {
    props: {
      preview,
      token: (preview && previewData.token) || "",
      source: (preview && previewData?.source) || "",
      data: {
        category: singleCategoryData || null,
        posts: posts || null,
        categories: categories || null,
        allArchives: archives || null,
        archive: null, // Since category pages don't have a specific archive context
      },
      defaultSeo: globalSEO || null,
    },
    revalidate: process.env.SANITY_REVALIDATE_SECRET ? undefined : 60,
  };
};

export const getStaticPaths: GetStaticPaths = async () => {
  const paths = await sanityClient.fetch(
    groq`*[_type == "category" && defined(slug.current)][].slug.current`
  );

  return {
    paths: paths.map((slug) => ({ params: { slug } })),
    fallback: true,
  };
};

export default React.memo(CategoryPage);
