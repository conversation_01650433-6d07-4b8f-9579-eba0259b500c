import React from "react";
import dynamic from "next/dynamic";
import {
  LabeledRoute,
  Portfolios,
  PortfoliosWithCategories,
  SectionsProps,
} from "../../../types";
import * as PortfolioVariant from "@stackshift-ui/portfolio";
import { PortableTextBlock } from "@sanity/types";

const Variants = {
  variant_a: PortfolioVariant.Portfolio_A,
  variant_b: PortfolioVariant.Portfolio_B,
  variant_c: PortfolioVariant.Portfolio_C,
  variant_d: PortfolioVariant.Portfolio_D,
  variant_e: dynamic(() => import("./variant_e")),
  variant_f: dynamic(() => import("./variant_f")),
  variant_g: dynamic(() => import("./variant_g")),
  variant_h: dynamic(() => import("./variant_h")),
};

export interface PortfolioProps {
  caption?: string;
  title?: string;
  portfoliosWithCategory?: PortfoliosWithCategories[];
  portfolios?: Portfolios[];
  primaryButton?: LabeledRoute;
  length?: number;
  firstColumn?: PortableTextBlock[];
  hasCalculator?: boolean;
}

const displayName = "Portfolio";

export const Portfolio: React.FC<SectionsProps> = ({ data }) => {
  const variant = data?.variant;
  const Variant = variant && Variants?.[variant as keyof typeof Variants];

  const props = {
    caption: data?.variants?.subtitle ?? undefined,
    title: data?.variants?.title ?? undefined,
    portfoliosWithCategory:
      data?.variants?.portfoliosWithCategories ?? undefined,
    portfolios: data?.variants?.portfolios ?? undefined,
    primaryButton: data?.variants?.primaryButton ?? undefined,
    length: data?.variants?.length,
    firstColumn: data?.variants?.firstColumn ?? undefined,
    hasCalculator: data?.variants?.hasCalculator ?? undefined,
  };

  return Variant ? <Variant {...props} /> : null;
};

Portfolio.displayName = displayName;
