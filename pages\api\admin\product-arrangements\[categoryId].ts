import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";

export interface GetArrangementsByCategoryResponse {
  data?: any[];
  error?: string;
}

export interface DeleteArrangementResponse {
  success?: boolean;
  error?: string;
}

async function getArrangementsByCategory(
  req: NextApiRequest,
  res: NextApiResponse<GetArrangementsByCategoryResponse>
) {
  try {
    const { categoryId } = req.query;

    if (!categoryId || typeof categoryId !== "string") {
      return res.status(400).json({ error: "Category ID is required" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    // Verify category exists
    const { data: category } = await supabaseAdminClient
      .from("categories")
      .select("id")
      .eq("id", categoryId)
      .single();

    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }

    // Get arrangements for this category
    const { data, error } = await supabaseAdminClient
      .from("product_arrangements")
      .select(`
        *,
        products:product_id (
          id,
          name,
          sku,
          price,
          image
        )
      `)
      .eq("category_id", categoryId)
      .order("position", { ascending: true });

    if (error) {
      console.error("Error fetching arrangements by category:", error);
      return res.status(500).json({ error: "Failed to fetch arrangements" });
    }

    return res.status(200).json({ data: data || [] });
  } catch (error) {
    console.error("Error in getArrangementsByCategory:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

async function deleteArrangement(
  req: NextApiRequest,
  res: NextApiResponse<DeleteArrangementResponse>
) {
  try {
    const { categoryId } = req.query;
    const { arrangement_id } = req.body;

    if (!categoryId || typeof categoryId !== "string") {
      return res.status(400).json({ error: "Category ID is required" });
    }

    if (!arrangement_id || typeof arrangement_id !== "string") {
      return res.status(400).json({ error: "Arrangement ID is required" });
    }

    const supabaseAdminClient = createSupabaseAdminClient();

    // Verify the arrangement exists and belongs to the category
    const { data: arrangement } = await supabaseAdminClient
      .from("product_arrangements")
      .select("id, position, category_id")
      .eq("id", arrangement_id)
      .eq("category_id", categoryId)
      .single();

    if (!arrangement) {
      return res.status(404).json({ error: "Arrangement not found" });
    }

    // Delete the arrangement
    const { error: deleteError } = await supabaseAdminClient
      .from("product_arrangements")
      .delete()
      .eq("id", arrangement_id);

    if (deleteError) {
      console.error("Error deleting arrangement:", deleteError);
      return res.status(500).json({ error: "Failed to delete arrangement" });
    }

    // Shift remaining arrangements to fill the gap
    const { data: remainingArrangements } = await supabaseAdminClient
      .from("product_arrangements")
      .select("id, position")
      .eq("category_id", categoryId)
      .gt("position", arrangement.position)
      .order("position", { ascending: true });

    // Update positions one by one to avoid conflicts
    if (remainingArrangements && remainingArrangements.length > 0) {
      for (const remainingArrangement of remainingArrangements) {
        await supabaseAdminClient
          .from("product_arrangements")
          .update({ position: (remainingArrangement.position ?? 0) - 1 })
          .eq("id", remainingArrangement.id);
      }
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error("Error in deleteArrangement:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
}

export default checkAdmin(matchRoute({
  GET: getArrangementsByCategory,
  DELETE: deleteArrangement,
}));