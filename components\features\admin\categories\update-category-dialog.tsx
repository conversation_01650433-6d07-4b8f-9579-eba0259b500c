import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog-shadcn";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/shadcn-button";
import { useToast } from "@/hooks/use-toast";
import { useGetAllCategoriesQuery, useUpdateCategoryMutation } from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import { Category } from "@/supabase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Pencil } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";

const updateCategorySchema = z.object({
    name: z.string().min(1, "Name is required"),
    value: z.string().min(1, "Value is required"),
    parent_category_id: z.string().optional(),
});

type UpdateCategoryFormValues = z.infer<typeof updateCategorySchema>;

interface UpdateCategoryDialogProps {
    category: Category;
}

export function UpdateCategoryDialog({ category }: UpdateCategoryDialogProps) {
    const [open, setOpen] = useState(false);
    const token = useAuthStore((state) => state.token);
    const { mutate: updateCategory, isPending } = useUpdateCategoryMutation(category.id, token);
    const { toast } = useToast();
    const { data: categoriesData } = useGetAllCategoriesQuery(1, 100, token);

    const form = useForm<UpdateCategoryFormValues>({
        resolver: zodResolver(updateCategorySchema),
        defaultValues: {
            name: category.name || "",
            value: category.value || "",
            parent_category_id: category.parent_category_id || "",
        },
    });

    const onSubmit = (data: UpdateCategoryFormValues) => {
        const formData = {
            ...data,
            parent_category_id: data.parent_category_id === "null"
                || data.parent_category_id === ""
                ? undefined : data.parent_category_id
        };

        updateCategory(formData, {
            onSuccess: () => {
                toast({
                    title: "Success",
                    description: "Category updated successfully",
                });
                setOpen(false);
            },
            onError: (error) => {
                toast({
                    title: "Error",
                    description: error.message || "Failed to update category",
                    variant: "destructive",
                });
            },
        });
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                    <Pencil className="h-4 w-4" />
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Update Category</DialogTitle>
                    <DialogDescription>
                        Update the category details.
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter category name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="value"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Value</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter category value" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="parent_category_id"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Parent Category (Optional)</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a parent category" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            <SelectItem value="null">None</SelectItem>
                                            {categoriesData?.categories?.map((cat) => (
                                                <SelectItem key={cat.id} value={cat.id}>
                                                    {cat.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <DialogFooter>
                            <Button type="submit" disabled={isPending} variant="primary">
                                {isPending ? "Updating..." : "Update Category"}
                                {isPending && <Loader2 className="h-4 w-4 ml-2 animate-spin" />}
                            </Button>
                        </DialogFooter>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
} 