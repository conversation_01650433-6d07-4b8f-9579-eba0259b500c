import { useEffect } from "react";

// Add at the top of the file
declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: any;
    pendingTranslation: string | null;
  }
}

// Define the language options
const languages = [
  { code: "en", text: "English", flag: "/assets/flags/united-states.png" },
  {
    code: "zh-CN",
    text: "Chinese (Simplified)",
    flag: "/assets/flags/chinese.png",
  },
  { code: "fr", text: "French", flag: "/assets/flags/french.png" },
  { code: "de", text: "German", flag: "/assets/flags/german.png" },
  { code: "ja", text: "Japanese", flag: "/assets/flags/japanese.png" },
  { code: "es", text: "Spanish", flag: "/assets/flags/spanish.png" },
];

// Function to create a custom desktop translator
export const createDesktopTranslator = () => {
  const desktopTranslatorContainer = document.querySelector(
    "#google_translate_element"
  );
  if (!desktopTranslatorContainer) return;

  // Clear existing content
  desktopTranslatorContainer.innerHTML = "";

  // Create a hidden div for the original Google Translate element
  const hiddenDiv = document.createElement("div");
  hiddenDiv.id = "google_translate_original";
  desktopTranslatorContainer.appendChild(hiddenDiv);

  // Create a custom dropdown container
  const dropdownContainer = document.createElement("div");
  dropdownContainer.className =
    "custom-dropdown-container desktop-dropdown text-black z-50";

  // Create the selected option display
  const selectedOption = document.createElement("div");
  selectedOption.className = "selected-option";

  // Create flag image for selected option
  const selectedFlag = document.createElement("img");
  selectedFlag.src = "/assets/flags/united-states.png";
  selectedFlag.alt = "Selected Language";
  selectedFlag.className = "w-5 h-5"; // Smaller flag for the header

  // Create text for selected option
  const selectedText = document.createElement("span");
  selectedText.textContent = "English";
  selectedText.className = "text-sm"; // Smaller text for the header

  // Create dropdown arrow
  const dropdownArrow = document.createElement("span");
  dropdownArrow.className = "dropdown-arrow text-xs"; // Smaller arrow
  dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

  // Add elements to selected option
  selectedOption.appendChild(selectedFlag);
  selectedOption.appendChild(selectedText);
  selectedOption.appendChild(dropdownArrow);

  // Create options container
  const optionsContainer = document.createElement("div");
  optionsContainer.className = "options-container desktop-options z-50";

  // Create option elements
  languages.forEach((lang) => {
    const option = document.createElement("div");
    option.className = "dropdown-option";
    option.dataset.value = lang.code;

    // Create flag image
    const flagImg = document.createElement("img");
    flagImg.src = lang.flag;
    flagImg.alt = lang.text;

    // Create text
    const text = document.createElement("span");
    text.textContent = lang.text;

    // Add elements to option
    option.appendChild(flagImg);
    option.appendChild(text);

    // Add click event
    option.addEventListener("click", function () {
      // Update selected option display
      selectedFlag.src = lang.flag;
      selectedText.textContent = lang.text;

      // Hide options
      optionsContainer.style.display = "none";

      // Save selected language
      localStorage.setItem("selectedLanguage", lang.code);

      // Improved approach to trigger Google Translate
      try {
        // First try the direct Google Translate API approach
        if (window.google?.translate?.TranslateElement) {
          // Force reinitialize Google Translate
          new window.google.translate.TranslateElement(
            {
              pageLanguage: "en",
              includedLanguages: "en,zh-CN,fr,de,ja,es",
              autoDisplay: false,
            },
            "google_translate_original"
          );

          // Wait for initialization and then set language
          setTimeout(() => {
            // Try the dropdown approach first
            const hiddenDropdown = document.querySelector(
              ".goog-te-combo"
            ) as HTMLSelectElement;

            if (hiddenDropdown) {
              hiddenDropdown.value = lang.code;
              hiddenDropdown.dispatchEvent(
                new Event("change", { bubbles: true })
              );
              return;
            }

            // If dropdown not found, try the iframe approach
            const iframe = document.querySelector(
              ".goog-te-menu-frame"
            ) as HTMLIFrameElement;

            if (iframe && iframe.contentDocument) {
              // Find all links in the iframe
              const links = iframe.contentDocument.querySelectorAll(
                "a.goog-te-menu2-item"
              );

              // Find the link for the selected language
              for (let i = 0; i < links.length; i++) {
                const link = links[i];
                if (link.textContent?.includes(lang.text)) {
                  // Click the link - fix TypeScript error by casting to HTMLElement
                  (link as HTMLElement).click();
                  return;
                }
              }
            }

            // Last resort - try the direct API if available
            if (window.google?.translate?.TranslateElement?.getInstance) {
              const translateInstance =
                window.google.translate.TranslateElement.getInstance();
              if (
                translateInstance &&
                typeof translateInstance.setLanguage === "function"
              ) {
                translateInstance.setLanguage(lang.code);
              }
            }
          }, 300);
        } else {
          // If Google Translate API not loaded, try to load it
          const script = document.createElement("script");
          script.src =
            "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
          script.async = true;
          document.body.appendChild(script);

          // Set a flag to apply translation after initialization
          window.pendingTranslation = lang.code;
        }
      } catch (error) {
        console.error("Error triggering Google Translate:", error);
      }
    });

    optionsContainer.appendChild(option);
  });

  // Toggle dropdown on click
  selectedOption.addEventListener("click", () => {
    const isVisible = optionsContainer.style.display === "block";
    optionsContainer.style.display = isVisible ? "none" : "block";
  });

  // Close dropdown when clicking outside
  document.addEventListener("click", (e) => {
    if (!dropdownContainer.contains(e.target as Node)) {
      optionsContainer.style.display = "none";
    }
  });

  // Set the saved language if any
  const savedLanguage = localStorage.getItem("selectedLanguage");
  if (savedLanguage) {
    const savedLang = languages.find((lang) => lang.code === savedLanguage);
    if (savedLang) {
      selectedFlag.src = savedLang.flag;
      selectedText.textContent = savedLang.text;
    }
  }

  // Add elements to dropdown container
  dropdownContainer.appendChild(selectedOption);
  dropdownContainer.appendChild(optionsContainer);

  // Add dropdown to container
  desktopTranslatorContainer.appendChild(dropdownContainer);
};

// Function to create a mobile translator
export const createManualTranslator = (mobileTranslatorRef) => {
  if (!mobileTranslatorRef.current) return;

  // Clear any existing content
  mobileTranslatorRef.current.innerHTML = "";

  // Create a custom dropdown container
  const dropdownContainer = document.createElement("div");
  dropdownContainer.className = "custom-dropdown-container";

  // Create the selected option display
  const selectedOption = document.createElement("div");
  selectedOption.className = "selected-option";

  // Create flag image for selected option
  const selectedFlag = document.createElement("img");
  selectedFlag.src = "/assets/flags/united-states.png";
  selectedFlag.alt = "Selected Language";

  // Create text for selected option
  const selectedText = document.createElement("span");
  selectedText.textContent = "English";

  // Create dropdown arrow
  const dropdownArrow = document.createElement("span");
  dropdownArrow.className = "dropdown-arrow";
  dropdownArrow.innerHTML = "&#9662;"; // Down arrow character

  // Add elements to selected option
  selectedOption.appendChild(selectedFlag);
  selectedOption.appendChild(selectedText);
  selectedOption.appendChild(dropdownArrow);

  // Create options container
  const optionsContainer = document.createElement("div");
  optionsContainer.className = "options-container";

  // Create option elements
  languages.forEach((lang) => {
    const option = document.createElement("div");
    option.className = "dropdown-option";
    option.dataset.value = lang.code;

    // Create flag image
    const flagImg = document.createElement("img");
    flagImg.src = lang.flag;
    flagImg.alt = lang.text;

    // Create text
    const text = document.createElement("span");
    text.textContent = lang.text;

    // Add elements to option
    option.appendChild(flagImg);
    option.appendChild(text);

    // Add click event
    option.addEventListener("click", function () {
      // Update selected option display
      selectedFlag.src = lang.flag;
      selectedText.textContent = lang.text;

      // Hide options
      optionsContainer.style.display = "none";

      // Save selected language
      localStorage.setItem("selectedLanguage", lang.code);

      // Improved approach to trigger Google Translate
      try {
        // First try the direct Google Translate API approach
        if (window.google?.translate?.TranslateElement) {
          // Force reinitialize Google Translate
          new window.google.translate.TranslateElement(
            {
              pageLanguage: "en",
              includedLanguages: "en,zh-CN,fr,de,ja,es",
              autoDisplay: false,
            },
            "google_translate_original"
          );

          // Wait for initialization and then set language
          setTimeout(() => {
            // Try the dropdown approach first
            const hiddenDropdown = document.querySelector(
              ".goog-te-combo"
            ) as HTMLSelectElement;

            if (hiddenDropdown) {
              hiddenDropdown.value = lang.code;
              hiddenDropdown.dispatchEvent(
                new Event("change", { bubbles: true })
              );
              return;
            }

            // If dropdown not found, try the iframe approach
            const iframe = document.querySelector(
              ".goog-te-menu-frame"
            ) as HTMLIFrameElement;

            if (iframe && iframe.contentDocument) {
              // Find all links in the iframe
              const links = iframe.contentDocument.querySelectorAll(
                "a.goog-te-menu2-item"
              );

              // Find the link for the selected language
              for (let i = 0; i < links.length; i++) {
                const link = links[i];
                if (link.textContent?.includes(lang.text)) {
                  // Click the link - fix TypeScript error by casting to HTMLElement
                  (link as HTMLElement).click();
                  return;
                }
              }
            }

            // Last resort - try the direct API if available
            if (window.google?.translate?.TranslateElement?.getInstance) {
              const translateInstance =
                window.google.translate.TranslateElement.getInstance();
              if (
                translateInstance &&
                typeof translateInstance.setLanguage === "function"
              ) {
                translateInstance.setLanguage(lang.code);
              }
            }
          }, 300);
        } else {
          // If Google Translate API not loaded, try to load it
          const script = document.createElement("script");
          script.src =
            "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
          script.async = true;
          document.body.appendChild(script);

          // Set a flag to apply translation after initialization
          window.pendingTranslation = lang.code;
        }
      } catch (error) {
        console.error("Error triggering Google Translate:", error);
      }
    });

    optionsContainer.appendChild(option);
  });

  // Toggle dropdown on click
  selectedOption.addEventListener("click", () => {
    const isVisible = optionsContainer.style.display === "block";
    optionsContainer.style.display = isVisible ? "none" : "block";
  });

  // Close dropdown when clicking outside
  document.addEventListener("click", (e) => {
    if (!dropdownContainer.contains(e.target as Node)) {
      optionsContainer.style.display = "none";
    }
  });

  // Set the saved language if any
  const savedLanguage = localStorage.getItem("selectedLanguage");
  if (savedLanguage) {
    const savedLang = languages.find((lang) => lang.code === savedLanguage);
    if (savedLang) {
      selectedFlag.src = savedLang.flag;
      selectedText.textContent = savedLang.text;
    }
  }

  // Add elements to dropdown container
  dropdownContainer.appendChild(selectedOption);
  dropdownContainer.appendChild(optionsContainer);

  // Add dropdown to container
  mobileTranslatorRef.current.appendChild(dropdownContainer);
};

// Hook to initialize Google Translate
export const useGoogleTranslate = (
  mobileTranslatorRef,
  setMobileTranslatorReady
) => {
  useEffect(() => {
    const addScript = () => {
      // Remove existing script if any
      const existingScript = document.querySelector(
        'script[src*="translate.google.com"]'
      );
      if (existingScript) {
        existingScript.remove();
      }

      const script = document.createElement("script");
      script.src =
        "//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
      script.async = true;
      document.body.appendChild(script);
    };

    window.googleTranslateElementInit = () => {
      // Create hidden div for Google Translate if it doesn't exist
      if (!document.getElementById("google_translate_original")) {
        const hiddenDiv = document.createElement("div");
        hiddenDiv.id = "google_translate_original";
        document.body.appendChild(hiddenDiv);
      }

      // Initialize Google Translate with a different layout
      new window.google.translate.TranslateElement(
        {
          pageLanguage: "en",
          includedLanguages: "en,zh-CN,fr,de,ja,es",
          autoDisplay: false,
          layout:
            window.google.translate.TranslateElement.FloatPosition.TOP_LEFT,
        },
        "google_translate_original"
      );

      // Initialize after the widget is loaded
      setTimeout(() => {
        // Create custom translators
        createDesktopTranslator();
        createManualTranslator(mobileTranslatorRef);
        setMobileTranslatorReady(true);

        // Check for pending translation
        if (window.pendingTranslation) {
          const pendingLang = window.pendingTranslation;
          window.pendingTranslation = null;

          const hiddenDropdown = document.querySelector(
            ".goog-te-combo"
          ) as HTMLSelectElement;

          if (hiddenDropdown) {
            hiddenDropdown.value = pendingLang;
            hiddenDropdown.dispatchEvent(
              new Event("change", { bubbles: true })
            );
          }
        } else {
          // Apply saved language if any
          const savedLanguage = localStorage.getItem("selectedLanguage");
          if (savedLanguage && savedLanguage !== "en") {
            try {
              const hiddenDropdown = document.querySelector(
                ".goog-te-combo"
              ) as HTMLSelectElement;

              if (hiddenDropdown) {
                hiddenDropdown.value = savedLanguage;
                hiddenDropdown.dispatchEvent(
                  new Event("change", { bubbles: true })
                );
              }
            } catch (error) {
              console.error("Error applying saved language:", error);
            }
          }
        }
      }, 1000);
    };

    addScript();
    return () => {
      window.googleTranslateElementInit = undefined;
      const script = document.querySelector(
        'script[src*="translate.google.com"]'
      );
      if (script) script.remove();
    };
  }, [mobileTranslatorRef, setMobileTranslatorReady]);
};
