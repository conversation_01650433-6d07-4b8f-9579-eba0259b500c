import {
  socialLinks,
  customText,
  webriqForms,
  blockOfText,
  title,
  mainImage,
  askedQuestions,
  arrayOfTitleAndDescription,
  hasCalculator,
} from "../../../common/fields";
import { hideIfVariantIn } from "@webriq-pagebuilder/sanity-plugin-schema-default";

export const contactSchema = [
  title(),
  mainImage(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_c",
      "variant_g",
      "variant_h",
    ])
  ),
  arrayOfTitleAndDescription(
    "Title",
    "Description",
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_e",
      "variant_h",
    ])
  ),

  customText(
    "contactDescription",
    "Description",
    "Add a description below the title by typing in the text field below",
    "Got any question? Let's talk about it.",
    1,
    hideIfVariantIn(["variant_d", "variant_f"])
  ),

  customText(
    "officeInformation",
    "Office",
    "Add office information by typing in the text field below",
    "359 Hidden Valley Road, NY",
    1,
    hideIfVariantIn(["variant_d", "variant_g"])
  ),
  customText(
    "officeHours",
    "Office Hours",
    "Add office hours by typing in the text field below",
    "7:00 AM – 4:00 PM PST",
    1,
    hideIfVariantIn(["variant_d", "variant_g", "variant_e"])
  ),

  customText(
    "contactEmail",
    "Email",
    "Add email information by typing in the text field below",
    "<EMAIL>",
    1,
    hideIfVariantIn(["variant_d", "variant_g"])
  ),
  customText(
    "contactNumber",
    "Contact Number",
    "Add contact number by typing in the text field below",
    "+48 698 033 101",
    1,
    hideIfVariantIn(["variant_d", "variant_g"])
  ),
  customText(
    "contactFaxNumber",
    "Contact Fax Number",
    "Add contact fax number by typing in the text field below",
    "+48 698 033 101",
    1,
    hideIfVariantIn(["variant_d", "variant_g", "variant_e"])
  ),
  socialLinks(
    hideIfVariantIn([
      "variant_d",
      "variant_c",
      "variant_g",
      "variant_e",
      "variant_h",
    ])
  ),
  webriqForms(hideIfVariantIn(["variant_b", "variant_f", "variant_e"])),
  blockOfText(
    hideIfVariantIn([
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_g",
      "variant_h",
    ])
  ),
  hasCalculator(
    hideIfVariantIn([
      "variant_a",
      "variant_b",
      "variant_c",
      "variant_d",
      "variant_e",
      "variant_f",
      "variant_h",
      "variant_i",
    ])
  ),
];
