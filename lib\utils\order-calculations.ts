import { OrderItemsWithProduct } from "@/queries/customer-queries";

/**
 * Calculates the expected total from order items
 * @param orderItems Array of order items with product information
 * @param taxRate Tax rate as decimal (e.g., 0.08 for 8%)
 * @param taxExempt Whether the order is tax exempt
 * @returns Object containing subtotal, tax amount, and total
 */
export function calculateOrderTotal(
  orderItems: OrderItemsWithProduct[],
  taxRate: number = 0,
  taxExempt: boolean = false
) {
  if (!orderItems || orderItems.length === 0) {
    return {
      subtotal: 0,
      taxAmount: 0,
      total: 0,
    };
  }

  // Calculate subtotal from all items
  const subtotal = orderItems.reduce((total, item) => {
    const itemPrice = item.item_price ?? item.products.price ?? 0;
    const optionsTotal = item.options?.reduce((optTotal, option) =>
      optTotal + (option.price || 0), 0) || 0;

    return total + ((itemPrice + optionsTotal) * item.quantity);
  }, 0);

  // Calculate tax amount
  const taxAmount = taxExempt ? 0 : subtotal * taxRate;

  // Calculate total
  const total = subtotal + taxAmount;

  return {
    subtotal: Number(subtotal.toFixed(2)),
    taxAmount: Number(taxAmount.toFixed(2)),
    total: Number(total.toFixed(2)),
  };
}

/**
 * Compares calculated total with stored total to detect missing products
 * @param calculatedTotal The calculated total from order items
 * @param storedTotal The total amount stored in the order
 * @param tolerance Tolerance for floating-point comparison (default: 0.01)
 * @returns Object indicating if products are missing and the difference
 */
export function detectMissingProducts(
  calculatedTotal: number,
  storedTotal: number,
  tolerance: number = 0.01
) {
  const difference = Math.abs(calculatedTotal - storedTotal);
  const hasMissingProducts = difference > tolerance && calculatedTotal < storedTotal;

  return {
    hasMissingProducts,
    difference: Number(difference.toFixed(2)),
    calculatedTotal: Number(calculatedTotal.toFixed(2)),
    storedTotal: Number(storedTotal.toFixed(2)),
  };
}

/**
 * Main function to check if an order has missing products
 * @param orderItems Array of order items with product information
 * @param storedTotal The total amount stored in the order
 * @param taxRate Tax rate as decimal
 * @param taxExempt Whether the order is tax exempt
 * @returns Detection result with missing products information
 */
export function checkForMissingProducts(
  orderItems: OrderItemsWithProduct[],
  storedTotal: number,
  taxRate: number = 0,
  taxExempt: boolean = false
) {
  try {
    const calculated = calculateOrderTotal(orderItems, taxRate, taxExempt);
    const detection = detectMissingProducts(calculated.total, storedTotal);

    return {
      ...detection,
      calculatedBreakdown: calculated,
      isValid: true,
      error: null,
    };
  } catch (error) {
    return {
      hasMissingProducts: false,
      difference: 0,
      calculatedTotal: 0,
      storedTotal,
      calculatedBreakdown: { subtotal: 0, taxAmount: 0, total: 0 },
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}