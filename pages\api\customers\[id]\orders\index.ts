import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAuth } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Order } from "@/supabase/types";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAuth(
    matchRoute({
        GET: getProductsHandler
    })
)

const getOrdersSchema = z.object({
    page: z.number()
        .default(1)
        .refine((page) => page > 0, "Page must be greater than 0"),
    limit: z.number()
        .default(10)
        .refine((limit) => limit > 0, "Limit must be greater than 0"),
});

export type GetOrdersQuery = z.infer<typeof getOrdersSchema>;

export interface GetOrdersByUserIdResponse {
    error?: string;
    orders?: Order[];
    total?: number;
}

async function getProductsHandler(req: NextApiRequestWithUserContext, res: NextApiResponse<GetOrdersByUserIdResponse>) {
    const user_id = req.query.id?.toString();
    const userId = req.user?.id;

    const pageQuery = parseInt(req.query.page?.toString() ?? "1");
    const limitQuery = parseInt(req.query.limit?.toString() ?? "10");

    if (!userId) {
        return res.status(403).json({ error: "Forbidden" });
    }

    if (userId !== user_id) {
        return res.status(403).json({ error: "Forbidden" });
    }

    const orderData = getOrdersSchema.safeParse({ page: pageQuery, limit: limitQuery });

    if (orderData.error) {
        return res.status(400).json({ error: orderData.error.issues[0].message });
    }

    const { page, limit } = orderData.data;

    const supabaseAdminClient = createSupabaseAdminClient();

    const customer = await supabaseAdminClient
        .from("customers")
        .select("id")
        .eq("user_id", userId)
        .single();

    if (customer.error) {
        return res.status(400).json({ error: customer.error.message });
    }

    const offset = (page - 1) * limit;

    // Get total count first
    const totalCount = await supabaseAdminClient
        .from("orders")
        .select("id", { count: "exact" })
        .eq("customer_id", customer.data.id)
        .eq("user_id", userId);

    if (totalCount.error) {
        return res.status(400).json({ error: totalCount.error.message });
    }

    const orders = await supabaseAdminClient
        .from("orders")
        .select("*, order_items(id, order_id, quantity, products(*), options, item_price), order_statuses(id, status, created_at)")
        .eq("customer_id", customer.data.id)
        .eq("user_id", userId)
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

    if (orders.error) {
        return res.status(400).json({ error: orders.error.message });
    }

    // Sort order_statuses by created_at in each order
    if (orders.data) {
        orders.data.forEach(order => {
            if (order.order_statuses && Array.isArray(order.order_statuses)) {
                order.order_statuses.sort((a, b) =>
                    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                );
            }
        });
    }

    return res.status(200).json({
        orders: orders.data,
        total: totalCount.count || 0
    });
}