import StoreLayout from "@/components/features/store/layout";
import { OrdersTable } from "@/components/features/store/orders-table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useGetOrdersByUserIdQuery } from "@/queries/customer-queries";
import useAuthStore from "@/stores/auth-store";
import { Clock, ExternalLink, Package, ShoppingBag } from "lucide-react";
import Head from "next/head";

export default function Orders() {
    const userData = useAuthStore(state => state.data);
    const userId = userData?.id;
    const { data: orderData } = useGetOrdersByUserIdQuery(userId || "", 1, 10);

    const totalOrders = orderData?.total || 0;
    const pendingOrders = orderData?.orders?.filter(order =>
        order.order_statuses?.[0]?.status === "pending"
    ).length || 0;
    const trackableOrders = orderData?.orders?.filter(order =>
        order.tracking_link
    ).length || 0;

    return <StoreLayout>
        <Head>
            <title>Orders</title>
        </Head>
        <div className="min-h-screen bg-white overflow-hidden">
            <div className="flex-1 space-y-8 p-8 pt-6">
                {/* Header Section */}
                <div className="flex flex-col space-y-6">
                    <div className="flex flex-col space-y-2">
                        <h1 className="text-3xl font-bold tracking-tight">Your Orders</h1>
                        <p className="text-muted-foreground">
                            View and manage all your orders in one place
                        </p>
                    </div>

                    <div className="flex flex-col md:flex-row gap-4">
                        <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                            <Package className="mr-2 h-4 w-4" />
                            <span className="text-primary">Order History</span>
                        </Badge>
                        <Badge variant="outline" className="w-fit px-6 py-2 text-lg">
                            <span>Customer: {userData?.email?.split('@')[0] || 'Customer'}</span>
                        </Badge>
                    </div>
                </div>

                {/* Quick Stats */}
                <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{totalOrders}</div>
                            <p className="text-xs text-muted-foreground">All time orders</p>
                        </CardContent>
                    </Card>
                    <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{pendingOrders}</div>
                            <p className="text-xs text-muted-foreground">Awaiting processing</p>
                        </CardContent>
                    </Card>
                    <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Last Order</CardTitle>
                            <Package className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold uppercase">{orderData?.orders?.[0]?.order_statuses?.[0]?.status || 'N/A'}</div>
                            <p className="text-xs text-muted-foreground">Most recent status</p>
                        </CardContent>
                    </Card>
                    <Card className="hover:shadow-md transition-all">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Trackable Orders</CardTitle>
                            <ExternalLink className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{trackableOrders}</div>
                            <p className="text-xs text-muted-foreground">Orders with tracking</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Orders Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Order History</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <OrdersTable />
                    </CardContent>
                </Card>
            </div>
        </div>
    </StoreLayout>
}