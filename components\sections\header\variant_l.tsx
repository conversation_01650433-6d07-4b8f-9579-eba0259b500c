import { Button } from "@stackshift-ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ButtonProps, HeaderProps } from ".";
import { PortableTextBlock } from "sanity";
import { PortableText } from "@portabletext/react";
import { MyPortableTextComponents } from "types";
import { description } from "../../../schemas/custom/sanity-plugin-schema-default/src/schemas/common/fields";
import { getImageDimensions } from "@sanity/asset-utils";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <Heading className="mb-6 leading-loose text-5xl sm:text-7xl">
          {children}
        </Heading>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-2xl  text-primary">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-base text-gray-500 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-6 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="pl-10 mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-6 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary-foreground hover:text-secondary-foreground"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImages: ({ value }) => {
      console.log("Value", value);

      const images = value?.images;

      if (!Array.isArray(images)) {
        console.error("Expected an array but got:", images);
        return null;
      }

      return (
        <Flex direction="row" gap={4} className="mt-6 justify-center">
          {images.map((image, index) => (
            <Image
              key={index}
              className="w-full h-full mb-10"
              width={300}
              height={300}
              src={urlFor(image?.image)}
              alt={image?.alt ?? image?.image?.asset?._ref}
            />
          ))}
        </Flex>
      );
    },
  },
};

export default function Header_L({
  title,
  primaryButton,
  secondaryButton,
  mainImage,
  firstColumn,
}: HeaderProps): JSX.Element {
  return (
    <Section className="py-6 xs:py-8 sm:py-12 lg:py-20 bg-background">
      <Container maxWidth={1280}>
        <Flex
          align="center"
          className="flex-col-reverse lg:flex-row gap-6 xs:gap-8 lg:gap-4"
          gap={4}
        >
          <Flex className="w-full 2xs:w-4/5 xs:w-3/4 sm:w-2/3 lg:w-1/2 justify-center lg:justify-end">
            <MainImage mainImage={mainImage} />
          </Flex>
          <Flex align="center" className="w-full lg:w-1/2" direction="col">
            <Container
              className="mx-auto items-center text-center lg:text-left px-4 xs:px-6 lg:px-8"
              maxWidth="md"
            >
              {firstColumn && (
                <div className="prose max-w-none">
                  <PortableText
                    value={firstColumn}
                    components={textComponentBlockStyling}
                    onMissingComponent={false}
                  />
                </div>
              )}
              <Buttons
                primaryButton={primaryButton}
                secondaryButton={secondaryButton}
              />
            </Container>
          </Flex>
        </Flex>
      </Container>
    </Section>
  );
}

function Buttons({
  primaryButton,
  secondaryButton,
}: {
  primaryButton?: ButtonProps;
  secondaryButton?: ButtonProps;
}) {
  return (
    <Flex
      align="center"
      className="flex items-center justify-center lg:justify-start gap-2 flex-col md:flex-row"
      gap={2}
    >
      {primaryButton?.label ? (
        <Button
          as="link"
          link={primaryButton}
          ariaLabel={primaryButton?.ariaLabel ?? primaryButton?.label}
          variant="solid"
          className="p-0 bg-transparent hover:opacity-80"
        >
          <Image
            src="/assets/elements/downloads/download.png"
            alt={primaryButton.label}
            width={180}
            height={54}
            style={{ objectFit: "contain" }}
          />
        </Button>
      ) : null}
      {secondaryButton?.label ? (
        <Button
          as="link"
          link={secondaryButton}
          ariaLabel={secondaryButton.ariaLabel ?? secondaryButton?.label}
          variant="solid"
          className="p-0 bg-transparent hover:opacity-80"
        >
          <Image
            src="/assets/elements/downloads/googleplay.png"
            alt={secondaryButton.label}
            width={180}
            height={54}
            style={{ objectFit: "contain" }}
          />
        </Button>
      ) : null}
    </Flex>
  );
}

interface MainImageProps {
  mainImage?: {
    image?: string | any;
    alt?: string;
  };
}

function MainImage({ mainImage }: MainImageProps) {
  if (!mainImage?.image) return null;

  return (
    <div className="w-full max-w-[200px] xs:max-w-[250px] sm:max-w-[300px] md:max-w-[350px] lg:max-w-md">
      <Image
        alt={mainImage.alt ?? "header-main-image"}
        className="rounded-md w-full h-auto"
        height={500}
        src={`${mainImage?.image}`}
        style={{ objectFit: "contain" }}
        width={250}
      />
    </div>
  );
}

export { Header_L };
