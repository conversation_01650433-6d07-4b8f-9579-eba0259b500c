import { groq } from "next-sanity";

const conditionalLink = `
  "type": linkType,
  "internalLink": linkInternal->slug.current,
  "externalLink": linkExternal,
  "linkTarget": linkTarget,
  "referenceType": linkInternal->_type
`;

const mainImage = `"mainImage": {
  "image": *[_type == "sanity.imageAsset" && _id == ^.mainImage.image.asset._ref][0].url,
  "alt": mainImage.alt
}`;

const logoImage = `"image": *[_type == "sanity.imageAsset" && _id == ^.image.asset._ref][0].url,`;

const featuredItems = `
featuredItems[] {
  ...,
  ${mainImage},
  primaryButton {
    label,
    ${conditionalLink}
  },
  "pdfFile": {
    "asset": {
      "url": *[_type == "sanity.fileAsset" && _id == ^.pdfFile.asset._ref][0].url
    }
  },
  "zipFile": {
    "asset": {
      "url": *[_type == "sanity.fileAsset" && _id == ^.zipFile.asset._ref][0].url
    }
  }
}`;

export const variants = `
  variants {
    ...,
    logo != null => {
      logo {
        alt,
        ${logoImage}
        linkTarget,
        ${conditionalLink}
      }
    },
    images != null => {
      images[] {
        ${logoImage}
        "alt": alt
      },
    },
    addImages != null => {
      addImages[] {
        ${logoImage}
        "alt": alt
      },
    },
    imagesCopy != null => {
      imagesCopy[] {
        ${logoImage}
        "alt": alt
      },
    },

    mainImage != null => {
      ${mainImage}
    },
    featuredItems != null => {
      ${featuredItems}
    },
    arrayOfImageTitleAndText != null => {
      arrayOfImageTitleAndText[] {
        ...,
        ${mainImage},
        primaryButton != null => {
          primaryButton {
            ...,
            label,
            ${conditionalLink}
          }
        },
      }
    },

      quickForms {
        ...,
        quickLinks[] {
          ...,
            primaryButton {
              ...,
              label,
              ${conditionalLink}
          },
        }
    },

    statItems != null => {
      "stats": statItems[] {
        ...,
        ${mainImage},
      }
    },
    faqsWithCategory != null => {
      "faqsWithCategories": faqsWithCategory[] {
        ...,
      }
    },
    askedQuestions != null => {
      "faqs": askedQuestions[] {
        ...,
      }
    },
    primaryButton != null => {
      primaryButton {
        ...,
        label,
        ${conditionalLink}
      }
    },
    secondaryButton != null => {
      secondaryButton {
        ...,
        label,
        ${conditionalLink}
      }
    },
    routes[] {
      ...,
      "linkInternal": linkInternal->slug.current,
      "linkExternal": linkExternal,
      "targetLink": linkTarget,
      multipleRoutes[] {
        ...,
        "linkInternal": linkInternal->slug.current,
        "linkExternal": linkExternal,
        "targetLink": linkTarget,
        "referenceType": linkInternal->_type,
        multipleInnerRoutes[] {
          ...,
          "linkInternal": linkInternal->slug.current,
          "linkExternal": linkExternal,
          "referenceType": linkInternal->_type,
          "targetLink": linkTarget
        }
      }
    },
    config != null => {
      config {
        ...,
        cookiePolicy {
          ...,
          cookiePolicyPage {
            ...,
            ${conditionalLink}
          }
        }
      }
    },
    contactLink != null => {
      contactLink {
        ...,
        label,
        ${conditionalLink}
      }
    },
    socialLinks != null => {
      socialLinks[] {
        ...,
        socialMediaIcon {
          "image": image.asset->url,
          alt
        },
      }
    },
    multipleMenus != null => {
      multipleMenus[] {
        ...,
        links != null => {
          links[] {
            ...,
            ${conditionalLink}
          }
        }
      }
    },
    menu != null => {
      menu[] {
        ...,
        ${conditionalLink}
      }
    },
    plans != null => {
      plans[] {
        ...,
        primaryButton {
          ...,
          label,
          ${conditionalLink}
        },
      }
    },
    formLinks != null => {
      formLinks[] {
        ...,
        ${conditionalLink}
      }
    },
    testimonials != null => {
      testimonials[] {
        ...,
        ${mainImage},
      }
    },
    portfolios != null => {
      portfolios[] {
        ...,
        ${mainImage},
        content[] {
          ...,
          primaryButton {
            label,
            ${conditionalLink}
          },
        },
        primaryButton {
          ...,
          label,
          ${conditionalLink}
        },
      }
    },
    portfoliosWithCategories != null => {
      portfoliosWithCategories[] {
        ...,
        content[] {
          ...,
         ${mainImage},
          primaryButton {
            ...,
            label,
            ${conditionalLink}
          },
        },
        primaryButton {
          ...,
          label,
          ${conditionalLink}
        },
        "zipFile": {
    "asset": {
      "url": *[_type == "sanity.fileAsset" && _id == ^.zipFile.asset._ref][0].url
    }
  }
      }
    },



    teams != null => {
      teams[] {
        ...,
        ${mainImage},
      }
    },
    signInLink != null => {
        signInLink {
        ...,
        ${conditionalLink}
      }
    },
    signinLink != null => {
        signinLink {
        ...,
        ${conditionalLink}
      }
    },
    blogPosts != null => {
      "posts": blogPosts[]->{
        ...,
        "link": slug.current,
        authors[]->{
          ...,
          "link": slug.current
        },
        "mainImage": *[_type == "sanity.imageAsset" && _id == ^.mainImage.asset._ref][0].url,
        categories[]->,
        tags[]->
      }
    },
    form != null => {
      form {
        ...,
        thankYouPage {
          ...,
          ${conditionalLink}
        }
      }
    },
    featured != null => { featured[]-> },
    collections != null => {
      collections->{
        ...,
        products[]->
      }
    },
    products != null => { products-> },
    allProducts != null => {
      allProducts[]-> {
        ...,
        products[]->
      }
    },
    arrayOfLinks != null => {
      arrayOfLinks[] {
        ...,
        ${conditionalLink},
      }
    },
    repairParts != null => {
      repairParts[] {
        ...,
         parts[] {
            ...,
            ${conditionalLink},
          }
      }
    },
    notesSection != null => {
      notesSection[] {
        ...,
        primaryButton {
          ...,
          label,
          ${conditionalLink}
        }
      }
    },
    multipleMenus != null => {
      multipleMenus[] {
        ...,
        links != null => {
          links[] {
            ..., 
            ${conditionalLink}
          }
        }
      }
    }
  }
`;

export const variantFNavigationQuery = groq`*[_type == "navigation" && variant == "variant_g"][0] {
  ...,
  ${variants}
}`;

export const variantEFooterQuery = groq`*[_type == "footer" && variant == "variant_e"][0] {
  ...,
  ${variants}
}`;

const allProjections = `
{
  ...,
  "slug": slug.current,
  sections[]-> {
    ...,
    ${variants} ,
    _type == "slotWishlist" => {
      ...,
      "variant": *[_type == "wishlistPage"][0].wishlistSectionVariant.variant,
    },
  },
  collections->
}
`;

export const homeQuery = groq`
  *[_type == "page" && (slug.current == "home" || slug.current == "Home") ] ${allProjections}
`;

export const slugQuery = groq`
  *[_type == "page" && slug.current == $slug] ${allProjections}
`;

export const blogQuery = groq`
  *[_type == "post" && slug.current == $slug]{
    ...,
    authors[]->{
      ...,
      "link": slug.current
    },
    categories[]->,
    tags[]->,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants},
    },
  }
`;

// query main product based on current slug
export const productsQuery = groq`*[_type == "mainProduct" && slug.current == $slug] {
  ...,
  "slug": slug.current,
  sections[]->{
    ...,
    ${variants},
    _type == "slotProductInfo" => {
     ...,
     "variant": *[_type == "mainProduct" && slug.current == $slug][0].productInfoVariant.variant,
     "variants": *[_type == "mainProduct" && slug.current == $slug][0].productInfo
   },
   _type == "slotCart" => {
      ...,
      "variant": *[_type == "cartPage"][0].cartSectionVariant.variant,
    },
    _type == "slotWishlist" => {
      ...,
      "variant": *[_type == "wishlistPage"][0].wishlistSectionVariant.variant,
    },
  },
  "commonSections": *[_type == "productSettings"][0]{
    _type,
    seo,
    sections[]-> {
      ...,
      ${variants},
      _type == "slotProductInfo" => {
        ...,
        "variant": *[_type == "productSettings"][0].defaultProductInfoVariant.variant
      }
    },
  },
}`;

// query product collection based on current slug
export const collectionsQuery = groq`*[_type == "mainCollection" && slug.current == $slug] {
  ...,
  "slug": slug.current,
  products[]->,
  sections[]-> {
    ...,
    ${variants},
    _type == "slotCollectionInfo" => {
      ...,
      "variant": *[_type == "mainCollection" && slug.current == $slug][0].collectionInfoVariant.variant,
      "variants": *[_type == "mainCollection" && slug.current == $slug][0]{
        "collections": {
          "title": name,
          products[]->,
        }
      }
    },
    _type == "slotCart" => {
      ...,
      "variant": *[_type == "cartPage"][0].cartSectionVariant.variant,
    },
    _type == "slotWishlist" => {
      ...,
      "variant": *[_type == "wishlistPage"][0].wishlistSectionVariant.variant,
    },
  },
  "commonSections": *[_type == "collectionSettings"][0]{
    _type,
    seo,
    sections[]->{
      ...,
      ${variants},
      _type == "slotCollectionInfo" => {
        ...,
        "variant": *[_type == "collectionSettings"][0].defaultCollectionInfoVariant.variant
      }
    }
  }
}`;

// query cart page
export const cartPageQuery = groq`*[_type == "cartPage"] {
  ...,
  sections[]-> {
    ...,
    ${variants},
    _type == "slotCart" => {
      ...,
      "variant": *[_type == "cartPage"][0].cartSectionVariant.variant,
    }
  }
}`;

// query wishlist page
export const wishlistPageQuery = groq`*[_type == "wishlistPage"] {
  ...,
  sections[]-> {
    ...,
    ${variants},
    _type == "slotWishlist" => {
      ...,
      "variant": *[_type == "wishlistPage"][0].wishlistSectionVariant.variant,
    }
  }
}`;

export const allPostsQuery = groq`*[_type == "post" && !(_id in path("drafts.**"))] | order(
  publishedAt desc,
  _createdAt desc,
  _updatedAt desc,
){
  ..., 
  authors[]->,
  categories[]->,
  tags[]->,
}`;

// query search page
export const searchPageQuery = groq`*[_type == "searchPage"] ${allProjections}`;

// query Global or Default SEO values
export const globalSEOQuery = groq`*[_type == 'defaultSeo' && !(_id in path("drafts.**"))][0]`;

// query sections/components
export const componentsQuery = groq`*[_type==$schema && !(_id in path("drafts.**"))] | order(variant asc, _updatedAt desc) {
  ...,
  ${variants}
}`;

// query theme page
export const themePageQuery = groq`*[_type == "themePage"] ${allProjections}`;

// query repair page
export const repairQuery = groq`
  *[_type == "repair" && slug.current == $slug] ${allProjections}
`;

// ################ Categories Queries ###############
// query all documents of type "category"
export const allCategories = groq`*[_type == "category"]`;

// query "category" document by current page slug
export const categoryQuery = groq`
  *[_type == "category" && slug.current == $slug]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    }
  }
`;

// query all published posts by category
export const postsByCategory = groq`
  *[_type == "post" && count((categories[]._ref)[@ in *[_type=="category" && slug.current == $slug]._id]) > 0]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    },
    authors[]->,
    categories[]->,
    tags[]->
  }
`;
// ################ END Categories Queries ###############

// ################ Archive Queries ###############
// query all archive years and months
export const allArchives = groq`{
  "years": *[_type == "archiveYear" && count(*[_type=="post" && references(^._id)]) > 0] | order(title desc),
  "months": *[_type == "archiveMonth" && count(*[_type=="post" && references(^._id)]) > 0] | order(title asc),
  "validCombinations": *[
    _type == "post" && 
    defined(archiveYear[0]) && 
    defined(archiveMonth[0])
  ]{
    "monthNum": select(
      archiveMonth[0]->title == "January" => 1,
      archiveMonth[0]->title == "February" => 2,
      archiveMonth[0]->title == "March" => 3,
      archiveMonth[0]->title == "April" => 4,
      archiveMonth[0]->title == "May" => 5,
      archiveMonth[0]->title == "June" => 6,
      archiveMonth[0]->title == "July" => 7,
      archiveMonth[0]->title == "August" => 8,
      archiveMonth[0]->title == "September" => 9,
      archiveMonth[0]->title == "October" => 10,
      archiveMonth[0]->title == "November" => 11,
      archiveMonth[0]->title == "December" => 12
    ),
    "year": archiveYear[0]->{
      "_id": _id,
      "title": title,
      "slug": slug.current
    },
    "month": archiveMonth[0]->{
      "_id": _id,
      "title": title,
      "slug": slug.current
    }
  } | order(year.title desc, monthNum desc)
}`;

// query posts by archive year and month
export const postsByArchive = groq`*[
  _type == "post" && 
  !(_id in path("drafts.**")) &&
  count((archiveYear[]._ref)[@ in [$yearId]]) > 0 &&
  count((archiveMonth[]._ref)[@ in [$monthId]]) > 0
] | order(publishedAt desc) {
  ...,
  "mainImage": *[_type == "sanity.imageAsset" && _id == ^.mainImage.asset._ref][0].url,
  authors[]->,
  categories[]->,
  tags[]->,
  "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
    ...,
    ${variants}
  },
  "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
    ...,
    ${variants}
  }
}`;

export const archiveQuery = groq`*[
  _type == "archiveYear" && slug.current == $year || 
  _type == "archiveMonth" && slug.current == $month
]{
  ...,
  "year": *[_type == "archiveYear" && slug.current == $year][0],
  "month": *[_type == "archiveMonth" && slug.current == $month][0],
  "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
    ...,
    ${variants}
  },
  "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
    ...,
    ${variants}
  }
}`;
// ################ END Archive Queries ###############

// ################ Author Queries ###############
// query "category" document by current page slug
export const authorQuery = groq`
  *[_type == "author" && slug.current == $slug]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    }
  }
`;

// query "author" document by current page slug
export const postsByAuthor = groq`
  *[_type == "post" && count((authors[]._ref)[@ in *[_type=="author" && slug.current == $slug]._id]) > 0]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    },
    authors[]->,
    categories[]->,
    tags[]->
  }
`;
// ################ End Author Queries ###############

// ################ Tag Queries ###############
export const tagQuery = groq`
  *[_type == "tag" && slug.current == $slug]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    }
  }
`;

// query posts by tag
export const postsByTag = groq`
  *[_type == "post" && count((tags[]._ref)[@ in *[_type=="tag" && slug.current == $slug]._id]) > 0]{
    ...,
    "navigation": *[_type=="page" && slug.current=="home"][0].sections[_type match "navigation"][0]->{
      ...,
      ${variants}
    },
    "footer": *[_type=="page" && slug.current=="home"][0].sections[_type match "footer"][0]->{
      ...,
      ${variants}
    },
    authors[]->,
    categories[]->,
    tags[]->
  }
`;
// ################ End Tag Queries ###############
