import { Card } from "@stackshift-ui/card";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { FeaturesProps } from ".";
import { ArrayOfImageTitleAndText, FeaturedItem } from "../../../types";

export default function Features_I({
  caption,
  title,
  description,
  featuredItems,
}: FeaturesProps) {
  return (
    <Section className="py-20 lg:py-28 bg-background">
      <Container maxWidth={1280}>
        <Container maxWidth={680} className="mb-8 text-center">
          <CaptionAndTitleSection
            caption={caption}
            title={title}
            description={description}
          />
        </Container>
        <FeatureItems features={featuredItems} />
      </Container>
    </Section>
  );
}

function CaptionAndTitleSection({
  caption,
  title,
  description,
}: {
  caption?: string;
  title?: string;
  description?: string;
}) {
  return (
    <>
      {caption && (
        <Text fontSize="xl" className="!text-primary !font-semibold mb-2">
          {caption}
        </Text>
      )}
      {title && (
        <Heading fontSize="3xl" type="h2" className="!text-gray-800">
          {title}
        </Heading>
      )}
    </>
  );
}

function FeatureItems({ features }: { features?: FeaturedItem[] }) {
  if (!features) return null;

  return (
    <Flex wrap justify="center" className="max-w-5xl mx-auto">
      {features.map((feature) => (
        <FeatureItem feature={feature} key={feature._key} />
      ))}
    </Flex>
  );
}

function FeatureItem({ feature }: { feature: FeaturedItem }) {
  return (
    <div className="w-full px-4 mt-8 lg:mb-0 md:w-1/2 lg:w-1/3">
      <div className="border-t-2 border-primary mb-4"></div>
      <Card
        className="h-full md:h-[330px] px-6 py-12 bg-white text-left"
        borderRadius="md"
      >
        <div className="flex justify-between items-center mb-10">
          <div className="rounded-lg">
            {feature?.mainImage?.image && (
              <Image
                className="object-scale-down"
                src={feature?.mainImage.image}
                width={70}
                height={70}
                alt={feature.mainImage.alt ?? `features-image-`}
              />
            )}
          </div>
          <Text
            weight="bold"
            className="text-white !text-[10px] bg-primary rounded-md p-2"
          >
            {feature.subtitle}
          </Text>
        </div>
        <Text fontSize="xl" weight="bold" className="px-0 mb-4 text-gray-700">
          {feature.title}
        </Text>
        <Text muted className="px-0 !text-sm">
          {feature.description}
        </Text>
      </Card>
    </div>
  );
}

export { Features_I };
