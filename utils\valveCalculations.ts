import { ValveData } from "../types/valve";

export function calculateInch(data: ValveData) {
  if (
    data.jackType === "direct" &&
    data.numJack &&
    data.jackDiameter &&
    data.carSpeed
  ) {
    const pistonArea =
      (data.numJack * Math.pow(data.jackDiameter, 2.0) * Math.PI) / 4;
    const gpm = Math.round((pistonArea * 12 * data.carSpeed) / 231);

    let minPsi =
      data.emptyWeightUnits !== "PSI" && data.emptyWeight
        ? Math.round(data.emptyWeight / pistonArea)
        : data.emptyWeight || 0;

    let maxPsi =
      data.capacity && data.capacity > 0
        ? Math.round(minPsi + data.capacity / pistonArea)
        : Math.round(2 * minPsi);

    return {
      ...data,
      sngPisArea: pistonArea,
      sngGPM: gpm,
      sngOutMinPSI: minPsi,
      sngOutMaxPSI: maxPsi,
    };
  }

  if (
    data.jackType === "roped" &&
    data.numJack &&
    data.jackDiameter &&
    data.carSpeed
  ) {
    const pistonArea =
      (data.numJack * Math.pow(data.jackDiameter, 2.0) * Math.PI) / 4;
    const gpm = Math.round((pistonArea * 12 * data.carSpeed) / 231 / 2);

    let minPsi =
      data.emptyWeightUnits !== "PSI" && data.emptyWeight
        ? Math.round((2 * data.emptyWeight) / pistonArea)
        : data.emptyWeight || 0;

    let maxPsi =
      data.capacity && data.capacity > 0
        ? Math.round(minPsi + 2 * (data.capacity / pistonArea))
        : Math.round(2 * minPsi);

    return {
      ...data,
      sngPisArea: pistonArea,
      sngGPM: gpm,
      sngOutMinPSI: minPsi,
      sngOutMaxPSI: maxPsi,
    };
  }

  return data;
}

export function calculateMetric(data: ValveData) {
  if (
    data.jackType === "direct" &&
    data.numJack &&
    data.jackDiameter &&
    data.carSpeed
  ) {
    const pistonArea =
      (data.numJack * (Math.pow(data.jackDiameter, 2.0) * Math.PI)) / 4;
    const lpm = (pistonArea * 60 * data.carSpeed) / 1000;
    const gpm = Math.round(lpm / 3.785);

    let minP =
      data.emptyWeightUnits !== "kg/cm2" && data.emptyWeight
        ? (data.emptyWeight * 100) / pistonArea
        : data.emptyWeight || 0;

    let maxP =
      data.capacity && data.capacity > 0
        ? minP + (data.capacity * 100) / pistonArea
        : minP;

    return {
      ...data,
      sngPisArea: pistonArea,
      sngLPM: lpm,
      sngGPM: gpm,
      sngOutMinPSI: Math.round((minP * 2.54 * 2.54) / 0.454),
      sngOutMaxPSI: Math.round((maxP * 2.54 * 2.54) / 0.454),
    };
  }

  if (
    data.jackType === "roped" &&
    data.numJack &&
    data.jackDiameter &&
    data.carSpeed
  ) {
    const pistonArea =
      (data.numJack * (Math.pow(data.jackDiameter, 2.0) * Math.PI)) / 4;
    const lpm = (pistonArea * 60 * data.carSpeed) / 1000 / 2;
    const gpm = Math.round(lpm / 3.785);

    let minP =
      data.emptyWeightUnits !== "kg/cm2" && data.emptyWeight
        ? (2 * data.emptyWeight * 100) / pistonArea
        : data.emptyWeight || 0;

    let maxP =
      data.capacity && data.capacity > 0
        ? minP + 2 * ((data.capacity * 100) / pistonArea)
        : minP;

    return {
      ...data,
      sngPisArea: pistonArea,
      sngLPM: lpm,
      sngGPM: gpm,
      sngOutMinPSI: Math.round((minP * 2.54 * 2.54) / 0.454),
      sngOutMaxPSI: Math.round((maxP * 2.54 * 2.54) / 0.454),
    };
  }

  return data;
}

export function knownGPMInch(data: ValveData) {
  if (!data.emptyStaticPressure || !data.ratedFlow) return data;

  const result = {
    ...data,
    sngOutMinPSI: data.emptyStaticPressure,
    sngGPM: Math.round(data.ratedFlow),
  };

  result.sngOutMaxPSI =
    data.loadedCarPressure && data.loadedCarPressure !== 0
      ? data.loadedCarPressure
      : 2 * result.sngOutMinPSI;

  return result;
}

export function knownGPMMetric(data: ValveData) {
  if (!data.emptyStaticPressure || !data.ratedFlow) return data;

  const result = {
    ...data,
    sngOutMinPSI: Math.round((data.emptyStaticPressure * 2.54 * 2.54) / 0.454),
    sngGPM: Math.round(data.ratedFlow / 3.785),
  };

  if (data.loadedCarPressure && data.loadedCarPressure !== 0) {
    result.sngOutMaxPSI = Math.round(
      (data.loadedCarPressure * 2.54 * 2.54) / 0.454
    );
  } else {
    result.sngOutMaxPSI = 2 * result.sngOutMinPSI;
  }

  return result;
}

export function selectValve(data: ValveData): ValveData {
  // OSV Selection Logic first
  if (data.sngOutMaxPSI && data.sngOutMinPSI && data.sngGPM) {
    if (
      data.sngOutMaxPSI < 1000 &&
      data.sngOutMinPSI >= 150 &&
      data.sngGPM >= 3 &&
      data.sngGPM <= 24
    ) {
      data.osvValve = "OSVB25";
    } else if (
      data.sngOutMaxPSI < 800 &&
      data.sngOutMinPSI >= 50 &&
      data.sngGPM >= 30 &&
      data.sngGPM <= 300
    ) {
      data.osvValve = data.sngGPM <= 100 ? "OSV-E1" : "OSV-STD";
    }
  }

  // Choose valve selection based on down speed regulation
  return data.downSpeedRegulation
    ? selectDSRValve(data)
    : selectNoDSRValve(data);
}

function selectNoDSRValve(data: ValveData): ValveData {
  // Ensure required properties have values before using them
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;

  // Get original flow value if available (for form2), otherwise use calculated sngGPM
  const originalFlow =
    data.formType === "form2" && data.ratedFlow
      ? data.units === "inch"
        ? data.ratedFlow
        : data.ratedFlow / 3.785
      : sngGPM;

  // Initial pressure and flow checks
  if (sngOutMinPSI < 50) {
    throw new Error(
      "Inadequate system pressure.\nConsult Maxton at 1-775-782-1700"
    );
  }
  if (sngOutMinPSI > 1000) {
    throw new Error(
      "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
    );
  }
  if (sngGPM < 3) {
    throw new Error("Flow is under 3 GPM.\nConsult Maxton at 1-775-782-1700");
  }

  // ADDED: Special case for 78-84 GPM at 100-174 PSI range, only if downspeed regulation is checked
  // Per client requirements, this should return "Consult Maxton"
  if (
    sngGPM >= 78 &&
    sngGPM <= 84 &&
    sngOutMinPSI >= 100 &&
    sngOutMinPSI <= 174
    && data.downSpeedRegulation
  ) {
    throw new Error("Consult Maxton at 1-775-782-1700");
  }

  // CLIENT REQUIREMENT 1: GPM 2.5-5.99 at 100+ PSI should return "Contact Maxton"
  // Using originalFlow to avoid rounding issues with values between 5.5-5.99
  if (originalFlow >= 2.5 && originalFlow < 6 && sngOutMinPSI >= 100) {
    throw new Error("Consult Maxton at 1-775-782-1700");
  }

  const result = { ...data };

  // Initialize all GPM values with defaults
  result.S2GPM = 0;
  result.E1GPM = 0;
  result.E2GPM = 0;
  result.STDGPM = 0;
  result.UC1AGPM = 0;
  result.HDSTDGPM = 0;

  // Calculate GPM points
  if (sngOutMinPSI < 325) {
    result.S2GPM = 0.0686 * sngOutMinPSI + 3.1432;
  } else {
    result.S2GPM = 25.4;
  }

  if (sngOutMinPSI < 600) {
    result.E1GPM =
      -0.0003 * Math.pow(sngOutMinPSI, 2) + 0.361 * sngOutMinPSI + 11.698;
  } else {
    result.E1GPM = 120.3;
  }

  if (sngOutMinPSI < 310) {
    result.E2GPM =
      -0.0018 * Math.pow(sngOutMinPSI, 2) + 1.179 * sngOutMinPSI - 5.7908;
  } else {
    result.E2GPM = 185;
  }

  if (sngOutMinPSI < 145) {
    result.STDGPM =
      -0.006 * Math.pow(sngOutMinPSI, 2) + 2.3564 * sngOutMinPSI - 31.313;
  } else {
    result.STDGPM = 185;
  }

  if (sngOutMinPSI < 175) {
    result.UC1AGPM =
      -0.0055 * Math.pow(sngOutMinPSI, 2) + 2.928 * sngOutMinPSI + 17.467;
  } else {
    result.UC1AGPM = 360;
  }

  // HDSTDGPM Calculation
  if (sngOutMinPSI >= 100 && sngOutMinPSI < 200) {
    result.HDSTDGPM =
      -0.0028 * Math.pow(sngOutMinPSI, 2) + 1.51 * sngOutMinPSI - 4.869;
  } else if (sngOutMinPSI >= 200 && sngOutMinPSI < 800) {
    result.HDSTDGPM = 185;
  }

  // Valve Selection Logic

  // CLIENT REQUIREMENT 2: 6-12 GPM with 100+ PSI should use UC4/4M S2
  // Also using originalFlow to maintain consistency with condition above
  if (originalFlow >= 6 && originalFlow <= 12 && sngOutMinPSI >= 100) {
    return UC4MS2(result);
  }

  if (originalFlow <= (result.S2GPM ?? 0) && originalFlow <= 25.4) {
    if (sngOutMinPSI >= 100) {
      return UC4MS2(result);
    }
    throw new Error("Consult Maxton at 1-775-782-1700");
  }

  if (
    result.E1GPM &&
    originalFlow <= result.E1GPM &&
    originalFlow > (result.S2GPM ?? 0)
  ) {
    return UC4ME1(result);
  }

  if (
    result.E2GPM !== undefined &&
    originalFlow <= result.E2GPM &&
    result.E1GPM &&
    originalFlow > (result.S2GPM ?? 0)
  ) {
    return UC4ME2(result);
  }

  if (
    result.STDGPM &&
    result.E2GPM !== undefined &&
    originalFlow <= result.STDGPM &&
    originalFlow > result.E2GPM
  ) {
    return UC4Mstd(result);
  }

  if (result.UC1AGPM && originalFlow <= result.UC1AGPM && originalFlow <= 360) {
    return UC1A(result);
  }

  throw new Error(
    "Multiple valves required.\nConsult Maxton at 1-775-782-1700"
  );
}

function selectDSRValve(data: ValveData): ValveData {
  // Ensure required properties have values before using them
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;

  // Get original flow value if available (for form2), otherwise use calculated sngGPM
  const originalFlow =
    data.formType === "form2" && data.ratedFlow
      ? data.units === "inch"
        ? data.ratedFlow
        : data.ratedFlow / 3.785
      : sngGPM;

  // Initial pressure and flow checks
  if (sngOutMinPSI < 50) {
    throw new Error(
      "Inadequate system pressure.\nConsult Maxton at 1-775-782-1700"
    );
  }
  if (sngOutMinPSI > 1000) {
    throw new Error(
      "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
    );
  }
  if (sngGPM < 3) {
    throw new Error("Flow is under 3 GPM.\nConsult Maxton at 1-775-782-1700");
  }

  // EMV10 Checks - using originalFlow to avoid rounding issues
  if (originalFlow >= 3 && originalFlow <= 12) {
    if (sngOutMinPSI >= 150 && sngOutMaxPSI <= 1000) {
      throw new Error("Consult Maxton at 1-775-782-1700");
    }
    if (sngOutMinPSI < 150 && sngOutMaxPSI <= 1000) {
      throw new Error(
        "Inadequate system pressure.\nConsult Maxton at 1-775-782-1700"
      );
    }
  }

  // ADDED: Special case for 78-84 GPM at 100-174 PSI range
  // Per client requirements, this should return "Consult Maxton"
  if (
    originalFlow >= 78 &&
    originalFlow <= 84 &&
    sngOutMinPSI >= 100 &&
    sngOutMinPSI <= 174
  ) {
    throw new Error("Consult Maxton at 1-775-782-1700");
  }

  // CLIENT REQUIREMENT 1: GPM 2.5-5.99 at 100+ PSI should return "Contact Maxton"
  if (originalFlow >= 2.5 && originalFlow < 6 && sngOutMinPSI >= 100) {
    throw new Error("Consult Maxton at 1-775-782-1700");
  }

  const result = { ...data };

  // E1GPM Calculation
  result.E1GPM = sngOutMinPSI >= 175 ? 28.6 : 0;

  // E2GPM Calculation
  if (sngOutMinPSI >= 175 && sngOutMinPSI < 190) {
    result.E2GPM =
      -0.003 * Math.pow(sngOutMinPSI, 2) + 1.0816 * sngOutMinPSI - 8.3545;
  } else if (sngOutMinPSI >= 190 && sngOutMinPSI < 800) {
    result.E2GPM = 88.7;
  }

  // STDGPM Calculation
  if (sngOutMinPSI >= 175) {
    result.STDGPM =
      sngOutMinPSI <= 225
        ? -0.0032 * Math.pow(sngOutMinPSI, 2) + 1.5915 * sngOutMinPSI - 11.072
        : 185;
  } else {
    result.STDGPM = 0;
  }

  // UC1AGPM Calculation
  result.UC1AGPM =
    sngOutMinPSI < 240
      ? -0.0028 * Math.pow(sngOutMinPSI, 2) + 2.0213 * sngOutMinPSI + 37.027
      : 360;

  // HDSTDGPM Calculation
  if (sngOutMinPSI >= 100 && sngOutMinPSI < 200) {
    result.HDSTDGPM =
      -0.0028 * Math.pow(sngOutMinPSI, 2) + 1.51 * sngOutMinPSI - 4.869;
  } else if (sngOutMinPSI >= 200 && sngOutMinPSI < 800) {
    result.HDSTDGPM = 185;
  }

  // Valve Selection Logic
  if (originalFlow <= (result.E1GPM ?? 0)) {
    return UC4MRE1(result);
  }

  if (
    originalFlow <= (result.E2GPM ?? 0) &&
    originalFlow > (result.E1GPM ?? 0)
  ) {
    return UC4MRE2(result);
  }

  if (
    originalFlow <= (result.STDGPM ?? 0) &&
    originalFlow > (result.E2GPM ?? 0)
  ) {
    return UC4MRSTD(result);
  }

  if (
    originalFlow <= (result.UC1AGPM ?? 0) &&
    originalFlow > (result.STDGPM ?? 0)
  ) {
    return UC2A(result);
  }

  if (originalFlow >= 78 && originalFlow <= (result.HDSTDGPM ?? 0)) {
    return UC4MHD(result);
  }

  throw new Error(
    "Multiple valves required.\nConsult Maxton at 1-775-782-1700"
  );
}

// Valve Type Selection Functions
function UC4MS2(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;

  if (sngOutMaxPSI <= 760) {
    return { ...data, valveType: 2 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4ME1(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngOutMaxPSI <= 760) {
    if (
      sngGPM >= 78 &&
      data.HDSTDGPM &&
      sngGPM <= hdstdgpm &&
      sngOutMinPSI >= 100
    ) {
      return { ...data, valveType: 11 };
    }
    return { ...data, valveType: 3 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4ME2(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngOutMaxPSI <= 750) {
    if (
      sngGPM >= 78 &&
      data.HDSTDGPM &&
      sngGPM <= hdstdgpm &&
      sngOutMinPSI >= 100
    ) {
      return { ...data, valveType: 12 };
    }
    return { ...data, valveType: 4 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4Mstd(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngOutMaxPSI <= 710) {
    if (
      sngGPM >= 78 &&
      data.HDSTDGPM &&
      sngGPM <= hdstdgpm &&
      sngOutMinPSI >= 100
    ) {
      return { ...data, valveType: 13 };
    }
    return { ...data, valveType: 5 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4MRE1(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;

  if (sngOutMaxPSI <= 750) {
    return { ...data, valveType: 10 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4MRE2(data: ValveData): ValveData {
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngOutMinPSI >= 175) {
    if (sngOutMaxPSI <= 750) {
      if (sngGPM >= 78 && sngGPM <= hdstdgpm) {
        return { ...data, valveType: 14 };
      }
      return { ...data, valveType: 6 };
    }
    throw new Error(
      "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
    );
  }

  if (
    sngOutMinPSI >= 100 &&
    sngOutMaxPSI <= 750 &&
    sngGPM >= 78 &&
    sngGPM <= hdstdgpm
  ) {
    return { ...data, valveType: 16 };
  }
  if (sngOutMinPSI >= 50 && sngOutMaxPSI <= 560 && sngGPM >= 85) {
    return { ...data, valveType: 9 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4MHD(data: ValveData): ValveData {
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;

  if (sngOutMinPSI >= 100 && sngOutMaxPSI <= 710) {
    return { ...data, valveType: 16 };
  }
  throw new Error(
    "Multiple valves required.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC4MRSTD(data: ValveData): ValveData {
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngOutMinPSI >= 175) {
    if (sngOutMaxPSI <= 710) {
      if (sngGPM >= 78 && sngGPM <= hdstdgpm) {
        return { ...data, valveType: 15 };
      }
      return { ...data, valveType: 7 };
    }
    throw new Error(
      "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
    );
  }

  if (
    sngOutMinPSI >= 100 &&
    sngOutMaxPSI <= 710 &&
    sngGPM >= 78 &&
    sngGPM <= hdstdgpm
  ) {
    return { ...data, valveType: 16 };
  }
  if (sngOutMinPSI >= 50 && sngOutMaxPSI <= 560 && sngGPM >= 85) {
    return { ...data, valveType: 9 };
  }
  throw new Error(
    "Maximum pressure over ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC1A(data: ValveData): ValveData {
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;

  if (sngOutMaxPSI <= 550) {
    return { ...data, valveType: 8 };
  }
  throw new Error(
    "Maximum working pressure might exceed pressure ratings.\nConsult Maxton at 1-775-782-1700"
  );
}

function UC2A(data: ValveData): ValveData {
  const sngOutMinPSI = data.sngOutMinPSI ?? 0;
  const sngOutMaxPSI = data.sngOutMaxPSI ?? 0;
  const sngGPM = data.sngGPM ?? 0;
  const hdstdgpm = data.HDSTDGPM ?? 0;

  if (sngGPM >= 85 && sngOutMinPSI >= 50 && sngOutMaxPSI <= 560) {
    if (
      sngOutMinPSI >= 100 &&
      sngOutMaxPSI <= 710 &&
      sngGPM >= 78 &&
      sngGPM <= hdstdgpm
    ) {
      return { ...data, valveType: 17 };
    }
    return { ...data, valveType: 9 };
  }

  if (sngGPM >= 78 && sngGPM <= hdstdgpm) {
    return UC4MHD(data);
  }
  throw new Error(
    "Inadequate system pressure.\nConsult Maxton at 1-775-782-1700"
  );
}

function EMV10(data: ValveData): ValveData {
  throw new Error("Consult Maxton at 1-775-782-1700");
}
