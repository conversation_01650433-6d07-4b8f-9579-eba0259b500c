import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { RejectedUser } from "@/supabase/types";
import { NextApiResponse } from "next";

export default checkAdmin(
    matchRoute({
        GET: getRejectedUsersHandler,
    })
)

export interface GetRejectedUsersResponse {
    error?: string;
    rejectedUsers?: RejectedUser[];
    total?: number;
    totalPages?: number;
}

async function getRejectedUsersHandler(req: NextApiRequestWithUserContext, res: NextApiResponse<GetRejectedUsersResponse>) {
    const query = req.query;
    const page = query.page ? parseInt(query.page as string) : 1;
    const limit = query.limit ? parseInt(query.limit as string) : 10;

    const supabaseAdminClient = createSupabaseAdminClient();

    const { data, error } = await supabaseAdminClient
        .schema("public")
        .from("rejected_users")
        .select("*", { count: "exact" })
        .range((page - 1) * limit, page * limit - 1);

    if (error) {
        return res.status(500).json({ error: error.message });
    }

    const total = data?.length;
    const totalPages = Math.ceil(total / limit);

    return res.status(200).json({ rejectedUsers: data, total, totalPages });
}
