import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Heading } from "@stackshift-ui/heading";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import { Image } from "@stackshift-ui/image";
import React, { useState } from "react";
import { TestimonialProps } from ".";
import { Testimonial as iTestimonial } from "types";
import { MdChevronLeft, MdChevronRight } from "react-icons/md";
import { IoPerson } from "react-icons/io5";
import { urlFor } from "lib/sanity";

export default function Testimonial_F({
  caption,
  title,
  testimonials,
  mainImage,
}: TestimonialProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  // const [testimony, setTestimony] = useState(testimonials);

  const nextTestimonial = () => {
    setActiveIndex(
      (prevIndex) => (prevIndex + 1) % (testimonials?.length || 1)
    );
  };

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? (testimonials?.length || 1) - 1 : prevIndex - 1
    );
  };

  if (!testimonials || testimonials.length === 0) return null;

  const currentTestimonial = testimonials[activeIndex];

  console.log(currentTestimonial);

  return (
    <Section className="relative w-full py-16 md:py-24 overflow-hidden bg-white">
      {/* Background image or color */}
      <div className="absolute inset-0 z-0 ">
        <div
          className="w-full h-full "
          style={{
            backgroundImage: `url(${mainImage?.image})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        ></div>
      </div>

      {/* Content container */}
      <Container
        maxWidth={1280}
        className="relative z-20 mx-auto h-full bg-primary/70 py-10 px-20 rounded-xl"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-14 w-full">
          {/* Left column - Heading */}
          <div className="flex items-center w-full">
            <div>
              {caption && (
                <Text className="text-gray-200 font-medium uppercase tracking-wider mb-4">
                  {caption}
                </Text>
              )}
              <Heading className="text-white text-4xl md:text-5xl font-bold tracking-tight leading-tight">
                {title ? (
                  title
                ) : (
                  <>
                    WHAT
                    <br />
                    OUR CLIENT
                    <br />
                    SAY
                  </>
                )}
              </Heading>
            </div>
          </div>

          {/* Right column - Testimonial */}
          <div className="flex flex-col justify-center space-y-8 h-[500px] w-full">
            {/* Testimonial number */}
            <div className="border-b-4 border-gray-300 w-12">
              <Text className="text-white !text-3xl font-bold">
                {activeIndex + 1 < 10
                  ? `0${activeIndex + 1}`
                  : `${activeIndex + 1}`}
                .
              </Text>
            </div>

            {/* Testimonial quote with fixed height and scrolling */}
            <div className="h-[300px] overflow-y-auto pr-4 custom-scrollbar">
              <blockquote className="text-gray-200 text-lg italic">
                "{currentTestimonial?.testimony}"
              </blockquote>
            </div>

            {/* Author info */}
            <div className="flex items-center justify-between mt-auto border-t border-gray-300 pt-4">
              <div className="flex items-center space-x-4">
                {currentTestimonial?.mainImage?.image ? (
                  <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white">
                    <Image
                      src={`${currentTestimonial.mainImage.image}`}
                      alt={currentTestimonial.name || "Testimonial author"}
                      width={48}
                      height={48}
                      className="object-cover w-full h-full bg-gray-200"
                    />
                  </div>
                ) : (
                  <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white">
                    <IoPerson className="w-full h-full p-1 text-white" />
                  </div>
                )}
                <div>
                  <Text className="text-white font-semibold">
                    {currentTestimonial?.name}
                  </Text>
                  <Text className="text-sm text-gray-200">
                    {currentTestimonial?.jobTitle}
                  </Text>
                </div>
              </div>

              {/* Navigation buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={prevTestimonial}
                  className="w-10 h-10 rounded-full border border-white/30 flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                  aria-label="Previous testimonial"
                >
                  <MdChevronLeft size={20} />
                </button>
                <button
                  onClick={nextTestimonial}
                  className="w-10 h-10 rounded-full border border-white/30 flex items-center justify-center text-white hover:bg-white/10 transition-colors"
                  aria-label="Next testimonial"
                >
                  <MdChevronRight size={20} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
}

export { Testimonial_F };
