import { PortableText, PortableTextComponents } from "@portabletext/react";
import Button from "components/ui/button";
import { Container } from "@stackshift-ui/container";
import { Flex } from "@stackshift-ui/flex";
import { Form } from "@stackshift-ui/form";
import { FormField } from "@stackshift-ui/form-field";
import { Heading } from "@stackshift-ui/heading";
import { Image } from "@stackshift-ui/image";
import { Link } from "@stackshift-ui/link";
import { Section } from "@stackshift-ui/section";
import { Text } from "@stackshift-ui/text";
import React from "react";
import { ContactProps } from ".";
import { thankYouPageLink } from "../../../helper";
import { urlFor } from "lib/sanity";
import { ArrayOfTitleAndDescription, MyPortableTextComponents } from "types";
import { Form as iForm } from "../../../types";
import { Dialog } from "@/components/ui/dialog-shadcn";

export const textComponentBlockStyling: MyPortableTextComponents = {
  block: {
    h1: ({ children }) => {
      return (
        <h1 className="mb-6 leading-loose text-gray-900 text-7xl">
          {children}
        </h1>
      );
    },
    h2: ({ children }) => {
      return <h2 className="mb-4 text-2xl  text-primary">{children}</h2>;
    },
    h3: ({ children }) => {
      return <h3 className="mb-4 text-xl  text-gray-800">{children}</h3>;
    },
    h4: ({ children }) => {
      return (
        <h4 className="mb-6 text-xl leading-loose text-gray-900">{children}</h4>
      );
    },
    normal: ({ children }) => {
      return (
        <p className="mb-5 font-body text-xs md:text-sm text-gray-800 leading-loose">
          {children}
        </p>
      );
    },
    blockquote: ({ children }) => {
      return (
        <blockquote className="mb-6 italic leading-loose text-gray-500 px-14">
          - {children}
        </blockquote>
      );
    },
  },
  code: ({ value }) => {
    return (
      <pre data-language={value.language}>
        <code>{value.code}</code>
      </pre>
    );
  },

  list: {
    bullet: ({ children }) => {
      return (
        <ul className="pl-10 mb-3 leading-loose text-gray-900 list-disc">
          {children}
        </ul>
      );
    },
    number: ({ children }) => {
      return (
        <ol className="mb-6 leading-loose text-gray-900 list-decimal">
          {children}
        </ol>
      );
    },
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="mb-3 leading-loose text-gray-900">{children}</li>
    ),
  },
  marks: {
    strong: ({ children }) => <strong>{children}</strong>,
    em: ({ children }) => <em>{children}</em>,
    code: ({ children }) => <code>{children}</code>,
    link: ({ children, value }) => (
      <a
        aria-label={value.href ?? "external link"}
        className="text-primary hover:text-primary/70 border-b border-primary"
        href={value?.href}
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
  },
  types: {
    addImage: ({ value }) => {
      return (
        <div>
          <Image
            className="w-[200px] h-full mb-10"
            width={300}
            height={300}
            src={urlFor(value?.image)}
            alt={value?.image?.alt ?? value?.image?.image?.asset?._ref}
          />
        </div>
      );
    },
  },
};
export default function Contact_F({
  contactDescription,
  form,
  block,
  title,
  arrayOfTitleAndDescription,
}: ContactProps) {
  const [isFormOpen, setIsFormOpen] = React.useState(false);

  return (
    <Section className="pt-16 pb-20 bg-background">
      <Container maxWidth={1280}>
        <Flex direction="col" align="center" className="w-full">
          <div className="max-w-4xl w-full">
            <div className="text-center mb-8">
              {title && <Heading type="h2">{title}</Heading>}
              {contactDescription && (
                <Text muted className="mt-5 leading-loose">
                  {contactDescription}
                </Text>
              )}
            </div>

            <div className="w-full flex justify-center mb-8">
              <Button
                as="button"
                ariaLabel={`${form?.name ?? ""}`}
                variant="solid"
                onClick={() => setIsFormOpen(true)}
                // className="px-6 py-3 font-semibold text-white rounded-global bg-primary hover:bg-primary-foreground"
              >
                {form?.name}
              </Button>
            </div>

            <TitleAndDescriptionDropdown
              arrayOfTitleAndDescription={arrayOfTitleAndDescription}
            />
          </div>
        </Flex>
      </Container>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <Dialog.Content className="w-full">
          <div className="flex justify-end">
            <Dialog.Close onClick={() => setIsFormOpen(false)} />
          </div>
          <div className="max-h-[80vh] overflow-y-auto">
            <FormFields
              form={form}
              block={block}
              blockCustomization={blockCustomization}
            />
          </div>
        </Dialog.Content>
      </Dialog>
    </Section>
  );
}

function ContactTitleAndDescription({
  title,
  contactDescription,
}: {
  title?: string;
  contactDescription?: string;
}) {
  return (
    <div>
      {title && <Heading type="h3">{title}</Heading>}
      {contactDescription && (
        <Text muted className="mt-5 leading-loose">
          {contactDescription}
        </Text>
      )}
    </div>
  );
}

function TitleAndDescriptionDropdown({
  arrayOfTitleAndDescription,
}: {
  arrayOfTitleAndDescription?: ArrayOfTitleAndDescription[];
}) {
  const [openItem, setOpenItem] = React.useState<string | null>(null);

  if (!arrayOfTitleAndDescription?.length) return null;

  const toggleItem = (key: string) => {
    setOpenItem((prev) => (prev === key ? null : key));
  };

  return (
    <div className="space-y-2">
      {arrayOfTitleAndDescription.map((item, index) => (
        <div
          key={item?._key || index}
          className="border rounded-lg overflow-hidden"
        >
          <Button
            as="button"
            variant="unstyled"
            className={`w-full text-left flex justify-between items-center px-4 py-4 group ${
              openItem === (item?._key || index.toString())
                ? "bg-primary text-white"
                : "hover:bg-primary hover:text-white text-gray-900"
            }`}
            onClick={() => toggleItem(item?._key || index.toString())}
          >
            {/* <span className="font-semibold">{item?.title}</span> */}
            <Heading
              type="h3"
              className={`text-base font-semibold ${
                openItem === (item?._key || index.toString())
                  ? " !text-white"
                  : "group-hover:!text-white !text-gray-900"
              }`}
            >
              {item?.title}
            </Heading>
            <svg
              className={`w-5 h-5 transition-transform duration-500 ${
                openItem === (item?._key || index.toString())
                  ? "rotate-180"
                  : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </Button>

          <div
            className={`overflow-hidden transition-all duration-500 ease-in-out ${
              openItem === (item?._key || index.toString())
                ? "max-h-[1000px] opacity-100"
                : "max-h-0 opacity-0"
            }`}
          >
            <div className="px-4 py-3 bg-white shadow-sm border-t border-gray-100">
              {item?.firstColumn && (
                <div className="prose max-w-none">
                  <PortableText
                    value={item.firstColumn}
                    components={textComponentBlockStyling}
                    onMissingComponent={false}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

function SubtitleAndHeadingText({ form }: { form?: iForm }) {
  return (
    <div className="mb-6 text-left  lg:mb-10">
      {form?.name ? (
        <Heading type="h2" className="text-2xl lg:text-2xl">
          {form?.name}
        </Heading>
      ) : null}
      {form?.subtitle && (
        <Text muted className="mt-3 !text-sm leading-loose">
          {form?.subtitle}
        </Text>
      )}
    </div>
  );
}

function FormFields({
  form,
  block,
  blockCustomization,
}: {
  form?: iForm;
  block: any;
  blockCustomization: PortableTextComponents;
}) {
  if (!form) return null;

  // Filter out textarea fields and process the remaining fields
  const nonTextareaFields =
    form.fields?.filter((field) => field.type !== "textarea") || [];
  const textareaFields =
    form.fields?.filter((field) => field.type === "textarea") || [];

  return (
    <div className="w-full bg-white rounded-lg shadow-xl p-5 d:p-8">
      <SubtitleAndHeadingText form={form} />
      {form?.fields && (
        <Form
          id={form?.id ?? undefined}
          name="Contact-VariantA-Form"
          className="lg:mx-auto text-xs space-y-3 font-semibold"
          thankyouPage={thankYouPageLink(form?.thankYouPage)}
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {nonTextareaFields.map((formField, index) => {
              const isLastSingle =
                index === nonTextareaFields.length - 1 &&
                nonTextareaFields.length % 2 !== 0;

              return (
                <div
                  key={index}
                  className={isLastSingle ? "sm:col-span-2" : ""}
                >
                  <FormField
                    noLabel
                    variant="primary"
                    name={formField?.name ?? ""}
                    placeholder={formField?.name}
                    required={formField?.isRequired}
                    className="!bg-gray-100 !text-xs !text-black"
                    {...formField}
                  />
                </div>
              );
            })}
          </div>

          {textareaFields.length > 0 && (
            <div className="mt-3">
              {textareaFields.map((formField, index) => (
                <div key={index} className="sm:col-span-2">
                  <FormField
                    noLabel
                    variant="primary"
                    name={formField?.name ?? ""}
                    placeholder={formField?.name}
                    required={formField?.isRequired}
                    className="!bg-gray-100 !text-xs !text-black"
                    {...formField}
                  />
                </div>
              ))}
            </div>
          )}

          <div className="flex flex-col items-center">
            <div>
              <div className="webriq-recaptcha" />
            </div>
            {form?.buttonLabel && (
              <div className="w-full flex justify-center mt-5">
                <Button
                  as="button"
                  ariaLabel={form?.buttonLabel ?? "Contact form submit button"}
                  className="w-full px-6 py-2 font-bold leading-loose text-white transition duration-200 rounded-global bg-primary hover:bg-primary-foreground"
                  type="submit"
                >
                  {form?.buttonLabel}
                </Button>
              </div>
            )}
          </div>
        </Form>
      )}
    </div>
  );
}

// block styling as props to `components` of the PortableText component
const blockCustomization: PortableTextComponents = {
  marks: {
    internalLink: ({ children, value }) => (
      <Link
        aria-label={value.href ?? "internal link"}
        style={{ color: "red" }}
        href={value.slug.current}
      >
        {children}
      </Link>
    ),
    link: ({ children, value }) =>
      value.blank ? (
        <Link
          aria-label={value.href ?? "external link"}
          href={value.href}
          target="_blank"
          rel="noopener noreferrer"
        >
          {children}
        </Link>
      ) : (
        <Link
          aria-label={value.href ?? "external link"}
          style={{ color: "blue" }}
          href={value.href}
        >
          {children}
        </Link>
      ),
  },
};

export { Contact_F };
