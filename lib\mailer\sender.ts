import { MailerClient, SendMailOption } from "./";

export async function sendEmail(mailer: MailerClient, {
    to,
    from,
    subject,
    cc = [],
    body,
    attachments = [],
    bcc = [],
}: SendMailOption) {
    try {
        await mailer.sendMail({
            from, // sender address
            to, // list of receivers, could be "string" or "array"
            subject, //
            cc: cc,
            html: body, // html body
            attachments,
            bcc,
        });
    } catch (error) {
        console.error(error);
    }
};