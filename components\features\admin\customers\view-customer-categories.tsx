import { Badge } from "@/components/ui/badge";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    <PERSON><PERSON>Header,
    <PERSON><PERSON>Title,
    DialogTrigger,
} from "@/components/ui/dialog-shadcn";
import {
    DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/shadcn-button";
import {
    TableBody,
    TableCell,
    Table as TableComponent,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { checkUserHasPermission } from "@/middlewares/auth-middleware";
import { PublicUserWithCustomer } from "@/pages/api/users/index";
import {
    useDeleteCustomerCategoryMutation,
    useGetCustomerCategoriesQuery
} from "@/queries/admin-queries";
import useAuthStore from "@/stores/auth-store";
import {
    Folder,
    Loader2
} from "lucide-react";
import { useState } from "react";

export function ViewCustomerCategoriesDialog({
    user,
}: {
    user: PublicUserWithCustomer;
}) {
    const [open, setOpen] = useState(false);
    const token = useAuthStore((state) => state.token);

    const customerId = user.customer_data?.[0]?.id;

    const {
        data: customerCategories,
        isLoading,
        error,
        refetch,
    } = useGetCustomerCategoriesQuery(customerId ?? "", token);

    const deleteCustomerCategoryMutation =
        useDeleteCustomerCategoryMutation(token);
    const permissions = useAuthStore((state) => state.permissions);
    const hasPermission = checkUserHasPermission(
        permissions,
        "delete:customer_categories"
    );

    const handleDelete = async (categoryId: string) => {
        if (!customerId) return;

        if (!hasPermission) {
            toast({
                title: "Error",
                description: "You are not authorized to remove customer categories",
                variant: "destructive",
                duration: 3000,
            });
            return;
        }

        try {
            await deleteCustomerCategoryMutation.mutateAsync({
                customerId,
                categoryId,
            });

            toast({
                title: "Success",
                description: "Category removed successfully",
                variant: "default",
                duration: 2000,
            });

            refetch();
        } catch (error: any) {
            toast({
                title: "Error",
                description: error.message || "Failed to remove category",
                variant: "destructive",
                duration: 2000,
            });
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Folder className="mr-2 h-4 w-4" />
                    <span>View assigned categories</span>
                </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent className="sm:max-w-3xl">
                <DialogHeader>
                    <DialogTitle>Customer Categories</DialogTitle>
                    <DialogDescription>
                        Categories assigned to {user.first_name} {user.last_name}
                    </DialogDescription>
                </DialogHeader>

                {isLoading ? (
                    <div className="flex justify-center py-4">
                        <Loader2 className="h-8 w-8 animate-spin" />
                    </div>
                ) : error ? (
                    <div className="text-red-500 py-2">
                        Error loading categories: {(error as Error).message}
                    </div>
                ) : customerCategories && customerCategories.length > 0 ? (
                    <ScrollArea className="h-[300px]">
                        <TableComponent>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[50%]">Name</TableHead>
                                    <TableHead className="w-[30%]">Value</TableHead>
                                    <TableHead className="text-right w-[20%]">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {customerCategories.map((item: any) => (
                                    <TableRow key={item.id}>
                                        <TableCell className="font-medium">
                                            {item.category?.name || "N/A"}
                                        </TableCell>
                                        <TableCell>
                                            {item.category?.value ? (
                                                <Badge
                                                    variant="outline"
                                                    className="font-mono text-xs bg-muted text-muted-foreground"
                                                >
                                                    {item.category.value}
                                                </Badge>
                                            ) : (
                                                "N/A"
                                            )}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDelete(item.category_id)}
                                                disabled={
                                                    deleteCustomerCategoryMutation.isPending ||
                                                    !hasPermission
                                                }
                                            >
                                                Remove
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </TableComponent>
                    </ScrollArea>
                ) : (
                    <div className="py-4 text-center text-muted-foreground">
                        No categories assigned to this customer.
                    </div>
                )}

                <DialogFooter className="gap-2">
                    <Button type="button" onClick={() => setOpen(false)}>
                        Close
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}