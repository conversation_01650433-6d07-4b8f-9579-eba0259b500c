import AdminLayout from "@/components/features/admin/layout";
import { PendingBillingAddressesDataTable } from "@/components/features/admin/requests/pending-billing-addresses";
import Head from "next/head";


export default function Requests() {
    return (
        <AdminLayout>
            <Head>
                <title>Customers | Pending Billing Addresses</title>
            </Head>
            <div className="space-y-4">
                <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    <div>
                        <h2 className="text-4xl font-bold tracking-tight">Pending Billing Addresses</h2>
                        <p className="text-sm text-muted-foreground">
                            Manage pending billing addresses for customers.
                        </p>
                    </div>
                </div>
                <div className="relative w-full h-full flex flex-col gap-12 pt-5">
                    <PendingBillingAddressesDataTable />
                </div>
            </div>
        </AdminLayout>
    )
}
