import { Button } from "@/components/ui/shadcn-button";
import { useGetImages } from "@/queries/customer-queries";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useMemo, useState } from "react";

interface ProductImageCarouselProps {
  mainImage: string;
  additionalImages?: string[] | null;
}

export function ProductImageCarousel({
  mainImage,
  additionalImages,
}: ProductImageCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Build the full array of images
  const allImages = useMemo(() => {
    // Start with the main image
    const images = [mainImage];

    // Add additional images if available
    if (additionalImages && additionalImages.length > 0) {
      images.push(...additionalImages);
    }

    return images.filter(Boolean);
  }, [mainImage, additionalImages]);

  // Use useGetImages to load all images at once (proper hook usage)
  const imageResults = useGetImages(allImages);

  // Get the current displayed image URL
  const displayedImageUrl = useMemo(() => {
    // Check if we have results and the current index exists
    if (
      imageResults &&
      imageResults[currentIndex] &&
      imageResults[currentIndex].data
    ) {
      // The data might be a string or undefined, handle both cases
      return typeof imageResults[currentIndex].data === "string"
        ? imageResults[currentIndex].data
        : allImages[currentIndex];
    }
    return allImages[currentIndex];
  }, [allImages, currentIndex, imageResults]);

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? allImages.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === allImages.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  // Function to get the image URL for a specific index
  const getImageUrlForIndex = (index: number): string => {
    // Same logic as above, properly handle types
    if (imageResults && imageResults[index] && imageResults[index].data) {
      return typeof imageResults[index].data === "string"
        ? imageResults[index].data
        : allImages[index];
    }
    return allImages[index];
  };

  // Always display the carousel layout, regardless of image count
  return (
    <div className="w-full max-w-lg mx-auto flex flex-col space-y-4">
      {/* Main carousel */}
      <div className="relative aspect-square bg-muted rounded-none overflow-hidden shadow-sm border border-black/40 p-6">
        {/* Main Image */}
        <img
          src={displayedImageUrl}
          alt={`Product image ${currentIndex + 1}`}
          className="w-full h-full object-contain transition-opacity duration-500"
        />

        {/* Navigation buttons - only show if there are multiple images */}
        {allImages.length > 1 && (
          <div className="absolute inset-0 flex items-center justify-between p-4">
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
              onClick={goToPrevious}
              aria-label="Previous image"
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full bg-white/80 backdrop-blur-sm hover:bg-white"
              onClick={goToNext}
              aria-label="Next image"
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </div>
        )}

        {/* Indicators - only show if there are multiple images */}
        {allImages.length > 1 && (
          <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
            {allImages.map((_, index) => (
              <button
                key={`carousel-indicator-${index}`}
                onClick={() => goToSlide(index)}
                className={`w-2.5 h-2.5 rounded-full ${
                  index === currentIndex
                    ? "bg-primary"
                    : "bg-gray-300 hover:bg-gray-400"
                } transition-colors`}
                aria-label={`Go to slide ${index + 1}`}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    goToSlide(index);
                    e.preventDefault();
                  }
                }}
              />
            ))}
          </div>
        )}
      </div>

      {/* Thumbnails - always show even with a single image */}
      <div className="flex justify-start gap-2 overflow-x-auto pb-2 -mt-1">
        {allImages.map((_, index) => {
          // Get the URL for this thumbnail
          const thumbSrc = getImageUrlForIndex(index);

          return (
            <button
              key={`carousel-thumbnail-${index}`}
              onClick={() => goToSlide(index)}
              className={`relative flex-shrink-0 w-16 h-16 border-2 rounded-none overflow-hidden ${
                index === currentIndex
                  ? "border-primary"
                  : "border-transparent hover:border-gray-300"
              } transition-colors`}
              aria-label={`View image ${index + 1}`}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  goToSlide(index);
                  e.preventDefault();
                }
              }}
            >
              <img
                src={thumbSrc}
                alt={`Thumbnail ${index + 1}`}
                className="w-full h-full object-contain object-center"
              />
            </button>
          );
        })}
      </div>
    </div>
  );
}
