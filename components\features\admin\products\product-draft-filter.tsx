import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/shadcn-button";
import { Filter } from "lucide-react";
import { Table } from "@tanstack/react-table";

interface ProductDraftFilterProps {
  table: Table<any>;
}

export function ProductDraftFilter({ table }: ProductDraftFilterProps) {
  const statusOptions = [
    { value: "true", label: "Draft" },
    { value: "false", label: "Published" },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="hidden w-fit lg:flex h-12 px-4 border-dashed">
          <Filter className="mr-2 h-4 w-4" />
          Status
          {table.getColumn("draft")?.getFilterValue() ? (
            <Badge variant="secondary" className="ml-2 rounded-sm px-1 font-normal">
              1
            </Badge>
          ) : null}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[200px]">
        {statusOptions.map((option) => {
          const selected = table.getColumn("draft")?.getFilterValue() === option.value;

          return (
            <DropdownMenuCheckboxItem
              key={option.value}
              checked={selected}
              onCheckedChange={() => {
                if (selected) {
                  table.getColumn("draft")?.setFilterValue(undefined);
                } else {
                  table.getColumn("draft")?.setFilterValue(option.value);
                }
              }}
            >
              {option.label}
            </DropdownMenuCheckboxItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 