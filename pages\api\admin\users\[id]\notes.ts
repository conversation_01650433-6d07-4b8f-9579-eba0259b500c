import { NextApiRequestWithUserContext } from "@/middlewares";
import { checkAdmin } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiResponse } from "next";
import { z } from "zod";

export default checkAdmin(
  matchRoute({
    PUT: updateUserNotesHandler,
  })
);

export const userNotesSchema = z.object({
  notes: z.string().nullable().optional(),
});

export type UserNotes = z.infer<typeof userNotesSchema>;

export interface UpdateUserNotesResponse {
  error?: string;
  success?: boolean;
}

async function updateUserNotesHandler(
  req: NextApiRequestWithUserContext,
  res: NextApiResponse<UpdateUserNotesResponse>
) {
  const id = req.query.id?.toString();

  if (!id) {
    return res.status(400).json({ error: "Missing user id" });
  }

  const result = userNotesSchema.safeParse(req.body);

  if (!result.success) {
    return res.status(400).json({ error: result.error.message });
  }

  const { notes } = result.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { error } = await supabaseAdminClient
    .from("users")
    .update({
      notes: notes,
    } as any)
    .eq("id", id);

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.status(200).json({ success: true });
}
