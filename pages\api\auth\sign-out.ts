import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";
import { resetSessionCookie } from "./refresh";

export default matchRoute({
    POST: handler
});

const signoutSchema = z.object({
    access_token: z.string().min(1)
});

async function handler(req: NextApiRequest, res: NextApiResponse) {
    const data = signoutSchema.safeParse(req.body);

    if (data.error) {
        return res.status(400).json({
            error: data.error.issues[0].message
        });
    }

    const supabaseAdminClient = createSupabaseAdminClient();
    await supabaseAdminClient.auth.admin.signOut(data.data.access_token);

    resetSessionCookie(res);

    return res.status(200).json({
        success: true,
        message: "Session ended."
    })
}