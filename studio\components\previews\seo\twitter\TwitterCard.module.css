.seoItem {
  margin: 15px;
  margin-top: 50px;
}

.tweetWrapper {
  position: relative;
  text-align: left;
  font-size: 14px;
  background-color: white;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
  border-color: #f2f3f5;
  padding: 15px 40px 15px 60px;
}

.tweet<PERSON>uthor {
  position: relative;
  margin-right: 75px;
  margin-bottom: 10px;
}

.tweetAuthorAvatar {
  position: absolute;
  top: 3px;
  left: -42px;
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

.tweetAuthorName {
  margin-right: 4px;
  font-weight: 700;
  color: #333;
}

.tweetAuthorHandle {
  padding: 0;
  margin-top: 2px;
  margin-left: 2px;
  font-size: 12px;
  color: #333;
}

.tweetBullet {
  margin-top: 2px;
  margin-left: 4px;
  margin-right: 2px;
}

.tweetText p {
  margin: 0 0 10px;
}

.tweetUrlWrapper {
  text-decoration: none;
  color: inherit;
}

.tweetCardPreview {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 10px;
  border-color: rgba(136, 153, 166, 0.5);
  border-width: 1px;
  border-style: solid;
}

.tweetCardContent {
  padding: 10px 12px;
  color: black;
}

.tweetCardImageContainer {
  display: flex;
  width: 100%;
  overflow: hidden;
}

.tweetCardImage {
  max-height: 250px;
  width: 100%;
  object-fit: cover;
}

.tweetCardTitle {
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
}

.tweetCardTitle a {
  color: #1d2129;
  font-family: inherit;
  font-size: 16px;
  font-weight: bold;
  line-height: 20px;
  margin: 3px 0 0;
  padding-top: 2px;
  text-decoration: none;
}

.tweetCardDescription {
  max-height: 3.9em;
}

.tweetCardDescription p {
  overflow: hidden;
  margin-top: 0.32333em;
}

.tweetCardDestination {
  margin-top: 0.32333em;
  text-transform: lowercase;
  color: #8899a6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
