import { checkAdmin, checkPermission } from "@/middlewares/auth-middleware";
import { matchRoute } from "@/middlewares/match-route";
import { createSupabaseAdminClient } from "@/supabase";
import { Category } from "@/supabase/types";
import { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

export default matchRoute({
  GET: getAllCategoriesHandler,
  POST: checkAdmin(checkPermission("create:categories", createCategoryHandler)),
});

export interface CategoryWithParent extends Category {
  parent_category_data?: Partial<Category>;
}

export interface GetAllCategoriesResponse {
  error?: string;
  categories?: CategoryWithParent[];
  total?: number;
  totalPages?: number;
}

async function getAllCategoriesHandler(
  req: NextApiRequest,
  res: NextApiResponse<GetAllCategoriesResponse>,
) {
  const { page = 1, limit = 10 } = req.query;

  const pageNumber = Number(page);
  const limitNumber = Number(limit);

  if (isNaN(pageNumber) || isNaN(limitNumber)) {
    return res.status(400).json({ error: "Invalid page or limit" });
  }

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, count, error } = await supabaseAdminClient
    .schema("public")
    .from("categories")
    .select("*, parent_category_data:parent_category_id(id, name, value)", {
      count: "exact",
    })
    .range((pageNumber - 1) * limitNumber, pageNumber * limitNumber - 1)
    .order("created_at", { ascending: false });

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  const total = count ?? 0;
  const totalPages = Math.ceil(total / limitNumber);

  return res.status(200).json({
    categories: data as CategoryWithParent[],
    total,
    totalPages,
  });
}

export const createCategorySchema = z.object({
  name: z.string().min(1, { message: "Name is required" }),
  value: z.string().min(1, { message: "Value is required" }),
  parent_category_id: z.string().optional(),
});

export type CreateCategoryRequest = z.infer<typeof createCategorySchema>;

export interface CreateCategoryResponse {
  error?: string;
  category?: Category;
}

async function createCategoryHandler(
  req: NextApiRequest,
  res: NextApiResponse<CreateCategoryResponse>,
) {
  const parsedData = createCategorySchema.safeParse(req.body);

  if (parsedData.error) {
    return res.status(400).json({ error: parsedData.error.issues[0].message });
  }

  const { name, value, parent_category_id } = parsedData.data;

  const supabaseAdminClient = createSupabaseAdminClient();

  const { data, error } = await supabaseAdminClient
    .schema("public")
    .from("categories")
    .insert({ name, value, parent_category_id })
    .select("*")
    .single();

  if (error) {
    return res.status(400).json({ error: error.message });
  }

  return res.status(200).json({ category: data });
}
