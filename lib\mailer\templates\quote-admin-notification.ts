import emailTemplate from ".";

interface QuoteAdminNotificationProps {
  to: string;
  userName: string;
  userEmail: string;
  companyName?: string;
  productName: string;
  quantity: number;
  quoteText?: string;
  userId: string;
  productId: string;
}

export default function createQuoteAdminNotificationTemplate({
  to,
  userName,
  userEmail,
  companyName,
  productName,
  quantity,
  quoteText,
  userId,
  productId,
}: QuoteAdminNotificationProps) {
  const APP_NAME = process.env.APP_NAME ?? "Maxton Manufacturing Company";
  const APP_EMAIL_FROM = process.env.MAIL_FROM ?? "<EMAIL>";
  const from = `${APP_NAME} <${APP_EMAIL_FROM}>`;
  const adminDashboard = `${process.env.NEXT_PUBLIC_SITE_URL}/admin/quotes`;

  return emailTemplate({
    to,
    subject: `New Quote Request from - ${userName}`,
    from,
    body: `
      <h2 style="color: #0045d8; background-color: #e6f0ff; padding: 10px; border-left: 4px solid #0045d8;">New Quote Request</h2>
      <p>A new quote request has been submitted through the website.</p>
      
      <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f5f5f5;">
        <h3 style="margin-top: 0;">Quote Details</h3>
        <ul style="padding-left: 20px; margin-bottom: 0;">
          <li><strong>Product:</strong> ${productName} (ID: ${productId})</li>
          <li><strong>Quantity:</strong> ${quantity}</li>
          ${
            quoteText
              ? `<li><strong>Additional Information:</strong><br />
              <div style="margin: 10px 0; padding: 10px; background-color: #fff; border-left: 3px solid #0045d8;">
                ${quoteText.replace(/\n/g, "<br />")}
              </div>
            </li>`
              : ""
          }
        </ul>
        
        <h3 style="margin-top: 15px;">Customer Information</h3>
        <ul style="padding-left: 20px; margin-bottom: 0;">
          <li><strong>Name:</strong> ${userName}</li>
          ${
            companyName
              ? `<li><strong>Company/Business:</strong> ${companyName}</li>`
              : ""
          }
          <li><strong>Email:</strong> ${userEmail}</li>
          <li><strong>User ID:</strong> ${userId}</li>
        </ul>
      </div>
      
      <p>Please review this quote request and respond to the customer as soon as possible.</p>
    `,
  });
}
