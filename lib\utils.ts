import { ProductOptionProps } from "@/components/features/store/product-option";
import { ProductOptionNumber, ProductOptionSelect, ProductOptions } from "@/supabase/types";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

type Currency = "USD" | "EUR" | "GBP" | "CAD" | "JPY" | "KRW" | "PHP";

export function formatPrice(price: number, currency: Currency = "USD") {
  return price.toLocaleString("en-US", {
    style: "currency",
    currency: currency,
  })
}

export function parseProductOptions(options: ProductOptions): ProductOptionProps[] {
  if (!options) return [];

  try {
    if (typeof options === 'string') {
      return JSON.parse(options);
    }

    if (Array.isArray(options)) {
      return options.map(option => {
        const transformedOptions = option.type === 'select' ? (option as ProductOptionSelect).options?.map(opt => ({
          name: opt.name ?? "",
          value: opt.value,
          price: opt.price ?? 0
        })) : undefined;
        const min = option.type === 'number' ? (option as ProductOptionNumber).min : undefined;
        const max = option.type === 'number' ? (option as ProductOptionNumber).max : undefined;

        return {
          name: option.name,
          type: option.type,
          value: option.value,
          required: true,
          options: transformedOptions,
          min,
          max,
        };
      });
    }

    return [];
  } catch (error) {
    console.error("Error parsing product options:", error);
    return [];
  }
}